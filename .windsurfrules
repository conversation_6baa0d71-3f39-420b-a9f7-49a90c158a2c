# Rules for HanaWeb Metadata Management

# Svelte Component Rules

- You are in latest Svelete 5 with SvelteKit 2.
- Use Svelte 5 runes syntax ($state, $derived, $effect, $props), no double dollar sign allowed.
- Use Svelte 5 Snippets instead of slots
- You are in typescript, all the params require types, implicit any type is not allowed
- Only use shadcn-svelte@next and avoid shadcn-svelte for Svelte 4
- Use callback props instead of createEventDispatcher or events
- Use onclick={handler} instead of on:click={handler}
- In genertal follow the official Svelte 5 Migration Guide to avoid legacy syntax
- Use proper TypeScript types for all components and props
- when load function, do not await on load so we are not blocking the initial render, await in palce of using the loaded data instead.
- Provide loading states and error feedback for async operations (APIs, database queries, file uploads, form submissions, etc.)
- Follow shadcn-svelte component patterns
- Clear sensitive data after component unmounts
- Use {@render children()} instead of <slot> for content projection
- Ensure render tags only contain function call expressions
- Use $props() for component props, $bindable for bindable props
- Use $derived for computed values
- Use $state for reactive state management
- Handle deprecated API warnings proactively
- for shadcn-svelte add command, always pass in --yes to avoid interactive prompts
- always use '@sveltejs/kit' native types whenever possible
- avoid deprciated svelte features such as $app/stores or $page.
- Never modify $derived values directly, use $state with $effect instead
- Prefer URL-driven state over local state for navigation features
- Use SvelteKit's load function to handle URL parameters
- Never use `<svelte:component>`
- Self-closing HTML tags for non-void elements are ambiguous — use `<icon ...></icon>` rather than `<icon ... />`
- Never use `export let` for props.
- Never use $$ of anything, $$ is gone in Svelte 5
- Do not clean up code that is out of your request.
- Do not touch my design unless I ask you to in my user prompt.

# Code Organization

- Keep components modular and focused
- Follow consistent naming conventions
- Colocate components and pages
- Keep business logic separate from UI
- A senior frontend enginner will read your code, make sure your code is easy to follow
- Let the code speak for itself instead of comments

# Form Handling

- Validate inputs before submission
- Show clear error messages
- Handle edge cases gracefully

# API Integration

- Use typed API responses
- Handle API and GrpahQL errors gracefully
- Cache responses when appropriate
- Use Supabase official SDK for API calls

# Supabase Integration

- Use createServerClient with getAll and setAll methods for cookie handling
- Properly type and handle cookie data structures
- Implement proper session and user management in hooks.server.ts
- Use safeGetSession pattern for reliable session checks
- Handle both client and server-side Supabase initialization properly

# SvelteKit Auth Patterns

- Separate server-side logic into +layout.server.ts
- Handle cookie management at the server level
- Implement proper auth guards in hooks.server.ts
- Use redirect(303, path) for auth-related redirects
- Provide fallback values for data in load functions

# Localization Rules for JSONB Columns

1. Always use the `LocalizedText` type for JSONB columns containing localized text:

```typescript
type LocalizedText = {
	[key: string]: string;
};
```

2. Use `getLocalizedText` helper for retrieving localized text:

```typescript
getLocalizedText(jsonbColumn as LocalizedText, currentLocale);
```

3. When creating new localized text objects, always cast as `LocalizedText`:

```typescript
{
  [currentLocale]: text
} as LocalizedText
```

4. Handle null/undefined JSONB values by casting to LocalizedText:

```typescript
landmark?.title_short as LocalizedText;
```

5. Always provide a locale parameter when retrieving text to ensure proper fallback behavior

# Props Typing Rules in Svelte 5

1. Always type props using an interface:

```typescript
interface Props {
	propName: PropType;
}
```

2. Use type annotation with $props() instead of generic type:

```typescript
// ✅ DO
let { propName }: Props = $props();

// ❌ DON'T
let { propName } = $props<Props>();
```

3. For optional props, use the `?` operator in the interface:

```typescript
interface Props {
	optionalProp?: string;
	requiredProp: string;
}
```

4. For union types in props, use proper indentation:

```typescript
interface Props {
	prop?: TypeA | TypeB | null;
}
```

5. Always define prop types at the top of the component, before any state or derived values

Talk as less as possible, focus on the code quality
