# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development Tasks

- `npm run dev` - Start development server with HMR on port 8888
- `npm run build` - Build production bundle
- `npm run preview` - Preview production build locally
- `npm run check` - Run Svelte type checking
- `npm run check:watch` - Type checking in watch mode
- `npm run lint` - Check code formatting with Prettier
- `npm run format` - Format code with Prettier
- `npm run supabase` - Generate TypeScript types from Supabase schema
- `npm run dev:clean` - Clean .svelte-kit cache and restart dev server

### Key Configuration

- Development server runs on port 8888 with host binding
- Vercel adapter configured for deployment
- TypeScript strict mode enabled
- Supabase project ID: `tilepcwykspsgbllptbf`

## Architecture Overview

### Tech Stack

- **Frontend**: Svelte 5 with SvelteKit 2, TypeScript
- **UI Components**: shadcn-svelte (next version), Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **Payments**: Stripe integration
- **Internationalization**: Paraglide (en, zh, ko, ja)
- **Notifications**: Novu framework
- **AI/ML**: OpenAI integration via Mastra client
- **Background Jobs**: Trigger.dev

### Core Concepts

#### Multi-Brand Architecture

- Each brand has its own domain/subdomain routing
- Brand resolution via `hooks.server.ts` using domain matching
- Brand-specific feature permissions and access control
- Development uses `my.googirlart.com` as default brand

#### Authentication & Authorization

- Supabase Auth with JWT validation
- Email verification required for private routes
- Role-based permissions via `profile_feature_access` table
- Brand owners have all permissions by default
- Route-level permission guards in `hooks.server.ts`

#### Data Layer Patterns

- **Never destructure data from load functions** - breaks reactivity
- Always access via `data.property` pattern
- Use `invalidateAll()` for data refreshing
- Supabase types auto-generated via `npm run supabase`

#### Localization Strategy

- JSONB columns for localized text using `LocalizedText` type
- `getLocalizedText()` helper for retrieving translated content
- Fallback behavior for missing translations
- Multi-language support with Paraglide

### Key Directories

#### `/src/routes/`

- **`/auth/`** - Authentication flows (sign-in, sign-up, verification)
- **`/event/`** - Public event browsing and registration
- **`/private/`** - Protected admin/user dashboard
- **`/api/`** - API endpoints for various services
- **`/@[username]/`** - User profile pages

#### `/src/lib/`

- **`/components/shared/`** - Reusable components across the app
- **`/components/ui/`** - shadcn-svelte UI components
- **`/supabase/`** - Database types and client configuration
- **`/types/`** - TypeScript type definitions
- **`/utils/`** - Utility functions and helpers

### Important Files

#### `src/hooks.server.ts`

Multi-stage request handling:

1. **Supabase client setup** - Cookie-based session management
2. **Brand initialization** - Domain-to-brand resolution with caching
3. **Auth guard** - Route protection and email verification
4. **Permission guard** - Feature-based access control

#### `src/routes/+layout.server.ts`

- Brand-specific navigation generation
- Feature access loading (non-blocking)
- User session management
- Dynamic sidebar content based on permissions

#### `src/lib/supabase/database.types.ts`

Auto-generated TypeScript types from Supabase schema. Regenerate with `npm run supabase`.

### Development Workflow

#### Local Development

- Uses `my.googirlart.com` as default brand for testing
- IP/localhost access supported with adjusted cookie settings
- HMR timeout set to 5 seconds for stability

#### Database Changes

- Always run `npm run supabase` after schema changes
- Types are committed to version control
- Use proper TypeScript types for all database operations

#### Component Development

- Follow Svelte 5 patterns (runes, no legacy syntax)
- Use callback props instead of event dispatchers
- Implement proper loading states and error handling
- Maintain consistent prop typing with interfaces
- **ALWAYS use '@lucide/svelte' for Lucide icons, NEVER 'lucide-svelte'**

#### Form Handling

- Use Superforms for form validation and submission
- Schema-based validation with Zod
- Proper error messaging and user feedback

### Security Considerations

- Cookie-based session management with proper security flags
- CSRF protection via SameSite settings
- JWT validation on each request
- Feature-based permission system
- Brand isolation via database row-level security
