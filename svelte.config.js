import adapter from '@sveltejs/adapter-vercel';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	kit: {
		adapter: adapter({
			images: {
				sizes: [640, 750, 828, 1080, 1200, 1920],
				formats: ['image/avif', 'image/webp'],
				minimumCacheTTL: 300,
				domains: [],
				remotePatterns: [
					{
						protocol: 'https',
						hostname: '^.*\\.supabase\\.co$',
						pathname: '^/storage/v1/object/public/.*$'
					}
				],
				localPatterns: [
					{
						pathname: '^/assets/.*$'
					}
				]
			}
		}),
		prerender: {
			handleMissingId: 'warn'
		},
		alias: {
			'@/*': './src/lib/*',
			'$lib/components': './src/lib/components',
			'$lib/utils': './src/lib/utils'
		}
	},
	preprocess: vitePreprocess()
};

export default config;
