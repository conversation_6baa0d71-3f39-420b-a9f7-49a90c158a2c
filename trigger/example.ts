import { task } from '@trigger.dev/sdk/v3';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '../src/lib/supabase/database.types';

/**
 * A simple example task that logs a message
 */
export const helloWorld = task({
	id: 'hello-world',
	run: async (payload: { message: string }) => {
		console.log(payload.message);
		return `Hello, ${payload.message}!`;
	}
});

/**
 * Example task that reads data from Supabase
 */
export const supabaseReadExample = task({
	id: 'supabase-read-example',
	run: async (payload: { eventId: string }) => {
		const { eventId } = payload;

		// Initialize Supabase client with service role key for admin access
		const supabase = createClient<Database>(
			process.env.PUBLIC_SUPABASE_URL as string,
			process.env.SUPABASE_SERVICE_ROLE_KEY as string
		);

		// Get the event data
		const { data: event, error } = await supabase
			.from('event')
			.select('id, start_at, title_full, product_id')
			.eq('id', eventId)
			.single();

		if (error) {
			throw new Error(`Failed to fetch event: ${error.message}`);
		}

		if (!event) {
			throw new Error(`Event not found: ${eventId}`);
		}

		return {
			message: `Successfully retrieved event: ${eventId}`,
			event
		};
	}
});
