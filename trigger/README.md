# Trigger.dev Tasks

This directory contains all the background tasks for our application, powered by [Trigger.dev](https://trigger.dev) - an open source background jobs framework.

## Directory Structure

- `/trigger` - Root directory for all Trigger.dev tasks
  - `client.ts` - Creates the Trigger.dev client instance
  - `example.ts` - A simple example task and Supabase read example
  - `product-cancel.ts` - Product cancellation notification task
  - `/utils` - Helper utilities for tasks
    - `novu-notifications.ts` - Helper for sending Novu notifications
    - `product-cancel-trigger.ts` - Logic for processing product cancellations

## Development

To run the Trigger.dev development server and test these tasks:

```bash
npx trigger.dev@latest dev
```

This will watch for changes in the `/trigger` directory and register tasks with the Trigger.dev platform.

## Available Tasks

### Product Cancellation

Triggered when a product is canceled through the admin interface. The task:

1. Fetches details about the event, registrations, and brand
2. Sends cancellation notifications to all registered users
3. Updates the event's publishing state in the database
4. Returns a summary of successful and failed notifications

This task is triggered from `src/routes/private/request/product-cancel/+page.server.ts` with:

```typescript
await client.sendEvent({
	name: 'product.cancelled',
	payload: {
		eventId,
		reasonId,
		comments,
		brandId: brand.id
	}
});
```

### Supabase Integration

We use Supabase as our database provider, and have implemented the following best practices for Supabase operations in Trigger.dev:

1. **Service Role Authentication** - Using service role keys for admin-level access that bypasses Row Level Security
2. **Proper Error Handling** - Different handling strategies based on error types:
   - Using `AbortTaskRunError` for non-retryable errors (e.g., records not found)
   - Using standard errors for transient issues that should be retried
3. **Validation** - Validating required environment variables before operations
4. **Database Updates** - Updating the database state as part of the workflow
5. **Detailed Logging** - Comprehensive logging for monitoring and debugging

### Example Supabase Task

The `supabaseReadExample` task demonstrates reading data from Supabase:

```typescript
const supabaseReadExample = task({
	id: 'supabase-read-example',
	run: async (payload: { eventId: string }) => {
		// Initialize Supabase client
		const supabase = createClient<Database>(
			process.env.PUBLIC_SUPABASE_URL as string,
			process.env.SUPABASE_SERVICE_ROLE_KEY as string
		);

		// Query the database
		const { data, error } = await supabase
			.from('event')
			.select('id, start_at, title_full')
			.eq('id', payload.eventId)
			.single();

		// Handle errors and return results
		// ...
	}
});
```

## Novu Integration

This project integrates [Novu](https://novu.co/), an open-source notification infrastructure, with Trigger.dev for reliable background notification processing. The integration:

1. Uses the `@novu/api` package for communication with Novu
2. Implements retry logic for resilient notification delivery
3. Provides detailed success/failure metrics for each notification batch

### How It Works

When a product cancellation is triggered:

1. Trigger.dev receives the event and processes it asynchronously
2. Relevant data is fetched from Supabase
3. Notifications are sent to affected users with appropriate localization
4. Detailed logs are maintained for monitoring and debugging

### Best Practices Implemented

- Payload validation before processing
- Comprehensive error handling and logging
- Automatic retries for failed Novu API calls
- Detailed metrics for notification delivery status

## Environment Variables

Make sure the following environment variables are set:

- `TRIGGER_API_KEY` - Your Trigger.dev API key
- `TRIGGER_API_URL` (optional) - Custom Trigger.dev API URL
- `NOVU_SECRET_KEY` - Your Novu API key
- `PUBLIC_SUPABASE_URL` - Your Supabase URL
- `SUPABASE_SERVICE_ROLE_KEY` - Your Supabase service role key

## Resources

- [Novu and Trigger.dev Integration Guide](https://docs.novu.co/guides/triggerdotdev)
- [Supabase Database Operations Guide](https://trigger.dev/docs/guides/examples/supabase-database-operations)
