import { createClient } from '@supabase/supabase-js';
import { format } from 'date-fns';
import { AbortTaskRunError } from '@trigger.dev/sdk/v3';
import { eventTrigger } from '@trigger.dev/sdk';
import { client } from '../client';
import { Novu } from '@novu/api';
import type { Database } from '../../src/lib/supabase/database.types';

// Define LocalizedText directly in this file to avoid import conflicts
type LocalizedText = {
	[key: string]: string;
};

/**
 * Get localized text value with fallback
 */
function getLocalizedText(jsonb: unknown, locale: string = 'en', fallback: string = ''): string {
	if (!jsonb) return fallback;

	// Convert JSONB to LocalizedText
	const localizedText = jsonb as { [key: string]: string };

	return localizedText[locale] || localizedText['en'] || fallback;
}

/**
 * Process product cancellation notifications
 */
export async function processProductCancellation(params: {
	eventId: string;
	reasonId: string;
	comments?: string;
	brandId?: string;
}) {
	const { eventId, reasonId, comments, brandId } = params;

	// Get environment variables
	const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
	const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
	const novuApiKey = process.env.NOVU_SECRET_KEY;

	// Validate environment variables
	if (!supabaseUrl) {
		throw new AbortTaskRunError('PUBLIC_SUPABASE_URL is not defined in environment variables');
	}

	if (!supabaseServiceKey) {
		throw new AbortTaskRunError(
			'SUPABASE_SERVICE_ROLE_KEY is not defined in environment variables'
		);
	}

	if (!novuApiKey) {
		throw new AbortTaskRunError('NOVU_SECRET_KEY is not defined in environment variables');
	}

	try {
		// Initialize Supabase client with admin privileges using service role key
		// This bypasses Row Level Security policies for admin-level access
		const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);

		// Get the event data
		const { data: event, error: eventError } = await supabase
			.from('event')
			.select('*, product:product_id(*)')
			.eq('id', eventId)
			.single();

		if (eventError) {
			// Throw an AbortTaskRunError for non-retryable errors
			if (eventError.code === 'PGRST116') {
				// PGRST116 = Row not found
				throw new AbortTaskRunError(`Event not found: ${eventId}`);
			}
			throw new Error(`Error fetching event: ${eventError.message}`);
		}

		// Get brand_id from event if not provided in params
		const eventBrandId = event.brand_id;
		if (!eventBrandId) {
			throw new AbortTaskRunError(`Event does not have a brand_id: ${eventId}`);
		}

		// Use brandId from params or from event
		const targetBrandId = brandId || eventBrandId;

		// Get the brand data
		const { data: brand, error: brandError } = await supabase
			.from('brand')
			.select('*')
			.eq('id', targetBrandId)
			.single();

		if (brandError || !brand) {
			// Throw an AbortTaskRunError for non-retryable errors
			if (brandError?.code === 'PGRST116') {
				throw new AbortTaskRunError(`Brand not found: ${targetBrandId}`);
			}
			throw new Error(`Error fetching brand: ${brandError?.message || 'Unknown error'}`);
		}

		// Get the cancellation reason
		const { data: reason, error: reasonError } = await supabase
			.from('change_reason')
			.select('*')
			.eq('id', reasonId)
			.single();

		if (reasonError) {
			if (reasonError.code === 'PGRST116') {
				throw new AbortTaskRunError(`Cancellation reason not found: ${reasonId}`);
			}
			throw new Error(`Error fetching reason: ${reasonError.message}`);
		}

		// Get all registrations for this event
		const { data: registrations, error: registrationsError } = await supabase
			.from('event_member')
			.select('*, profile:actor_profile_id(*)')
			.eq('event_id', eventId);

		if (registrationsError) {
			throw new Error(`Error fetching registrations: ${registrationsError.message}`);
		}

		if (!registrations || registrations.length === 0) {
			console.log(`No registrations found for event: ${eventId}`);
			return { success: true, message: 'No registrations found for this event' };
		}

		// Format date for notifications
		const formattedDateTime = format(new Date(event.start_at), 'PPpp');

		// Initialize Novu client
		const novu = new Novu({
			secretKey: novuApiKey
		});

		// Track successful and failed notifications
		let successful = 0;
		let failed = 0;

		// Send notifications to each registration
		const notificationPromises = registrations.map(async (reg) => {
			// Skip registrations with missing or invalid profile information
			if (
				!reg.profile ||
				typeof reg.profile === 'string' ||
				(typeof reg.profile === 'object' && 'error' in reg.profile)
			) {
				return false;
			}

			// Type assertion for TypeScript to understand this is a valid profile
			const profile = reg.profile as {
				id?: string;
				email?: string;
				phone?: string;
				first_name?: string;
				last_name?: string;
				locale?: string;
			};

			// Skip if essential profile data is missing
			if (!profile.id || !profile.email) {
				return false;
			}

			const userLocale = profile.locale || 'en';

			try {
				// Call Novu API directly with the event-canceled workflow ID
				await novu.trigger({
					workflowId: 'event:canceled',
					to: {
						subscriberId: `${brand.id}:${profile.id}`,
						email: profile.email,
						phone: profile.phone
					},
					payload: {
						userName: `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'User',
						eventTitle: getLocalizedText(event.auto_final_title, userLocale, 'Event'),
						eventSubtitle: getLocalizedText(event.auto_final_subtitle, userLocale),
						eventDateTime: formattedDateTime,
						cancellationReason: getLocalizedText(
							reason.title,
							userLocale,
							'This event has been canceled'
						),
						hostMessage: comments,
						refundInfo: 'You will receive a full refund within 5-7 business days.',
						brand: {
							id: brand.id,
							name_full: brand.name_full,
							name_short: brand.name_short,
							logo_url: brand.logo_url,
							portal_url: brand.portal_url || undefined,
							portal_url_dev: brand.portal_url_dev || undefined
						},
						locale: userLocale,
						portalUrl: brand.portal_url || undefined,
						tenant: {
							id: brand.id,
							name: getLocalizedText(brand.name_full, userLocale, 'Our Platform'),
							logo: brand.logo_url
						}
					}
				});
				successful++;
				return true;
			} catch (error) {
				console.error(
					`Failed to send notification to ${profile.email}:`,
					error instanceof Error ? error.message : 'Unknown error'
				);
				failed++;
				return false;
			}
		});

		// Wait for all notifications to complete
		await Promise.all(notificationPromises);

		// Update the event's publishing state to 'cancelled' in the database
		const { error: updateError } = await supabase
			.from('event')
			.update({ publishing_state: 'cancelled', updated_at: new Date().toISOString() })
			.eq('id', eventId);

		if (updateError) {
			throw new Error(`Error updating event status: ${updateError.message}`);
		}

		return {
			success: true,
			message: `Successfully processed cancellation for event: ${eventId}`,
			notificationResults: {
				successful,
				failed,
				total: registrations.length
			},
			registrationsCount: registrations.length
		};
	} catch (error) {
		// Re-throw AbortTaskRunError to indicate non-retryable errors
		if (error instanceof AbortTaskRunError) {
			throw error;
		}

		console.error('Error processing product cancellation:', error);
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Unknown error'
		};
	}
}

/**
 * Trigger.dev job for processing product cancellation notifications
 */
export const productCancellationJob = client.defineJob({
	id: 'product-cancellation',
	name: 'Product Cancellation',
	version: '1.0.0',
	trigger: eventTrigger({
		name: 'product.cancelled'
	}),
	run: async (payload, io, ctx) => {
		// Validate required payload fields
		if (!payload.eventId) {
			throw new Error('eventId is required');
		}
		if (!payload.reasonId) {
			throw new Error('reasonId is required');
		}

		// Log the start of the notification process with better context
		io.logger.info('Starting product cancellation notification process', {
			eventId: payload.eventId,
			reasonId: payload.reasonId,
			brandId: payload.brandId || 'will be fetched from event',
			comments: payload.comments ? 'Provided' : 'Not provided',
			runId: ctx.run.id
		});

		try {
			// Process the product cancellation using the local function
			const result = await processProductCancellation(payload);

			if (!result.success) {
				throw new Error(result.error || 'Unknown processing error');
			}

			// Log success metrics
			io.logger.info('Product cancellation notifications completed', {
				success: true,
				notificationsSent: result.notificationResults?.successful || 0,
				notificationsFailed: result.notificationResults?.failed || 0,
				totalRegistrations: result.registrationsCount || 0,
				runId: ctx.run.id
			});

			// Return detailed result information
			return {
				success: true,
				runId: ctx.run.id,
				eventId: payload.eventId,
				notificationResults: {
					sent: result.notificationResults?.successful || 0,
					failed: result.notificationResults?.failed || 0,
					total: result.notificationResults?.total || 0
				},
				registrationsCount: result.registrationsCount || 0
			};
		} catch (error) {
			// Enhanced error logging with more context
			io.logger.error('Error processing product cancellation', {
				error: error instanceof Error ? error.message : 'Unknown error',
				stack: error instanceof Error ? error.stack : undefined,
				eventId: payload.eventId,
				brandId: payload.brandId,
				reasonId: payload.reasonId,
				runId: ctx.run.id,
				timestamp: new Date().toISOString()
			});

			// Rethrow to trigger retry mechanism
			throw error;
		}
	}
});
