import type { Reroute } from '@sveltejs/kit';
import { deLocalizeUrl } from '$lib/paraglide/runtime';
import { dev } from '$app/environment';

export const reroute: Reroute = (request) => {
	const delocalizedPathname = deLocalizeUrl(request.url).pathname;
	const domain = request.url.hostname;
	const isGlobalDomain = domain === 'hana.one' || domain === 'www.hana.one';
	const isLocalOrIp = dev && (domain === 'localhost' || /^(\d{1,3}\.){3}\d{1,3}$/.test(domain));
	const isGlobalMode = isGlobalDomain || (isLocalOrIp && request.url.searchParams.has('global'));

	console.log('[hooks.ts] Reroute called for:', delocalizedPathname, 'isGlobalMode:', isGlobalMode);

	// Route to appropriate homepage based on context
	if (delocalizedPathname === '/') {
		console.log('[hooks.ts] Rerouting homepage');
		if (isGlobalMode) {
			// Route to global marketplace homepage
			console.log('[hooks.ts] Routing to /(app)/home');
			return '/app-home';
		} else {
			// Route to brand-specific homepage
			console.log('[hooks.ts] Routing to /(brand)/brand-home');
			return '/brand-home';
		}
	}

	// Return the delocalized pathname for all other routes
	return delocalizedPathname;
};
