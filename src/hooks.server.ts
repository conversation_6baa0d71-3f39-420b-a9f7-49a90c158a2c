import { createServerClient } from '@supabase/ssr';
import { type Handle, redirect, error } from '@sveltejs/kit';
import { sequence } from '@sveltejs/kit/hooks';
import { dev } from '$app/environment';
import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY } from '$env/static/public';
import { paraglideMiddleware } from '$lib/paraglide/server';

// Define permission paths mappings - extend as needed
const pathPermissionMap: Record<string, string[]> = {
	'/private/payroll': ['brand_payroll_read', 'brand_payroll_write'],
	'/private/payroll/transactions': ['brand_payroll_read', 'brand_payroll_write'],
	'/private/payroll/settings': ['brand_payroll_write'],
	'/private/payroll/formulas': ['brand_payroll_read', 'brand_payroll_write'],
	'/private/payroll/payees': ['brand_payroll_read', 'brand_payroll_write']
};

const supabase: Handle = async ({ event, resolve }) => {
	/**
	 * Creates a Supabase client specific to this server request.
	 *
	 * The Supabase client gets the Auth token from the request cookies.
	 */
	console.log('[hooks.server] Starting request handling');
	event.locals.supabase = createServerClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
		cookies: {
			getAll: () => event.cookies.getAll(),
			/**
			 * SvelteKit's cookies API requires `path` to be explicitly set in
			 * the cookie options. Setting `path` to `/` replicates previous/
			 * standard behavior.
			 */
			setAll: (cookiesToSet) => {
				cookiesToSet.forEach(({ name, value, options }) => {
					// Adjust cookie options for IP-based access

					const updatedOptions = {
						...options,
						path: '/',
						// In development, don't use Secure for localhost/IP
						secure: !dev
						// Important for IP-based access: set SameSite to lax
						// sameSite: 'lax' as const
						// Set httpOnly for better security
						// httpOnly: true
					};

					event.cookies.set(name, value, updatedOptions);
				});
			}
		}
	});

	/**
	 * Unlike `supabase.auth.getSession()`, which returns the session _without_
	 * validating the JWT, this function also calls `getUser()` to validate the
	 * JWT before returning the session.
	 */
	event.locals.safeGetSession = async () => {
		console.log('[hooks.server] Getting session');
		const {
			data: { session }
		} = await event.locals.supabase.auth.getSession();
		if (!session) {
			return { session: null, user: null };
		}

		const {
			data: { user },
			error
		} = await event.locals.supabase.auth.getUser();
		if (error) {
			// JWT validation has failed
			console.error('[hooks.server] JWT validation failed:', error);
			return { session: null, user: null };
		}

		return { session, user };
	};

	return resolve(event, {
		filterSerializedResponseHeaders(name) {
			/**
			 * Supabase libraries use the `content-range` and `x-supabase-api-version`
			 * headers, so we need to tell SvelteKit to pass it through.
			 */
			return name === 'content-range' || name === 'x-supabase-api-version';
		}
	});
};

const brandInit: Handle = async ({ event, resolve }) => {
	console.log('[hooks.server] Getting brand');

	// Skip brand check for /api/novu endpoint as it's centralized for all brands
	if (event.url.pathname.startsWith('/api/novu')) {
		console.log('[hooks.server] Skipping brand check for Novu API endpoint');
		return resolve(event);
	}

	const cachedBrand = event.cookies.get('brand');
	const domain = event.url.hostname;
	const isLocalOrIp = dev && (domain === 'localhost' || /^(\d{1,3}\.){3}\d{1,3}$/.test(domain));

	// Check if this is the global hana.one domain
	const isGlobalDomain = domain === 'hana.one' || domain === 'www.hana.one';

	// For development, check if we're explicitly in global mode
	const isGlobalMode = isLocalOrIp && event.url.searchParams.has('global');

	if (isGlobalDomain || isGlobalMode) {
		console.log('[hooks.server] Global marketplace mode detected');
		// Set a special global context instead of a specific brand
		event.locals.brand = null;

		// Clear any cached brand cookie for global mode
		event.cookies.delete('brand', { path: '/' });

		return resolve(event);
	}

	// For brand-specific domains, continue with existing logic
	if (!dev && cachedBrand) {
		try {
			const brand = JSON.parse(cachedBrand);
			console.log('[hooks.server] Using CACHED brand:', brand);
			event.locals.brand = brand;
			return resolve(event);
		} catch (e) {
			console.error('[hooks.server] Failed to parse cached brand:', e);
			// Continue to fetch fresh data
		}
	}

	// Check if domain matches [slug].hana.one pattern
	const hanaOneMatch = domain.match(/^([^.]+)\.hana\.one$/);
	const queryDomain = isLocalOrIp ? 'dev-my.eds.dance' : domain;
	// const queryDomain = isLocalOrIp ? 'my.googirlart.com' : domain;
	// const queryDomain = isLocalOrIp ? 'my.findacappella.com' : domain;

	// Build query conditions - always check portal_url, and add slug condition for hana.one domains
	let orConditions = `portal_url.eq.${queryDomain},portal_url_dev.eq.${queryDomain}`;

	if (hanaOneMatch && !isLocalOrIp) {
		const slug = hanaOneMatch[1];
		orConditions += `,slug.eq.${slug}`;
		console.log('[hooks.server] Looking up brand by slug or portal_url:', { slug, queryDomain });
	} else {
		console.log('[hooks.server] Looking up brand by portal_url:', queryDomain);
	}

	const { data, error: err } = await event.locals.supabase
		.from('brand')
		.select(
			'id, slug, logo_url, name_full, name_short, portal_url, portal_url_dev, owner_profile_id, stripe_account_id, stripe_fee_paid_by'
		)
		.or(orConditions)
		.single();

	if (err || !data) {
		console.error(
			'[hooks.server] No brand found for',
			hanaOneMatch ? `slug: ${hanaOneMatch[1]}` : `domain: ${domain}`,
			`(requested from ${event.url.href})`,
			'\nDatabase error:',
			err?.message
		);
		error(404, `Brand not found for domain: ${domain}`);
	}

	// Cache the complete brand object
	event.cookies.set('brand', JSON.stringify(data), {
		path: '/',
		maxAge: 60 * 60 * 24 * 30, // 30 days
		httpOnly: true,
		secure: !dev,
		sameSite: 'lax'
	});

	event.locals.brand = data;
	return resolve(event);
};

const authGuard: Handle = async ({ event, resolve }) => {
	const { session, user } = await event.locals.safeGetSession();
	event.locals.user = user;
	event.locals.session = session;

	if (
		!user &&
		(event.url.pathname.startsWith('/private') || event.url.pathname.startsWith('/event/private'))
	) {
		console.log('[hooks.server] No user found, redirecting to login');
		redirect(303, '/auth/sign-in');
	}

	// Check if user exists but email is not verified when trying to access private routes
	if (
		user &&
		!user.email_confirmed_at &&
		(event.url.pathname.startsWith('/private') || event.url.pathname.startsWith('/event/private'))
	) {
		console.log('[hooks.server] Unverified email, redirecting to verification page');
		redirect(303, '/auth/verify-account');
	}

	if (user && event.url.pathname === '/auth') {
		console.log('[hooks.server] User found, redirecting to event');
		redirect(303, '/event');
	}

	return resolve(event);
};

/**
 * Permission guard that centralizes permission checks for routes
 * Checks both feature access permissions and brand ownership
 */
const permissionGuard: Handle = async ({ event, resolve }) => {
	const { user, brand, supabase } = event.locals;

	// In global marketplace mode (no brand), we don't have brand-specific permissions
	if (!brand) {
		event.locals.security = {
			hasPermission: () => false,
			isBrandOwner: false
		};
		return resolve(event);
	}

	// Initialize security for all routes
	// Brand owners have all permissions by default
	const isBrandOwner = user?.id === brand.owner_profile_id;

	// Check if this path requires permission verification
	const pathRequiresPermissions = Object.keys(pathPermissionMap).some((path) =>
		event.url.pathname.startsWith(path)
	);

	// Initialize permissions array for non-brand owners
	type FeatureAccess = { feature_access_id: string };
	let userPermissions: FeatureAccess[] = [];

	// Only fetch permissions if the user is not a brand owner AND the path requires permissions
	if (user && !isBrandOwner && pathRequiresPermissions) {
		const { data: permissions, error: permissionsError } = await supabase
			.from('profile_feature_access')
			.select('feature_access_id')
			.eq('profile_id', user.id)
			.eq('auto_feature_access_brand_id', brand.id);

		if (permissionsError) {
			console.error('[hooks.server] Error fetching permissions:', permissionsError);
			// Continue with empty permissions - don't block the request
		}

		userPermissions = permissions || [];
	}

	// Make security utilities available for all routes
	event.locals.security = {
		hasPermission: (permission: string) => {
			if (isBrandOwner) return true;
			return userPermissions.some((p) => p.feature_access_id === permission);
		},
		isBrandOwner: isBrandOwner || false
	};

	// Only check specific route permissions for protected routes
	if (pathRequiresPermissions && user && !isBrandOwner) {
		// Get required permissions for this path
		const pathKey = Object.keys(pathPermissionMap).find((path) =>
			event.url.pathname.startsWith(path)
		);

		const requiredPermissions = pathKey ? pathPermissionMap[pathKey] : [];

		// Check if user has at least one of the required permissions
		const hasPermission =
			requiredPermissions.length === 0 ||
			requiredPermissions.some((perm) =>
				userPermissions.some((userPerm) => userPerm.feature_access_id === perm)
			);

		if (!hasPermission) {
			console.log('[hooks.server] User lacks permission for route:', event.url.pathname);
			redirect(303, '/private/access-denied');
		}
	}

	return resolve(event);
};

// creating a handle to use the paraglide middleware
const paraglideHandle: Handle = ({ event, resolve }) =>
	paraglideMiddleware(event.request, ({ request: localizedRequest, locale }) => {
		event.request = localizedRequest;
		return resolve(event, {
			transformPageChunk: ({ html }) => {
				return html.replace('%lang%', locale);
			}
		});
	});

export const handle: Handle = sequence(
	paraglideHandle,
	supabase,
	brandInit,
	authGuard,
	permissionGuard
);
