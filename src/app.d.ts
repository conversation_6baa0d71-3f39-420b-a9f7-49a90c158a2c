import type { Locale } from '$lib/paraglide/runtime';
import type { SupabaseClient, User } from '@supabase/supabase-js';

type LocalizedText = {
	[key: string]: string;
};

interface Brand {
	id: string;
	logo_url: string;
	name_full: LocalizedText;
	name_short: LocalizedText;
	portal_url: string;
	portal_url_dev: string;
	owner_profile_id: string;
	stripe_account_id?: string;
	stripe_fee_paid_by: string;
}

// Security utility types
interface SecurityUtils {
	hasPermission: (permission: string) => boolean;
	isBrandOwner: boolean;
}

declare global {
	namespace App {
		interface Error {
			message: string;
		}
		interface Locals {
			supabase: SupabaseClient;
			safeGetSession: () => Promise<{ session: Session | null; user: User | null }>;
			user: User | null;
			session: Session | null;
			brand: Brand | null;
			security: SecurityUtils;
		}
		interface PageData {
			user: User | null;
			session: Session | null;
			brand: Brand | null;
		}
		// interface Platform {}
	}
}

export {};
