export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      address: {
        Row: {
          airtable_id: string | null
          auto_normalized_address: string | null
          auto_normalized_address_local: string | null
          city: string
          country: string
          country_code: string
          created_at: string
          creator_id: string
          delivery_instruction: string | null
          delivery_options: string | null
          email: string | null
          id: string
          name: string | null
          phone: string | null
          phoneic_name: string | null
          postal_code: string
          state: string
          street: string
          sub_admin_area: string | null
          sub_locality: string | null
          time_zone: string
          updated_at: string
        }
        Insert: {
          airtable_id?: string | null
          auto_normalized_address?: string | null
          auto_normalized_address_local?: string | null
          city: string
          country: string
          country_code: string
          created_at?: string
          creator_id: string
          delivery_instruction?: string | null
          delivery_options?: string | null
          email?: string | null
          id?: string
          name?: string | null
          phone?: string | null
          phoneic_name?: string | null
          postal_code: string
          state: string
          street: string
          sub_admin_area?: string | null
          sub_locality?: string | null
          time_zone: string
          updated_at?: string
        }
        Update: {
          airtable_id?: string | null
          auto_normalized_address?: string | null
          auto_normalized_address_local?: string | null
          city?: string
          country?: string
          country_code?: string
          created_at?: string
          creator_id?: string
          delivery_instruction?: string | null
          delivery_options?: string | null
          email?: string | null
          id?: string
          name?: string | null
          phone?: string | null
          phoneic_name?: string | null
          postal_code?: string
          state?: string
          street?: string
          sub_admin_area?: string | null
          sub_locality?: string | null
          time_zone?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "address_time_zone_fkey"
            columns: ["time_zone"]
            isOneToOne: false
            referencedRelation: "time_zone"
            referencedColumns: ["name"]
          },
        ]
      }
      app_role: {
        Row: {
          created_at: string
          desc: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          desc?: string | null
          id: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          desc?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      app_role_user: {
        Row: {
          app_role_id: string
          id: string
          roleId: string
          user_id: string
          userId: string
        }
        Insert: {
          app_role_id: string
          id?: string
          roleId: string
          user_id: string
          userId: string
        }
        Update: {
          app_role_id?: string
          id?: string
          roleId?: string
          user_id?: string
          userId?: string
        }
        Relationships: [
          {
            foreignKeyName: "app_role_user_app_role_id_fkey"
            columns: ["app_role_id"]
            isOneToOne: false
            referencedRelation: "app_role"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "app_role_user_roleId_fkey"
            columns: ["roleId"]
            isOneToOne: false
            referencedRelation: "app_role"
            referencedColumns: ["id"]
          },
        ]
      }
      brand: {
        Row: {
          admin_group_id: string | null
          banner_url: string
          created_at: string
          domain: string
          func_name_full_en_first: string | null
          func_name_short_en_first: string | null
          homepage_url: string | null
          id: string
          intro_full: Json | null
          intro_short: Json | null
          logo_url: string
          master_brand_id: string | null
          name_full: Json
          name_short: Json | null
          nid: number
          owner_profile_id: string
          portal_url: string | null
          portal_url_dev: string | null
          publishing_state: string
          slogen: Json | null
          slug: string
          stripe_account_id: string | null
          stripe_fee_paid_by: string
          support_email: string | null
          support_phone: string | null
          support_web_url: string | null
          updated_at: string
          web_url: string
        }
        Insert: {
          admin_group_id?: string | null
          banner_url: string
          created_at?: string
          domain: string
          func_name_full_en_first?: string | null
          func_name_short_en_first?: string | null
          homepage_url?: string | null
          id?: string
          intro_full?: Json | null
          intro_short?: Json | null
          logo_url: string
          master_brand_id?: string | null
          name_full: Json
          name_short?: Json | null
          nid?: number
          owner_profile_id: string
          portal_url?: string | null
          portal_url_dev?: string | null
          publishing_state?: string
          slogen?: Json | null
          slug: string
          stripe_account_id?: string | null
          stripe_fee_paid_by: string
          support_email?: string | null
          support_phone?: string | null
          support_web_url?: string | null
          updated_at?: string
          web_url: string
        }
        Update: {
          admin_group_id?: string | null
          banner_url?: string
          created_at?: string
          domain?: string
          func_name_full_en_first?: string | null
          func_name_short_en_first?: string | null
          homepage_url?: string | null
          id?: string
          intro_full?: Json | null
          intro_short?: Json | null
          logo_url?: string
          master_brand_id?: string | null
          name_full?: Json
          name_short?: Json | null
          nid?: number
          owner_profile_id?: string
          portal_url?: string | null
          portal_url_dev?: string | null
          publishing_state?: string
          slogen?: Json | null
          slug?: string
          stripe_account_id?: string | null
          stripe_fee_paid_by?: string
          support_email?: string | null
          support_phone?: string | null
          support_web_url?: string | null
          updated_at?: string
          web_url?: string
        }
        Relationships: [
          {
            foreignKeyName: "brand_admin_group_id_fkey"
            columns: ["admin_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "brand_domain_fkey"
            columns: ["domain"]
            isOneToOne: false
            referencedRelation: "brand_domain"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "brand_master_brand_id_fkey"
            columns: ["master_brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "brand_owner_profile_id_fkey1"
            columns: ["owner_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "brand_publishing_state_fkey"
            columns: ["publishing_state"]
            isOneToOne: false
            referencedRelation: "publishing_state"
            referencedColumns: ["id"]
          },
        ]
      }
      brand_domain: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      brand_managed_profile: {
        Row: {
          brand_id: string
          created_at: string
          id: string
          managed_profile_id: string
          preferred_landmark_ids: string[] | null
          updated_at: string
        }
        Insert: {
          brand_id: string
          created_at?: string
          id?: string
          managed_profile_id: string
          preferred_landmark_ids?: string[] | null
          updated_at?: string
        }
        Update: {
          brand_id?: string
          created_at?: string
          id?: string
          managed_profile_id?: string
          preferred_landmark_ids?: string[] | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "brand_managed_profile_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "brand_managed_profile_managed_profile_id_fkey"
            columns: ["managed_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      bundle: {
        Row: {
          created_at: string
          id: string
          metadata_id: string
          selection_max: number
          selection_min: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          metadata_id: string
          selection_max?: number
          selection_min?: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          metadata_id?: string
          selection_max?: number
          selection_min?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "bundle_metadata_id_fkey"
            columns: ["metadata_id"]
            isOneToOne: false
            referencedRelation: "metadata"
            referencedColumns: ["id"]
          },
        ]
      }
      change_actor_role: {
        Row: {
          created_at: string
          id: string
          title: Json
        }
        Insert: {
          created_at?: string
          id: string
          title: Json
        }
        Update: {
          created_at?: string
          id?: string
          title?: Json
        }
        Relationships: []
      }
      change_event: {
        Row: {
          apply_to_event_id: string
          change_request_id: string
          created_at: string
          id: string
          novu_transaction_id: string
          novu_transaction_status: string | null
          override_duration_minute: number | null
          override_event_id: string | null
          override_publishing_state: string | null
          override_start_at: string | null
        }
        Insert: {
          apply_to_event_id: string
          change_request_id: string
          created_at?: string
          id?: string
          novu_transaction_id: string
          novu_transaction_status?: string | null
          override_duration_minute?: number | null
          override_event_id?: string | null
          override_publishing_state?: string | null
          override_start_at?: string | null
        }
        Update: {
          apply_to_event_id?: string
          change_request_id?: string
          created_at?: string
          id?: string
          novu_transaction_id?: string
          novu_transaction_status?: string | null
          override_duration_minute?: number | null
          override_event_id?: string | null
          override_publishing_state?: string | null
          override_start_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "change_event_apply_to_event_id_fkey"
            columns: ["apply_to_event_id"]
            isOneToOne: false
            referencedRelation: "event"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_event_change_request_id_fkey"
            columns: ["change_request_id"]
            isOneToOne: false
            referencedRelation: "change_request"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_event_override_event_id_fkey"
            columns: ["override_event_id"]
            isOneToOne: false
            referencedRelation: "event"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_event_override_publishing_state_fkey"
            columns: ["override_publishing_state"]
            isOneToOne: false
            referencedRelation: "publishing_state"
            referencedColumns: ["id"]
          },
        ]
      }
      change_metadata_wikipage: {
        Row: {
          apply_to_metadata_wikipage_id: string
          change_request_id: string
          created_at: string
          id: string
          novu_transaction_id: string
          novu_transaction_status: string | null
          override_relation: string | null
          override_wikipage_id: string | null
        }
        Insert: {
          apply_to_metadata_wikipage_id: string
          change_request_id: string
          created_at?: string
          id?: string
          novu_transaction_id: string
          novu_transaction_status?: string | null
          override_relation?: string | null
          override_wikipage_id?: string | null
        }
        Update: {
          apply_to_metadata_wikipage_id?: string
          change_request_id?: string
          created_at?: string
          id?: string
          novu_transaction_id?: string
          novu_transaction_status?: string | null
          override_relation?: string | null
          override_wikipage_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "change_metadata_wikipage_apply_to_metadata_wikipage_id_fkey"
            columns: ["apply_to_metadata_wikipage_id"]
            isOneToOne: false
            referencedRelation: "metadata_wikipage"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_metadata_wikipage_change_request_id_fkey"
            columns: ["change_request_id"]
            isOneToOne: false
            referencedRelation: "change_request"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_metadata_wikipage_override_relation_fkey"
            columns: ["override_relation"]
            isOneToOne: false
            referencedRelation: "metadata_wikipage_relation"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_metadata_wikipage_override_wikipage_id_fkey"
            columns: ["override_wikipage_id"]
            isOneToOne: false
            referencedRelation: "wikipage"
            referencedColumns: ["id"]
          },
        ]
      }
      change_order_price: {
        Row: {
          apply_to_order_price_id: string
          change_request_id: string
          created_at: string
          id: string
          novu_transaction_id: string
          novu_transaction_status: string | null
          override_expire_at: string | null
          override_money_int: number | null
          override_unit: number | null
        }
        Insert: {
          apply_to_order_price_id: string
          change_request_id: string
          created_at?: string
          id?: string
          novu_transaction_id: string
          novu_transaction_status?: string | null
          override_expire_at?: string | null
          override_money_int?: number | null
          override_unit?: number | null
        }
        Update: {
          apply_to_order_price_id?: string
          change_request_id?: string
          created_at?: string
          id?: string
          novu_transaction_id?: string
          novu_transaction_status?: string | null
          override_expire_at?: string | null
          override_money_int?: number | null
          override_unit?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "change_order_price_apply_to_order_price_id_fkey"
            columns: ["apply_to_order_price_id"]
            isOneToOne: false
            referencedRelation: "order_price"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_order_price_change_request_id_fkey"
            columns: ["change_request_id"]
            isOneToOne: false
            referencedRelation: "change_request"
            referencedColumns: ["id"]
          },
        ]
      }
      change_order_product: {
        Row: {
          apply_to_order_product_id: string
          change_request_id: string
          created_at: string
          id: string
          novu_transaction_id: string
          novu_transaction_status: string | null
          override_canceled_at_should_return_unit: number | null
          override_cost_unit: number | null
          override_product_id: string | null
          override_publishing_state: string | null
          override_purchased_count: number | null
        }
        Insert: {
          apply_to_order_product_id: string
          change_request_id: string
          created_at?: string
          id?: string
          novu_transaction_id: string
          novu_transaction_status?: string | null
          override_canceled_at_should_return_unit?: number | null
          override_cost_unit?: number | null
          override_product_id?: string | null
          override_publishing_state?: string | null
          override_purchased_count?: number | null
        }
        Update: {
          apply_to_order_product_id?: string
          change_request_id?: string
          created_at?: string
          id?: string
          novu_transaction_id?: string
          novu_transaction_status?: string | null
          override_canceled_at_should_return_unit?: number | null
          override_cost_unit?: number | null
          override_product_id?: string | null
          override_publishing_state?: string | null
          override_purchased_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "change_order_product_apply_to_order_product_id_fkey"
            columns: ["apply_to_order_product_id"]
            isOneToOne: false
            referencedRelation: "order_product"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_order_product_change_request_id_fkey"
            columns: ["change_request_id"]
            isOneToOne: false
            referencedRelation: "change_request"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_order_product_override_product_id_fkey"
            columns: ["override_product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_order_product_override_publishing_state_fkey"
            columns: ["override_publishing_state"]
            isOneToOne: false
            referencedRelation: "publishing_state"
            referencedColumns: ["id"]
          },
        ]
      }
      change_pattern: {
        Row: {
          created_at: string
          id: string
          novu_workflow_id: string
          title: Json
        }
        Insert: {
          created_at?: string
          id: string
          novu_workflow_id: string
          title: Json
        }
        Update: {
          created_at?: string
          id?: string
          novu_workflow_id?: string
          title?: Json
        }
        Relationships: []
      }
      change_product: {
        Row: {
          apply_to_product_id: string
          change_request_id: string
          created_at: string
          id: string
          novu_transaction_id: string
          novu_transaction_status: string | null
          override_publishing_state: string | null
          override_subtitle: Json | null
          override_title: Json | null
        }
        Insert: {
          apply_to_product_id: string
          change_request_id: string
          created_at?: string
          id?: string
          novu_transaction_id: string
          novu_transaction_status?: string | null
          override_publishing_state?: string | null
          override_subtitle?: Json | null
          override_title?: Json | null
        }
        Update: {
          apply_to_product_id?: string
          change_request_id?: string
          created_at?: string
          id?: string
          novu_transaction_id?: string
          novu_transaction_status?: string | null
          override_publishing_state?: string | null
          override_subtitle?: Json | null
          override_title?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "change_product_apply_to_product_id_fkey"
            columns: ["apply_to_product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_product_change_request_id_fkey"
            columns: ["change_request_id"]
            isOneToOne: false
            referencedRelation: "change_request"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_product_override_publishing_state_fkey"
            columns: ["override_publishing_state"]
            isOneToOne: false
            referencedRelation: "publishing_state"
            referencedColumns: ["id"]
          },
        ]
      }
      change_reason: {
        Row: {
          created_at: string
          id: string
          requires_message: boolean
          title: Json
        }
        Insert: {
          created_at?: string
          id: string
          requires_message?: boolean
          title: Json
        }
        Update: {
          created_at?: string
          id?: string
          requires_message?: boolean
          title?: Json
        }
        Relationships: []
      }
      change_request: {
        Row: {
          actor_profile_id: string
          auto_change_actor_role_id: string
          belong_to_creation_request_id: string | null
          brand_id: string | null
          change_reason_id: string | null
          change_reason_message: string | null
          created_at: string
          form_data: Json | null
          id: string
          status: string
          updated_at: string
        }
        Insert: {
          actor_profile_id: string
          auto_change_actor_role_id: string
          belong_to_creation_request_id?: string | null
          brand_id?: string | null
          change_reason_id?: string | null
          change_reason_message?: string | null
          created_at?: string
          form_data?: Json | null
          id?: string
          status?: string
          updated_at?: string
        }
        Update: {
          actor_profile_id?: string
          auto_change_actor_role_id?: string
          belong_to_creation_request_id?: string | null
          brand_id?: string | null
          change_reason_id?: string | null
          change_reason_message?: string | null
          created_at?: string
          form_data?: Json | null
          id?: string
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "change_request_actor_profile_id_fkey"
            columns: ["actor_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_request_auto_change_actor_role_id_fkey"
            columns: ["auto_change_actor_role_id"]
            isOneToOne: false
            referencedRelation: "change_actor_role"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_request_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_request_change_reason_id_fkey"
            columns: ["change_reason_id"]
            isOneToOne: false
            referencedRelation: "change_reason"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_request_status_fkey"
            columns: ["status"]
            isOneToOne: false
            referencedRelation: "change_request_status"
            referencedColumns: ["id"]
          },
        ]
      }
      change_request_review: {
        Row: {
          change_request_id: string
          created_at: string
          id: string
          message: string | null
          reviewer_profile_id: string
          status: string
          status_updated_at: string
        }
        Insert: {
          change_request_id: string
          created_at?: string
          id?: string
          message?: string | null
          reviewer_profile_id: string
          status: string
          status_updated_at?: string
        }
        Update: {
          change_request_id?: string
          created_at?: string
          id?: string
          message?: string | null
          reviewer_profile_id?: string
          status?: string
          status_updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "change_request_review_change_request_id_fkey"
            columns: ["change_request_id"]
            isOneToOne: false
            referencedRelation: "change_request"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_request_review_reviewer_profile_id_fkey"
            columns: ["reviewer_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_request_review_status_fkey"
            columns: ["status"]
            isOneToOne: false
            referencedRelation: "review_status"
            referencedColumns: ["id"]
          },
        ]
      }
      change_request_status: {
        Row: {
          created_at: string
          id: string
          title: Json
        }
        Insert: {
          created_at?: string
          id: string
          title: Json
        }
        Update: {
          created_at?: string
          id?: string
          title?: Json
        }
        Relationships: []
      }
      change_rule: {
        Row: {
          change_actor_role: string
          change_pattern: string
          change_rule_group_id: string
          created_at: string
          id: string
          is_activated_since: string
          review_count_required: number
          review_fallback_minute: number
          reviewer_group_id: string | null
          updated_at: string
        }
        Insert: {
          change_actor_role: string
          change_pattern: string
          change_rule_group_id: string
          created_at?: string
          id?: string
          is_activated_since?: string
          review_count_required?: number
          review_fallback_minute?: number
          reviewer_group_id?: string | null
          updated_at?: string
        }
        Update: {
          change_actor_role?: string
          change_pattern?: string
          change_rule_group_id?: string
          created_at?: string
          id?: string
          is_activated_since?: string
          review_count_required?: number
          review_fallback_minute?: number
          reviewer_group_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "change_rule_change_actor_role_fkey"
            columns: ["change_actor_role"]
            isOneToOne: false
            referencedRelation: "change_actor_role"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_rule_change_pattern_fkey"
            columns: ["change_pattern"]
            isOneToOne: false
            referencedRelation: "change_pattern"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_rule_change_rule_group_id_fkey"
            columns: ["change_rule_group_id"]
            isOneToOne: false
            referencedRelation: "change_rule_group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_rule_reviewer_group_id_fkey"
            columns: ["reviewer_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
        ]
      }
      change_rule_compensation: {
        Row: {
          change_rule_compensation_kind: string
          change_rule_id: string
          created_at: string
          id: string
          value: number
        }
        Insert: {
          change_rule_compensation_kind: string
          change_rule_id: string
          created_at?: string
          id?: string
          value: number
        }
        Update: {
          change_rule_compensation_kind?: string
          change_rule_id?: string
          created_at?: string
          id?: string
          value?: number
        }
        Relationships: [
          {
            foreignKeyName: "change_rule_compensation_change_rule_compensation_kind_fkey"
            columns: ["change_rule_compensation_kind"]
            isOneToOne: false
            referencedRelation: "change_rule_compensation_kind"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_rule_compensation_change_rule_id_fkey"
            columns: ["change_rule_id"]
            isOneToOne: false
            referencedRelation: "change_rule"
            referencedColumns: ["id"]
          },
        ]
      }
      change_rule_compensation_kind: {
        Row: {
          apply_to_field: string | null
          created_at: string
          id: string
          measure_type: string
          title: Json
        }
        Insert: {
          apply_to_field?: string | null
          created_at?: string
          id: string
          measure_type: string
          title: Json
        }
        Update: {
          apply_to_field?: string | null
          created_at?: string
          id?: string
          measure_type?: string
          title?: Json
        }
        Relationships: []
      }
      change_rule_group: {
        Row: {
          brand_id: string
          created_at: string
          id: string
          is_activated_since: string
          title: Json
          updated_at: string
        }
        Insert: {
          brand_id: string
          created_at?: string
          id?: string
          is_activated_since?: string
          title: Json
          updated_at?: string
        }
        Update: {
          brand_id?: string
          created_at?: string
          id?: string
          is_activated_since?: string
          title?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "change_rule_group_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
        ]
      }
      change_rule_time_window: {
        Row: {
          change_rule_id: string
          created_at: string
          id: string
          minute_from: number
          minute_to: number
          time_window_reference: string
        }
        Insert: {
          change_rule_id: string
          created_at?: string
          id?: string
          minute_from: number
          minute_to: number
          time_window_reference: string
        }
        Update: {
          change_rule_id?: string
          created_at?: string
          id?: string
          minute_from?: number
          minute_to?: number
          time_window_reference?: string
        }
        Relationships: [
          {
            foreignKeyName: "change_rule_time_window_change_rule_id_fkey"
            columns: ["change_rule_id"]
            isOneToOne: false
            referencedRelation: "change_rule"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "change_rule_time_window_time_window_reference_fkey"
            columns: ["time_window_reference"]
            isOneToOne: false
            referencedRelation: "change_time_window_reference"
            referencedColumns: ["id"]
          },
        ]
      }
      change_time_window_reference: {
        Row: {
          created_at: string
          id: string
          reference_field: string
          title: Json
        }
        Insert: {
          created_at?: string
          id: string
          reference_field: string
          title: Json
        }
        Update: {
          created_at?: string
          id?: string
          reference_field?: string
          title?: Json
        }
        Relationships: []
      }
      coupon: {
        Row: {
          amount: number
          brand_id: string
          created_at: string
          creator_id: string
          discount: Json
          end_after: number | null
          end_at: string | null
          id: string
          kind: string
          nid: number
          start_at: string
          updated_at: string
        }
        Insert: {
          amount: number
          brand_id: string
          created_at?: string
          creator_id?: string
          discount: Json
          end_after?: number | null
          end_at?: string | null
          id?: string
          kind: string
          nid?: number
          start_at?: string
          updated_at?: string
        }
        Update: {
          amount?: number
          brand_id?: string
          created_at?: string
          creator_id?: string
          discount?: Json
          end_after?: number | null
          end_at?: string | null
          id?: string
          kind?: string
          nid?: number
          start_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "coupon_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
        ]
      }
      coupon_kind: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      currency: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      day_of_week: {
        Row: {
          func_name_full_en_first: string | null
          func_name_short_en_first: string | null
          id: number
          index_mon_first: number
          index_sun_first: number
          name_full: Json
          name_short: Json
        }
        Insert: {
          func_name_full_en_first?: string | null
          func_name_short_en_first?: string | null
          id?: number
          index_mon_first: number
          index_sun_first: number
          name_full: Json
          name_short: Json
        }
        Update: {
          func_name_full_en_first?: string | null
          func_name_short_en_first?: string | null
          id?: number
          index_mon_first?: number
          index_sun_first?: number
          name_full?: Json
          name_short?: Json
        }
        Relationships: []
      }
      event: {
        Row: {
          airtable_id: string | null
          auto_end_at: string | null
          auto_end_at_local_time: string | null
          auto_final_subtitle: Json
          auto_final_timezone: string | null
          auto_final_title: Json
          auto_start_at_local_dow: number | null
          auto_start_at_local_time: string | null
          avatar: string | null
          brand_id: string | null
          calendar_event_id: string | null
          calendar_event_url: string | null
          canceled_at: string | null
          canceled_by: string | null
          created_at: string
          creation_request_id: string | null
          creator_id: string
          custom_attribute: Json
          duration_minute: number
          id: string
          kind: string
          landmark_id: string | null
          metadata_id: string
          metadata_override: string | null
          nid: number | null
          poster_image_render_id: string | null
          poster_image_url: string | null
          poster_video_render_id: string | null
          poster_video_url: string | null
          preview: Json | null
          publishing_state: string
          space_id: string | null
          start_at: string
          subtitle_prefix: Json
          subtitle_suffix: Json
          title: Json | null
          updated_at: string
          virtual_room_id: string | null
          virtual_room_password: string | null
          virtual_room_url: string | null
        }
        Insert: {
          airtable_id?: string | null
          auto_end_at?: string | null
          auto_end_at_local_time?: string | null
          auto_final_subtitle?: Json
          auto_final_timezone?: string | null
          auto_final_title?: Json
          auto_start_at_local_dow?: number | null
          auto_start_at_local_time?: string | null
          avatar?: string | null
          brand_id?: string | null
          calendar_event_id?: string | null
          calendar_event_url?: string | null
          canceled_at?: string | null
          canceled_by?: string | null
          created_at?: string
          creation_request_id?: string | null
          creator_id: string
          custom_attribute?: Json
          duration_minute?: number
          id?: string
          kind: string
          landmark_id?: string | null
          metadata_id: string
          metadata_override?: string | null
          nid?: number | null
          poster_image_render_id?: string | null
          poster_image_url?: string | null
          poster_video_render_id?: string | null
          poster_video_url?: string | null
          preview?: Json | null
          publishing_state: string
          space_id?: string | null
          start_at: string
          subtitle_prefix?: Json
          subtitle_suffix?: Json
          title?: Json | null
          updated_at?: string
          virtual_room_id?: string | null
          virtual_room_password?: string | null
          virtual_room_url?: string | null
        }
        Update: {
          airtable_id?: string | null
          auto_end_at?: string | null
          auto_end_at_local_time?: string | null
          auto_final_subtitle?: Json
          auto_final_timezone?: string | null
          auto_final_title?: Json
          auto_start_at_local_dow?: number | null
          auto_start_at_local_time?: string | null
          avatar?: string | null
          brand_id?: string | null
          calendar_event_id?: string | null
          calendar_event_url?: string | null
          canceled_at?: string | null
          canceled_by?: string | null
          created_at?: string
          creation_request_id?: string | null
          creator_id?: string
          custom_attribute?: Json
          duration_minute?: number
          id?: string
          kind?: string
          landmark_id?: string | null
          metadata_id?: string
          metadata_override?: string | null
          nid?: number | null
          poster_image_render_id?: string | null
          poster_image_url?: string | null
          poster_video_render_id?: string | null
          poster_video_url?: string | null
          preview?: Json | null
          publishing_state?: string
          space_id?: string | null
          start_at?: string
          subtitle_prefix?: Json
          subtitle_suffix?: Json
          title?: Json | null
          updated_at?: string
          virtual_room_id?: string | null
          virtual_room_password?: string | null
          virtual_room_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "event_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_creation_request_id_fkey"
            columns: ["creation_request_id"]
            isOneToOne: false
            referencedRelation: "change_request"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_kind_fkey"
            columns: ["kind"]
            isOneToOne: false
            referencedRelation: "event_kind"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_landmark_id_fkey"
            columns: ["landmark_id"]
            isOneToOne: false
            referencedRelation: "landmark"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_metadata_id_fkey"
            columns: ["metadata_id"]
            isOneToOne: false
            referencedRelation: "metadata"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_publishing_state_fkey"
            columns: ["publishing_state"]
            isOneToOne: false
            referencedRelation: "publishing_state"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_space_id_fkey"
            columns: ["space_id"]
            isOneToOne: false
            referencedRelation: "space"
            referencedColumns: ["id"]
          },
        ]
      }
      event_kind: {
        Row: {
          id: string
          name: Json
        }
        Insert: {
          id: string
          name?: Json
        }
        Update: {
          id?: string
          name?: Json
        }
        Relationships: []
      }
      event_member: {
        Row: {
          checked_in_at: string | null
          checked_in_by: string | null
          checked_out_at: string | null
          created_at: string
          display_name: Json
          event_id: string
          id: string
          member_profile_id: string
          role: string
          source_order_product_id: string | null
          updated_at: string
        }
        Insert: {
          checked_in_at?: string | null
          checked_in_by?: string | null
          checked_out_at?: string | null
          created_at?: string
          display_name?: Json
          event_id: string
          id?: string
          member_profile_id: string
          role: string
          source_order_product_id?: string | null
          updated_at?: string
        }
        Update: {
          checked_in_at?: string | null
          checked_in_by?: string | null
          checked_out_at?: string | null
          created_at?: string
          display_name?: Json
          event_id?: string
          id?: string
          member_profile_id?: string
          role?: string
          source_order_product_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "event_member_checked_in_by_fkey"
            columns: ["checked_in_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_member_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_member_member_profile_id_fkey"
            columns: ["member_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_member_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "event_member_role"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_member_source_order_product_id_fkey"
            columns: ["source_order_product_id"]
            isOneToOne: false
            referencedRelation: "order_product"
            referencedColumns: ["id"]
          },
        ]
      }
      event_member_role: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      event_product: {
        Row: {
          auto_event_end_at: string
          auto_event_start_at: string
          created_at: string
          event_id: string
          id: string
          kind: string | null
          product_id: string
          updated_at: string
        }
        Insert: {
          auto_event_end_at: string
          auto_event_start_at: string
          created_at?: string
          event_id: string
          id?: string
          kind?: string | null
          product_id: string
          updated_at?: string
        }
        Update: {
          auto_event_end_at?: string
          auto_event_start_at?: string
          created_at?: string
          event_id?: string
          id?: string
          kind?: string | null
          product_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "event_product_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_product_kind_fkey"
            columns: ["kind"]
            isOneToOne: false
            referencedRelation: "event_product_relation"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_product_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
        ]
      }
      event_product_relation: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      feature_access: {
        Row: {
          created_at: string
          description: Json
          id: string
          name: Json
          scope: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: Json
          id: string
          name: Json
          scope?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: Json
          id?: string
          name?: Json
          scope?: string
          updated_at?: string
        }
        Relationships: []
      }
      file: {
        Row: {
          auto_metadata_video_duration_readable: string | null
          created_at: string
          creator_id: string
          file_provider_download_url: string
          file_provider_file_id: string
          file_provider_file_url: string
          file_provider_path: string | null
          hashtags: string[] | null
          id: string
          message_id: string | null
          metadata_landmark_id: string | null
          metadata_taken_at: string | null
          metadata_video_duration_second: number | null
          name: string
          note: string | null
          preview_height: number
          preview_id: string | null
          preview_path: string | null
          preview_url: string | null
          preview_width: number
          size_byte: number
          uniform_type_id: string
          updated_at: string
          webpage_url: string | null
        }
        Insert: {
          auto_metadata_video_duration_readable?: string | null
          created_at?: string
          creator_id: string
          file_provider_download_url: string
          file_provider_file_id: string
          file_provider_file_url: string
          file_provider_path?: string | null
          hashtags?: string[] | null
          id?: string
          message_id?: string | null
          metadata_landmark_id?: string | null
          metadata_taken_at?: string | null
          metadata_video_duration_second?: number | null
          name: string
          note?: string | null
          preview_height: number
          preview_id?: string | null
          preview_path?: string | null
          preview_url?: string | null
          preview_width: number
          size_byte: number
          uniform_type_id: string
          updated_at?: string
          webpage_url?: string | null
        }
        Update: {
          auto_metadata_video_duration_readable?: string | null
          created_at?: string
          creator_id?: string
          file_provider_download_url?: string
          file_provider_file_id?: string
          file_provider_file_url?: string
          file_provider_path?: string | null
          hashtags?: string[] | null
          id?: string
          message_id?: string | null
          metadata_landmark_id?: string | null
          metadata_taken_at?: string | null
          metadata_video_duration_second?: number | null
          name?: string
          note?: string | null
          preview_height?: number
          preview_id?: string | null
          preview_path?: string | null
          preview_url?: string | null
          preview_width?: number
          size_byte?: number
          uniform_type_id?: string
          updated_at?: string
          webpage_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "asset_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "message"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "public_asset_uniform_type_id_fkey"
            columns: ["uniform_type_id"]
            isOneToOne: false
            referencedRelation: "uniform_type"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "public_file_metadata_landmark_id_fkey"
            columns: ["metadata_landmark_id"]
            isOneToOne: false
            referencedRelation: "landmark"
            referencedColumns: ["id"]
          },
        ]
      }
      file_provider: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      group: {
        Row: {
          announcement: Json | null
          archived_at: string | null
          avatar: Json | null
          brand_id: string | null
          created_at: string
          creator_id: string
          deleted_at: string | null
          id: string
          join_rule: string
          kind: string | null
          last_message_id: string | null
          name: string
          nid: number | null
          parent_group_id: string | null
          updated_at: string
        }
        Insert: {
          announcement?: Json | null
          archived_at?: string | null
          avatar?: Json | null
          brand_id?: string | null
          created_at?: string
          creator_id: string
          deleted_at?: string | null
          id?: string
          join_rule?: string
          kind?: string | null
          last_message_id?: string | null
          name: string
          nid?: number | null
          parent_group_id?: string | null
          updated_at?: string
        }
        Update: {
          announcement?: Json | null
          archived_at?: string | null
          avatar?: Json | null
          brand_id?: string | null
          created_at?: string
          creator_id?: string
          deleted_at?: string | null
          id?: string
          join_rule?: string
          kind?: string | null
          last_message_id?: string | null
          name?: string
          nid?: number | null
          parent_group_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "group_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_join_rule_fkey"
            columns: ["join_rule"]
            isOneToOne: false
            referencedRelation: "group_join_rule"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_kind_fkey"
            columns: ["kind"]
            isOneToOne: false
            referencedRelation: "group_kind"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_last_message_id_fkey"
            columns: ["last_message_id"]
            isOneToOne: false
            referencedRelation: "message"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_parent_group_id_fkey"
            columns: ["parent_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
        ]
      }
      group_join_rule: {
        Row: {
          description: Json
          id: string
          name: Json
        }
        Insert: {
          description: Json
          id: string
          name: Json
        }
        Update: {
          description?: Json
          id?: string
          name?: Json
        }
        Relationships: []
      }
      group_kind: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      group_member: {
        Row: {
          accepted_at: string | null
          airtable_id: string | null
          avatar: Json | null
          created_at: string
          deleted_at: string | null
          desc: Json | null
          display_name_full: string | null
          display_name_short: string | null
          group_id: string
          id: string
          ins_handles: string[] | null
          position_titles: string[] | null
          rate: Json | null
          rejected_at: string | null
          role: string
          source_inviter_id: string | null
          source_order_product_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          accepted_at?: string | null
          airtable_id?: string | null
          avatar?: Json | null
          created_at?: string
          deleted_at?: string | null
          desc?: Json | null
          display_name_full?: string | null
          display_name_short?: string | null
          group_id: string
          id?: string
          ins_handles?: string[] | null
          position_titles?: string[] | null
          rate?: Json | null
          rejected_at?: string | null
          role: string
          source_inviter_id?: string | null
          source_order_product_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          accepted_at?: string | null
          airtable_id?: string | null
          avatar?: Json | null
          created_at?: string
          deleted_at?: string | null
          desc?: Json | null
          display_name_full?: string | null
          display_name_short?: string | null
          group_id?: string
          id?: string
          ins_handles?: string[] | null
          position_titles?: string[] | null
          rate?: Json | null
          rejected_at?: string | null
          role?: string
          source_inviter_id?: string | null
          source_order_product_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "group_member_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_member_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "group_member_role"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_member_source_order_product_id_fkey"
            columns: ["source_order_product_id"]
            isOneToOne: false
            referencedRelation: "order_product"
            referencedColumns: ["id"]
          },
        ]
      }
      group_member_role: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      group_member_title: {
        Row: {
          id: string
          title: Json
        }
        Insert: {
          id: string
          title: Json
        }
        Update: {
          id?: string
          title?: Json
        }
        Relationships: []
      }
      group_snapshot: {
        Row: {
          brand_id: string
          created_at: string
          duration: number | null
          graph_id: string
          group_id: string
          id: string
          kind: string
          node_data: Json | null
          path: string
          platform: string
          result: Json | null
          start_ts: string | null
          state: Json
          status: string | null
          ts: string | null
          updated_at: string
        }
        Insert: {
          brand_id: string
          created_at?: string
          duration?: number | null
          graph_id: string
          group_id: string
          id: string
          kind: string
          node_data?: Json | null
          path?: string
          platform?: string
          result?: Json | null
          start_ts?: string | null
          state: Json
          status?: string | null
          ts?: string | null
          updated_at?: string
        }
        Update: {
          brand_id?: string
          created_at?: string
          duration?: number | null
          graph_id?: string
          group_id?: string
          id?: string
          kind?: string
          node_data?: Json | null
          path?: string
          platform?: string
          result?: Json | null
          start_ts?: string | null
          state?: Json
          status?: string | null
          ts?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      hashtag: {
        Row: {
          airtable_id: string | null
          archived_at: string | null
          created_at: string
          creator_id: string
          id: string
          kind: string
        }
        Insert: {
          airtable_id?: string | null
          archived_at?: string | null
          created_at?: string
          creator_id: string
          id: string
          kind: string
        }
        Update: {
          airtable_id?: string | null
          archived_at?: string | null
          created_at?: string
          creator_id?: string
          id?: string
          kind?: string
        }
        Relationships: []
      }
      invitation: {
        Row: {
          code: string
          created_at: string
          end_at: string | null
          id: string
          inviter_id: string
          nid: number
          rule: string
          start_at: string
          state: string
          to_event_id: string | null
          to_group_id: string | null
          to_product_id: string | null
          to_user_id: string | null
          updated_at: string
        }
        Insert: {
          code: string
          created_at?: string
          end_at?: string | null
          id?: string
          inviter_id: string
          nid?: number
          rule: string
          start_at?: string
          state: string
          to_event_id?: string | null
          to_group_id?: string | null
          to_product_id?: string | null
          to_user_id?: string | null
          updated_at?: string
        }
        Update: {
          code?: string
          created_at?: string
          end_at?: string | null
          id?: string
          inviter_id?: string
          nid?: number
          rule?: string
          start_at?: string
          state?: string
          to_event_id?: string | null
          to_group_id?: string | null
          to_product_id?: string | null
          to_user_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invitation_state_fkey"
            columns: ["state"]
            isOneToOne: false
            referencedRelation: "invitation_state"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitation_to_event_id_fkey"
            columns: ["to_event_id"]
            isOneToOne: false
            referencedRelation: "event"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitation_to_group_id_fkey"
            columns: ["to_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitation_to_product_id_fkey"
            columns: ["to_product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
        ]
      }
      invitation_state: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      landmark: {
        Row: {
          address_id: string | null
          airtable_id: string | null
          avatar: Json | null
          brand_id: string | null
          capacity: number | null
          created_at: string
          desc: Json | null
          func_title_full_en_first: string | null
          func_title_short_en_first: string | null
          id: string
          kind: string
          map_url: string | null
          parking_info: Json | null
          title_full: Json
          title_short: Json | null
          updated_at: string
        }
        Insert: {
          address_id?: string | null
          airtable_id?: string | null
          avatar?: Json | null
          brand_id?: string | null
          capacity?: number | null
          created_at?: string
          desc?: Json | null
          func_title_full_en_first?: string | null
          func_title_short_en_first?: string | null
          id?: string
          kind: string
          map_url?: string | null
          parking_info?: Json | null
          title_full: Json
          title_short?: Json | null
          updated_at?: string
        }
        Update: {
          address_id?: string | null
          airtable_id?: string | null
          avatar?: Json | null
          brand_id?: string | null
          capacity?: number | null
          created_at?: string
          desc?: Json | null
          func_title_full_en_first?: string | null
          func_title_short_en_first?: string | null
          id?: string
          kind?: string
          map_url?: string | null
          parking_info?: Json | null
          title_full?: Json
          title_short?: Json | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "landmark_address_id_fkey"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "address"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "landmark_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "landmark_kind_fkey"
            columns: ["kind"]
            isOneToOne: false
            referencedRelation: "landmark_kind"
            referencedColumns: ["id"]
          },
        ]
      }
      landmark_kind: {
        Row: {
          id: string
          name: Json
        }
        Insert: {
          id: string
          name?: Json
        }
        Update: {
          id?: string
          name?: Json
        }
        Relationships: []
      }
      lang_code: {
        Row: {
          emoji: string
          id: string
          local_name: string
        }
        Insert: {
          emoji: string
          id: string
          local_name: string
        }
        Update: {
          emoji?: string
          id?: string
          local_name?: string
        }
        Relationships: []
      }
      message: {
        Row: {
          attachments: Json[] | null
          author_profile_id: string
          class: string
          content: string
          edit_history: Json[] | null
          edited_at: string | null
          group_id: string
          id: string
          image: Json | null
          mentioned_user_ids: string[] | null
          mentions: Json | null
          metadata: Json | null
          novu_notification_id: string | null
          pinned_at: string | null
          pinned_by: string | null
          reactions: Json[] | null
          read_by: Json[] | null
          recall_reason: string | null
          recalled_at: string | null
          replied_message_id: string | null
          sent_at: string
          sent_order: number
          updated_at: string
          video: Json
        }
        Insert: {
          attachments?: Json[] | null
          author_profile_id: string
          class: string
          content: string
          edit_history?: Json[] | null
          edited_at?: string | null
          group_id: string
          id?: string
          image?: Json | null
          mentioned_user_ids?: string[] | null
          mentions?: Json | null
          metadata?: Json | null
          novu_notification_id?: string | null
          pinned_at?: string | null
          pinned_by?: string | null
          reactions?: Json[] | null
          read_by?: Json[] | null
          recall_reason?: string | null
          recalled_at?: string | null
          replied_message_id?: string | null
          sent_at?: string
          sent_order: number
          updated_at?: string
          video: Json
        }
        Update: {
          attachments?: Json[] | null
          author_profile_id?: string
          class?: string
          content?: string
          edit_history?: Json[] | null
          edited_at?: string | null
          group_id?: string
          id?: string
          image?: Json | null
          mentioned_user_ids?: string[] | null
          mentions?: Json | null
          metadata?: Json | null
          novu_notification_id?: string | null
          pinned_at?: string | null
          pinned_by?: string | null
          reactions?: Json[] | null
          read_by?: Json[] | null
          recall_reason?: string | null
          recalled_at?: string | null
          replied_message_id?: string | null
          sent_at?: string
          sent_order?: number
          updated_at?: string
          video?: Json
        }
        Relationships: [
          {
            foreignKeyName: "message_author_profile_id_fkey"
            columns: ["author_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "message_class_fkey"
            columns: ["class"]
            isOneToOne: false
            referencedRelation: "message_class"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "message_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "message_replied_message_id_fkey"
            columns: ["replied_message_id"]
            isOneToOne: false
            referencedRelation: "message"
            referencedColumns: ["id"]
          },
        ]
      }
      message_class: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      metadata: {
        Row: {
          airtable_id: string | null
          auto_final_subtitle: Json
          auto_final_title: Json
          created_at: string
          creation_request_id: string | null
          creator_brand_id: string | null
          creator_group_id: string | null
          creator_id: string | null
          custom_attribute: Json
          desc: Json | null
          func_title_en_first: string | null
          id: string
          kind: string
          message: Json | null
          promo_image_url: string | null
          promo_message: Json | null
          promo_video_file_id: string | null
          promo_video_kind: string | null
          promo_video_render_id: string | null
          promo_video_thumbnail_url: string | null
          promo_video_url: string | null
          promo_webpage_short_url: string | null
          promo_webpage_url: string | null
          subtitle: Json | null
          title: Json | null
          updated_at: string
        }
        Insert: {
          airtable_id?: string | null
          auto_final_subtitle?: Json
          auto_final_title?: Json
          created_at?: string
          creation_request_id?: string | null
          creator_brand_id?: string | null
          creator_group_id?: string | null
          creator_id?: string | null
          custom_attribute?: Json
          desc?: Json | null
          func_title_en_first?: string | null
          id?: string
          kind?: string
          message?: Json | null
          promo_image_url?: string | null
          promo_message?: Json | null
          promo_video_file_id?: string | null
          promo_video_kind?: string | null
          promo_video_render_id?: string | null
          promo_video_thumbnail_url?: string | null
          promo_video_url?: string | null
          promo_webpage_short_url?: string | null
          promo_webpage_url?: string | null
          subtitle?: Json | null
          title?: Json | null
          updated_at?: string
        }
        Update: {
          airtable_id?: string | null
          auto_final_subtitle?: Json
          auto_final_title?: Json
          created_at?: string
          creation_request_id?: string | null
          creator_brand_id?: string | null
          creator_group_id?: string | null
          creator_id?: string | null
          custom_attribute?: Json
          desc?: Json | null
          func_title_en_first?: string | null
          id?: string
          kind?: string
          message?: Json | null
          promo_image_url?: string | null
          promo_message?: Json | null
          promo_video_file_id?: string | null
          promo_video_kind?: string | null
          promo_video_render_id?: string | null
          promo_video_thumbnail_url?: string | null
          promo_video_url?: string | null
          promo_webpage_short_url?: string | null
          promo_webpage_url?: string | null
          subtitle?: Json | null
          title?: Json | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "metadata_creation_request_id_fkey"
            columns: ["creation_request_id"]
            isOneToOne: false
            referencedRelation: "change_request"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "metadata_creator_brand_id_fkey"
            columns: ["creator_brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "metadata_creator_group_id_fkey"
            columns: ["creator_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "metadata_kind_fkey"
            columns: ["kind"]
            isOneToOne: false
            referencedRelation: "metadata_kind"
            referencedColumns: ["id"]
          },
        ]
      }
      metadata_group_member: {
        Row: {
          created_at: string
          group_member_id: string
          id: string
          metadata_id: string
          position_title: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          group_member_id: string
          id?: string
          metadata_id: string
          position_title?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          group_member_id?: string
          id?: string
          metadata_id?: string
          position_title?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "metadata_group_member_group_member_id_fkey"
            columns: ["group_member_id"]
            isOneToOne: false
            referencedRelation: "group_member"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "metadata_group_member_metadata_id_fkey"
            columns: ["metadata_id"]
            isOneToOne: false
            referencedRelation: "metadata"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "metadata_group_member_position_title_fkey"
            columns: ["position_title"]
            isOneToOne: false
            referencedRelation: "group_member_title"
            referencedColumns: ["id"]
          },
        ]
      }
      metadata_hashtag: {
        Row: {
          created_at: string
          hashtag_id: string
          id: string
          metadata_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          hashtag_id: string
          id?: string
          metadata_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          hashtag_id?: string
          id?: string
          metadata_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "metadata_hashtag_hashtag_id_fkey"
            columns: ["hashtag_id"]
            isOneToOne: false
            referencedRelation: "hashtag"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "metadata_hashtag_metadata_id_fkey"
            columns: ["metadata_id"]
            isOneToOne: false
            referencedRelation: "metadata"
            referencedColumns: ["id"]
          },
        ]
      }
      metadata_kind: {
        Row: {
          description: Json
          id: string
          title: Json
        }
        Insert: {
          description?: Json
          id: string
          title?: Json
        }
        Update: {
          description?: Json
          id?: string
          title?: Json
        }
        Relationships: []
      }
      metadata_track: {
        Row: {
          created_at: string
          id: string
          metadata_id: string
          track_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          metadata_id: string
          track_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          metadata_id?: string
          track_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "metadata_track_metadata_id_fkey"
            columns: ["metadata_id"]
            isOneToOne: false
            referencedRelation: "metadata"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "metadata_track_track_id_fkey"
            columns: ["track_id"]
            isOneToOne: false
            referencedRelation: "track"
            referencedColumns: ["id"]
          },
        ]
      }
      metadata_wikipage: {
        Row: {
          created_at: string
          id: string
          metadata_id: string
          relation: string
          relation_desc: Json
          updated_at: string
          wikipage_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          metadata_id: string
          relation: string
          relation_desc?: Json
          updated_at?: string
          wikipage_id: string
        }
        Update: {
          created_at?: string
          id?: string
          metadata_id?: string
          relation?: string
          relation_desc?: Json
          updated_at?: string
          wikipage_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "metadata_wikipage_metadata_id_fkey"
            columns: ["metadata_id"]
            isOneToOne: false
            referencedRelation: "metadata"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "metadata_wikipage_relation_fkey"
            columns: ["relation"]
            isOneToOne: false
            referencedRelation: "metadata_wikipage_relation"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "metadata_wikipage_wikipage_id_fkey"
            columns: ["wikipage_id"]
            isOneToOne: false
            referencedRelation: "wikipage"
            referencedColumns: ["id"]
          },
        ]
      }
      metadata_wikipage_relation: {
        Row: {
          id: string
          sf_symbol: string | null
          supported_wikipage_kinds: string[]
          title: Json
        }
        Insert: {
          id: string
          sf_symbol?: string | null
          supported_wikipage_kinds?: string[]
          title?: Json
        }
        Update: {
          id?: string
          sf_symbol?: string | null
          supported_wikipage_kinds?: string[]
          title?: Json
        }
        Relationships: []
      }
      modifier: {
        Row: {
          brand_id: string
          cost_units: number
          created_at: string
          creator_id: string
          desc: Json
          id: string
          updated_at: string
        }
        Insert: {
          brand_id: string
          cost_units: number
          created_at?: string
          creator_id: string
          desc: Json
          id?: string
          updated_at?: string
        }
        Update: {
          brand_id?: string
          cost_units?: number
          created_at?: string
          creator_id?: string
          desc?: Json
          id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "modifier_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
        ]
      }
      modifier_kind: {
        Row: {
          id: string
          title: Json
        }
        Insert: {
          id: string
          title: Json
        }
        Update: {
          id?: string
          title?: Json
        }
        Relationships: []
      }
      nc_evolutions: {
        Row: {
          batch: number | null
          checksum: string | null
          created: string | null
          created_at: string | null
          description: string | null
          id: number
          status: number | null
          title: string
          titleDown: string | null
          updated_at: string | null
        }
        Insert: {
          batch?: number | null
          checksum?: string | null
          created?: string | null
          created_at?: string | null
          description?: string | null
          id?: number
          status?: number | null
          title: string
          titleDown?: string | null
          updated_at?: string | null
        }
        Update: {
          batch?: number | null
          checksum?: string | null
          created?: string | null
          created_at?: string | null
          description?: string | null
          id?: number
          status?: number | null
          title?: string
          titleDown?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      order: {
        Row: {
          auto_cancel_at: string | null
          canceled_at: string | null
          created_at: string
          creation_request_id: string | null
          id: string
          nid: number | null
          open_for_minute: number
          shipping_address_id: string | null
          updated_at: string
        }
        Insert: {
          auto_cancel_at?: string | null
          canceled_at?: string | null
          created_at?: string
          creation_request_id?: string | null
          id?: string
          nid?: number | null
          open_for_minute?: number
          shipping_address_id?: string | null
          updated_at?: string
        }
        Update: {
          auto_cancel_at?: string | null
          canceled_at?: string | null
          created_at?: string
          creation_request_id?: string | null
          id?: string
          nid?: number | null
          open_for_minute?: number
          shipping_address_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_creation_request_id_fkey"
            columns: ["creation_request_id"]
            isOneToOne: false
            referencedRelation: "change_request"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_shipping_address_id_fkey"
            columns: ["shipping_address_id"]
            isOneToOne: false
            referencedRelation: "address"
            referencedColumns: ["id"]
          },
        ]
      }
      order_coupon: {
        Row: {
          consumer_id: string
          coupon_id: string
          created_at: string
          end_at: string
          id: string
          order_id: string | null
          start_at: string
          updated_at: string
        }
        Insert: {
          consumer_id: string
          coupon_id: string
          created_at?: string
          end_at: string
          id?: string
          order_id?: string | null
          start_at?: string
          updated_at?: string
        }
        Update: {
          consumer_id?: string
          coupon_id?: string
          created_at?: string
          end_at?: string
          id?: string
          order_id?: string | null
          start_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_coupon_coupon_id_fkey"
            columns: ["coupon_id"]
            isOneToOne: false
            referencedRelation: "coupon"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_coupon_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "order"
            referencedColumns: ["id"]
          },
        ]
      }
      order_price: {
        Row: {
          airtable_id: string | null
          auto_money_int_unpaid: number
          auto_price_option_currency_code: string
          auto_price_option_expire_at: string
          auto_price_option_money_int: number
          auto_price_option_price_id: string
          auto_price_option_units: number
          auto_units_available: number
          close_at: string | null
          consumer_profile_id: string
          created_at: string
          deal_expire_at: string
          deal_money_int: number
          deal_units: number
          id: string
          note: string | null
          order_id: string
          payer_id: string
          price_option_id: string
          price_option_purchase_count: number
          updated_at: string
        }
        Insert: {
          airtable_id?: string | null
          auto_money_int_unpaid: number
          auto_price_option_currency_code: string
          auto_price_option_expire_at: string
          auto_price_option_money_int: number
          auto_price_option_price_id: string
          auto_price_option_units: number
          auto_units_available: number
          close_at?: string | null
          consumer_profile_id: string
          created_at?: string
          deal_expire_at: string
          deal_money_int: number
          deal_units: number
          id?: string
          note?: string | null
          order_id: string
          payer_id: string
          price_option_id: string
          price_option_purchase_count: number
          updated_at?: string
        }
        Update: {
          airtable_id?: string | null
          auto_money_int_unpaid?: number
          auto_price_option_currency_code?: string
          auto_price_option_expire_at?: string
          auto_price_option_money_int?: number
          auto_price_option_price_id?: string
          auto_price_option_units?: number
          auto_units_available?: number
          close_at?: string | null
          consumer_profile_id?: string
          created_at?: string
          deal_expire_at?: string
          deal_money_int?: number
          deal_units?: number
          id?: string
          note?: string | null
          order_id?: string
          payer_id?: string
          price_option_id?: string
          price_option_purchase_count?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_price_auto_price_option_currency_code_fkey"
            columns: ["auto_price_option_currency_code"]
            isOneToOne: false
            referencedRelation: "currency"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_price_auto_price_option_price_id_fkey"
            columns: ["auto_price_option_price_id"]
            isOneToOne: false
            referencedRelation: "price"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_price_consumer_profile_id_fkey"
            columns: ["consumer_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_price_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "order"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_price_price_option_id_fkey"
            columns: ["price_option_id"]
            isOneToOne: false
            referencedRelation: "price_option"
            referencedColumns: ["id"]
          },
        ]
      }
      order_price_consumption: {
        Row: {
          auto_order_product_canceled_at: string | null
          cost_units: number
          created_at: string
          id: string
          order_price_id: string
          order_product_id: string
          returned_units: number
          updated_at: string
        }
        Insert: {
          auto_order_product_canceled_at?: string | null
          cost_units: number
          created_at?: string
          id?: string
          order_price_id: string
          order_product_id: string
          returned_units?: number
          updated_at?: string
        }
        Update: {
          auto_order_product_canceled_at?: string | null
          cost_units?: number
          created_at?: string
          id?: string
          order_price_id?: string
          order_product_id?: string
          returned_units?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_price_consumption_order_price_id_fkey"
            columns: ["order_price_id"]
            isOneToOne: false
            referencedRelation: "order_price"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_price_consumption_order_product_id_fkey"
            columns: ["order_product_id"]
            isOneToOne: false
            referencedRelation: "order_product"
            referencedColumns: ["id"]
          },
        ]
      }
      order_price_payment: {
        Row: {
          billing_address_id: string | null
          created_at: string
          creator_id: string | null
          currency_code: string
          failed_at: string | null
          id: string
          money_received_int: number
          note: string | null
          order_price_id: string
          payment_source: string
          stripe_customer_id: string | null
          stripe_payment_intent_id: string | null
          succeed_at: string | null
          updated_at: string
        }
        Insert: {
          billing_address_id?: string | null
          created_at?: string
          creator_id?: string | null
          currency_code: string
          failed_at?: string | null
          id?: string
          money_received_int: number
          note?: string | null
          order_price_id: string
          payment_source: string
          stripe_customer_id?: string | null
          stripe_payment_intent_id?: string | null
          succeed_at?: string | null
          updated_at?: string
        }
        Update: {
          billing_address_id?: string | null
          created_at?: string
          creator_id?: string | null
          currency_code?: string
          failed_at?: string | null
          id?: string
          money_received_int?: number
          note?: string | null
          order_price_id?: string
          payment_source?: string
          stripe_customer_id?: string | null
          stripe_payment_intent_id?: string | null
          succeed_at?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_price_payment_billing_address_id_fkey"
            columns: ["billing_address_id"]
            isOneToOne: false
            referencedRelation: "address"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_price_payment_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_price_payment_currency_code_fkey"
            columns: ["currency_code"]
            isOneToOne: false
            referencedRelation: "currency"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_price_payment_order_price_id_fkey"
            columns: ["order_price_id"]
            isOneToOne: false
            referencedRelation: "order_price"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_price_payment_payment_source_fkey"
            columns: ["payment_source"]
            isOneToOne: false
            referencedRelation: "payment_source"
            referencedColumns: ["id"]
          },
        ]
      }
      order_price_refund: {
        Row: {
          created_at: string
          id: string
          money_currency_code: string
          money_refunded_int: number
          order_price_id: string
          stripe_customer_id: string | null
          stripe_payment_intent_id: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          money_currency_code: string
          money_refunded_int: number
          order_price_id: string
          stripe_customer_id?: string | null
          stripe_payment_intent_id?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          money_currency_code?: string
          money_refunded_int?: number
          order_price_id?: string
          stripe_customer_id?: string | null
          stripe_payment_intent_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_price_refund_money_currency_code_fkey"
            columns: ["money_currency_code"]
            isOneToOne: false
            referencedRelation: "currency"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_price_refund_order_price_id_fkey"
            columns: ["order_price_id"]
            isOneToOne: false
            referencedRelation: "order_price"
            referencedColumns: ["id"]
          },
        ]
      }
      order_price_state: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      order_price_subscription: {
        Row: {
          cancel_at_period_end: boolean
          canceled_at: string | null
          created_at: string
          current_period_end: string
          current_period_start: string
          deal_currency: string
          deal_money: number
          id: string
          order_price_id: string | null
          status: string
          stripe_subscription_id: string | null
          updated_at: string
        }
        Insert: {
          cancel_at_period_end: boolean
          canceled_at?: string | null
          created_at?: string
          current_period_end: string
          current_period_start: string
          deal_currency: string
          deal_money: number
          id?: string
          order_price_id?: string | null
          status: string
          stripe_subscription_id?: string | null
          updated_at?: string
        }
        Update: {
          cancel_at_period_end?: boolean
          canceled_at?: string | null
          created_at?: string
          current_period_end?: string
          current_period_start?: string
          deal_currency?: string
          deal_money?: number
          id?: string
          order_price_id?: string | null
          status?: string
          stripe_subscription_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_price_subscription_order_price_id_fkey"
            columns: ["order_price_id"]
            isOneToOne: false
            referencedRelation: "order_price"
            referencedColumns: ["id"]
          },
        ]
      }
      order_product: {
        Row: {
          airtable_id: string | null
          auto_cancel_at_far: string | null
          auto_cancel_at_far_return_units: number | null
          auto_cancel_at_near: string | null
          auto_cancel_at_near_return_units: number | null
          auto_order_shipping_address_id: string | null
          auto_product_price_cost_units: number
          auto_product_price_price_id: string | null
          auto_product_price_product_id: string
          auto_units_owed: number
          canceled_at: string | null
          canceled_at_should_return_units: number | null
          consumer_profile_id: string
          created_at: string
          deal_expire_at: string | null
          id: string
          order_id: string
          product_price_id: string
          product_purchased_count: number
          updated_at: string
        }
        Insert: {
          airtable_id?: string | null
          auto_cancel_at_far?: string | null
          auto_cancel_at_far_return_units?: number | null
          auto_cancel_at_near?: string | null
          auto_cancel_at_near_return_units?: number | null
          auto_order_shipping_address_id?: string | null
          auto_product_price_cost_units?: number
          auto_product_price_price_id?: string | null
          auto_product_price_product_id: string
          auto_units_owed?: number
          canceled_at?: string | null
          canceled_at_should_return_units?: number | null
          consumer_profile_id: string
          created_at?: string
          deal_expire_at?: string | null
          id?: string
          order_id: string
          product_price_id: string
          product_purchased_count: number
          updated_at?: string
        }
        Update: {
          airtable_id?: string | null
          auto_cancel_at_far?: string | null
          auto_cancel_at_far_return_units?: number | null
          auto_cancel_at_near?: string | null
          auto_cancel_at_near_return_units?: number | null
          auto_order_shipping_address_id?: string | null
          auto_product_price_cost_units?: number
          auto_product_price_price_id?: string | null
          auto_product_price_product_id?: string
          auto_units_owed?: number
          canceled_at?: string | null
          canceled_at_should_return_units?: number | null
          consumer_profile_id?: string
          created_at?: string
          deal_expire_at?: string | null
          id?: string
          order_id?: string
          product_price_id?: string
          product_purchased_count?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_product_auto_order_shipping_address_id_fkey"
            columns: ["auto_order_shipping_address_id"]
            isOneToOne: false
            referencedRelation: "address"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_product_auto_product_price_price_id_fkey"
            columns: ["auto_product_price_price_id"]
            isOneToOne: false
            referencedRelation: "price"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_product_auto_product_price_product_id_fkey"
            columns: ["auto_product_price_product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_product_consumer_profile_id_fkey"
            columns: ["consumer_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_product_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "order"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_product_product_price_id_fkey"
            columns: ["product_price_id"]
            isOneToOne: false
            referencedRelation: "product_price"
            referencedColumns: ["id"]
          },
        ]
      }
      order_requirement: {
        Row: {
          consumer_product_count_max: number | null
          created_at: string
          id: string
          payer_product_count_max: number | null
          product_count_in_days: number
          require_consumer_owning_product_id: string | null
          require_passcode: string | null
          require_payer_owning_product_id: string | null
          title_short: Json
          updated_at: string
        }
        Insert: {
          consumer_product_count_max?: number | null
          created_at?: string
          id?: string
          payer_product_count_max?: number | null
          product_count_in_days: number
          require_consumer_owning_product_id?: string | null
          require_passcode?: string | null
          require_payer_owning_product_id?: string | null
          title_short?: Json
          updated_at?: string
        }
        Update: {
          consumer_product_count_max?: number | null
          created_at?: string
          id?: string
          payer_product_count_max?: number | null
          product_count_in_days?: number
          require_consumer_owning_product_id?: string | null
          require_passcode?: string | null
          require_payer_owning_product_id?: string | null
          title_short?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_requirement_require_consumer_owning_product_id_fkey"
            columns: ["require_consumer_owning_product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_requirement_require_payer_owning_product_id_fkey"
            columns: ["require_payer_owning_product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
        ]
      }
      order_state: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      payment_source: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      payroll_entry: {
        Row: {
          adjustment_amount: number | null
          adjustment_by: string | null
          adjustment_reason: string | null
          calculation_details: Json
          created_at: string
          entry_for_event_id: string | null
          entry_for_product_id: string | null
          entry_for_profile_id: string | null
          entry_type: string
          event_formula_inputs:
            | Database["public"]["CompositeTypes"]["payroll_event_formula_inputs"]
            | null
          final_amount: number | null
          formula_amount: number
          id: string
          payroll_formula_id: string
          payroll_period_id: string
          payroll_profile_id: string
          product_formula_inputs:
            | Database["public"]["CompositeTypes"]["payroll_product_formula_inputs"]
            | null
          profile_formula_inputs:
            | Database["public"]["CompositeTypes"]["payroll_profile_formula_inputs"]
            | null
          updated_at: string
        }
        Insert: {
          adjustment_amount?: number | null
          adjustment_by?: string | null
          adjustment_reason?: string | null
          calculation_details: Json
          created_at?: string
          entry_for_event_id?: string | null
          entry_for_product_id?: string | null
          entry_for_profile_id?: string | null
          entry_type: string
          event_formula_inputs?:
            | Database["public"]["CompositeTypes"]["payroll_event_formula_inputs"]
            | null
          final_amount?: number | null
          formula_amount: number
          id?: string
          payroll_formula_id: string
          payroll_period_id: string
          payroll_profile_id: string
          product_formula_inputs?:
            | Database["public"]["CompositeTypes"]["payroll_product_formula_inputs"]
            | null
          profile_formula_inputs?:
            | Database["public"]["CompositeTypes"]["payroll_profile_formula_inputs"]
            | null
          updated_at?: string
        }
        Update: {
          adjustment_amount?: number | null
          adjustment_by?: string | null
          adjustment_reason?: string | null
          calculation_details?: Json
          created_at?: string
          entry_for_event_id?: string | null
          entry_for_product_id?: string | null
          entry_for_profile_id?: string | null
          entry_type?: string
          event_formula_inputs?:
            | Database["public"]["CompositeTypes"]["payroll_event_formula_inputs"]
            | null
          final_amount?: number | null
          formula_amount?: number
          id?: string
          payroll_formula_id?: string
          payroll_period_id?: string
          payroll_profile_id?: string
          product_formula_inputs?:
            | Database["public"]["CompositeTypes"]["payroll_product_formula_inputs"]
            | null
          profile_formula_inputs?:
            | Database["public"]["CompositeTypes"]["payroll_profile_formula_inputs"]
            | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payroll_entry_adjustment_by_fkey"
            columns: ["adjustment_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_entry_entry_for_event_id_fkey"
            columns: ["entry_for_event_id"]
            isOneToOne: false
            referencedRelation: "event"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_entry_entry_for_product_id_fkey"
            columns: ["entry_for_product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_entry_entry_for_profile_id_fkey"
            columns: ["entry_for_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_entry_payroll_formula_id_fkey"
            columns: ["payroll_formula_id"]
            isOneToOne: false
            referencedRelation: "payroll_formula"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_entry_payroll_period_id_fkey"
            columns: ["payroll_period_id"]
            isOneToOne: false
            referencedRelation: "payroll_period"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_entry_payroll_profile_id_fkey"
            columns: ["payroll_profile_id"]
            isOneToOne: false
            referencedRelation: "payroll_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      payroll_formula: {
        Row: {
          brand_id: string
          created_at: string
          description: string | null
          earning_source_kind: string
          event_kind: string | null
          formula_js: string
          id: string
          is_active: boolean
          is_default: boolean
          name: string
          product_kind: string | null
          updated_at: string
        }
        Insert: {
          brand_id: string
          created_at?: string
          description?: string | null
          earning_source_kind: string
          event_kind?: string | null
          formula_js: string
          id?: string
          is_active?: boolean
          is_default?: boolean
          name: string
          product_kind?: string | null
          updated_at?: string
        }
        Update: {
          brand_id?: string
          created_at?: string
          description?: string | null
          earning_source_kind?: string
          event_kind?: string | null
          formula_js?: string
          id?: string
          is_active?: boolean
          is_default?: boolean
          name?: string
          product_kind?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payroll_formula_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_formula_event_kind_fkey"
            columns: ["event_kind"]
            isOneToOne: false
            referencedRelation: "event_kind"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_formula_product_kind_fkey"
            columns: ["product_kind"]
            isOneToOne: false
            referencedRelation: "product_kind"
            referencedColumns: ["id"]
          },
        ]
      }
      payroll_formula_assignment: {
        Row: {
          auto_payroll_formula_earning_source_kind: string | null
          created_at: string
          id: string
          payroll_formula_id: string
          payroll_profile_id: string
          updated_at: string
        }
        Insert: {
          auto_payroll_formula_earning_source_kind?: string | null
          created_at?: string
          id?: string
          payroll_formula_id: string
          payroll_profile_id: string
          updated_at?: string
        }
        Update: {
          auto_payroll_formula_earning_source_kind?: string | null
          created_at?: string
          id?: string
          payroll_formula_id?: string
          payroll_profile_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payroll_formula_assignment_payroll_formula_id_fkey"
            columns: ["payroll_formula_id"]
            isOneToOne: false
            referencedRelation: "payroll_formula"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_formula_assignment_payroll_profile_id_fkey"
            columns: ["payroll_profile_id"]
            isOneToOne: false
            referencedRelation: "payroll_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      payroll_period: {
        Row: {
          brand_id: string
          created_at: string
          creator_id: string
          end_at: string
          id: string
          name: string
          start_at: string
          status: string
          updated_at: string
        }
        Insert: {
          brand_id: string
          created_at?: string
          creator_id: string
          end_at: string
          id?: string
          name: string
          start_at: string
          status?: string
          updated_at?: string
        }
        Update: {
          brand_id?: string
          created_at?: string
          creator_id?: string
          end_at?: string
          id?: string
          name?: string
          start_at?: string
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payroll_period_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_period_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      payroll_profile: {
        Row: {
          accountant_notes: string | null
          created_at: string
          creator_id: string
          currency_code: string
          enrolled_at: string
          id: string
          level: string | null
          profile_id: string
          title: string | null
          unenrolled_at: string | null
          updated_at: string
        }
        Insert: {
          accountant_notes?: string | null
          created_at?: string
          creator_id: string
          currency_code: string
          enrolled_at?: string
          id?: string
          level?: string | null
          profile_id: string
          title?: string | null
          unenrolled_at?: string | null
          updated_at?: string
        }
        Update: {
          accountant_notes?: string | null
          created_at?: string
          creator_id?: string
          currency_code?: string
          enrolled_at?: string
          id?: string
          level?: string | null
          profile_id?: string
          title?: string | null
          unenrolled_at?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payroll_profile_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_profile_currency_code_fkey"
            columns: ["currency_code"]
            isOneToOne: false
            referencedRelation: "currency"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_profile_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      price: {
        Row: {
          airtable_record_id: string | null
          brand_id: string
          cancel_at_far_minute: number | null
          cancel_at_far_return_ratio: number | null
          cancel_at_near_minute: number | null
          cancel_at_near_return_ratio: number | null
          cancel_at_relative_to: string | null
          cancel_at_return_step: number | null
          color_primary_hex: string | null
          color_primary_semantic: string
          created_at: string
          creator_id: string
          func_product_classification_en_first: string | null
          func_title_en_first: string
          id: string
          money_back_primary_window_minute: number
          payment_kind: string
          product_classification: Json
          semantic_order: number
          state: string | null
          title: Json
          title_short: Json
          unit_kind: string
          unit_step: number
          updated_at: string
        }
        Insert: {
          airtable_record_id?: string | null
          brand_id: string
          cancel_at_far_minute?: number | null
          cancel_at_far_return_ratio?: number | null
          cancel_at_near_minute?: number | null
          cancel_at_near_return_ratio?: number | null
          cancel_at_relative_to?: string | null
          cancel_at_return_step?: number | null
          color_primary_hex?: string | null
          color_primary_semantic: string
          created_at?: string
          creator_id: string
          func_product_classification_en_first?: string | null
          func_title_en_first?: string
          id?: string
          money_back_primary_window_minute?: number
          payment_kind: string
          product_classification: Json
          semantic_order: number
          state?: string | null
          title: Json
          title_short?: Json
          unit_kind: string
          unit_step: number
          updated_at?: string
        }
        Update: {
          airtable_record_id?: string | null
          brand_id?: string
          cancel_at_far_minute?: number | null
          cancel_at_far_return_ratio?: number | null
          cancel_at_near_minute?: number | null
          cancel_at_near_return_ratio?: number | null
          cancel_at_relative_to?: string | null
          cancel_at_return_step?: number | null
          color_primary_hex?: string | null
          color_primary_semantic?: string
          created_at?: string
          creator_id?: string
          func_product_classification_en_first?: string | null
          func_title_en_first?: string
          id?: string
          money_back_primary_window_minute?: number
          payment_kind?: string
          product_classification?: Json
          semantic_order?: number
          state?: string | null
          title?: Json
          title_short?: Json
          unit_kind?: string
          unit_step?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "price_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "price_cancel_at_relative_to_fkey"
            columns: ["cancel_at_relative_to"]
            isOneToOne: false
            referencedRelation: "price_cancel_at"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "price_payment_kind_fkey"
            columns: ["payment_kind"]
            isOneToOne: false
            referencedRelation: "price_payment_kind"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "price_state_fkey"
            columns: ["state"]
            isOneToOne: false
            referencedRelation: "price_option_state"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "price_unit_kind_fkey"
            columns: ["unit_kind"]
            isOneToOne: false
            referencedRelation: "price_unit_kind"
            referencedColumns: ["id"]
          },
        ]
      }
      price_cancel_at: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      price_option: {
        Row: {
          airtable_record_id: string | null
          country_code: string
          created_at: string
          currency_code: string
          func_title_en_first: string
          id: string
          money_int: number
          order_requirement_id: string | null
          original_price_option_id: string | null
          price_id: string
          recurring_interval: string | null
          state: string
          title: Json
          units: number
          updated_at: string
          valid_for_minute: number
        }
        Insert: {
          airtable_record_id?: string | null
          country_code: string
          created_at?: string
          currency_code: string
          func_title_en_first?: string
          id?: string
          money_int: number
          order_requirement_id?: string | null
          original_price_option_id?: string | null
          price_id: string
          recurring_interval?: string | null
          state: string
          title: Json
          units: number
          updated_at?: string
          valid_for_minute: number
        }
        Update: {
          airtable_record_id?: string | null
          country_code?: string
          created_at?: string
          currency_code?: string
          func_title_en_first?: string
          id?: string
          money_int?: number
          order_requirement_id?: string | null
          original_price_option_id?: string | null
          price_id?: string
          recurring_interval?: string | null
          state?: string
          title?: Json
          units?: number
          updated_at?: string
          valid_for_minute?: number
        }
        Relationships: [
          {
            foreignKeyName: "price_option_currency_code_fkey"
            columns: ["currency_code"]
            isOneToOne: false
            referencedRelation: "currency"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "price_option_order_requirement_id_fkey"
            columns: ["order_requirement_id"]
            isOneToOne: false
            referencedRelation: "order_requirement"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "price_option_original_price_option_id_fkey"
            columns: ["original_price_option_id"]
            isOneToOne: false
            referencedRelation: "price_option"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "price_option_price_id_fkey"
            columns: ["price_id"]
            isOneToOne: false
            referencedRelation: "price"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "price_option_state_fkey"
            columns: ["state"]
            isOneToOne: false
            referencedRelation: "price_option_state"
            referencedColumns: ["id"]
          },
        ]
      }
      price_option_state: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      price_payment_kind: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      price_unit_kind: {
        Row: {
          created_at: string
          id: string
          quantity_forms: Json
          quantity_forms_short: Json
          updated_at: string
        }
        Insert: {
          created_at?: string
          id: string
          quantity_forms?: Json
          quantity_forms_short?: Json
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          quantity_forms?: Json
          quantity_forms_short?: Json
          updated_at?: string
        }
        Relationships: []
      }
      product: {
        Row: {
          airtable_id: string | null
          auto_first_event_id: string | null
          auto_first_event_start_at: string | null
          auto_last_event_end_at: string | null
          auto_last_event_id: string | null
          auto_stock_available: number
          avatar: string | null
          brand_id: string
          close_to_buy_at: string
          consumer_group_id: string
          created_at: string
          creation_request_id: string | null
          creator_id: string
          id: string
          kind: string
          landmark_id: string | null
          metadata: Json | null
          metadata_id: string | null
          nid: number | null
          open_to_buy_at: string | null
          product_category_id: string | null
          publishing_state: string
          stock: number
          title: Json | null
          updated_at: string
          waiting_group_id: string | null
        }
        Insert: {
          airtable_id?: string | null
          auto_first_event_id?: string | null
          auto_first_event_start_at?: string | null
          auto_last_event_end_at?: string | null
          auto_last_event_id?: string | null
          auto_stock_available?: number
          avatar?: string | null
          brand_id: string
          close_to_buy_at?: string
          consumer_group_id: string
          created_at?: string
          creation_request_id?: string | null
          creator_id?: string
          id?: string
          kind: string
          landmark_id?: string | null
          metadata?: Json | null
          metadata_id?: string | null
          nid?: number | null
          open_to_buy_at?: string | null
          product_category_id?: string | null
          publishing_state: string
          stock: number
          title?: Json | null
          updated_at?: string
          waiting_group_id?: string | null
        }
        Update: {
          airtable_id?: string | null
          auto_first_event_id?: string | null
          auto_first_event_start_at?: string | null
          auto_last_event_end_at?: string | null
          auto_last_event_id?: string | null
          auto_stock_available?: number
          avatar?: string | null
          brand_id?: string
          close_to_buy_at?: string
          consumer_group_id?: string
          created_at?: string
          creation_request_id?: string | null
          creator_id?: string
          id?: string
          kind?: string
          landmark_id?: string | null
          metadata?: Json | null
          metadata_id?: string | null
          nid?: number | null
          open_to_buy_at?: string | null
          product_category_id?: string | null
          publishing_state?: string
          stock?: number
          title?: Json | null
          updated_at?: string
          waiting_group_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "product_auto_first_event_id_fkey"
            columns: ["auto_first_event_id"]
            isOneToOne: false
            referencedRelation: "event"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_auto_last_event_id_fkey"
            columns: ["auto_last_event_id"]
            isOneToOne: false
            referencedRelation: "event"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_consumer_group_id_fkey"
            columns: ["consumer_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_creation_request_id_fkey"
            columns: ["creation_request_id"]
            isOneToOne: false
            referencedRelation: "change_request"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_kind_fkey"
            columns: ["kind"]
            isOneToOne: false
            referencedRelation: "product_kind"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_landmark_id_fkey"
            columns: ["landmark_id"]
            isOneToOne: false
            referencedRelation: "landmark"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_metadata_id_fkey"
            columns: ["metadata_id"]
            isOneToOne: false
            referencedRelation: "metadata"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_product_category_id_fkey"
            columns: ["product_category_id"]
            isOneToOne: false
            referencedRelation: "product_category"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_publishing_state_fkey"
            columns: ["publishing_state"]
            isOneToOne: false
            referencedRelation: "publishing_state"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_waiting_group_id_fkey"
            columns: ["waiting_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
        ]
      }
      product_bundle: {
        Row: {
          bundle_id: string
          created_at: string
          id: string
          product_id: string
          updated_at: string
        }
        Insert: {
          bundle_id: string
          created_at?: string
          id?: string
          product_id: string
          updated_at?: string
        }
        Update: {
          bundle_id?: string
          created_at?: string
          id?: string
          product_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_bundle_bundle_id_fkey"
            columns: ["bundle_id"]
            isOneToOne: false
            referencedRelation: "bundle"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_bundle_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
        ]
      }
      product_category: {
        Row: {
          brand_id: string
          created_at: string
          creator_id: string
          icon: Json
          id: string
          nid: number
          parent_product_category_id: string | null
          publishing_state: string
          semantic_order: number
          title: Json
          updated_at: string
        }
        Insert: {
          brand_id: string
          created_at?: string
          creator_id: string
          icon?: Json
          id?: string
          nid?: number
          parent_product_category_id?: string | null
          publishing_state: string
          semantic_order: number
          title: Json
          updated_at?: string
        }
        Update: {
          brand_id?: string
          created_at?: string
          creator_id?: string
          icon?: Json
          id?: string
          nid?: number
          parent_product_category_id?: string | null
          publishing_state?: string
          semantic_order?: number
          title?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_category_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_category_parent_product_category_id_fkey"
            columns: ["parent_product_category_id"]
            isOneToOne: false
            referencedRelation: "product_category"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_category_publishing_state_fkey"
            columns: ["publishing_state"]
            isOneToOne: false
            referencedRelation: "publishing_state"
            referencedColumns: ["id"]
          },
        ]
      }
      product_feature_access: {
        Row: {
          auto_product_brand_id: string
          created_at: string
          feature_access_id: string
          feature_access_settings: Json
          feature_access_target_group_id: string | null
          feature_access_target_landmark_id: string | null
          id: string
          product_id: string
          updated_at: string
        }
        Insert: {
          auto_product_brand_id: string
          created_at?: string
          feature_access_id: string
          feature_access_settings?: Json
          feature_access_target_group_id?: string | null
          feature_access_target_landmark_id?: string | null
          id?: string
          product_id: string
          updated_at?: string
        }
        Update: {
          auto_product_brand_id?: string
          created_at?: string
          feature_access_id?: string
          feature_access_settings?: Json
          feature_access_target_group_id?: string | null
          feature_access_target_landmark_id?: string | null
          id?: string
          product_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_feature_access_auto_product_brand_id_fkey"
            columns: ["auto_product_brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_feature_access_feature_access_id_fkey"
            columns: ["feature_access_id"]
            isOneToOne: false
            referencedRelation: "feature_access"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_feature_access_feature_access_target_group_id_fkey"
            columns: ["feature_access_target_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_feature_access_feature_access_target_landmark_id_fkey"
            columns: ["feature_access_target_landmark_id"]
            isOneToOne: false
            referencedRelation: "landmark"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_feature_access_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
        ]
      }
      product_kind: {
        Row: {
          event_count_min: number
          id: string
          key_metadata_wikipage_relation: string | null
          title: Json
        }
        Insert: {
          event_count_min?: number
          id: string
          key_metadata_wikipage_relation?: string | null
          title?: Json
        }
        Update: {
          event_count_min?: number
          id?: string
          key_metadata_wikipage_relation?: string | null
          title?: Json
        }
        Relationships: [
          {
            foreignKeyName: "product_kind_key_metadata_wikipage_relation_fkey"
            columns: ["key_metadata_wikipage_relation"]
            isOneToOne: false
            referencedRelation: "metadata_wikipage_relation"
            referencedColumns: ["id"]
          },
        ]
      }
      product_modifier: {
        Row: {
          amount: number
          created_at: string
          id: string
          modifier_id: string
          product_id: string
          section_index: number
          section_selection_max: number
          section_title: Json
          updated_at: string
        }
        Insert: {
          amount: number
          created_at?: string
          id?: string
          modifier_id: string
          product_id: string
          section_index: number
          section_selection_max: number
          section_title: Json
          updated_at?: string
        }
        Update: {
          amount?: number
          created_at?: string
          id?: string
          modifier_id?: string
          product_id?: string
          section_index?: number
          section_selection_max?: number
          section_title?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_modifier_modifier_id_fkey"
            columns: ["modifier_id"]
            isOneToOne: false
            referencedRelation: "modifier"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_modifier_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
        ]
      }
      product_price: {
        Row: {
          auto_cancel_at_far: string | null
          auto_cancel_at_far_return_units: number | null
          auto_cancel_at_near: string | null
          auto_cancel_at_near_return_units: number | null
          cost_units: number
          created_at: string
          end_at: string | null
          id: string
          order_requirement_id: string | null
          price_id: string
          product_id: string
          reward_price_id: string | null
          reward_price_units: number | null
          start_at: string
          stock: number | null
          updated_at: string
        }
        Insert: {
          auto_cancel_at_far?: string | null
          auto_cancel_at_far_return_units?: number | null
          auto_cancel_at_near?: string | null
          auto_cancel_at_near_return_units?: number | null
          cost_units: number
          created_at?: string
          end_at?: string | null
          id?: string
          order_requirement_id?: string | null
          price_id: string
          product_id: string
          reward_price_id?: string | null
          reward_price_units?: number | null
          start_at?: string
          stock?: number | null
          updated_at?: string
        }
        Update: {
          auto_cancel_at_far?: string | null
          auto_cancel_at_far_return_units?: number | null
          auto_cancel_at_near?: string | null
          auto_cancel_at_near_return_units?: number | null
          cost_units?: number
          created_at?: string
          end_at?: string | null
          id?: string
          order_requirement_id?: string | null
          price_id?: string
          product_id?: string
          reward_price_id?: string | null
          reward_price_units?: number | null
          start_at?: string
          stock?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_price_order_requirement_id_fkey"
            columns: ["order_requirement_id"]
            isOneToOne: false
            referencedRelation: "order_requirement"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_price_price_id_fkey"
            columns: ["price_id"]
            isOneToOne: false
            referencedRelation: "price"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_price_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_price_reward_price_id_fkey"
            columns: ["reward_price_id"]
            isOneToOne: false
            referencedRelation: "price"
            referencedColumns: ["id"]
          },
        ]
      }
      product_series: {
        Row: {
          created_at: string
          id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          updated_at?: string
        }
        Relationships: []
      }
      profile: {
        Row: {
          about: Json | null
          airtable_id: string | null
          auto_total_order_price_money_int_unpaid: number
          auto_user_email: string | null
          auto_user_phone: string | null
          avatar: string | null
          avatar_url: string | null
          created_at: string
          debug_pw: string | null
          facebook_id: string | null
          family_name: Json | null
          func_family_name_en_first: string | null
          func_given_name_en_first: string | null
          given_name: Json | null
          id: string
          instagram_id: string | null
          jetadmin_id: string | null
          middle_name: Json | null
          nickname: string
          nid: number
          postal_code: string | null
          primary_lang: string
          profile_url: string | null
          red_id: string | null
          secondary_term: boolean
          source: string | null
          twitter_id: string | null
          updated_at: string
          username: string | null
          vac_status: string | null
          venmo_id: string | null
          website: string | null
          wechat_id: string | null
          zelle_id: string | null
        }
        Insert: {
          about?: Json | null
          airtable_id?: string | null
          auto_total_order_price_money_int_unpaid?: number
          auto_user_email?: string | null
          auto_user_phone?: string | null
          avatar?: string | null
          avatar_url?: string | null
          created_at?: string
          debug_pw?: string | null
          facebook_id?: string | null
          family_name?: Json | null
          func_family_name_en_first?: string | null
          func_given_name_en_first?: string | null
          given_name?: Json | null
          id: string
          instagram_id?: string | null
          jetadmin_id?: string | null
          middle_name?: Json | null
          nickname?: string
          nid?: number
          postal_code?: string | null
          primary_lang?: string
          profile_url?: string | null
          red_id?: string | null
          secondary_term?: boolean
          source?: string | null
          twitter_id?: string | null
          updated_at?: string
          username?: string | null
          vac_status?: string | null
          venmo_id?: string | null
          website?: string | null
          wechat_id?: string | null
          zelle_id?: string | null
        }
        Update: {
          about?: Json | null
          airtable_id?: string | null
          auto_total_order_price_money_int_unpaid?: number
          auto_user_email?: string | null
          auto_user_phone?: string | null
          avatar?: string | null
          avatar_url?: string | null
          created_at?: string
          debug_pw?: string | null
          facebook_id?: string | null
          family_name?: Json | null
          func_family_name_en_first?: string | null
          func_given_name_en_first?: string | null
          given_name?: Json | null
          id?: string
          instagram_id?: string | null
          jetadmin_id?: string | null
          middle_name?: Json | null
          nickname?: string
          nid?: number
          postal_code?: string | null
          primary_lang?: string
          profile_url?: string | null
          red_id?: string | null
          secondary_term?: boolean
          source?: string | null
          twitter_id?: string | null
          updated_at?: string
          username?: string | null
          vac_status?: string | null
          venmo_id?: string | null
          website?: string | null
          wechat_id?: string | null
          zelle_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profile_primary_lang_fkey"
            columns: ["primary_lang"]
            isOneToOne: false
            referencedRelation: "lang_code"
            referencedColumns: ["id"]
          },
        ]
      }
      profile_feature_access: {
        Row: {
          auto_feature_access_brand_id: string
          auto_feature_access_target_group_id: string | null
          created_at: string
          feature_access_id: string
          feature_access_settings: Json
          id: string
          profile_id: string
          source_group_member_id: string | null
          source_order_product_id: string | null
          source_owner_of_brand_id: string | null
          updated_at: string
        }
        Insert: {
          auto_feature_access_brand_id: string
          auto_feature_access_target_group_id?: string | null
          created_at?: string
          feature_access_id: string
          feature_access_settings?: Json
          id?: string
          profile_id: string
          source_group_member_id?: string | null
          source_order_product_id?: string | null
          source_owner_of_brand_id?: string | null
          updated_at?: string
        }
        Update: {
          auto_feature_access_brand_id?: string
          auto_feature_access_target_group_id?: string | null
          created_at?: string
          feature_access_id?: string
          feature_access_settings?: Json
          id?: string
          profile_id?: string
          source_group_member_id?: string | null
          source_order_product_id?: string | null
          source_owner_of_brand_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "profile_feature_access_auto_feature_access_brand_id_fkey"
            columns: ["auto_feature_access_brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_feature_access_auto_feature_access_target_group_id_fkey"
            columns: ["auto_feature_access_target_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_feature_access_feature_access_id_fkey"
            columns: ["feature_access_id"]
            isOneToOne: false
            referencedRelation: "feature_access"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_feature_access_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_feature_access_source_group_member_id_fkey"
            columns: ["source_group_member_id"]
            isOneToOne: false
            referencedRelation: "group_member"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_feature_access_source_order_product_id_fkey"
            columns: ["source_order_product_id"]
            isOneToOne: false
            referencedRelation: "order_product"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_feature_access_source_owner_of_brand_id_fkey"
            columns: ["source_owner_of_brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
        ]
      }
      profile_wikipage: {
        Row: {
          created_at: string
          id: string
          kind: string
          profile_id: string
          updated_at: string
          wikipage_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          kind: string
          profile_id: string
          updated_at?: string
          wikipage_id: string
        }
        Update: {
          created_at?: string
          id?: string
          kind?: string
          profile_id?: string
          updated_at?: string
          wikipage_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "profile_wikipage_kind_fkey"
            columns: ["kind"]
            isOneToOne: false
            referencedRelation: "profile_wikipage_kind"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_wikipage_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_wikipage_wikipage_id_fkey"
            columns: ["wikipage_id"]
            isOneToOne: false
            referencedRelation: "wikipage"
            referencedColumns: ["id"]
          },
        ]
      }
      profile_wikipage_kind: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      program: {
        Row: {
          brand_id: string
          created_at: string | null
          creator_id: string | null
          event_count_max: number | null
          event_count_min: number
          event_duration_minute_max: number | null
          event_duration_minute_min: number
          event_kind: string | null
          event_kind_locked: boolean | null
          id: string
          product_kind: string
          product_kind_locked: boolean | null
          semantic_order: number
          title: Json
          updated_at: string | null
        }
        Insert: {
          brand_id: string
          created_at?: string | null
          creator_id?: string | null
          event_count_max?: number | null
          event_count_min?: number
          event_duration_minute_max?: number | null
          event_duration_minute_min?: number
          event_kind?: string | null
          event_kind_locked?: boolean | null
          id?: string
          product_kind: string
          product_kind_locked?: boolean | null
          semantic_order?: number
          title: Json
          updated_at?: string | null
        }
        Update: {
          brand_id?: string
          created_at?: string | null
          creator_id?: string | null
          event_count_max?: number | null
          event_count_min?: number
          event_duration_minute_max?: number | null
          event_duration_minute_min?: number
          event_kind?: string | null
          event_kind_locked?: boolean | null
          id?: string
          product_kind?: string
          product_kind_locked?: boolean | null
          semantic_order?: number
          title?: Json
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "program_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "program_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "program_event_kind_fkey"
            columns: ["event_kind"]
            isOneToOne: false
            referencedRelation: "event_kind"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "program_product_kind_fkey"
            columns: ["product_kind"]
            isOneToOne: false
            referencedRelation: "product_kind"
            referencedColumns: ["id"]
          },
        ]
      }
      program_metadata_wikipage: {
        Row: {
          allowed_wikipage_ids: string[] | null
          count_max: number | null
          count_min: number
          id: string
          is_required: boolean | null
          metadata_wikipage_relation: string
          program_id: string
        }
        Insert: {
          allowed_wikipage_ids?: string[] | null
          count_max?: number | null
          count_min?: number
          id?: string
          is_required?: boolean | null
          metadata_wikipage_relation: string
          program_id: string
        }
        Update: {
          allowed_wikipage_ids?: string[] | null
          count_max?: number | null
          count_min?: number
          id?: string
          is_required?: boolean | null
          metadata_wikipage_relation?: string
          program_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "program_metadata_wikipage_metadata_wikipage_relation_fkey"
            columns: ["metadata_wikipage_relation"]
            isOneToOne: false
            referencedRelation: "metadata_wikipage_relation"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "program_metadata_wikipage_program_id_fkey"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "program"
            referencedColumns: ["id"]
          },
        ]
      }
      program_price: {
        Row: {
          cost_unit_fixed: number | null
          cost_unit_locked: boolean | null
          cost_unit_max: number | null
          cost_unit_min: number
          cost_unit_per_minute: number | null
          id: string
          is_required: boolean | null
          order_requirement_id: string | null
          order_requirement_passcode: string | null
          order_requirement_passcode_locked: boolean | null
          price_id: string
          program_id: string
        }
        Insert: {
          cost_unit_fixed?: number | null
          cost_unit_locked?: boolean | null
          cost_unit_max?: number | null
          cost_unit_min?: number
          cost_unit_per_minute?: number | null
          id?: string
          is_required?: boolean | null
          order_requirement_id?: string | null
          order_requirement_passcode?: string | null
          order_requirement_passcode_locked?: boolean | null
          price_id: string
          program_id: string
        }
        Update: {
          cost_unit_fixed?: number | null
          cost_unit_locked?: boolean | null
          cost_unit_max?: number | null
          cost_unit_min?: number
          cost_unit_per_minute?: number | null
          id?: string
          is_required?: boolean | null
          order_requirement_id?: string | null
          order_requirement_passcode?: string | null
          order_requirement_passcode_locked?: boolean | null
          price_id?: string
          program_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "program_price_order_requirement_id_fkey"
            columns: ["order_requirement_id"]
            isOneToOne: false
            referencedRelation: "order_requirement"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "program_price_price_id_fkey"
            columns: ["price_id"]
            isOneToOne: false
            referencedRelation: "price"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "program_price_program_id_fkey"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "program"
            referencedColumns: ["id"]
          },
        ]
      }
      publishing_state: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      review_status: {
        Row: {
          created_at: string
          id: string
          title: Json
        }
        Insert: {
          created_at?: string
          id: string
          title: Json
        }
        Update: {
          created_at?: string
          id?: string
          title?: Json
        }
        Relationships: []
      }
      space: {
        Row: {
          brand_id: string
          capacity: number | null
          concurrent_event_limit: number
          created_at: string
          id: string
          landmark_id: string
          master_space_id: string | null
          name_full: Json
          name_short: Json | null
          updated_at: string
        }
        Insert: {
          brand_id: string
          capacity?: number | null
          concurrent_event_limit?: number
          created_at?: string
          id?: string
          landmark_id: string
          master_space_id?: string | null
          name_full: Json
          name_short?: Json | null
          updated_at?: string
        }
        Update: {
          brand_id?: string
          capacity?: number | null
          concurrent_event_limit?: number
          created_at?: string
          id?: string
          landmark_id?: string
          master_space_id?: string | null
          name_full?: Json
          name_short?: Json | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "space_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "space_landmark_id_fkey"
            columns: ["landmark_id"]
            isOneToOne: false
            referencedRelation: "landmark"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "space_master_space_id_fkey"
            columns: ["master_space_id"]
            isOneToOne: false
            referencedRelation: "space"
            referencedColumns: ["id"]
          },
        ]
      }
      tag: {
        Row: {
          airtable_id: string | null
          belongs_to_brand_id: string | null
          belongs_to_group_id: string | null
          belongs_to_user_id: string | null
          created_at: string
          creator_id: string
          desc: Json | null
          icon: string | null
          id: string
          title: Json
          updated_at: string
        }
        Insert: {
          airtable_id?: string | null
          belongs_to_brand_id?: string | null
          belongs_to_group_id?: string | null
          belongs_to_user_id?: string | null
          created_at?: string
          creator_id: string
          desc?: Json | null
          icon?: string | null
          id?: string
          title: Json
          updated_at?: string
        }
        Update: {
          airtable_id?: string | null
          belongs_to_brand_id?: string | null
          belongs_to_group_id?: string | null
          belongs_to_user_id?: string | null
          created_at?: string
          creator_id?: string
          desc?: Json | null
          icon?: string | null
          id?: string
          title?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "tag_belongs_to_brand_id_fkey"
            columns: ["belongs_to_brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tag_belongs_to_group_id_fkey"
            columns: ["belongs_to_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
        ]
      }
      tag_relation: {
        Row: {
          created_at: string
          creator_id: string
          id: string
          on_brand_id: string | null
          on_event_id: string | null
          on_group_id: string | null
          on_landmark_id: string | null
          on_product_id: string | null
          on_user_id: string | null
          tag_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          creator_id: string
          id?: string
          on_brand_id?: string | null
          on_event_id?: string | null
          on_group_id?: string | null
          on_landmark_id?: string | null
          on_product_id?: string | null
          on_user_id?: string | null
          tag_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          creator_id?: string
          id?: string
          on_brand_id?: string | null
          on_event_id?: string | null
          on_group_id?: string | null
          on_landmark_id?: string | null
          on_product_id?: string | null
          on_user_id?: string | null
          tag_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "tag_relation_on_brand_id_fkey"
            columns: ["on_brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tag_relation_on_event_id_fkey"
            columns: ["on_event_id"]
            isOneToOne: false
            referencedRelation: "event"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tag_relation_on_group_id_fkey"
            columns: ["on_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tag_relation_on_landmark_id_fkey"
            columns: ["on_landmark_id"]
            isOneToOne: false
            referencedRelation: "landmark"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tag_relation_on_product_id_fkey"
            columns: ["on_product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tag_relation_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "tag"
            referencedColumns: ["id"]
          },
        ]
      }
      term: {
        Row: {
          brand_id: string
          created_at: string
          creator_id: string
          id: string
          legal_jurisdiction: string[]
          require_signature_full: boolean
          require_signature_initials: boolean
          term_kind_id: string
          title: Json
          updated_at: string
        }
        Insert: {
          brand_id: string
          created_at?: string
          creator_id: string
          id?: string
          legal_jurisdiction?: string[]
          require_signature_full?: boolean
          require_signature_initials?: boolean
          term_kind_id: string
          title: Json
          updated_at?: string
        }
        Update: {
          brand_id?: string
          created_at?: string
          creator_id?: string
          id?: string
          legal_jurisdiction?: string[]
          require_signature_full?: boolean
          require_signature_initials?: boolean
          term_kind_id?: string
          title?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "term_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "term_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "term_term_kind_id_fkey"
            columns: ["term_kind_id"]
            isOneToOne: false
            referencedRelation: "term_kind"
            referencedColumns: ["id"]
          },
        ]
      }
      term_agreement: {
        Row: {
          agreed_at: string
          browser_fingerprint: string | null
          consumer_email: string | null
          consumer_full_name: string | null
          consumer_profile_id: string
          id: string
          ip_address: unknown
          is_current: boolean
          revocation_reason: string | null
          revoked_at: string | null
          revoked_by: string | null
          signature_full: string | null
          signature_initials: string | null
          signer_email: string | null
          signer_full_name: string | null
          signer_profile_id: string
          source_product_id: string | null
          term_group_item_id: string | null
          term_version_id: string
          user_agent: string | null
        }
        Insert: {
          agreed_at?: string
          browser_fingerprint?: string | null
          consumer_email?: string | null
          consumer_full_name?: string | null
          consumer_profile_id: string
          id?: string
          ip_address: unknown
          is_current?: boolean
          revocation_reason?: string | null
          revoked_at?: string | null
          revoked_by?: string | null
          signature_full?: string | null
          signature_initials?: string | null
          signer_email?: string | null
          signer_full_name?: string | null
          signer_profile_id: string
          source_product_id?: string | null
          term_group_item_id?: string | null
          term_version_id: string
          user_agent?: string | null
        }
        Update: {
          agreed_at?: string
          browser_fingerprint?: string | null
          consumer_email?: string | null
          consumer_full_name?: string | null
          consumer_profile_id?: string
          id?: string
          ip_address?: unknown
          is_current?: boolean
          revocation_reason?: string | null
          revoked_at?: string | null
          revoked_by?: string | null
          signature_full?: string | null
          signature_initials?: string | null
          signer_email?: string | null
          signer_full_name?: string | null
          signer_profile_id?: string
          source_product_id?: string | null
          term_group_item_id?: string | null
          term_version_id?: string
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "term_agreement_consumer_profile_id_fkey"
            columns: ["consumer_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "term_agreement_revoked_by_fkey"
            columns: ["revoked_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "term_agreement_signer_profile_id_fkey"
            columns: ["signer_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "term_agreement_source_product_id_fkey"
            columns: ["source_product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "term_agreement_term_group_item_id_fkey"
            columns: ["term_group_item_id"]
            isOneToOne: false
            referencedRelation: "term_group_item"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "term_agreement_term_version_id_fkey"
            columns: ["term_version_id"]
            isOneToOne: false
            referencedRelation: "term_version"
            referencedColumns: ["id"]
          },
        ]
      }
      term_group: {
        Row: {
          brand_id: string
          created_at: string
          id: string
          title: Json
          updated_at: string
        }
        Insert: {
          brand_id: string
          created_at?: string
          id?: string
          title: Json
          updated_at?: string
        }
        Update: {
          brand_id?: string
          created_at?: string
          id?: string
          title?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "term_group_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
        ]
      }
      term_group_item: {
        Row: {
          created_at: string
          id: string
          is_required: boolean
          semantic_order: number
          term_group_id: string
          term_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_required?: boolean
          semantic_order?: number
          term_group_id: string
          term_id: string
        }
        Update: {
          created_at?: string
          id?: string
          is_required?: boolean
          semantic_order?: number
          term_group_id?: string
          term_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "term_group_item_term_group_id_fkey"
            columns: ["term_group_id"]
            isOneToOne: false
            referencedRelation: "term_group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "term_group_item_term_id_fkey"
            columns: ["term_id"]
            isOneToOne: false
            referencedRelation: "term"
            referencedColumns: ["id"]
          },
        ]
      }
      term_kind: {
        Row: {
          created_at: string
          group_id: string
          icon: string | null
          id: string
          semantic_order: number
          subtitle: Json
          title: Json
        }
        Insert: {
          created_at?: string
          group_id: string
          icon?: string | null
          id: string
          semantic_order?: number
          subtitle: Json
          title: Json
        }
        Update: {
          created_at?: string
          group_id?: string
          icon?: string | null
          id?: string
          semantic_order?: number
          subtitle?: Json
          title?: Json
        }
        Relationships: []
      }
      term_version: {
        Row: {
          change_summary: Json | null
          content: Json
          created_at: string
          creator_id: string
          hash: string
          id: string
          ip_address_created: unknown | null
          is_current: boolean
          published_at: string | null
          term_id: string
          version_date: string
          version_number: string
        }
        Insert: {
          change_summary?: Json | null
          content: Json
          created_at?: string
          creator_id: string
          hash: string
          id?: string
          ip_address_created?: unknown | null
          is_current?: boolean
          published_at?: string | null
          term_id: string
          version_date?: string
          version_number: string
        }
        Update: {
          change_summary?: Json | null
          content?: Json
          created_at?: string
          creator_id?: string
          hash?: string
          id?: string
          ip_address_created?: unknown | null
          is_current?: boolean
          published_at?: string | null
          term_id?: string
          version_date?: string
          version_number?: string
        }
        Relationships: [
          {
            foreignKeyName: "term_version_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "term_version_term_id_fkey"
            columns: ["term_id"]
            isOneToOne: false
            referencedRelation: "term"
            referencedColumns: ["id"]
          },
        ]
      }
      time_repeat_interval: {
        Row: {
          id: string
          name: Json
        }
        Insert: {
          id: string
          name: Json
        }
        Update: {
          id?: string
          name?: Json
        }
        Relationships: []
      }
      time_slot: {
        Row: {
          auto_end_at: string
          created_at: string
          duration_minute: number
          id: string
          space_id: string
          start_at: string
          time_slot_rule_id: string
          updated_at: string
        }
        Insert: {
          auto_end_at?: string
          created_at?: string
          duration_minute: number
          id?: string
          space_id: string
          start_at: string
          time_slot_rule_id: string
          updated_at?: string
        }
        Update: {
          auto_end_at?: string
          created_at?: string
          duration_minute?: number
          id?: string
          space_id?: string
          start_at?: string
          time_slot_rule_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "time_slot_rule_id_fkey"
            columns: ["time_slot_rule_id"]
            isOneToOne: false
            referencedRelation: "time_slot_rule"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slot_space_id_fkey"
            columns: ["space_id"]
            isOneToOne: false
            referencedRelation: "space"
            referencedColumns: ["id"]
          },
        ]
      }
      time_slot_rule: {
        Row: {
          auto_end_date: string
          auto_end_time: string | null
          auto_space_time_zone: string
          available_to_group_id: string | null
          available_to_profile_id: string | null
          created_at: string
          creator_id: string
          duration_minute: number
          excluded_dates: string[]
          id: string
          repeat_count: number | null
          repeat_every: number | null
          repeat_interval: string | null
          repeat_on_days: number[]
          repeat_until: string | null
          space_id: string
          start_date: string
          start_time: string
          updated_at: string
        }
        Insert: {
          auto_end_date?: string
          auto_end_time?: string | null
          auto_space_time_zone: string
          available_to_group_id?: string | null
          available_to_profile_id?: string | null
          created_at?: string
          creator_id: string
          duration_minute: number
          excluded_dates?: string[]
          id?: string
          repeat_count?: number | null
          repeat_every?: number | null
          repeat_interval?: string | null
          repeat_on_days?: number[]
          repeat_until?: string | null
          space_id: string
          start_date: string
          start_time: string
          updated_at?: string
        }
        Update: {
          auto_end_date?: string
          auto_end_time?: string | null
          auto_space_time_zone?: string
          available_to_group_id?: string | null
          available_to_profile_id?: string | null
          created_at?: string
          creator_id?: string
          duration_minute?: number
          excluded_dates?: string[]
          id?: string
          repeat_count?: number | null
          repeat_every?: number | null
          repeat_interval?: string | null
          repeat_on_days?: number[]
          repeat_until?: string | null
          space_id?: string
          start_date?: string
          start_time?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "time_slot_rule_available_to_group_fkey"
            columns: ["available_to_group_id"]
            isOneToOne: false
            referencedRelation: "group"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slot_rule_available_to_profile_fkey"
            columns: ["available_to_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slot_rule_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slot_rule_repeat_interval_fkey"
            columns: ["repeat_interval"]
            isOneToOne: false
            referencedRelation: "time_repeat_interval"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slot_rule_space_id_fkey"
            columns: ["space_id"]
            isOneToOne: false
            referencedRelation: "space"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_slot_rule_time_zone_fkey"
            columns: ["auto_space_time_zone"]
            isOneToOne: false
            referencedRelation: "time_zone"
            referencedColumns: ["name"]
          },
        ]
      }
      time_zone: {
        Row: {
          abbrev: string
          is_dst: boolean
          name: string
          utc_offset: unknown
        }
        Insert: {
          abbrev: string
          is_dst: boolean
          name: string
          utc_offset: unknown
        }
        Update: {
          abbrev?: string
          is_dst?: boolean
          name?: string
          utc_offset?: unknown
        }
        Relationships: []
      }
      track: {
        Row: {
          airtable_id: string | null
          archived_at: string | null
          auto_composed_title: Json
          cover: string | null
          created_at: string
          creator_id: string
          func_title_en_first: string | null
          genre: string | null
          id: string
          kind: string
          nid: number | null
          title: Json
          updated_at: string
          web_page_url: string | null
        }
        Insert: {
          airtable_id?: string | null
          archived_at?: string | null
          auto_composed_title?: Json
          cover?: string | null
          created_at?: string
          creator_id: string
          func_title_en_first?: string | null
          genre?: string | null
          id?: string
          kind: string
          nid?: number | null
          title: Json
          updated_at?: string
          web_page_url?: string | null
        }
        Update: {
          airtable_id?: string | null
          archived_at?: string | null
          auto_composed_title?: Json
          cover?: string | null
          created_at?: string
          creator_id?: string
          func_title_en_first?: string | null
          genre?: string | null
          id?: string
          kind?: string
          nid?: number | null
          title?: Json
          updated_at?: string
          web_page_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "track_genre_fkey"
            columns: ["genre"]
            isOneToOne: false
            referencedRelation: "track_genre"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "track_kind_fkey"
            columns: ["kind"]
            isOneToOne: false
            referencedRelation: "track_kind"
            referencedColumns: ["id"]
          },
        ]
      }
      track_genre: {
        Row: {
          created_at: string
          func_name_en_first: string | null
          id: string
          master_genre_id: string | null
          name: Json
          updated_at: string
        }
        Insert: {
          created_at?: string
          func_name_en_first?: string | null
          id: string
          master_genre_id?: string | null
          name: Json
          updated_at?: string
        }
        Update: {
          created_at?: string
          func_name_en_first?: string | null
          id?: string
          master_genre_id?: string | null
          name?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "track_genre_master_genre_id_fkey"
            columns: ["master_genre_id"]
            isOneToOne: false
            referencedRelation: "track_genre"
            referencedColumns: ["id"]
          },
        ]
      }
      track_hashtag: {
        Row: {
          created_at: string
          hashtag_id: string
          id: string
          track_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          hashtag_id: string
          id?: string
          track_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          hashtag_id?: string
          id?: string
          track_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "track_hashtag_hashtag_id_fkey"
            columns: ["hashtag_id"]
            isOneToOne: false
            referencedRelation: "hashtag"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "track_hashtag_track_id_fkey"
            columns: ["track_id"]
            isOneToOne: false
            referencedRelation: "track"
            referencedColumns: ["id"]
          },
        ]
      }
      track_kind: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      track_role: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      track_wikipage: {
        Row: {
          created_at: string
          id: string
          role: string
          track_id: string
          updated_at: string
          wikipage_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role: string
          track_id: string
          updated_at?: string
          wikipage_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: string
          track_id?: string
          updated_at?: string
          wikipage_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "track_wikipage_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "track_role"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "track_wikipage_track_id_fkey"
            columns: ["track_id"]
            isOneToOne: false
            referencedRelation: "track"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "track_wikipage_wikipage_id_fkey"
            columns: ["wikipage_id"]
            isOneToOne: false
            referencedRelation: "wikipage"
            referencedColumns: ["id"]
          },
        ]
      }
      uniform_type: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      user_invitation: {
        Row: {
          accepted_at: string | null
          closed_at: string | null
          created_at: string
          end_at: string
          id: string
          invitee_email: string
          invitee_id: string | null
          invitee_phone: string
          inviter_id: string
          nid: number
          start_at: string
          state: string
          to_product_id: string | null
          updated_at: string
        }
        Insert: {
          accepted_at?: string | null
          closed_at?: string | null
          created_at?: string
          end_at: string
          id?: string
          invitee_email: string
          invitee_id?: string | null
          invitee_phone: string
          inviter_id: string
          nid?: number
          start_at?: string
          state: string
          to_product_id?: string | null
          updated_at?: string
        }
        Update: {
          accepted_at?: string | null
          closed_at?: string | null
          created_at?: string
          end_at?: string
          id?: string
          invitee_email?: string
          invitee_id?: string | null
          invitee_phone?: string
          inviter_id?: string
          nid?: number
          start_at?: string
          state?: string
          to_product_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_invitation_state_fkey"
            columns: ["state"]
            isOneToOne: false
            referencedRelation: "user_invitation_state"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_invitation_to_product_id_fkey"
            columns: ["to_product_id"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
        ]
      }
      user_invitation_state: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
      user_managed_profile: {
        Row: {
          created_at: string
          id: string
          managed_profile_id: string
          relation: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          managed_profile_id: string
          relation: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          managed_profile_id?: string
          relation?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_managed_profile_managed_profile_id_fkey"
            columns: ["managed_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      wikipage: {
        Row: {
          airtable_id: string | null
          avatar: Json | null
          bilibili_id: string | null
          brand_id: string | null
          brief: Json | null
          created_at: string
          creator_id: string
          decorating_profile_id: string | null
          desc: Json | null
          domain: string[]
          facebook_id: string | null
          func_title_en_first: string | null
          icon: string | null
          id: string
          instagram_id: string | null
          kind: string
          nid: number | null
          purchase_limit_per_consumer: number | null
          red_id: string | null
          semantic_id: string | null
          semantic_order: number | null
          title: Json | null
          twitter_id: string | null
          updated_at: string
        }
        Insert: {
          airtable_id?: string | null
          avatar?: Json | null
          bilibili_id?: string | null
          brand_id?: string | null
          brief?: Json | null
          created_at?: string
          creator_id: string
          decorating_profile_id?: string | null
          desc?: Json | null
          domain: string[]
          facebook_id?: string | null
          func_title_en_first?: string | null
          icon?: string | null
          id?: string
          instagram_id?: string | null
          kind: string
          nid?: number | null
          purchase_limit_per_consumer?: number | null
          red_id?: string | null
          semantic_id?: string | null
          semantic_order?: number | null
          title?: Json | null
          twitter_id?: string | null
          updated_at?: string
        }
        Update: {
          airtable_id?: string | null
          avatar?: Json | null
          bilibili_id?: string | null
          brand_id?: string | null
          brief?: Json | null
          created_at?: string
          creator_id?: string
          decorating_profile_id?: string | null
          desc?: Json | null
          domain?: string[]
          facebook_id?: string | null
          func_title_en_first?: string | null
          icon?: string | null
          id?: string
          instagram_id?: string | null
          kind?: string
          nid?: number | null
          purchase_limit_per_consumer?: number | null
          red_id?: string | null
          semantic_id?: string | null
          semantic_order?: number | null
          title?: Json | null
          twitter_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "wikipage_brand_id_fkey"
            columns: ["brand_id"]
            isOneToOne: false
            referencedRelation: "brand"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "wikipage_decorating_profile_id_fkey"
            columns: ["decorating_profile_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "wikipage_kind_fkey"
            columns: ["kind"]
            isOneToOne: false
            referencedRelation: "wikipage_kind"
            referencedColumns: ["id"]
          },
        ]
      }
      wikipage_hashtag: {
        Row: {
          created_at: string
          hashtag_id: string
          id: string
          updated_at: string
          wikipage_id: string
        }
        Insert: {
          created_at?: string
          hashtag_id: string
          id?: string
          updated_at?: string
          wikipage_id: string
        }
        Update: {
          created_at?: string
          hashtag_id?: string
          id?: string
          updated_at?: string
          wikipage_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "wikipage_hashtag_hashtag_id_fkey"
            columns: ["hashtag_id"]
            isOneToOne: false
            referencedRelation: "hashtag"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "wikipage_hashtag_wikipage_id_fkey"
            columns: ["wikipage_id"]
            isOneToOne: false
            referencedRelation: "wikipage"
            referencedColumns: ["id"]
          },
        ]
      }
      wikipage_kind: {
        Row: {
          id: string
        }
        Insert: {
          id: string
        }
        Update: {
          id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_any_classpass: {
        Args:
          | {
              user_email: string
              points_added?: number
              expiration_in_days?: number
              added_notes?: string
              passtype?: string
              price_option_type?: string
            }
          | {
              user_email: string
              points_added?: number
              expiration_in_days?: number
              passtype?: string
              added_notes?: string
            }
        Returns: string
      }
      add_classpass: {
        Args: {
          user_email: string
          points_added?: number
          expiration_in_days?: number
          passtype?: string
          added_notes?: string
        }
        Returns: string
      }
      add_junior_classpass: {
        Args: {
          user_email: string
          points_added?: number
          expiration_in_days?: number
          added_notes?: string
          class_pass_classification?: string
          price_option_type?: string
        }
        Returns: string
      }
      add_minutes_to_time: {
        Args: { t: string; minutes: number }
        Returns: string
      }
      add_minutes_to_timestamp: {
        Args: { ts: string; minutes: number }
        Returns: string
      }
      add_new_classpass: {
        Args: {
          user_email: string
          points_added?: number
          expiration_in_days?: number
          added_notes?: string
          class_pass_classification?: string
          price_option_type?: string
        }
        Returns: string
      }
      adjust_classpass_expiration: {
        Args:
          | {
              user_email: string
              points_added?: number
              expiration_in_days?: number
              added_notes?: string
              class_pass_classification?: string
              price_option_type?: string
            }
          | {
              user_email: string
              points_added?: number
              expiration_in_days?: number
              added_notes?: string
              class_pass_classification?: string
              price_option_type?: string
            }
        Returns: string
      }
      admin_cancel_order_product_on_product: {
        Args:
          | {
              input_product_id: string
              input_add_consumers_to_product_id?: string
              input_hide_product_events?: boolean
            }
          | {
              input_product_id: string
              input_add_to_product_id?: string
              input_hide_product_events?: boolean
              input_perform_update?: boolean
            }
        Returns: Json
      }
      calculate_auto_units_and_money: {
        Args: { order_price_id: string }
        Returns: undefined
      }
      can_afford_product: {
        Args: { input_user_id: string; input_product_id: string }
        Returns: {
          order_price_id: string
          cost_order_price_units: number
        }[]
      }
      cancel_order: {
        Args: { input_order_id: string }
        Returns: Json
      }
      cancel_order_product: {
        Args: {
          input_order_product_id: string
          input_perform_update: boolean
          input_admin_override?: boolean
        }
        Returns: Json
      }
      check_remaining_purchase_limit: {
        Args: { input_consumer_profile_id: string; input_product_id: string }
        Returns: number
      }
      checkin_student: {
        Args: { user_email: string; class_id: string; role?: string }
        Returns: string
      }
      client_check_term_compliance: {
        Args: { input_consumer_profile_id: string; input_term_group_id: string }
        Returns: Database["public"]["CompositeTypes"]["term_compliance_result"]
      }
      client_delete_offering: {
        Args: {
          input_creation_request_id: string
          input_perform_action: boolean
        }
        Returns: Database["public"]["CompositeTypes"]["delete_offering_result"]
      }
      client_delete_user: {
        Args: { input_password: string; input_is_soft?: boolean }
        Returns: undefined
      }
      client_find_options_to_order_products: {
        Args: {
          input_payer_id: string
          input: Database["public"]["CompositeTypes"]["find_options_input_items"]
        }
        Returns: Database["public"]["CompositeTypes"]["find_options_output_result"]
      }
      client_helper_check_order_requirement: {
        Args: {
          input_order_requirement_id: string
          input_consumer_profile_id?: string
          input_payer_id?: string
          input_passcode?: string
        }
        Returns: boolean
      }
      client_helper_find_consumer_order_prices: {
        Args: { input_payer_id: string; input_consumer_ids: string[] }
        Returns: {
          consumer_profile_id: string
          price_id: string
          order_price_id: string
          order_price_auto_units_available: number
          order_price_deal_expire_at: string
          deal_units: number
          auto_price_option_expire_at: string
          price_option_purchase_count: number
          consumer_profile_given_name: Json
          consumer_profile_family_name: Json
          price_title: Json
          price_color_semantic: string
          price_color_primary_hex: string
          brand_id: string
          brand_name_short: Json
          brand_name_full: Json
          price_option_title: Json
        }[]
      }
      client_helper_find_consumer_product_price_info: {
        Args: {
          input_payer_id: string
          input_items: Database["public"]["CompositeTypes"]["consumer_product_price_info_input_item"][]
        }
        Returns: {
          result_consumer_profile_id: string
          result_product_id: string
          result_consumer_profile_given_name: Json
          result_consumer_profile_family_name: Json
          result_product_price_id: string
          result_price_id: string
          result_product_price_cost_units: number
          result_item_product_count: number
          result_item_total_cost_units: number
          result_consumer_total_units_available: number
          result_payer_total_units_available: number
          result_product_price_order_requirement_id: string
          result_price_title: Json
          result_price_color_semantic: string
          result_price_state: string
          result_price_semantic_order: number
          result_price_product_classification: Json
          result_brand_id: string
          result_brand_name_short: Json
          result_brand_name_full: Json
          result_auto_cancel_at_far: string
          result_auto_cancel_at_far_return_units: number
          result_auto_cancel_at_near: string
          result_auto_cancel_at_near_return_units: number
        }[]
      }
      client_helper_find_price_option_combinations: {
        Args: {
          input_price_id: string
          input_required_units: number
          input_consumer_profile_id?: string
          input_payer_id?: string
          input_passcode?: string
        }
        Returns: {
          option_group: Database["public"]["CompositeTypes"]["price_option_combination_item"][]
        }[]
      }
      client_helper_find_price_option_details: {
        Args: { price_option_ids: string[] }
        Returns: {
          id: string
          state: string
          country_code: string
          currency_code: string
          title: Json
          money_int: number
          recurring_interval: string
          units: number
          valid_for_minute: number
          order_requirement_id: string
          order_requirement_title_short: Json
          original_money_int: number
          original_units: number
          original_valid_for_minute: number
        }[]
      }
      client_helper_new_order_product: {
        Args: {
          input_order_id: string
          input_payer_id: string
          input_consumer_profile_id: string
          input_product_price_count: number
          input_product_price_id: string
          input_selected_fill_up_option: Database["public"]["CompositeTypes"]["client_new_order_fill_up_option"][]
        }
        Returns: string
      }
      client_new_order: {
        Args: {
          payer_id: string
          consumer_profiles: Database["public"]["CompositeTypes"]["client_new_order_consumer_profile"][]
        }
        Returns: Database["public"]["CompositeTypes"]["client_new_order_result"]
      }
      client_new_order_price: {
        Args: {
          input_order_id: string
          input_payer_id: string
          input_consumer_profile_id: string
          input_price_option_id: string
          input_price_option_count?: number
          input_open_for_minute?: number
        }
        Returns: string
      }
      client_upsert_offering: {
        Args: {
          upsert_offering_input_all_event_product: Database["public"]["CompositeTypes"]["upsert_offering_input_event_product"][]
          upsert_offering_input_all_product: Database["public"]["CompositeTypes"]["upsert_offering_input_product"][]
          upsert_offering_input_all_event: Database["public"]["CompositeTypes"]["upsert_offering_input_event"][]
          upsert_offering_input_all_metadata: Database["public"]["CompositeTypes"]["upsert_offering_input_metadata"][]
          upsert_offering_input_change_request: Database["public"]["CompositeTypes"]["upsert_offering_input_change_request"]
          upsert_offering_input_perform_action: boolean
        }
        Returns: Database["public"]["CompositeTypes"]["upsert_offering_result"]
      }
      client_upsert_product: {
        Args: {
          input: Database["public"]["CompositeTypes"]["upsert_product_input_product"]
        }
        Returns: string
      }
      compose_auto_final_subtitle: {
        Args:
          | { event_id: string }
          | { var_event_id: string; input_auto_final_subtitle: Json }
        Returns: Json
      }
      delete_group_member: {
        Args: { input_group_id: string; input_user_id: string }
        Returns: Json
      }
      find_ways_to_buy_product: {
        Args: {
          input_consumer_profile_id: string
          input_product_id: string
          input_product_count?: number
          input_order_prices_preferred?: string[]
        }
        Returns: Json
      }
      gbt_bit_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bool_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bool_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bpchar_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bytea_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_cash_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_cash_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_date_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_date_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_enum_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_enum_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float4_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float4_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float8_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float8_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_inet_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int2_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int2_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int4_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int4_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int8_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int8_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_intv_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_intv_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_intv_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad8_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad8_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_numeric_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_oid_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_oid_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_text_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_time_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_time_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_timetz_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_ts_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_ts_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_tstz_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_uuid_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_uuid_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_var_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_var_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey_var_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey_var_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey16_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey16_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey2_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey2_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey32_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey32_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey4_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey4_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey8_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey8_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      generate_chat_token: {
        Args: { input_supabase_token: string }
        Returns: string
      }
      generate_nid: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      generate_ulid: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_consumer_id: {
        Args:
          | {
              consumer_id: string
              points_added?: number
              class_pass_type?: string
              unit_option_type?: string
            }
          | {
              user_email: string
              points_added?: number
              expiration_in_days?: number
              added_notes?: string
              class_pass_classification?: string
              price_option_type?: string
            }
        Returns: string
      }
      get_dow: {
        Args: { ts: string } | { ts: string }
        Returns: number
      }
      get_first_nonempty_localized_string: {
        Args: { localized_jsonb: Json; primary_lang_code: string }
        Returns: string
      }
      helper_profile_feature_access_upsert: {
        Args: {
          p_profile_id: string
          p_feature_access_id: string
          p_auto_feature_access_brand_id: string
          p_auto_feature_access_target_group_id: string
          p_feature_access_settings: Json
          p_source_owner_of_brand_id?: string
          p_source_group_member_id?: string
          p_source_order_product_id?: string
        }
        Returns: undefined
      }
      is_bare_hostname: {
        Args: { domain: string }
        Returns: boolean
      }
      jsonb_has_nonempty_value: {
        Args: { jsonb_data: Json }
        Returns: boolean
      }
      new_event_member_for_check_in_out: {
        Args: {
          input_member_profile_id: string
          input_event_id: string
          input_role?: string
          input_is_check_in?: boolean
        }
        Returns: Json
      }
      new_order_price: {
        Args: {
          input_payer_id: string
          input_consumer_profile_id: string
          input_price_option_id: string
          input_product_id?: string
          input_product_count?: number
          input_price_option_count?: number
          input_order_id?: string
          input_shipping_address_id?: string
          input_open_for_minute?: number
        }
        Returns: string
      }
      new_order_price_payment: {
        Args: {
          input_payment_intent_id: string
          input_payment_source: string
          input_currency_code: string
          input_money_received_int: number
          input_order_id: string
        }
        Returns: Json
      }
      new_order_product: {
        Args: {
          input_consumer_profile_id: string
          input_product_id: string
          input_product_count?: number
          input_product_price_id?: string
          input_order_price_ids_preferred?: string[]
          input_order_id?: string
        }
        Returns: string
      }
      new_product_group_member: {
        Args: {
          input_product_id: string
          input_user_id: string
          input_group_kind: string
        }
        Returns: Json
      }
      order_price_calculate_auto_units_and_money: {
        Args: { input_order_price: Record<string, unknown> }
        Returns: Record<string, unknown>
      }
      order_price_consumption_cancel_at: {
        Args: { input_order_product_id: string }
        Returns: undefined
      }
      order_price_lookup_price_option: {
        Args: { input_order_price: Record<string, unknown> }
        Returns: Record<string, unknown>
      }
      order_product_calc_auto_units_owed: {
        Args: { input_order_product_id: string }
        Returns: undefined
      }
      order_product_lookup_spo_cost_units: {
        Args: { input_order_product: Record<string, unknown> }
        Returns: Record<string, unknown>
      }
      order_product_sync_event_member: {
        Args: {
          var_member_profile_id: string
          var_auto_product_price_product_id: string
          var_auto_units_owed: number
          var_canceled_at: string
          var_order_product_id: string
        }
        Returns: undefined
      }
      price_options_for_product: {
        Args: { input_consumer_profile_id: string; input_product_id: string }
        Returns: Json
      }
      process_all_order_products: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      product_event_times_update: {
        Args: { input_product_id: string }
        Returns: undefined
      }
      product_first_event_start_at: {
        Args: { input_product_id: string }
        Returns: undefined
      }
      product_price_cancel_at: {
        Args: { input_product_price_id: string }
        Returns: undefined
      }
      product_update_stock_available: {
        Args: { input_product_id: string }
        Returns: undefined
      }
      profile_calculate_unpaid_balance: {
        Args: { input_profile_id: string }
        Returns: number
      }
      round_to_step_unit: {
        Args: { input_step: number; input_number: number }
        Returns: number
      }
      time_slot_rule_compute_end_date: {
        Args: {
          input_start_date: string
          input_repeat_until: string
          input_repeat_interval: string
          input_repeat_count: number
          input_repeat_every: number
          input_repeat_on_days: number[]
          input_excluded_dates?: string[]
        }
        Returns: string
      }
      time_slot_rule_generate_slot_dates: {
        Args: {
          input_rule: Database["public"]["Tables"]["time_slot_rule"]["Row"]
        }
        Returns: string[]
      }
      undo_event_member_for_check_in_out: {
        Args: { input_event_member_id: string; input_is_check_in?: boolean }
        Returns: Json
      }
      update_auto_final_subtitle_core: {
        Args:
          | { input_metadata_id: string }
          | { input_record: Database["public"]["Tables"]["metadata"]["Row"] }
        Returns: Json
      }
      update_auto_final_title_core: {
        Args: { input_metadata_id: string; input_title?: Json }
        Returns: Json
      }
      update_event_product_auto_start_at: {
        Args: { input_event_id: string; input_start_at: string }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      client_helper_check_order_requirement_result: {
        is_satisfied: boolean | null
        message: string | null
      }
      client_new_order_consumer_profile: {
        consumer_profile_id: string | null
        product_count: number | null
        product_prices:
          | Database["public"]["CompositeTypes"]["client_new_order_product_price"][]
          | null
      }
      client_new_order_fill_up_option: {
        price_option_id: string | null
        quantity: number | null
      }
      client_new_order_input: {
        payer_id: string | null
        consumer_profiles:
          | Database["public"]["CompositeTypes"]["client_new_order_consumer_profile"][]
          | null
      }
      client_new_order_order_price: {
        order_price_id: string | null
        units_cost: number | null
      }
      client_new_order_product_price: {
        product_price_id: string | null
        fill_up_with_price_option_combinations:
          | Database["public"]["CompositeTypes"]["client_new_order_fill_up_option"][]
          | null
        order_requirement_passcode: string | null
      }
      client_new_order_result: {
        order_id: string | null
        order_product_ids: string[] | null
        order_price_ids: string[] | null
      }
      consumer_product_price_info_input_item: {
        consumer_profile_id: string | null
        product_id: string | null
        product_count: number | null
      }
      delete_offering_result: {
        creation_request_id: string | null
        success: boolean | null
        message: string | null
        actions:
          | Database["public"]["CompositeTypes"]["delete_offering_result_action_item"][]
          | null
      }
      delete_offering_result_action_item: {
        action: string | null
        entity_type: string | null
        entity_id: string | null
        details: string | null
      }
      find_options_input_items: {
        items:
          | Database["public"]["CompositeTypes"]["find_options_input_order_product_item"][]
          | null
      }
      find_options_input_order_product_item: {
        consumer_profile_id: string | null
        product_id: string | null
        product_count: number | null
        product_price_passcode: string | null
      }
      find_options_output_brand: {
        id: string | null
        name_short: Json | null
        name_full: Json | null
      }
      find_options_output_brand_record: {
        id: string | null
        name_short: Json | null
        name_full: Json | null
      }
      find_options_output_consumer_item: {
        consumer_profile:
          | Database["public"]["CompositeTypes"]["find_options_output_consumer_profile"]
          | null
        product_count: number | null
        product_prices:
          | Database["public"]["CompositeTypes"]["find_options_output_product_price_item"][]
          | null
      }
      find_options_output_consumer_profile: {
        id: string | null
        given_name: Json | null
        family_name: Json | null
      }
      find_options_output_consumer_profile_record: {
        id: string | null
        given_name: Json | null
        family_name: Json | null
      }
      find_options_output_order_price: {
        id: string | null
        units_available: number | null
        units_cost: number | null
        deal_units: number | null
        auto_price_option_expire_at: string | null
        price_option_purchase_count: number | null
        price_option_title: Json | null
        price_color_primary_semantic: string | null
        price_color_primary_hex: string | null
      }
      find_options_output_order_price_item: {
        order_price:
          | Database["public"]["CompositeTypes"]["find_options_output_order_price"]
          | null
      }
      find_options_output_order_price_record: {
        id: string | null
        units_available: number | null
        units_cost: number | null
        deal_units: number | null
        auto_price_option_expire_at: string | null
        price_option_purchase_count: number | null
      }
      find_options_output_price: {
        id: string | null
        title: Json | null
        color_semantic: string | null
        semantic_order: number | null
        product_classification: Json | null
        brand:
          | Database["public"]["CompositeTypes"]["find_options_output_brand"]
          | null
      }
      find_options_output_price_option: {
        id: string | null
        state: string | null
        country_code: string | null
        currency_code: string | null
        title: Json | null
        money_int: number | null
        recurring_interval: string | null
        units: number | null
        valid_for_minute: number | null
        order_requirement_id: string | null
        order_requirement_title_short: Json | null
        original_money_int: number | null
        original_units: number | null
        original_valid_for_minute: number | null
      }
      find_options_output_price_option_combination: {
        options:
          | Database["public"]["CompositeTypes"]["find_options_output_price_option_item"][]
          | null
      }
      find_options_output_price_option_item: {
        quantity: number | null
        total_units: number | null
        price_option:
          | Database["public"]["CompositeTypes"]["find_options_output_price_option"]
          | null
      }
      find_options_output_price_option_record: {
        id: string | null
        state: string | null
        price_id: string | null
        units: number | null
        money_int: number | null
        money_int_original: number | null
      }
      find_options_output_price_record: {
        id: string | null
        title: Json | null
        color_primary_semantic: string | null
        semantic_order: number | null
        product_classification: Json | null
        brand:
          | Database["public"]["CompositeTypes"]["find_options_output_brand_record"]
          | null
      }
      find_options_output_product_price: {
        id: string | null
        auto_cancel_at_far: string | null
        auto_cancel_at_far_return_units: number | null
        auto_cancel_at_near: string | null
        auto_cancel_at_near_return_units: number | null
        price:
          | Database["public"]["CompositeTypes"]["find_options_output_price"]
          | null
      }
      find_options_output_product_price_item: {
        index: number | null
        product_price:
          | Database["public"]["CompositeTypes"]["find_options_output_product_price"]
          | null
        order_prices:
          | Database["public"]["CompositeTypes"]["find_options_output_order_price_item"][]
          | null
        price_option_combinations:
          | Database["public"]["CompositeTypes"]["find_options_output_price_option_combination"][]
          | null
        consumer_total_units_cost: number | null
        consumer_total_units_available: number | null
        consumer_total_units_remaining: number | null
        payer_total_units_available: number | null
      }
      find_options_output_product_price_record: {
        id: string | null
        auto_cancel_at_far: string | null
        auto_cancel_at_far_return_units: number | null
        auto_cancel_at_near: string | null
        auto_cancel_at_near_return_units: number | null
        price:
          | Database["public"]["CompositeTypes"]["find_options_output_price_record"]
          | null
      }
      find_options_output_result: {
        payer_id: string | null
        consumer_items:
          | Database["public"]["CompositeTypes"]["find_options_output_consumer_item"][]
          | null
        debug_info: Json | null
      }
      payroll_event_formula_inputs: {
        payee_enrolled_at: string | null
        payee_title: string | null
        payee_level: string | null
        payee_event_role: string | null
        payee_event_role_count: number | null
        event_minutes: number | null
        event_start_at: string | null
        event_space_id: string | null
        event_consumer_total_cost: number | null
        event_consumer_registered: number | null
        event_consumer_checked_in: number | null
      }
      payroll_product_formula_inputs: {
        payee_enrolled_at: string | null
        payee_title: string | null
        payee_level: string | null
        product_id: string | null
        product_kind: string | null
        product_sale_count: number | null
        product_total_revenue: number | null
        product_cost: number | null
      }
      payroll_profile_formula_inputs: {
        payee_enrolled_at: string | null
        payee_title: string | null
        payee_level: string | null
        payee_total_event_count: number | null
        payee_total_student_count: number | null
        payee_total_product_sales: number | null
        payee_total_hours: number | null
      }
      price_option_combination_item: {
        price_option_id: string | null
        quantity: number | null
        price_option_total_units: number | null
        price_option_state: string | null
      }
      term_compliance_item: {
        term_id: string | null
        term_group_item_id: string | null
        term_title: Json | null
        term_kind_id: string | null
        term_kind_title: Json | null
        term_kind_subtitle: Json | null
        term_require_signature_full: boolean | null
        term_require_signature_initials: boolean | null
        last_agreed_version: string | null
        current_version: string | null
      }
      term_compliance_result: {
        required_not_agreed:
          | Database["public"]["CompositeTypes"]["term_compliance_item"][]
          | null
        required_outdated:
          | Database["public"]["CompositeTypes"]["term_compliance_item"][]
          | null
        optional_not_agreed:
          | Database["public"]["CompositeTypes"]["term_compliance_item"][]
          | null
        optional_outdated:
          | Database["public"]["CompositeTypes"]["term_compliance_item"][]
          | null
        required_all_agreed: boolean | null
        all_agreed: boolean | null
      }
      upsert_offering_input_change_request: {
        change_request_id: string | null
        change_request_actor_role_id: string | null
        change_request_actor_profile_id: string | null
        change_request_change_reason_id: string | null
        change_request_change_reason_message: string | null
        change_request_brand_id: string | null
        change_request_status: string | null
        change_request_form_data: Json | null
      }
      upsert_offering_input_event: {
        event_id: string | null
        event_kind: string | null
        event_start_at: string | null
        event_duration_minute: number | null
        event_space_id: string | null
        event_publishing_state: string | null
        event_metadata_id: string | null
      }
      upsert_offering_input_event_product: {
        event_product_id: string | null
        event_product_relation: string | null
        event_id: string | null
        product_id: string | null
      }
      upsert_offering_input_metadata: {
        metadata_id: string | null
        metadata_kind: string | null
        metadata_title: Json | null
        metadata_subtitle: Json | null
        metadata_desc: Json | null
        metadata_message: Json | null
        metadata_custom_attribute: Json | null
        metadata_promo_message: Json | null
        metadata_promo_image_url: string | null
        metadata_promo_video_url: string | null
        metadata_promo_webpage_url: string | null
        metadata_wikipages:
          | Database["public"]["CompositeTypes"]["upsert_offering_input_metadata_wikipage"][]
          | null
        metadata_tracks:
          | Database["public"]["CompositeTypes"]["upsert_offering_input_metadata_track"][]
          | null
      }
      upsert_offering_input_metadata_track: {
        metadata_track_id: string | null
        track_id: string | null
      }
      upsert_offering_input_metadata_wikipage: {
        metadata_wikipage_id: string | null
        metadata_wikipage_relation: string | null
        wikipage_id: string | null
      }
      upsert_offering_input_product: {
        product_id: string | null
        product_kind: string | null
        product_stock: number | null
        product_publishing_state: string | null
        product_open_to_buy_at: string | null
        product_close_to_buy_at: string | null
        product_prices:
          | Database["public"]["CompositeTypes"]["upsert_offering_input_product_price"][]
          | null
        product_metadata_id: string | null
      }
      upsert_offering_input_product_price: {
        product_price_id: string | null
        product_price_cost_units: number | null
        product_price_start_at: string | null
        product_price_end_at: string | null
        product_price_stock: number | null
        product_price_order_requirement_id: string | null
        product_price_reward_price_id: string | null
        product_price_reward_price_units: number | null
        product_price_price_id: string | null
      }
      upsert_offering_result: {
        request_id: string | null
        success: boolean | null
        message: string | null
        actions:
          | Database["public"]["CompositeTypes"]["upsert_offering_result_action_item"][]
          | null
      }
      upsert_offering_result_action_item: {
        action: string | null
        entity_type: string | null
        entity_id: string | null
        details: string | null
      }
      upsert_product_input_event_product: {
        event_product_id: string | null
        event_product_relation: string | null
        event_id: string | null
        event_kind: string | null
        event_start_at: string | null
        event_duration_minute: number | null
        event_space_id: string | null
      }
      upsert_product_input_metadata: {
        metadata_id: string | null
        metadata_kind: string | null
        metadata_title: Json | null
        metadata_subtitle: Json | null
        metadata_desc: Json | null
        metadata_message: Json | null
        metadata_custom_attribute: Json | null
        metadata_promo_message: Json | null
        metadata_promo_image_url: string | null
        metadata_promo_video_url: string | null
        metadata_promo_webpage_url: string | null
      }
      upsert_product_input_metadata_track: {
        metadata_track_id: string | null
        track_id: string | null
      }
      upsert_product_input_metadata_wikipage: {
        metadata_wikipage_relation: string | null
        metadata_wikipage_id: string | null
        wikipage_id: string | null
      }
      upsert_product_input_product: {
        product_id: string | null
        product_brand_id: string | null
        product_kind: string | null
        product_stock: number | null
        product_publishing_state: string | null
        product_open_to_buy_at: string | null
        product_close_to_buy_at: string | null
        product_creator_id: string | null
        event_products:
          | Database["public"]["CompositeTypes"]["upsert_product_input_event_product"][]
          | null
        product_prices:
          | Database["public"]["CompositeTypes"]["upsert_product_input_product_price"][]
          | null
        metadata_wikipages:
          | Database["public"]["CompositeTypes"]["upsert_product_input_metadata_wikipage"][]
          | null
        metadata_tracks:
          | Database["public"]["CompositeTypes"]["upsert_product_input_metadata_track"][]
          | null
        metadata:
          | Database["public"]["CompositeTypes"]["upsert_product_input_metadata"]
          | null
      }
      upsert_product_input_product_price: {
        product_price_id: string | null
        product_price_cost_units: number | null
        product_price_start_at: string | null
        product_price_end_at: string | null
        product_price_stock: number | null
        product_price_order_requirement_id: string | null
        product_price_reward_price_id: string | null
        product_price_reward_price_units: number | null
        product_price_price_id: string | null
      }
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
