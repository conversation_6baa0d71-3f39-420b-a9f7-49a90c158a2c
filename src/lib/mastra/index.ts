import { dev } from '$app/environment';
import { MastraClient, type ClientOptions, type GenerateParams, type StreamParams } from '@mastra/client-js';

/**
 * Base URL for the Mastra API
 * The default port for Mastra dev server is 4111
 */
export const MASTRA_API_BASE_URL = dev 
  ? 'http://localhost:4111'  // Use local development server in dev mode
  : 'http://localhost:4111'; // Replace with production URL when deployed

/**
 * Configuration options for the Mastra client
 */
const mastraOptions: ClientOptions = {
  baseUrl: MASTRA_API_BASE_URL,
  // Optional retry configuration
  retries: 3,
  backoffMs: 300,
  maxBackoffMs: 5000,
};

/**
 * Create an instance of the Mastra client
 * This client is used to interact with the Mastra API
 */
export const mastraClient = new MastraClient(mastraOptions);

/**
 * Helper function to get the customer service agent
 * This simplifies interacting with the agent in other components
 */
export function getCustomerServiceAgent() {
  return mastraClient.getAgent('customerServiceAgent');
}

/**
 * User authentication context to pass to <PERSON><PERSON>
 * This ensures the agent has the necessary user context for processing requests
 */
export interface UserAuthContext {
  userId: string;
  token: string;
  email?: string;
}

/**
 * Helper function to create authentication headers
 * Used to pass Supabase auth token and user ID to Mastra
 * 
 * @param authContext - User authentication context
 * @returns Object with headers to add to requests
 */
export function createAuthHeaders(authContext: UserAuthContext): Record<string, string> {
  const headers: Record<string, string> = {
    'Authorization': `Bearer ${authContext.token}`,
    'x-user-id': authContext.userId
  };
  
  // Add optional email for additional user context if available
  if (authContext.email) {
    headers['x-user-email'] = authContext.email;
  }
  
  return headers;
}

/**
 * Helper function to create generate parameters with authentication
 * 
 * @param message - User message to send to the agent
 * @param authContext - User authentication context
 * @returns Generate parameters with auth headers
 */
export function createGenerateParams(message: string, authContext: UserAuthContext): GenerateParams {
  return {
    messages: [{ role: 'user', content: message }],
    headers: createAuthHeaders(authContext)
  };
}

/**
 * Helper function to create stream parameters with authentication
 * 
 * @param message - User message to send to the agent
 * @param authContext - User authentication context
 * @returns Stream parameters with auth headers
 */
export function createStreamParams(message: string, authContext: UserAuthContext): StreamParams {
  return {
    messages: [{ role: 'user', content: message }],
    headers: createAuthHeaders(authContext)
  };
} 