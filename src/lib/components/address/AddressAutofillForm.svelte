<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { Control, Label, Field, FieldErrors } from 'formsnap';
	import { Input } from '$lib/components/ui/input';
	import type { SuperForm } from 'sveltekit-superforms';

	interface Props {
		form: SuperForm<any>;
		prefix?: string;
		readonly?: boolean;
	}

	let { form, prefix = 'address', readonly = false }: Props = $props();
	let autofillCollection: any;

	// Extract the form data for address fields
	const formData = $derived(form.form);

	onMount(async () => {
		// Dynamically import Mapbox Search JS Web
		const { autofill } = await import('@mapbox/search-js-web');

		// Initialize autofill with theme
		autofillCollection = autofill({
			accessToken:
				'pk.eyJ1IjoibGl3ZWloYW5hIiwiYSI6ImNtYmFnZGZwZjBhYmgyam42ejFoeGt2ZWoifQ.taj80o0XVRe-UFlJhDAnWg',
			options: {
				language: 'en',
				country: 'US'
			},
			theme: {
				variables: {
					colorBackground: 'hsl(var(--popover))',
					colorBackgroundActive: 'hsl(var(--accent))',
					colorBackgroundHover: 'hsl(var(--accent))',
					colorText: 'hsl(var(--popover-foreground))',
					colorSecondary: 'hsl(var(--muted-foreground))',
					fontFamily: 'inherit',
					border: '1px solid hsl(var(--border))',
					borderRadius: 'calc(var(--radius) - 2px)',
					boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
					unit: '1rem',
					padding: '0.5rem',
					minWidth: '200px'
				},
				cssText: `
					.mapbox-search-js-theme {
						background-color: hsl(var(--popover)) !important;
						border: 1px solid hsl(var(--border)) !important;
					}
					.MapboxAddressAutofill {
						background-color: hsl(var(--popover)) !important;
						color: hsl(var(--popover-foreground)) !important;
					}
					.MapboxAddressAutofill-suggestion {
						background-color: transparent !important;
						color: hsl(var(--popover-foreground)) !important;
						transition: background-color 0.15s ease;
					}
					.MapboxAddressAutofill-suggestion:hover,
					.MapboxAddressAutofill-suggestion[aria-selected="true"] {
						background-color: hsl(var(--accent)) !important;
						color: hsl(var(--accent-foreground)) !important;
					}
					.MapboxAddressAutofill-suggestion--active {
						background-color: hsl(var(--accent)) !important;
						color: hsl(var(--accent-foreground)) !important;
					}
					.MapboxAddressAutofill-suggestionTitle {
						color: inherit !important;
					}
					.MapboxAddressAutofill-suggestionSubtitle {
						color: hsl(var(--muted-foreground)) !important;
						opacity: 0.8;
					}
					.MapboxAddressAutofill-suggestion:hover .MapboxAddressAutofill-suggestionSubtitle,
					.MapboxAddressAutofill-suggestion--active .MapboxAddressAutofill-suggestionSubtitle {
						color: hsl(var(--accent-foreground)) !important;
						opacity: 0.9;
					}
					.MapboxAddressAutofill-attribution {
						background-color: hsl(var(--popover)) !important;
						color: hsl(var(--muted-foreground)) !important;
						border-top: 1px solid hsl(var(--border)) !important;
					}
					.MapboxAddressAutofill-noResults {
						background-color: hsl(var(--popover)) !important;
						color: hsl(var(--muted-foreground)) !important;
					}
				`
			}
		});

		// The autofill will automatically find form inputs with the proper autocomplete attributes
		// Listen for the retrieve event
		autofillCollection.addEventListener('retrieve', (event: any) => {
			const feature = event.detail.features[0];
			if (!feature?.properties) return;

			const props = feature.properties;

			// Update form data with the retrieved address
			if ($formData && $formData[prefix]) {
				$formData[prefix].street = props.address_line1 || '';
				$formData[prefix].sub_locality = props.address_line2 || null;
				$formData[prefix].city = props.address_level2 || '';
				$formData[prefix].state = props.address_level1 || '';
				$formData[prefix].sub_admin_area = props.address_level3 || null;
				$formData[prefix].postal_code = props.postcode || '';

				// Capitalize country
				$formData[prefix].country = (props.country || '').toUpperCase();

				// Capitalize country code
				const countryCode = props.country_code || props.country || '';
				$formData[prefix].country_code = countryCode.toUpperCase();

				// Set time zone based on country
				if (props.country_code === 'US') {
					$formData[prefix].time_zone = 'America/Los_Angeles';
				} else if (props.country_code === 'HK') {
					$formData[prefix].time_zone = 'Asia/Hong_Kong';
				} else {
					$formData[prefix].time_zone = 'UTC';
				}
			}
		});

		// Manually trigger update to attach to new elements
		autofillCollection.update();
	});

	onDestroy(() => {
		if (autofillCollection) {
			autofillCollection.remove();
		}
	});
</script>

<div class="mapbox-address-autofill-container space-y-4">
	<Field {form} name={`${prefix}.street`}>
		<Control>
			{#snippet children({ props })}
				<Label>Street Address</Label>
				<Input
					{...props}
					type="text"
					autocomplete="street-address"
					placeholder="Enter a street address"
					{readonly}
				/>
			{/snippet}
		</Control>
		<FieldErrors />
	</Field>

	<Field {form} name={`${prefix}.city`}>
		<Control>
			{#snippet children({ props })}
				<Label>City</Label>
				<Input type="text" autocomplete="address-level2" {readonly} {...props} />
			{/snippet}
		</Control>
		<FieldErrors />
	</Field>

	<Field {form} name={`${prefix}.state`}>
		<Control>
			{#snippet children({ props })}
				<Label>State/Province</Label>
				<Input type="text" autocomplete="address-level1" {readonly} {...props} />
			{/snippet}
		</Control>
		<FieldErrors />
	</Field>

	<Field {form} name={`${prefix}.postal_code`}>
		<Control>
			{#snippet children({ props })}
				<Label>Postal Code</Label>
				<Input type="text" autocomplete="postal-code" {readonly} {...props} />
			{/snippet}
		</Control>
		<FieldErrors />
	</Field>

	<Field {form} name={`${prefix}.country`}>
		<Control>
			{#snippet children({ props })}
				<Label>Country</Label>
				<Input type="text" autocomplete="country" {readonly} {...props} />
			{/snippet}
		</Control>
		<FieldErrors />
	</Field>

	<!-- Hidden fields -->
	<input
		type="hidden"
		name={`${prefix}.sub_locality`}
		value={$formData[prefix]?.sub_locality || ''}
	/>
	<input
		type="hidden"
		name={`${prefix}.sub_admin_area`}
		value={$formData[prefix]?.sub_admin_area || ''}
	/>
	<input
		type="hidden"
		name={`${prefix}.country_code`}
		value={$formData[prefix]?.country_code || ''}
	/>
	<input type="hidden" name={`${prefix}.time_zone`} value={$formData[prefix]?.time_zone || ''} />
</div>

<style>
	/* Container for proper positioning */
	.mapbox-address-autofill-container {
		position: relative;
	}
</style>
