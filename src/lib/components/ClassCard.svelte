<script lang="ts">
	import type { ClassInfo } from '$lib/types/class';
	import { MapPin, Send } from '@lucide/svelte';
	import StatusIndicator from './StatusIndicator.svelte';

	let { classInfo } = $props<{ classInfo: ClassInfo }>();
</script>

<div class="bg-white transition-all duration-200 hover:bg-gray-50/50">
	<div class="px-4 py-4 lg:px-6">
		<!-- Status Bar -->
		<div class="mb-3 flex items-center justify-between">
			<StatusIndicator status={classInfo.status} />
			<button class="-mr-1.5 p-1.5 text-[#8E8E93] transition-colors hover:text-[#636366]">
				<Send class="h-5 w-5" />
			</button>
		</div>

		<!-- Class Info -->
		<div class="flex gap-4">
			<div
				class="h-24 w-24 shrink-0 overflow-hidden rounded-sm"
				style="background-color: {classInfo.thumbnail}"
			></div>
			<div class="min-w-0 flex-1">
				<div class="flex items-center justify-between gap-3">
					<span class="rounded-sm bg-[#FF9500] px-2.5 py-0.5 text-base font-medium text-white">
						{classInfo.time}
					</span>
					<span class="whitespace-nowrap text-sm text-[#8E8E93]">{classInfo.duration}</span>
				</div>
				<h3 class="mt-2 truncate text-xl font-semibold">{classInfo.title}</h3>
				<p class="mt-0.5 line-clamp-1 text-sm text-[#8E8E93]">{classInfo.subtitle}</p>
				<div class="mt-2 flex items-center gap-4">
					<div class="flex items-center gap-2">
						<div
							class="flex h-6 w-6 items-center justify-center rounded-sm bg-primary/10 text-sm font-medium text-primary"
						>
							{classInfo.instructor[0].toUpperCase()}
						</div>
						<span class="text-sm">{classInfo.instructor}</span>
					</div>
					<div class="flex items-center gap-1.5 text-[#8E8E93]">
						<MapPin class="h-4 w-4" />
						<span class="text-sm">{classInfo.location}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
