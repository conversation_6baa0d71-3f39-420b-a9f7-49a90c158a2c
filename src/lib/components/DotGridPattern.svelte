<script lang="ts">
	import { generateUUID } from '$lib/utils';
	import { cn } from '$lib/utils';
	import { mode } from 'mode-watcher';

	interface Props {
		/**
		 * Width of each grid cell
		 */
		width?: number;
		/**
		 * Height of each grid cell
		 */
		height?: number;
		/**
		 * X offset of the pattern
		 */
		x?: number;
		/**
		 * Y offset of the pattern
		 */
		y?: number;
		/**
		 * Radius of each dot
		 */
		dotRadius?: number;
		/**
		 * Color of the dots
		 */
		dotColor?: string;
		/**
		 * Additional CSS classes for the SVG
		 */
		className?: string;
	}

	let {
		width = 20,
		height = 20,
		x = -1,
		y = -1,
		dotRadius = 1,
		dotColor,
		className = ''
	}: Props = $props();

	// Get current mode for color calculation
	const currentMode = $derived(mode.current);

	// Determine color based on mode if not provided
	const computedDotColor = $derived(
		dotColor || (currentMode === 'dark' ? 'rgba(255, 255, 255, 0.25)' : 'rgba(0, 0, 0, 0.5)')
	);

	// Generate a unique ID for the pattern to avoid conflicts
	const id = generateUUID();
</script>

<svg aria-hidden="true" class={cn('pointer-events-none absolute inset-0 h-full w-full', className)}>
	<defs>
		<pattern {id} {width} {height} patternUnits="userSpaceOnUse" {x} {y}>
			<circle cx={width / 2} cy={height / 2} r={dotRadius} fill={computedDotColor} />
		</pattern>
	</defs>
	<rect width="100%" height="100%" stroke-width="0" fill={`url(#${id})`} />
</svg>
