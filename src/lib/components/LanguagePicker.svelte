<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Button } from '$lib/components/ui/button';
	import { Languages } from '@lucide/svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';

	interface Language {
		code: string;
		label: string;
	}

	const languages: Language[] = [
		{ code: 'en', label: 'English' },
		{ code: 'zh', label: '中文' },
		{ code: 'ko', label: '한국어' },
		{ code: 'ja', label: '日本語' }
	];

	let currentPath = $derived(page.url.pathname);
	let currentLang = $derived(() => {
		const match = currentPath.match(/^\/([a-z]{2})\//);
		return match ? match[1] : 'en';
	});

	function handleLanguageSelect(langCode: string) {
		const newPath = currentPath.replace(/^\/[a-z]{2}\//, `/${langCode}/`);
		goto(newPath);
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger asChild>
		<Button variant="outline" size="icon">
			<Languages class="h-[1.2rem] w-[1.2rem]" />
			<span class="sr-only">Toggle language</span>
		</Button>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end">
		<DropdownMenu.Label>Select Language</DropdownMenu.Label>
		{#each languages as { code, label }}
			<DropdownMenu.Item
				class="cursor-pointer"
				onclick={() => handleLanguageSelect(code)}
				data-state={currentLang === code ? 'checked' : ''}
			>
				{label}
				{#if currentLang === code}
					<DropdownMenu.Separator />
					<DropdownMenu.ItemIndicator>✓</DropdownMenu.ItemIndicator>
				{/if}
			</DropdownMenu.Item>
		{/each}
	</DropdownMenu.Content>
</DropdownMenu.Root>
