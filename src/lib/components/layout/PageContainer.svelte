<!-- src/lib/components/layout/PageContainer.svelte -->
<script lang="ts">
	import type { Snippet } from 'svelte';
	import { Separator } from '$lib/components/ui/separator';

	interface Props {
		title?: string;
		description?: string;
		actions?: Snippet;
		content: Snippet;
		footer?: Snippet;
	}

	let props: Props = $props();
</script>

<div>
	<div class="pb-20 md:pb-0">
		<div class="mx-auto max-w-(--breakpoint-xl) px-4 py-4 sm:px-4 md:px-6 md:py-6">
			<div class="mb-6 flex items-center justify-between">
				<div>
					{#if props.title}
						<h1 class="text-2xl font-semibold">{props.title}</h1>
					{/if}
					{#if props.description}
						<p class="text-sm text-muted-foreground">{props.description}</p>
					{/if}
				</div>
				{@render props.actions?.()}
			</div>

			{@render props.content?.()}
		</div>

		{#if props.footer}
			<Separator />
			<div class="mx-auto max-w-(--breakpoint-xl) px-3 py-4 sm:px-4 md:px-6 md:py-6">
				{@render props.footer?.()}
			</div>
		{/if}
	</div>
</div>
