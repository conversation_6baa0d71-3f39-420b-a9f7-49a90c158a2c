<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import * as AlertDialog from '$lib/components/ui/alert-dialog';
	import { Badge } from '$lib/components/ui/badge';
	import { Separator } from '$lib/components/ui/separator';
	import {
		Loader2,
		Trash2,
		Package,
		Calendar,
		FileText,
		Tag,
		Link,
		Music,
		AlertTriangle,
		Info
	} from '@lucide/svelte';
	import { toast } from 'svelte-sonner';
	import type { Component } from 'svelte';

	interface DeleteAction {
		action: string;
		entity_type: string;
		entity_id: string;
		details: string;
	}

	interface DeleteResult {
		creation_request_id: string;
		success: boolean;
		message: string;
		actions: DeleteAction[];
	}

	interface Props {
		creationRequestId: string;
		onSuccess?: () => void;
		// Button customization
		variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
		size?: 'default' | 'sm' | 'lg' | 'icon';
		class?: string;
		disabled?: boolean;
		showIcon?: boolean;
		buttonText?: string;
	}

	let {
		creationRequestId,
		onSuccess,
		variant = 'outline',
		size = 'sm',
		class: className = '',
		disabled = false,
		showIcon = true,
		buttonText = 'Delete'
	}: Props = $props();

	let loading = $state(false);
	let dialogOpen = $state(false);
	let dryRunResult = $state<DeleteResult | null>(null);
	let deleting = $state(false);

	// Get icon for entity type
	function getEntityIcon(entityType: string): Component {
		switch (entityType) {
			case 'product':
			case 'product_price':
				return Package;
			case 'event':
			case 'event_product':
				return Calendar;
			case 'metadata':
			case 'metadata_wikipage':
			case 'metadata_track':
			case 'metadata_group_member':
			case 'metadata_hashtag':
				return FileText;
			case 'group':
				return Tag;
			case 'wikipage':
				return Link;
			case 'track':
				return Music;
			case 'change_request':
				return FileText;
			default:
				return FileText;
		}
	}

	// Get entity type display name
	function getEntityTypeDisplay(entityType: string): string {
		switch (entityType) {
			case 'product':
				return 'Product';
			case 'product_price':
				return 'Product Price';
			case 'event':
				return 'Event';
			case 'event_product':
				return 'Event-Product Link';
			case 'metadata':
				return 'Metadata';
			case 'metadata_wikipage':
				return 'Metadata Wikipage';
			case 'metadata_track':
				return 'Metadata Track';
			case 'metadata_group_member':
				return 'Metadata Group Member';
			case 'metadata_hashtag':
				return 'Metadata Hashtag';
			case 'group':
				return 'Consumer Group';
			case 'change_request':
				return 'Creation Request';
			default:
				return entityType.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
		}
	}

	// Group actions by entity type
	function groupActionsByType(actions: DeleteAction[]): Record<string, DeleteAction[]> {
		return actions.reduce(
			(acc, action) => {
				if (!acc[action.entity_type]) {
					acc[action.entity_type] = [];
				}
				acc[action.entity_type].push(action);
				return acc;
			},
			{} as Record<string, DeleteAction[]>
		);
	}

	// Get action counts
	function getActionCounts(actions: DeleteAction[]) {
		const deleteActions = actions.filter(
			(a) => a.action === 'will_delete' || a.action === 'delete'
		);
		const skipActions = actions.filter((a) => a.action === 'will_skip' || a.action === 'skipped');
		return {
			toDelete: deleteActions.length,
			toSkip: skipActions.length,
			total: actions.length
		};
	}

	// Perform dry run
	async function performDryRun() {
		loading = true;
		try {
			const response = await fetch('/api/offerings/delete', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					creation_request_id: creationRequestId,
					perform_action: false
				})
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to perform dry run');
			}

			dryRunResult = result.data;
			dialogOpen = true;
		} catch (error) {
			console.error('Error performing dry run:', error);
			toast.error(error instanceof Error ? error.message : 'Failed to check deletions');
		} finally {
			loading = false;
		}
	}

	// Perform actual deletion
	async function performDelete() {
		deleting = true;
		try {
			const response = await fetch('/api/offerings/delete', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					creation_request_id: creationRequestId,
					perform_action: true
				})
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to delete offering');
			}

			const counts = getActionCounts(result.data.actions);
			if (counts.toSkip > 0) {
				toast.success(
					`Successfully deleted ${counts.toDelete} items. ${counts.toSkip} items were skipped.`
				);
			} else {
				toast.success('Offering deleted successfully');
			}

			dialogOpen = false;
			onSuccess?.();
		} catch (error) {
			console.error('Error deleting offering:', error);
			toast.error(error instanceof Error ? error.message : 'Failed to delete offering');
		} finally {
			deleting = false;
		}
	}
</script>

<AlertDialog.Root bind:open={dialogOpen}>
	<AlertDialog.Trigger>
		<Button
			{variant}
			{size}
			class={className}
			onclick={performDryRun}
			disabled={loading || disabled}
		>
			{#if loading}
				<Loader2 class="h-4 w-4 animate-spin{buttonText ? ' mr-2' : ''}" />
			{:else if showIcon}
				<Trash2 class="h-4 w-4{buttonText ? ' mr-2' : ''}" />
			{/if}
			{#if buttonText}
				{buttonText}
			{/if}
		</Button>
	</AlertDialog.Trigger>

	<AlertDialog.Content class="max-w-2xl">
		<AlertDialog.Header>
			<AlertDialog.Title>Confirm Deletion</AlertDialog.Title>
			<AlertDialog.Description>
				{#if dryRunResult}
					{@const counts = getActionCounts(dryRunResult.actions)}
					This action will permanently delete the following items created by request
					<code class="bg-muted rounded px-1 py-0.5 text-xs">
						#{dryRunResult.creation_request_id.substring(0, 8)}
					</code>
					{#if counts.toSkip > 0}
						<br />
						<span class="text-sm text-amber-600">
							Note: {counts.toSkip} item{counts.toSkip === 1 ? '' : 's'} will be skipped due to external
							references.
						</span>
					{/if}
				{:else}
					Checking items to be deleted...
				{/if}
			</AlertDialog.Description>
		</AlertDialog.Header>

		{#if dryRunResult && dryRunResult.actions.length > 0}
			{@const groupedActions = groupActionsByType(dryRunResult.actions)}
			{@const counts = getActionCounts(dryRunResult.actions)}

			<div class="max-h-[400px] space-y-4 overflow-y-auto py-4">
				{#each Object.entries(groupedActions) as [entityType, actions]}
					{@const IconComponent = getEntityIcon(entityType)}
					<div class="space-y-2">
						<div class="flex items-center justify-between">
							<div class="flex items-center gap-2">
								<IconComponent class="text-muted-foreground h-4 w-4" />
								<h4 class="text-sm font-medium">
									{getEntityTypeDisplay(entityType)}
								</h4>
							</div>
							<Badge variant="secondary" class="text-xs">
								{actions.length}
							</Badge>
						</div>
						<div class="space-y-1 pl-6">
							{#each actions as action}
								<div class="flex items-center justify-between text-sm">
									<div class="flex items-center gap-2">
										{#if action.action === 'will_skip' || action.action === 'skipped'}
											<span class="text-xs text-amber-600">[SKIP]</span>
										{/if}
										<span class="text-muted-foreground">{action.details}</span>
									</div>
									<button
										type="button"
										class="bg-muted text-muted-foreground hover:bg-muted/80 hover:text-foreground rounded px-2 py-1 font-mono text-xs transition-colors"
										onclick={() => {
											navigator.clipboard.writeText(action.entity_id);
											toast.success('UUID copied to clipboard');
										}}
										title="Click to copy full UUID: {action.entity_id}"
									>
										{action.entity_id.substring(0, 8)}
									</button>
								</div>
							{/each}
						</div>
					</div>
					{#if Object.keys(groupedActions).indexOf(entityType) < Object.keys(groupedActions).length - 1}
						<Separator />
					{/if}
				{/each}
			</div>

			<div class="border-destructive/50 bg-destructive/10 rounded-lg border p-4">
				<div class="flex items-start gap-3">
					<div class="bg-destructive/20 mt-0.5 rounded-full p-1">
						<svg
							class="text-destructive h-4 w-4"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"
							/>
						</svg>
					</div>
					<div class="flex-1">
						<h5 class="text-destructive text-sm font-medium">Permanent Deletion Warning</h5>
						<p class="text-destructive/90 mt-1 text-sm">
							This action cannot be undone.
							{#if counts.toDelete > 0}
								<strong>{counts.toDelete}</strong> item{counts.toDelete === 1 ? '' : 's'} will be permanently
								deleted from the system.
							{/if}
							{#if counts.toSkip > 0}
								{#if counts.toDelete > 0}<br />{/if}
								<strong>{counts.toSkip}</strong> item{counts.toSkip === 1 ? '' : 's'} will be skipped
								due to being referenced by other records.
							{/if}
						</p>
					</div>
				</div>
			</div>
		{:else if dryRunResult}
			<div class="text-muted-foreground py-8 text-center">
				<p>No items found to delete.</p>
			</div>
		{/if}

		<AlertDialog.Footer>
			<AlertDialog.Cancel disabled={deleting}>Cancel</AlertDialog.Cancel>
			<AlertDialog.Action
				onclick={performDelete}
				disabled={deleting || !dryRunResult || dryRunResult.actions.length === 0}
				class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
			>
				{#if deleting}
					<Loader2 class="mr-2 h-4 w-4 animate-spin" />
					Deleting...
				{:else if dryRunResult}
					{@const counts = getActionCounts(dryRunResult.actions)}
					{#if counts.toDelete > 0}
						Delete {counts.toDelete} item{counts.toDelete === 1 ? '' : 's'}
						{#if counts.toSkip > 0}
							(skip {counts.toSkip})
						{/if}
					{:else}
						No items to delete
					{/if}
				{:else}
					Delete items
				{/if}
			</AlertDialog.Action>
		</AlertDialog.Footer>
	</AlertDialog.Content>
</AlertDialog.Root>
