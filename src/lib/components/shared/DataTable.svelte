<!-- src/lib/components/shared/DataTable.svelte -->
<script lang="ts" generics="TData">
	import {
		type ColumnDef,
		type PaginationState,
		type SortingState,
		type VisibilityState,
		type RowSelectionState,
		getCoreRowModel,
		getPaginationRowModel,
		getSortedRowModel
	} from '@tanstack/table-core';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import * as Table from '$lib/components/ui/table';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { ChevronDown, Columns, ChevronLeft, ChevronRight } from '@lucide/svelte';
	import { createSvelteTable, FlexRender } from '$lib/components/ui/data-table';

	interface Props<TData> {
		data: TData[];
		columns: ColumnDef<TData, any>[];
		onRowAction?: (row: TData) => void;
		pageSize?: number;
	}

	let { data, columns, onRowAction, pageSize = 10 }: Props<TData> = $props();

	let pagination = $state<PaginationState>({ pageIndex: 0, pageSize });
	let sorting = $state<SortingState>([]);
	let columnVisibility = $state<VisibilityState>({});
	let rowSelection = $state<RowSelectionState>({});

	const table = createSvelteTable({
		get data() {
			return data;
		},
		columns,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		onPaginationChange: (updater) => {
			if (typeof updater === 'function') {
				pagination = updater(pagination);
			} else {
				pagination = updater;
			}
		},
		onSortingChange: (updater) => {
			if (typeof updater === 'function') {
				sorting = updater(sorting);
			} else {
				sorting = updater;
			}
		},
		onColumnVisibilityChange: (updater) => {
			if (typeof updater === 'function') {
				columnVisibility = updater(columnVisibility);
			} else {
				columnVisibility = updater;
			}
		},
		onRowSelectionChange: (updater) => {
			if (typeof updater === 'function') {
				rowSelection = updater(rowSelection);
			} else {
				rowSelection = updater;
			}
		},
		state: {
			get pagination() {
				return pagination;
			},
			get sorting() {
				return sorting;
			},
			get columnVisibility() {
				return columnVisibility;
			},
			get rowSelection() {
				return rowSelection;
			}
		}
	});
</script>

<div class="w-full">
	<div class="flex items-center gap-2 py-4">
		<DropdownMenu.Root>
			<DropdownMenu.Trigger>
				{#snippet child({ props })}
					<Button {...props} variant="outline" size="icon" class="ml-auto w-14 p-0">
						<Columns class="size-4" />
					</Button>
				{/snippet}
			</DropdownMenu.Trigger>
			<DropdownMenu.Content align="end">
				{#each table.getAllColumns().filter((col) => col.getCanHide()) as column}
					<DropdownMenu.CheckboxItem
						class="capitalize"
						checked={column.getIsVisible()}
						onCheckedChange={(value) => column.toggleVisibility(!!value)}
					>
						{column.id}
					</DropdownMenu.CheckboxItem>
				{/each}
			</DropdownMenu.Content>
		</DropdownMenu.Root>
	</div>

	<div class="relative -mx-6 w-screen sm:mx-0 sm:w-auto">
		<div class="overflow-x-auto border-y sm:rounded-md sm:border">
			<div class="min-w-max">
				<Table.Root>
					<Table.Header>
						{#each table.getHeaderGroups() as headerGroup (headerGroup.id)}
							<Table.Row>
								{#each headerGroup.headers as header (header.id)}
									<Table.Head class="whitespace-nowrap [&:has([role=checkbox])]:pl-3">
										{#if !header.isPlaceholder}
											<FlexRender
												content={header.column.columnDef.header}
												context={header.getContext()}
											></FlexRender>
										{/if}
									</Table.Head>
								{/each}
							</Table.Row>
						{/each}
					</Table.Header>
					<Table.Body>
						{#each table.getRowModel().rows as row (row.id)}
							<Table.Row
								data-state={row.getIsSelected() && 'selected'}
								onclick={() => onRowAction?.(row.original)}
							>
								{#each row.getVisibleCells() as cell (cell.id)}
									<Table.Cell class="whitespace-nowrap p-2 sm:p-4 [&:has([role=checkbox])]:pl-3">
										<FlexRender content={cell.column.columnDef.cell} context={cell.getContext()}
										></FlexRender>
									</Table.Cell>
								{/each}
							</Table.Row>
						{:else}
							<Table.Row>
								<Table.Cell colspan={columns.length} class="h-24 text-center"
									>No results.</Table.Cell
								>
							</Table.Row>
						{/each}
					</Table.Body>
				</Table.Root>
			</div>
		</div>
	</div>

	<div class="flex flex-col gap-4 pt-4 sm:flex-row sm:items-center sm:justify-end sm:space-x-2">
		<div class="text-sm text-muted-foreground">
			{table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length}
			row(s) selected.
		</div>
		<div class="flex items-center justify-between gap-2 sm:justify-end">
			<Button
				variant="outline"
				size="sm"
				onclick={() => table.previousPage()}
				disabled={!table.getCanPreviousPage()}
				class="flex items-center gap-1"
			>
				<ChevronLeft class="size-4" />
				Previous
			</Button>
			<div class="flex items-center gap-1 text-sm">
				<span class="text-muted-foreground">Page</span>
				<Input
					type="number"
					min={1}
					max={table.getPageCount()}
					value={table.getState().pagination.pageIndex + 1}
					onchange={(e) => {
						const page = e.currentTarget.value ? Number(e.currentTarget.value) - 1 : 0;
						table.setPageIndex(page);
					}}
					class="h-8 w-16 text-center [-moz-appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
				/>
				<span class="text-muted-foreground"> of {table.getPageCount()}</span>
			</div>
			<Button
				variant="outline"
				size="sm"
				onclick={() => table.nextPage()}
				disabled={!table.getCanNextPage()}
				class="flex items-center gap-1"
			>
				Next
				<ChevronRight class="size-4" />
			</Button>
		</div>
	</div>
</div>
