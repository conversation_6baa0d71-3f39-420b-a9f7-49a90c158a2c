<script lang="ts">
	import type { Component } from 'svelte';

	interface Props {
		icon: Component;
		title: string;
	}

	let { icon: Icon, title }: Props = $props();
</script>

<div
	class="mb-6 flex items-center gap-2.5 rounded-lg bg-gradient-to-r from-blue-50 to-blue-100 px-4 py-3 dark:from-blue-950/50 dark:to-blue-900/50"
>
	<Icon class="h-4 w-4 text-blue-600 dark:text-blue-400" />
	<h2 class="text-base font-medium text-blue-900 dark:text-blue-100">{title}</h2>
</div>
