<script lang="ts">
	import { Search, X, MapPin } from '@lucide/svelte';
	import { Button } from '$lib/components/ui/button';
	import * as Popover from '$lib/components/ui/popover';
	import { Input } from '$lib/components/ui/input';
	import { Badge } from '$lib/components/ui/badge';
	import { ScrollArea } from '$lib/components/ui/scroll-area';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { Database } from '$lib/supabase/database.types';
	import { cn } from '$lib/utils';

	type Landmark = {
		id: string;
		title_full: LocalizedText | null;
		title_short: LocalizedText | null;
	};

	type Space = {
		id: string;
		name_full: LocalizedText | null;
		name_short: LocalizedText | null;
		capacity: number | null;
		landmark: Landmark | null;
	};

	interface Props {
		value?: string;
		onValueChange?: (value: string) => void;
		placeholder?: string;
		brandId: string;
		disabled?: boolean;
		class?: string;
	}

	let {
		value = $bindable(),
		onValueChange,
		placeholder = 'Search and select space',
		brandId,
		disabled = false,
		class: className = ''
	}: Props = $props();

	let search = $state('');
	let spaces = $state<Space[]>([]);
	let selectedSpace = $state<Space | null>(null);
	let loading = $state(false);
	let open = $state(false);

	// Fetch spaces when component mounts or when brand changes
	$effect(() => {
		if (brandId) {
			fetchSpaces();
		}
	});

	// Fetch selected space details when value changes
	$effect(() => {
		if (value && (!selectedSpace || selectedSpace.id !== value)) {
			fetchSelectedSpace();
		} else if (!value) {
			selectedSpace = null;
		}
	});

	async function fetchSpaces() {
		loading = true;
		try {
			const params = new URLSearchParams({
				brand_id: brandId
			});

			// Add search if exists
			if (search) {
				params.append('search', search);
			}

			const response = await fetch(`/api/spaces?${params}`);
			if (!response.ok) {
				throw new Error('Failed to fetch spaces');
			}

			spaces = await response.json();
		} catch (error) {
			console.error('Error fetching spaces:', error);
			spaces = [];
		} finally {
			loading = false;
		}
	}

	async function fetchSelectedSpace() {
		if (!value) return;

		try {
			const params = new URLSearchParams({
				brand_id: brandId
			});

			const response = await fetch(`/api/spaces?${params}`);
			if (!response.ok) {
				throw new Error('Failed to fetch spaces');
			}

			const allSpaces: Space[] = await response.json();
			selectedSpace = allSpaces.find((s) => s.id === value) || null;
		} catch (error) {
			console.error('Error fetching selected space:', error);
		}
	}

	function handleSelect(space: Space) {
		value = space.id;
		selectedSpace = space;
		onValueChange?.(space.id);
		open = false;
		search = '';
	}

	function handleClear() {
		value = undefined;
		selectedSpace = null;
		onValueChange?.('');
		search = '';
	}

	// Handle search with debounce
	let searchTimeout: ReturnType<typeof setTimeout>;
	function handleSearch(newSearch: string) {
		search = newSearch;
		clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			fetchSpaces();
		}, 300);
	}

	$effect(() => {
		return () => clearTimeout(searchTimeout);
	});

	// Format display text
	function getSpaceDisplayText(space: Space): string {
		const spaceName =
			getLocalizedText(space.name_full as LocalizedText) ||
			getLocalizedText(space.name_short as LocalizedText) ||
			'Unnamed Space';
		return spaceName;
	}

	function getLandmarkDisplayText(space: Space): string | null {
		if (!space.landmark) return null;
		return (
			getLocalizedText(space.landmark.title_short as LocalizedText) ||
			getLocalizedText(space.landmark.title_full as LocalizedText)
		);
	}
</script>

<Popover.Root bind:open>
	<Popover.Trigger {disabled}>
		{#snippet child({ props })}
			<Button
				{...props}
				variant="outline"
				role="combobox"
				aria-expanded={open}
				class={cn('h-9 w-full justify-between text-left font-normal', className)}
				{disabled}
			>
				{#if selectedSpace}
					{@const landmarkText = getLandmarkDisplayText(selectedSpace)}
					<div class="flex items-center gap-2 truncate">
						<MapPin class="text-muted-foreground h-4 w-4 shrink-0" />
						<span class="truncate">
							{getSpaceDisplayText(selectedSpace)}
						</span>
						{#if landmarkText}
							<Badge variant="secondary" class="ml-auto shrink-0 text-xs">
								{landmarkText}
							</Badge>
						{/if}
					</div>
				{:else}
					<span class="text-muted-foreground">{placeholder}</span>
				{/if}

				{#if value && !disabled}
					<Button
						size="icon"
						variant="ghost"
						class="ml-2 h-4 w-4 shrink-0 p-0 hover:bg-transparent"
						onclick={(e) => {
							e.stopPropagation();
							handleClear();
						}}
					>
						<X class="h-3 w-3" />
					</Button>
				{:else}
					<Search class="ml-2 h-4 w-4 shrink-0 opacity-50" />
				{/if}
			</Button>
		{/snippet}
	</Popover.Trigger>

	<Popover.Content class="w-[--radix-popover-trigger-width] p-0" align="start">
		<div class="border-b p-2">
			<div class="relative">
				<Search class="text-muted-foreground absolute top-2.5 left-2 h-4 w-4" />
				<Input
					type="text"
					placeholder="Search spaces..."
					class="h-9 pl-8"
					value={search}
					oninput={(e) => handleSearch(e.currentTarget.value)}
				/>
			</div>
		</div>

		<ScrollArea class="h-[200px]">
			{#if loading}
				<div class="text-muted-foreground p-4 text-center text-sm">Loading...</div>
			{:else if spaces.length === 0}
				<div class="text-muted-foreground p-4 text-center text-sm">
					{search ? 'No spaces found' : 'No spaces available'}
				</div>
			{:else}
				<div class="p-1">
					{#each spaces as space}
						{@const landmarkText = getLandmarkDisplayText(space)}
						<button
							class="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm focus:outline-none"
							onclick={() => handleSelect(space)}
						>
							<MapPin class="text-muted-foreground h-4 w-4 shrink-0" />
							<span class="flex-1 truncate text-left">
								{getSpaceDisplayText(space)}
							</span>
							{#if landmarkText}
								<Badge variant="outline" class="ml-auto shrink-0 text-xs">
									{landmarkText}
								</Badge>
							{/if}
							{#if space.capacity}
								<span class="text-muted-foreground text-xs">
									Cap: {space.capacity}
								</span>
							{/if}
						</button>
					{/each}
				</div>
			{/if}
		</ScrollArea>
	</Popover.Content>
</Popover.Root>
