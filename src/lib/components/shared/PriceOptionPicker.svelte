<script lang="ts">
	import * as Select from '$lib/components/ui/select';
	import { Label } from '$lib/components/ui/label';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { Coins, AlertCircle } from '@lucide/svelte';

	interface PriceOption {
		id: string;
		title: LocalizedText;
		units: number;
		money_int: number;
		currency_code?: string;
		sort_order?: number;
	}

	interface Price {
		id: string;
		title: LocalizedText;
		semantic_order?: number;
		price_option?: PriceOption[];
	}

	interface Props {
		priceId?: string | undefined;
		brandId?: string | undefined;
		selectedPriceOptionId?: string;
		selectedPriceOption?: PriceOption | null;
		onPriceOptionChange?: (priceOptionId: string) => void;
		label?: string;
		required?: boolean;
		mode?: 'single-price' | 'all-prices';
	}

	let {
		priceId,
		brandId,
		selectedPriceOptionId = $bindable(''),
		selectedPriceOption = $bindable<PriceOption | null>(null),
		onPriceOptionChange,
		label = 'Price Options',
		required = false,
		mode = 'single-price'
	}: Props = $props();

	// State
	let priceOptions = $state<PriceOption[]>([]);
	let prices = $state<Price[]>([]);
	let loading = $state(false);
	let error = $state<string | null>(null);

	// Load data based on mode
	$effect(() => {
		if (mode === 'single-price' && priceId) {
			loadPriceOptions();
		} else if (mode === 'all-prices' && brandId) {
			loadAllPrices();
		} else {
			priceOptions = [];
			prices = [];
			selectedPriceOptionId = '';
			selectedPriceOption = null;
		}
	});

	async function loadPriceOptions(): Promise<void> {
		if (!priceId) return;

		loading = true;
		error = null;

		try {
			const response = await fetch(`/api/price-options?priceId=${encodeURIComponent(priceId)}`);

			if (!response.ok) {
				const result = await response.json();
				throw new Error(result.error || 'Failed to fetch price options');
			}

			const result = await response.json();
			priceOptions = result.priceOptions || [];

			// If only one option available, auto-select it
			if (priceOptions.length === 1) {
				selectedPriceOptionId = priceOptions[0].id;
				selectedPriceOption = priceOptions[0];
				onPriceOptionChange?.(priceOptions[0].id);
			}
		} catch (err) {
			console.error('Error loading price options:', err);
			error = err instanceof Error ? err.message : 'Failed to load price options';
		} finally {
			loading = false;
		}
	}

	async function loadAllPrices(): Promise<void> {
		if (!brandId) return;

		loading = true;
		error = null;

		try {
			const response = await fetch(`/api/prices?brandId=${encodeURIComponent(brandId)}`);

			if (!response.ok) {
				const result = await response.json();
				throw new Error(result.error || 'Failed to fetch prices');
			}

			const result = await response.json();
			prices = result.prices || [];

			// If only one price option across all prices, auto-select it
			const allOptions = prices.flatMap((p) => p.price_option || []);
			if (allOptions.length === 1) {
				selectedPriceOptionId = allOptions[0].id;
				selectedPriceOption = allOptions[0];
				onPriceOptionChange?.(allOptions[0].id);
			}
		} catch (err) {
			console.error('Error loading prices:', err);
			error = err instanceof Error ? err.message : 'Failed to load prices';
		} finally {
			loading = false;
		}
	}

	// Handle selection change
	function handleChange(value: string | undefined) {
		if (value) {
			selectedPriceOptionId = value;

			// Find the selected option
			let option: PriceOption | undefined;
			if (mode === 'single-price') {
				option = priceOptions.find((opt) => opt.id === value);
			} else {
				for (const price of prices) {
					option = price.price_option?.find((opt) => opt.id === value);
					if (option) break;
				}
			}

			selectedPriceOption = option || null;
			onPriceOptionChange?.(value);
		}
	}

	// Format price option display
	function formatPriceOption(option: PriceOption): string {
		const title = getLocalizedText(option.title as LocalizedText);
		const price = option.money_int / 100; // Convert cents to dollars
		const currency = option.currency_code || 'USD';
		return `${title} - ${option.units} units (${currency} ${price.toFixed(2)})`;
	}

	// Get price title for grouping
	function getPriceTitle(price: Price): string {
		return getLocalizedText(price.title as LocalizedText) || 'Untitled Price';
	}
</script>

<div class="space-y-2">
	<Label for="price-options">
		{label}
		{#if required}
			<span class="text-destructive">*</span>
		{/if}
	</Label>

	{#if loading}
		<Skeleton class="h-10 w-full" />
	{:else if error}
		<Alert variant="destructive">
			<AlertCircle class="h-4 w-4" />
			<AlertDescription>{error}</AlertDescription>
		</Alert>
	{:else if mode === 'single-price' && !priceId}
		<Alert>
			<AlertCircle class="h-4 w-4" />
			<AlertDescription>Select a price option first</AlertDescription>
		</Alert>
	{:else if mode === 'all-prices' && !brandId}
		<Alert>
			<AlertCircle class="h-4 w-4" />
			<AlertDescription>Brand information is missing</AlertDescription>
		</Alert>
	{:else if (mode === 'single-price' && priceOptions.length === 0) || (mode === 'all-prices' && prices.length === 0)}
		<Alert>
			<AlertCircle class="h-4 w-4" />
			<AlertDescription>No price options available</AlertDescription>
		</Alert>
	{:else}
		<Select.Root type="single" bind:value={selectedPriceOptionId} onValueChange={handleChange}>
			<Select.Trigger>
				<div class="flex items-center gap-2">
					<Coins class="h-4 w-4" />
					{#if selectedPriceOptionId}
						{@const selectedOption =
							mode === 'single-price'
								? priceOptions.find((opt) => opt.id === selectedPriceOptionId)
								: prices
										.flatMap((p) => p.price_option || [])
										.find((opt) => opt.id === selectedPriceOptionId)}
						{selectedOption ? formatPriceOption(selectedOption) : 'Select price option'}
					{:else}
						Select price option
					{/if}
				</div>
			</Select.Trigger>
			<Select.Content>
				{#if mode === 'single-price'}
					{#each priceOptions as option}
						<Select.Item value={option.id}>
							<div class="flex items-center gap-2">
								<Coins class="h-4 w-4" />
								{formatPriceOption(option)}
							</div>
						</Select.Item>
					{/each}
				{:else}
					{#each prices as price}
						{#if price.price_option && price.price_option.length > 0}
							<Select.Group>
								<Select.GroupHeading>{getPriceTitle(price)}</Select.GroupHeading>
								{#each price.price_option as option}
									<Select.Item value={option.id}>
										<div class="flex items-center gap-2">
											<Coins class="h-4 w-4" />
											{formatPriceOption(option)}
										</div>
									</Select.Item>
								{/each}
							</Select.Group>
						{/if}
					{/each}
				{/if}
			</Select.Content>
		</Select.Root>
	{/if}

	<input type="hidden" name="price_option_id" value={selectedPriceOptionId} />
</div>
