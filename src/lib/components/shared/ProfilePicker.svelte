<script lang="ts">
	import { Check, ChevronsUpDown, UserCircle } from '@lucide/svelte';
	import { tick } from 'svelte';
	import * as Command from '$lib/components/ui/command/index.js';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { cn } from '$lib/utils.js';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { onMount } from 'svelte';

	interface UserProfile {
		id: string;
		given_name: LocalizedText;
		family_name: LocalizedText;
		username: string;
		avatar_url?: string | null;
		auto_user_email?: string;
		auto_user_phone?: string;
	}

	interface Props {
		placeholder?: string;
		selectedProfileId?: string;
		disabled?: boolean;
		width?: string;
		maxHeight?: string;
	}

	let {
		placeholder = 'Select a user...',
		selectedProfileId = $bindable(''),
		disabled = false,
		width = 'w-full', // Default to full width
		maxHeight = '300px'
	}: Props = $props();

	let open = $state(false);
	let searchTerm = $state('');
	let triggerRef = $state<HTMLButtonElement>(null!);
	let loading = $state(false);
	let searchError = $state<string | null>(null);
	let lastSearchQuery = $state('');
	let displayResults = $state<UserProfile[]>([]);
	let selectedUser = $state<UserProfile | null>(null);

	// Update the display value when relevant data changes
	$effect(() => {
		if (selectedProfileId && (!selectedUser || selectedUser.id !== selectedProfileId)) {
			loadUserById(selectedProfileId);
		}
	});

	// Pre-load selected user
	onMount(async () => {
		if (selectedProfileId) {
			await loadUserById(selectedProfileId);
		}
	});

	// Get selected user's display name
	function getSelectedUserDisplay(): string {
		return selectedUser ? getFullName(selectedUser) : placeholder;
	}

	let selectedUserDisplay = $state(getSelectedUserDisplay());

	// Update display when selected user changes
	$effect(() => {
		selectedUserDisplay = getSelectedUserDisplay();
	});

	// Load a specific user by ID
	async function loadUserById(userId: string) {
		try {
			const res = await fetch(`/api/profile/search?id=${userId}`);
			if (res.ok) {
				const data = await res.json();
				if (data.profiles?.length > 0) {
					selectedUser = data.profiles[0];
				}
			}
		} catch (error) {
			console.error('Error fetching selected user:', error);
		}
	}

	// Perform a search when the search term changes
	$effect(() => {
		if (searchTerm) {
			searchUsers(searchTerm);
		} else {
			displayResults = [];
		}
	});

	// Debounced search function
	let searchTimer: number | undefined;
	function searchUsers(query: string) {
		clearTimeout(searchTimer);
		searchTimer = window.setTimeout(async () => {
			if (query.trim()) {
				await performSearch(query.trim());
			} else {
				displayResults = [];
			}
		}, 300);
	}

	// Perform the actual search
	async function performSearch(query: string) {
		loading = true;
		searchError = null;
		lastSearchQuery = query;

		try {
			const res = await fetch(`/api/profile/search?q=${encodeURIComponent(query)}`);

			if (!res.ok) {
				const errorData = await res.json();
				console.error('Search error:', errorData);
				searchError = errorData.error || 'Failed to search';
				displayResults = [];
				return;
			}

			const data = await res.json();

			if (Array.isArray(data.profiles)) {
				// Process the search results
				const results = data.profiles.map((profile: any) => ({
					id: profile.id || '',
					given_name: ensureLocalizedText(profile.given_name),
					family_name: ensureLocalizedText(profile.family_name),
					username: profile.username || '',
					avatar_url: profile.avatar_url || null,
					auto_user_email: profile.auto_user_email || null,
					auto_user_phone: profile.auto_user_phone || null
				}));

				displayResults = results;
			} else {
				console.error('Invalid search results format:', data);
				searchError = 'Invalid response format';
				displayResults = [];
			}
		} catch (error) {
			console.error('Search error:', error);
			searchError = 'An error occurred';
			displayResults = [];
		} finally {
			loading = false;
		}
	}

	// Ensure a value is a LocalizedText object
	function ensureLocalizedText(value: any): LocalizedText {
		if (!value) return { en: '' } as LocalizedText;

		if (typeof value === 'object') {
			if ('en' in value) return value as LocalizedText;

			const firstValue = Object.values(value)[0];
			if (typeof firstValue === 'string') {
				return { en: firstValue } as LocalizedText;
			}
		}

		if (typeof value === 'string') {
			return { en: value } as LocalizedText;
		}

		return { en: '' } as LocalizedText;
	}

	// Get full name for display
	function getFullName(user: UserProfile): string {
		const firstName = getLocalizedText(user.given_name, 'en');
		const lastName = getLocalizedText(user.family_name, 'en');
		return `${firstName} ${lastName}`.trim();
	}

	// Handle user selection
	function handleSelect(userId: string) {
		selectedProfileId = userId;

		// Find the selected user in results to update display immediately
		const user = displayResults.find((u) => u.id === userId);
		if (user) {
			selectedUser = user;
		}

		// Use tick for better focus handling
		open = false;
		tick().then(() => {
			triggerRef?.focus();
		});
	}

	// Handle open/close
	function handleOpenChange(isOpen: boolean) {
		open = isOpen;
		if (!isOpen) {
			searchTerm = '';
			displayResults = [];
		}
	}
</script>

<Popover.Root bind:open onOpenChange={handleOpenChange}>
	<Popover.Trigger bind:ref={triggerRef}>
		{#snippet child({ props })}
			<Button
				variant="outline"
				class={`${width} justify-between`}
				{...props}
				role="combobox"
				aria-expanded={open}
				{disabled}
			>
				<span class="flex-1 truncate text-left">
					{selectedUserDisplay}
				</span>
				<ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
			</Button>
		{/snippet}
	</Popover.Trigger>
	<Popover.Content class="p-0" style="width: var(--bits-popover-anchor-width);" sideOffset={4}>
		<Command.Root shouldFilter={false}>
			<Command.Input placeholder="Search by name, email, or username..." bind:value={searchTerm} />
			<Command.List style={`max-height: ${maxHeight}; overflow-y: auto;`}>
				{#if loading}
					<div class="text-muted-foreground flex items-center justify-center py-6 text-sm">
						Loading users...
					</div>
				{:else if searchError}
					<div class="text-destructive flex items-center justify-center py-6 text-sm">
						{searchError}
					</div>
				{:else if displayResults.length === 0}
					<Command.Empty
						>No users found{lastSearchQuery ? ` for "${lastSearchQuery}"` : ''}.</Command.Empty
					>
				{:else}
					<Command.Group>
						{#each displayResults as user (user.id)}
							<Command.Item value={user.id} onSelect={() => handleSelect(user.id)}>
								<div class="flex w-full items-center">
									<Check
										class={cn('mr-2 h-4 w-4', selectedProfileId !== user.id && 'text-transparent')}
									/>
									{#if user.avatar_url}
										<img
											src={user.avatar_url}
											alt={getFullName(user)}
											class="mr-2 size-5 rounded-full"
										/>
									{:else}
										<UserCircle class="text-muted-foreground mr-2 size-5" />
									{/if}
									<div class="flex min-w-0 flex-1 flex-col">
										<span class="truncate">{getFullName(user)}</span>
										<div class="text-muted-foreground flex truncate text-xs">
											{#if user.username}
												<span class="truncate">@{user.username}</span>
											{/if}
											{#if user.auto_user_email}
												<span class="ml-1 truncate"
													>{user.username ? '•' : ''} {user.auto_user_email}</span
												>
											{/if}
										</div>
									</div>
								</div>
							</Command.Item>
						{/each}
					</Command.Group>
				{/if}

				{#if searchTerm && displayResults.length > 0}
					<div class="border-border text-muted-foreground border-t p-2 text-xs">
						Found {displayResults.length} results for "{searchTerm}"
					</div>
				{/if}
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>
