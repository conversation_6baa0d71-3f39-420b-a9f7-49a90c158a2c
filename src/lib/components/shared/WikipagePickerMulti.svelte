<script lang="ts">
	import { Search, X, Check } from '@lucide/svelte';
	import { Button } from '$lib/components/ui/button';
	import * as Popover from '$lib/components/ui/popover';
	import { Input } from '$lib/components/ui/input';
	import { Badge } from '$lib/components/ui/badge';
	import { ScrollArea } from '$lib/components/ui/scroll-area';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { Database } from '$lib/supabase/database.types';
	import { cn } from '$lib/utils';

	type Wikipage = Pick<
		Database['public']['Tables']['wikipage']['Row'],
		'id' | 'title' | 'kind' | 'brief'
	>;

	interface Props {
		value?: string[];
		onValueChange?: (value: string[]) => void;
		placeholder?: string;
		brandId: string;
		supportedKinds?: string[];
		disabled?: boolean;
		class?: string;
		maxItems?: number;
	}

	let {
		value = $bindable([]),
		onValueChange,
		placeholder = 'Search and select',
		brandId,
		supportedKinds = [],
		disabled = false,
		class: className = '',
		maxItems
	}: Props = $props();

	let search = $state('');
	let wikipages = $state<Wikipage[]>([]);
	let selectedWikipages = $state<Wikipage[]>([]);
	let loading = $state(false);
	let open = $state(false);

	// Fetch wikipages when component mounts or when filters change
	$effect(() => {
		if (brandId) {
			fetchWikipages();
		}
	});

	// Fetch selected wikipages details when value changes
	$effect(() => {
		if (value && value.length > 0) {
			fetchSelectedWikipages();
		} else {
			selectedWikipages = [];
		}
	});

	async function fetchWikipages() {
		loading = true;
		try {
			const params = new URLSearchParams({
				brand_id: brandId
			});

			// Add kind filters
			supportedKinds.forEach((kind) => params.append('kind', kind));

			// Add search if exists
			if (search) {
				params.append('search', search);
			}

			const response = await fetch(`/api/wikipages?${params}`);
			if (!response.ok) {
				throw new Error('Failed to fetch wikipages');
			}

			wikipages = await response.json();
		} catch (error) {
			console.error('Error fetching wikipages:', error);
			wikipages = [];
		} finally {
			loading = false;
		}
	}

	async function fetchSelectedWikipages() {
		if (!value || value.length === 0) return;

		try {
			const params = new URLSearchParams({
				brand_id: brandId
			});

			const response = await fetch(`/api/wikipages?${params}`);
			if (!response.ok) {
				throw new Error('Failed to fetch wikipages');
			}

			const allWikipages: Wikipage[] = await response.json();
			selectedWikipages = allWikipages.filter((w) => value.includes(w.id));
		} catch (error) {
			console.error('Error fetching selected wikipages:', error);
		}
	}

	function toggleSelection(wikipage: Wikipage) {
		const isSelected = value.includes(wikipage.id);

		if (isSelected) {
			// Remove from selection
			value = value.filter((id) => id !== wikipage.id);
			selectedWikipages = selectedWikipages.filter((w) => w.id !== wikipage.id);
		} else {
			// Add to selection if not at max
			if (!maxItems || value.length < maxItems) {
				value = [...value, wikipage.id];
				selectedWikipages = [...selectedWikipages, wikipage];
			}
		}

		onValueChange?.(value);
	}

	function removeItem(wikipageId: string) {
		value = value.filter((id) => id !== wikipageId);
		selectedWikipages = selectedWikipages.filter((w) => w.id !== wikipageId);
		onValueChange?.(value);
	}

	function clearAll() {
		value = [];
		selectedWikipages = [];
		onValueChange?.([]);
		search = '';
	}

	// Handle search with debounce
	let searchTimeout: ReturnType<typeof setTimeout>;
	function handleSearch(newSearch: string) {
		search = newSearch;
		clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			fetchWikipages();
		}, 300);
	}

	$effect(() => {
		return () => clearTimeout(searchTimeout);
	});

	// Check if an item is selected
	function isSelected(wikipageId: string): boolean {
		return value.includes(wikipageId);
	}

	// Get display text for selected items
	function getSelectedDisplay(): string {
		if (selectedWikipages.length === 0) return '';
		if (selectedWikipages.length === 1) {
			return getLocalizedText(selectedWikipages[0].title as LocalizedText);
		}
		return `${selectedWikipages.length} selected`;
	}
</script>

<Popover.Root bind:open>
	<Popover.Trigger {disabled}>
		{#snippet child({ props })}
			<Button
				{...props}
				variant="outline"
				role="combobox"
				aria-expanded={open}
				class={cn('h-9 w-full justify-between text-left font-normal', className)}
				{disabled}
			>
				{#if selectedWikipages.length > 0}
					<div class="flex items-center gap-2 truncate">
						<span class="truncate">
							{getSelectedDisplay()}
						</span>
						{#if selectedWikipages.length > 1}
							<Badge variant="secondary" class="ml-auto shrink-0 text-xs">
								{selectedWikipages.length}
							</Badge>
						{/if}
					</div>
				{:else}
					<span class="text-muted-foreground">{placeholder}</span>
				{/if}

				{#if value.length > 0 && !disabled}
					<Button
						size="icon"
						variant="ghost"
						class="ml-2 h-4 w-4 shrink-0 p-0 hover:bg-transparent"
						onclick={(e) => {
							e.stopPropagation();
							clearAll();
						}}
					>
						<X class="h-3 w-3" />
					</Button>
				{:else}
					<Search class="ml-2 h-4 w-4 shrink-0 opacity-50" />
				{/if}
			</Button>
		{/snippet}
	</Popover.Trigger>

	<Popover.Content class="w-[--radix-popover-trigger-width] p-0" align="start">
		<!-- Selected items display -->
		{#if selectedWikipages.length > 0}
			<div class="border-b p-2">
				<div class="flex flex-wrap gap-1">
					{#each selectedWikipages as wikipage}
						<Badge variant="secondary" class="pr-1">
							<span class="text-xs">
								{getLocalizedText(wikipage.title as LocalizedText)}
							</span>
							<Button
								size="icon"
								variant="ghost"
								class="ml-1 h-4 w-4 p-0 hover:bg-transparent"
								onclick={() => removeItem(wikipage.id)}
							>
								<X class="h-2 w-2" />
							</Button>
						</Badge>
					{/each}
				</div>
			</div>
		{/if}

		<div class="border-b p-2">
			<div class="relative">
				<Search class="text-muted-foreground absolute top-2.5 left-2 h-4 w-4" />
				<Input
					type="text"
					placeholder="Search wikipages..."
					class="h-9 pl-8"
					value={search}
					oninput={(e) => handleSearch(e.currentTarget.value)}
				/>
			</div>
		</div>

		<ScrollArea class="h-[200px]">
			{#if loading}
				<div class="text-muted-foreground p-4 text-center text-sm">Loading...</div>
			{:else if wikipages.length === 0}
				<div class="text-muted-foreground p-4 text-center text-sm">
					{search ? 'No wikipages found' : 'No wikipages available'}
				</div>
			{:else}
				<div class="p-1">
					{#each wikipages as wikipage}
						{@const selected = isSelected(wikipage.id)}
						{@const isDisabled = !selected && maxItems && value.length >= maxItems}
						<button
							class="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
							onclick={() => !isDisabled && toggleSelection(wikipage)}
							disabled={Boolean(isDisabled)}
						>
							<Checkbox
								checked={Boolean(selected)}
								disabled={Boolean(isDisabled)}
								class="pointer-events-none"
							/>
							<span class="flex-1 truncate text-left">
								{getLocalizedText(wikipage.title as LocalizedText)}
							</span>
							{#if wikipage.brief}
								<span class="text-muted-foreground truncate text-xs">
									{getLocalizedText(wikipage.brief as LocalizedText)}
								</span>
							{/if}
							{#if wikipage.kind}
								<Badge variant="outline" class="ml-auto shrink-0 text-xs">
									{wikipage.kind}
								</Badge>
							{/if}
						</button>
					{/each}
				</div>
			{/if}
		</ScrollArea>

		{#if maxItems}
			<div class="text-muted-foreground border-t p-2 text-center text-xs">
				{value.length} / {maxItems} selected
			</div>
		{/if}
	</Popover.Content>
</Popover.Root>
