import type { ColumnDef } from '@tanstack/table-core';
import { createRawSnippet } from 'svelte';
import { renderComponent, renderSnippet } from '$lib/components/ui/data-table';
import { Button } from '$lib/components/ui/button';
import { Checkbox } from '$lib/components/ui/checkbox';
import ArrowUpDown from '@lucide/svelte/icons/arrow-up-down';
import type { LocalizedText, LocaleKey } from '$lib/utils/localization';
import type { Json } from '$lib/supabase/database.types';
import { getLocalizedText } from '$lib/utils/localization';

const arrowUpSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="size-4"><path d="m3 8 4-4 4 4"/><path d="m7 4v16"/></svg>`;
const arrowDownSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="size-4"><path d="m21 16-4 4-4-4"/><path d="m17 4v16"/></svg>`;

function getSortIcon(sort: false | 'asc' | 'desc') {
	if (sort === 'asc') return arrowUpSvg;
	if (sort === 'desc') return arrowDownSvg;
	return '';
}

export function createSelectColumn<T>(): ColumnDef<T> {
	return {
		id: 'select',
		header: ({ table }) =>
			renderComponent(Checkbox, {
				checked: table.getIsAllPageRowsSelected(),
				indeterminate: table.getIsSomePageRowsSelected() && !table.getIsAllPageRowsSelected(),
				onCheckedChange: (value: boolean) => table.toggleAllPageRowsSelected(!!value),
				controlledChecked: true,
				'aria-label': 'Select all'
			}),
		cell: ({ row }) =>
			renderComponent(Checkbox, {
				checked: row.getIsSelected(),
				onCheckedChange: (value: boolean) => row.toggleSelected(!!value),
				controlledChecked: true,
				'aria-label': 'Select row'
			}),
		enableSorting: false,
		enableHiding: false
	};
}

export function createSortableColumn<T>(
	id: string,
	header: string,
	accessor: (row: T) => string | number | null | undefined
): ColumnDef<T> {
	return {
		accessorKey: id,
		header: ({ column }) => {
			const sortState = column.getIsSorted();
			const headerSnippet = createRawSnippet(() => ({
				render: () => `
					<div class="flex items-center gap-2">
						${header}
						${getSortIcon(sortState)}
					</div>
				`
			}));

			return renderComponent(Button, {
				variant: 'ghost',
				onclick: () => {
					if (sortState === false) column.toggleSorting(false);
					else if (sortState === 'asc') column.toggleSorting(true);
					else column.clearSorting();
				},
				children: headerSnippet
			});
		},
		cell: ({ row }) => {
			const value = accessor(row.original);
			const cellSnippet = createRawSnippet<[string | number | null | undefined]>((getValue) => {
				const val = getValue();
				return {
					render: () => `<div>${val ?? ''}</div>`
				};
			});
			return renderSnippet(cellSnippet, value);
		},
		enableGlobalFilter: false,
		enableColumnFilter: false
	};
}

export function createLocalizedColumn<T>(
	id: string,
	header: string,
	accessor: (row: T) => LocalizedText | Json | null | undefined,
	currentLocale: LocaleKey
): ColumnDef<T> {
	return {
		accessorKey: id,
		header: ({ column }) => {
			const sortState = column.getIsSorted();
			const headerSnippet = createRawSnippet(() => ({
				render: () => `
					<div class="flex items-center gap-2">
						${header}
						${getSortIcon(sortState)}
					</div>
				`
			}));

			return renderComponent(Button, {
				variant: 'ghost',
				onclick: () => {
					if (sortState === false) column.toggleSorting(false);
					else if (sortState === 'asc') column.toggleSorting(true);
					else column.clearSorting();
				},
				children: headerSnippet
			});
		},
		cell: ({ row }) => {
			const value = accessor(row.original);
			const cellSnippet = createRawSnippet<[LocalizedText | Json | null | undefined]>(
				(getValue) => {
					const val = getValue();
					return {
						render: () =>
							`<div>${typeof val === 'object' ? getLocalizedText(val, currentLocale as LocaleKey) : ''}</div>`
					};
				}
			);
			return renderSnippet(cellSnippet, value);
		},
		enableGlobalFilter: false
	};
}

export function createDateColumn<T>(
	id: string,
	header: string,
	accessor: (row: T) => string | Date | null | undefined
): ColumnDef<T> {
	return {
		accessorKey: id,
		header: ({ column }) => {
			const sortState = column.getIsSorted();
			const headerSnippet = createRawSnippet(() => ({
				render: () => `
					<div class="flex items-center gap-2">
						${header}
						${getSortIcon(sortState)}
					</div>
				`
			}));

			return renderComponent(Button, {
				variant: 'ghost',
				onclick: () => {
					if (sortState === false) column.toggleSorting(false);
					else if (sortState === 'asc') column.toggleSorting(true);
					else column.clearSorting();
				},
				children: headerSnippet
			});
		},
		cell: ({ row }) => {
			const value = accessor(row.original);
			const cellSnippet = createRawSnippet<[string | Date | null | undefined]>((getValue) => {
				const val = getValue();
				if (!val) return { render: () => '<div></div>' };
				const date = val instanceof Date ? val : new Date(val);
				return {
					render: () => `<div>${date.toLocaleDateString()} ${date.toLocaleTimeString()}</div>`
				};
			});
			return renderSnippet(cellSnippet, value);
		},
		enableGlobalFilter: false
	};
}

export function createActionsColumn<T>(action: {
	label: string;
	onClick: (row: T) => void | Promise<void>;
	variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
	size?: 'default' | 'sm' | 'lg' | 'icon';
	icon?: any;
}): ColumnDef<T> {
	return {
		id: 'actions',
		cell: ({ row }) => {
			const actionsSnippet = createRawSnippet(() => ({
				render: () => `
					<div class="text-right whitespace-nowrap">
						${action.icon ? `<svg class="mr-2 h-4 w-4" aria-hidden="true"></svg>` : ''}
						${action.label}
					</div>
				`
			}));

			return renderComponent(Button, {
				variant: action.variant || 'ghost',
				size: action.size || 'sm',
				onclick: () => action.onClick(row.original),
				children: actionsSnippet
			});
		},
		enableSorting: false,
		enableHiding: false,
		enableResizing: false,
		size: 50,
		minSize: 50
	};
}
