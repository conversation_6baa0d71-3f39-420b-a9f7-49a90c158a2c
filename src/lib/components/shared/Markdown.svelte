<!-- Markdown Component -->
<script lang="ts">
	import { marked } from 'marked';

	interface Props {
		content: string;
		class?: string;
	}

	let { content, class: className = '' }: Props = $props();

	// Configure marked for security
	marked.setOptions({
		breaks: true,
		gfm: true
	});

	let htmlContent = $derived(marked(content || ''));
</script>

<div class={className}>
	{@html htmlContent}
</div>
