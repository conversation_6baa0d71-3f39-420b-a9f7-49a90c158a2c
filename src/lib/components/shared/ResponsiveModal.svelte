<!-- src/routes/private/product/components/ResponsiveModal.svelte -->
<script lang="ts">
	import { MediaQuery } from 'svelte/reactivity';
	import * as Dialog from '$lib/components/ui/dialog/index.js';
	import * as Drawer from '$lib/components/ui/drawer/index.js';
	import type { Snippet } from 'svelte';

	interface Props {
		open: boolean;
		title: string;
		description?: string;
		onOpenChange: (isOpen: boolean) => void;
		children: Snippet;
		header?: Snippet;
		footer?: Snippet;
	}

	let {
		open = $bindable(),
		title,
		description,
		onOpenChange,
		children,
		header,
		footer
	}: Props = $props();
	const isDesktop = new MediaQuery('(min-width: 768px)');

	// Animation duration
	const ANIMATION_DURATION = 300; // ms

	/**
	 * Handle modal state changes for both desktop and mobile
	 * Delays the onOpenChange callback when closing to allow animations to complete
	 */
	function handleStateChange(isOpen: boolean) {
		if (isOpen) {
			// For opening, update state immediately
			onOpenChange?.(true);
		} else {
			// For closing, delay the callback to allow animations to complete
			setTimeout(() => {
				onOpenChange?.(false);
			}, ANIMATION_DURATION);
		}
	}
</script>

{#if isDesktop.current}
	<Dialog.Root bind:open onOpenChange={handleStateChange}>
		<Dialog.Content
			class="flex h-[90vh] flex-col sm:max-w-[640px] md:max-w-[768px] lg:max-w-[900px]"
		>
			<Dialog.Header class="shrink-0">
				<Dialog.Title>{title}</Dialog.Title>
				{#if description}
					<Dialog.Description>
						{description}
					</Dialog.Description>
				{/if}
			</Dialog.Header>

			<!-- Custom header area if provided -->
			{#if header}
				<div class="shrink-0">
					{@render header()}
				</div>
			{/if}

			<!-- Main content area with flex-1 to take available space and min-h-0 to allow shrinking -->
			<div class="flex-1 overflow-auto">
				{@render children()}
			</div>

			{#if footer}
				<div class="w-full shrink-0 border-t pt-6">
					{@render footer()}
				</div>
			{/if}
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open onOpenChange={handleStateChange} shouldScaleBackground>
		<Drawer.Portal>
			<!-- Always include the overlay, will be shown only when drawer is open -->
			<Drawer.Content class="bg-background flex max-h-[90%]">
				<Drawer.Header class="shrink-0 px-4 text-left">
					<Drawer.Title>{title}</Drawer.Title>
					{#if description}
						<Drawer.Description>{description}</Drawer.Description>
					{/if}
				</Drawer.Header>

				<!-- Custom header area if provided -->
				{#if header}
					<div class="shrink-0 px-4">
						{@render header()}
					</div>
				{/if}

				<!-- Main content area -->
				<div class="flex-1 overflow-auto px-4 pb-20">
					{@render children()}
				</div>

				{#if footer}
					<div class="border-t">
						<Drawer.Footer>
							{@render footer()}
						</Drawer.Footer>
					</div>
				{/if}
			</Drawer.Content>
			<Drawer.Overlay class="bg-background/50 fixed inset-0 transition-all duration-300" />
		</Drawer.Portal>
	</Drawer.Root>
{/if}
