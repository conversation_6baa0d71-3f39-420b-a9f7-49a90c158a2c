<script lang="ts">
	import * as Select from '$lib/components/ui/select';

	interface Props {
		value: string;
		onValueChange: (value: string) => void;
		required?: boolean;
		class?: string;
	}

	let { value, onValueChange, required = false, class: className = '' }: Props = $props();

	const PUBLISHING_STATES = [
		{
			value: 'draft',
			label: 'Draft'
		},
		{
			value: 'ready_for_review',
			label: 'Ready for Review'
		},
		{
			value: 'published',
			label: 'Published'
		},
		{
			value: 'hidden',
			label: 'Hidden'
		}
	];

	function getStateLabel(stateValue: string) {
		return (
			PUBLISHING_STATES.find((state) => state.value === stateValue)?.label || 'Ready for Review'
		);
	}
</script>

<Select.Root
	type="single"
	{value}
	onValueChange={(val) => onValueChange(val || 'ready_for_review')}
>
	<Select.Trigger class={className}>
		{getStateLabel(value)}
	</Select.Trigger>
	<Select.Content>
		{#each PUBLISHING_STATES as state}
			<Select.Item value={state.value}>
				{state.label}
			</Select.Item>
		{/each}
	</Select.Content>
</Select.Root>
