<!-- src/lib/components/shared/LocalizedTextControl.svelte -->
<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import type { SuperForm } from 'sveltekit-superforms';
	import * as Tabs from '$lib/components/ui/tabs/index.js';

	type SupportedLanguage = 'en' | 'zh' | 'ja' | 'ko';

	interface Props {
		label: string;
		name: string;
		multiline?: boolean;
		form: SuperForm<any, any>;
		languages?: readonly SupportedLanguage[];
		required?: boolean;
	}

	let {
		label,
		name,
		multiline = false,
		languages = ['en', 'zh', 'ja', 'ko'] as const,
		form,
		required = false
	}: Props = $props();

	const { form: formData, errors, constraints } = form;

	const Component = multiline ? Textarea : Input;

	const langMap: Record<SupportedLanguage, string> = {
		en: 'English',
		zh: '中文',
		ja: '日本語',
		ko: '한국어'
	};

	const placeholderMap: Record<SupportedLanguage, string> = {
		en: `Enter ${label.toLowerCase()}`,
		zh: `输入 ${label}`,
		ja: `${label} を入力`,
		ko: `${label} 입력`
	};

	// Helper function to get nested value using path string
	function getNestedValue(obj: any, path: string, lang: string) {
		const parts = path.split('.');
		let current = obj;

		for (const part of parts) {
			if (current === undefined || current === null) {
				return undefined;
			}
			current = current[part];
		}

		return current?.[lang] || '';
	}

	// Update form data with a new structure to trigger reactivity
	function updateFormData(path: string, lang: string, value: string) {
		const parts = path.split('.');

		// Create a deep clone and update the nested structure
		const newFormData = JSON.parse(JSON.stringify($formData));

		// Navigate to the correct nested property
		let current = newFormData;
		for (let i = 0; i < parts.length - 1; i++) {
			if (!current[parts[i]]) {
				current[parts[i]] = {};
			}
			current = current[parts[i]];
		}

		const lastPart = parts[parts.length - 1];
		if (!current[lastPart]) {
			current[lastPart] = {};
		}

		// Update the language-specific value
		current[lastPart][lang] = value;

		// Trigger reactivity by assigning the new object
		$formData = newFormData;
	}

	// Check if there's an error for this field
	function hasError(errorsObj: any, path: string): boolean {
		const parts = path.split('.');
		let current = errorsObj;

		for (const part of parts) {
			if (current === undefined || current === null) {
				return false;
			}
			current = current[part];
		}

		return !!current;
	}

	// Get specific error message for a language if available
	function getErrorForLang(errorsObj: any, path: string, lang: string): string | undefined {
		const parts = path.split('.');
		let current = errorsObj;

		for (const part of parts) {
			if (current === undefined || current === null) {
				return undefined;
			}
			current = current[part];
		}

		// Check if there's a language-specific error
		if (current && current[lang]) {
			return current[lang];
		}

		// Return general error if available
		return current?._errors?.[0];
	}
</script>

<div class="space-y-1">
	<Tabs.Root value="en" class="w-full">
		<div class=" flex items-end justify-between">
			<label
				for={name}
				class="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
			>
				{label}
				{#if required}
					<span class="ml-1 text-red-500">*</span>
				{/if}
			</label>
			<Tabs.List class="flex">
				{#each languages as lang}
					<Tabs.Trigger value={lang}>{langMap[lang]}</Tabs.Trigger>
				{/each}
			</Tabs.List>
		</div>

		{#each languages as lang}
			<Tabs.Content value={lang}>
				<Component
					id={`${name}-${lang}`}
					name={`${name}[${lang}]`}
					value={getNestedValue($formData, name, lang)}
					oninput={(e) => updateFormData(name, lang, e.currentTarget.value)}
					placeholder={placeholderMap[lang]}
					class={hasError($errors, name) ? 'border-red-500' : ''}
				/>
				{#if getErrorForLang($errors, name, lang)}
					<p class="mt-1 text-xs text-red-500">{getErrorForLang($errors, name, lang)}</p>
				{/if}
			</Tabs.Content>
		{/each}
	</Tabs.Root>
</div>
