<script lang="ts">
	import { Search, X } from '@lucide/svelte';
	import { Button } from '$lib/components/ui/button';
	import * as Popover from '$lib/components/ui/popover';
	import { Input } from '$lib/components/ui/input';
	import { Badge } from '$lib/components/ui/badge';
	import { ScrollArea } from '$lib/components/ui/scroll-area';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { Database } from '$lib/supabase/database.types';
	import { cn } from '$lib/utils';

	type Wikipage = Pick<
		Database['public']['Tables']['wikipage']['Row'],
		'id' | 'title' | 'kind' | 'brief'
	>;

	interface Props {
		value?: string;
		onValueChange?: (value: string) => void;
		placeholder?: string;
		brandId: string;
		supportedKinds?: string[];
		disabled?: boolean;
		class?: string;
	}

	let {
		value = $bindable(),
		onValueChange,
		placeholder = 'Search and select',
		brandId,
		supportedKinds = [],
		disabled = false,
		class: className = ''
	}: Props = $props();

	let search = $state('');
	let wikipages = $state<Wikipage[]>([]);
	let selectedWikipage = $state<Wikipage | null>(null);
	let loading = $state(false);
	let open = $state(false);

	// Fetch wikipages when component mounts or when filters change
	$effect(() => {
		if (brandId) {
			fetchWikipages();
		}
	});

	// Fetch selected wikipage details when value changes
	$effect(() => {
		if (value && (!selectedWikipage || selectedWikipage.id !== value)) {
			fetchSelectedWikipage();
		} else if (!value) {
			selectedWikipage = null;
		}
	});

	async function fetchWikipages() {
		loading = true;
		try {
			const params = new URLSearchParams({
				brand_id: brandId
			});

			// Add kind filters
			supportedKinds.forEach((kind) => params.append('kind', kind));

			// Add search if exists
			if (search) {
				params.append('search', search);
			}

			const response = await fetch(`/api/wikipages?${params}`);
			if (!response.ok) {
				throw new Error('Failed to fetch wikipages');
			}

			wikipages = await response.json();
		} catch (error) {
			console.error('Error fetching wikipages:', error);
			wikipages = [];
		} finally {
			loading = false;
		}
	}

	async function fetchSelectedWikipage() {
		if (!value) return;

		try {
			const params = new URLSearchParams({
				brand_id: brandId
			});

			const response = await fetch(`/api/wikipages?${params}`);
			if (!response.ok) {
				throw new Error('Failed to fetch wikipages');
			}

			const allWikipages: Wikipage[] = await response.json();
			selectedWikipage = allWikipages.find((w) => w.id === value) || null;
		} catch (error) {
			console.error('Error fetching selected wikipage:', error);
		}
	}

	function handleSelect(wikipage: Wikipage) {
		value = wikipage.id;
		selectedWikipage = wikipage;
		onValueChange?.(wikipage.id);
		open = false;
		search = '';
	}

	function handleClear() {
		value = undefined;
		selectedWikipage = null;
		onValueChange?.('');
		search = '';
	}

	// Handle search with debounce
	let searchTimeout: ReturnType<typeof setTimeout>;
	function handleSearch(newSearch: string) {
		search = newSearch;
		clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			fetchWikipages();
		}, 300);
	}

	$effect(() => {
		return () => clearTimeout(searchTimeout);
	});

	// Helper to get translated titles
	function getTranslatedTitles(titleObj: LocalizedText | null | undefined): string {
		if (!titleObj) return '';

		const translations: string[] = [];
		const locales = ['cn', 'ko', 'ja'];

		for (const locale of locales) {
			if (titleObj[locale]) {
				translations.push(titleObj[locale]);
			}
		}

		return translations.join(' · ');
	}
</script>

<Popover.Root bind:open>
	<Popover.Trigger {disabled}>
		{#snippet child({ props })}
			<Button
				{...props}
				variant="outline"
				role="combobox"
				aria-expanded={open}
				class={cn('h-9 w-full justify-between text-left font-normal', className)}
				{disabled}
			>
				{#if selectedWikipage}
					<span class="truncate">
						{getLocalizedText(selectedWikipage.title as LocalizedText)}
					</span>
				{:else}
					<span class="text-muted-foreground">{placeholder}</span>
				{/if}

				{#if value && !disabled}
					<Button
						size="icon"
						variant="ghost"
						class="ml-2 h-4 w-4 shrink-0 p-0 hover:bg-transparent"
						onclick={(e) => {
							e.stopPropagation();
							handleClear();
						}}
					>
						<X class="h-3 w-3" />
					</Button>
				{:else}
					<Search class="ml-2 h-4 w-4 shrink-0 opacity-50" />
				{/if}
			</Button>
		{/snippet}
	</Popover.Trigger>

	<Popover.Content class="w-[--radix-popover-trigger-width] p-0" align="start">
		<div class="border-b p-2">
			<div class="relative">
				<Search class="text-muted-foreground absolute top-2.5 left-2 h-4 w-4" />
				<Input
					type="text"
					placeholder="Search wikipages..."
					class="h-9 pl-8"
					value={search}
					oninput={(e) => handleSearch(e.currentTarget.value)}
				/>
			</div>
		</div>

		<ScrollArea class="h-[200px]">
			{#if loading}
				<div class="text-muted-foreground p-4 text-center text-sm">Loading...</div>
			{:else if wikipages.length === 0}
				<div class="text-muted-foreground p-4 text-center text-sm">
					{search ? 'No wikipages found' : 'No wikipages available'}
				</div>
			{:else}
				<div class="p-1">
					{#each wikipages as wikipage}
						{@const translatedTitles = getTranslatedTitles(wikipage.title as LocalizedText)}
						<button
							class="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground flex w-full items-start gap-2 rounded-sm px-2 py-1.5 text-sm focus:outline-none"
							onclick={() => handleSelect(wikipage)}
						>
							<div class="flex-1 text-left">
								<div class="truncate">
									{getLocalizedText(wikipage.title as LocalizedText)}
								</div>
								{#if translatedTitles}
									<div class="text-muted-foreground truncate text-xs">
										{translatedTitles}
									</div>
								{/if}
							</div>
							{#if wikipage.brief}
								<span class="text-muted-foreground truncate text-xs">
									{getLocalizedText(wikipage.brief as LocalizedText)}
								</span>
							{/if}
						</button>
					{/each}
				</div>
			{/if}
		</ScrollArea>
	</Popover.Content>
</Popover.Root>
