<script lang="ts">
	import { Info } from '@lucide/svelte';
	import * as Popover from '$lib/components/ui/popover';
	import {
		formatTime,
		formatDateShort,
		formatSemanticDateTime,
		getBrowserTimezone,
		getTimezoneName,
		getTimezoneAbbreviation,
		isTimezoneDifferent
	} from '$lib/utils/datetime';
	import type { Snippet } from 'svelte';
	import { browser } from '$app/environment';

	interface Props {
		eventDate: string | Date;
		eventTimezone: string;
		children: Snippet;
		buttonClass?: string;
		contentClass?: string;
		showInfo?: boolean;
	}

	let {
		eventDate,
		eventTimezone,
		children,
		buttonClass = '',
		contentClass = '',
		showInfo = $bindable()
	}: Props = $props();

	// During SSR, assume user is in event timezone (99% of cases)
	// Only check browser timezone on client
	let browserTimezone = $state(eventTimezone);

	$effect(() => {
		if (browser) {
			browserTimezone = getBrowserTimezone();
		}
	});

	const timezoneDifferent = $derived(isTimezoneDifferent(eventTimezone, browserTimezone));

	// Update the bindable prop
	$effect(() => {
		showInfo = timezoneDifferent;
	});

	// Only compute expensive formatting when timezones are different
	const eventTime = $derived(
		timezoneDifferent ? formatTime(eventDate, { timeZone: eventTimezone }) : ''
	);
	const browserTime = $derived(
		timezoneDifferent ? formatTime(eventDate, { timeZone: browserTimezone }) : ''
	);

	const eventDateShort = $derived(
		timezoneDifferent ? formatDateShort(eventDate, { timeZone: eventTimezone }) : ''
	);
	const browserDateShort = $derived(
		timezoneDifferent ? formatDateShort(eventDate, { timeZone: browserTimezone }) : ''
	);

	// For browser timezone, show semantic time description
	const browserSemanticDate = $derived(
		timezoneDifferent
			? formatSemanticDateTime(eventDate, {
					timeZone: browserTimezone,
					includeTime: false,
					includeRelative: true
				})
			: ''
	);

	const eventTzAbbr = $derived(
		timezoneDifferent ? getTimezoneAbbreviation(eventTimezone, eventDate) : ''
	);
	const browserTzAbbr = $derived(
		timezoneDifferent ? getTimezoneAbbreviation(browserTimezone, eventDate) : ''
	);

	// Calculate time difference - only when needed
	function getTimeDiff(): string {
		if (!timezoneDifferent) return '';

		const eventDateObj = new Date(eventDate);

		// Create dates in each timezone
		const eventFormatter = new Intl.DateTimeFormat('en-US', {
			timeZone: eventTimezone,
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit',
			hour12: false
		});

		const browserFormatter = new Intl.DateTimeFormat('en-US', {
			timeZone: browserTimezone,
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit',
			hour12: false
		});

		// Get the actual offset by comparing the same moment in time
		const eventParts = eventFormatter.formatToParts(eventDateObj);
		const browserParts = browserFormatter.formatToParts(eventDateObj);

		// Extract components
		const getComponents = (parts: Intl.DateTimeFormatPart[]) => {
			const components: Record<string, number> = {};
			parts.forEach((part) => {
				if (part.type !== 'literal') {
					components[part.type] = parseInt(part.value);
				}
			});
			return components;
		};

		const eventComp = getComponents(eventParts);
		const browserComp = getComponents(browserParts);

		// Create UTC timestamps for comparison
		const eventTime = Date.UTC(
			eventComp.year,
			eventComp.month - 1,
			eventComp.day,
			eventComp.hour,
			eventComp.minute,
			eventComp.second || 0
		);

		const browserTime = Date.UTC(
			browserComp.year,
			browserComp.month - 1,
			browserComp.day,
			browserComp.hour,
			browserComp.minute,
			browserComp.second || 0
		);

		// Calculate the difference in hours
		const diffMs = browserTime - eventTime;
		const diffHours = Math.round(diffMs / (1000 * 60 * 60));

		if (diffHours === 0) return 'the same time as';
		const absHours = Math.abs(diffHours);
		const direction = diffHours > 0 ? 'ahead of' : 'behind';

		return `${absHours}h ${direction}`;
	}

	const timeDiff = $derived(timezoneDifferent ? getTimeDiff() : '');
</script>

{#if timezoneDifferent}
	<Popover.Root>
		<Popover.Trigger>
			<div class={buttonClass} title="Click to see timezone conversion" role="button" tabindex="0">
				{@render children()}
			</div>
		</Popover.Trigger>
		<Popover.Content class="w-60 p-3 {contentClass}">
			<div class="space-y-2.5">
				<!-- Event timezone row -->
				<div class="flex items-start justify-between">
					<div>
						<div class="text-muted-foreground/70 mb-0.5 text-[10px] tracking-wider uppercase">
							Event
						</div>
						<div class="font-mono text-base font-semibold whitespace-nowrap">{eventTime}</div>
					</div>
					<div class="text-right">
						<div class="text-xs font-medium">{eventTzAbbr}</div>
						<div class="text-muted-foreground text-[11px]">{eventDateShort}</div>
					</div>
				</div>

				<div class="border-t"></div>

				<!-- Your timezone row -->
				<div class="flex items-start justify-between">
					<div>
						<div class="text-muted-foreground/70 mb-0.5 text-[10px] tracking-wider uppercase">
							You
						</div>
						<div class="font-mono text-base font-semibold whitespace-nowrap">{browserTime}</div>
					</div>
					<div class="text-right">
						<div class="text-xs font-medium">{browserTzAbbr}</div>
						<div class="text-muted-foreground text-[11px]">{browserDateShort}</div>
						<div class="text-muted-foreground/80 mt-0.5 text-[10px]">{browserSemanticDate}</div>
					</div>
				</div>

				<div
					class="bg-muted/40 text-muted-foreground mt-1.5 rounded px-2 py-1 text-center text-[11px]"
				>
					{timeDiff} event time
				</div>
			</div>
		</Popover.Content>
	</Popover.Root>
{:else}
	<!-- If timezones are the same, just render children without wrapper -->
	{@render children()}
{/if}
