<script lang="ts">
	import { Check, ChevronsUpDown } from '@lucide/svelte';
	import { tick } from 'svelte';
	import * as Command from '$lib/components/ui/command';
	import * as Popover from '$lib/components/ui/popover';
	import { Button } from '$lib/components/ui/button';
	import { cn } from '$lib/utils';

	interface Option {
		value: string;
		label: string;
	}

	interface Props {
		options: Option[];
		value?: string;
		onValueChange?: (value: string) => void;
		placeholder?: string;
		searchPlaceholder?: string;
		emptyText?: string;
		class?: string;
		name?: string;
		disabled?: boolean;
	}

	let {
		options = [],
		value = $bindable(''),
		onValueChange,
		placeholder = 'Select an option...',
		searchPlaceholder = 'Search options...',
		emptyText = 'No options found.',
		class: className,
		name,
		disabled = false
	}: Props = $props();

	let open = $state(false);
	let triggerRef = $state<HTMLButtonElement>(null!);

	const selectedOption = $derived(options.find((opt) => opt.value === value));

	// We want to refocus the trigger button when the user selects
	// an item from the list so users can continue navigating the
	// rest of the form with the keyboard.
	function closeAndFocusTrigger() {
		open = false;
		tick().then(() => {
			triggerRef.focus();
		});
	}

	function handleSelect(selectedValue: string) {
		value = selectedValue;
		onValueChange?.(selectedValue);
		closeAndFocusTrigger();
	}
</script>

<Popover.Root bind:open>
	<Popover.Trigger bind:ref={triggerRef} {disabled}>
		{#snippet child({ props })}
			<Button
				variant="outline"
				class={cn('w-full justify-between', className)}
				{...props}
				role="combobox"
				aria-expanded={open}
				{disabled}
			>
				<span class={cn(!selectedOption && 'text-muted-foreground')}>
					{selectedOption?.label || placeholder}
				</span>
				<ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
			</Button>
		{/snippet}
	</Popover.Trigger>
	<Popover.Content class="w-[300px] p-0">
		<Command.Root>
			<Command.Input placeholder={searchPlaceholder} />
			<Command.List>
				<Command.Empty>{emptyText}</Command.Empty>
				<Command.Group>
					{#each options as option (option.value)}
						<Command.Item value={option.value} onSelect={() => handleSelect(option.value)}>
							<Check
								class={cn('mr-2 h-4 w-4', value === option.value ? 'opacity-100' : 'opacity-0')}
							/>
							{option.label}
						</Command.Item>
					{/each}
				</Command.Group>
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>

{#if name}
	<input type="hidden" {name} {value} />
{/if}
