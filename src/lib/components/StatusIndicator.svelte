<script lang="ts">
  import { Clock, Zap } from '@lucide/svelte';

  let { status } = $props<{
    status: 'in-progress' | 'upcoming';
  }>();
</script>

{#if status === 'in-progress'}
  <div class="flex items-center gap-2 text-[#FF9500]">
    <Zap class="h-5 w-5" />
    <span class="font-medium">In progress</span>
  </div>
{:else}
  <div class="flex items-center gap-2 text-[#8E8E93]">
    <Clock class="h-5 w-5" />
    <span>Starts in 1:39:23</span>
  </div>
{/if}
