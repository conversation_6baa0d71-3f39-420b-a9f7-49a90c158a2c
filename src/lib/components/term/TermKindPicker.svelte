<script lang="ts">
	import * as Select from '$lib/components/ui/select';
	import { onMount } from 'svelte';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { LocalizedText } from '$lib/utils/localization';

	interface TermKind {
		id: string;
		title: LocalizedText;
		subtitle: LocalizedText;
		group_id: string;
		semantic_order: number;
	}

	interface Props {
		value?: string;
		onValueChange?: (value: string | undefined) => void;
		required?: boolean;
		disabled?: boolean;
		placeholder?: string;
	}

	let {
		value = $bindable(),
		onValueChange,
		required = false,
		disabled = false,
		placeholder = 'Select a document type'
	}: Props = $props();

	let termKinds = $state<TermKind[]>([]);
	let loading = $state(true);
	let currentLocale: 'en' | 'zh' | 'ja' | 'ko' = $state('en');

	onMount(async () => {
		try {
			const response = await fetch('/api/term-kinds');
			const data = await response.json();
			if (data.termKinds) {
				termKinds = data.termKinds;
			}
		} catch (error) {
			console.error('Failed to load term kinds:', error);
		} finally {
			loading = false;
		}
	});

	// Handle value changes
	$effect(() => {
		onValueChange?.(value);
	});

	// Group term kinds by group_id
	const groupedTermKinds = $derived(() => {
		const groups: Record<string, TermKind[]> = {};
		termKinds.forEach((kind) => {
			if (!groups[kind.group_id]) {
				groups[kind.group_id] = [];
			}
			groups[kind.group_id].push(kind);
		});
		return groups;
	});

	// Group labels
	const groupLabels: Record<string, string> = {
		legal: 'Legal Documents',
		health: 'Health & Safety',
		media: 'Media & Privacy',
		financial: 'Financial & Business',
		conduct: 'Behavioral & Conduct',
		minors: 'Minors & Guardians',
		property: 'Equipment & Property',
		professional: 'Professional Services',
		insurance: 'Insurance & Indemnity',
		events: 'Specialized Services',
		general: 'General'
	};
</script>

<Select.Root type="single" {disabled} {required} bind:value>
	<Select.Trigger class="w-full">
		{#if value}
			{@const selectedKind = termKinds.find((k) => k.id === value)}
			{#if selectedKind}
				{getLocalizedText(selectedKind.title, currentLocale)}
			{:else}
				{placeholder}
			{/if}
		{:else}
			{placeholder}
		{/if}
	</Select.Trigger>
	<Select.Content>
		{#if loading}
			<Select.Item value="loading" disabled>Loading...</Select.Item>
		{:else}
			{#each Object.entries(groupedTermKinds()) as [groupId, kinds]}
				<!-- Group Header -->
				<div class="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
					{groupLabels[groupId] || groupId}
				</div>
				{#each kinds as kind}
					<Select.Item value={kind.id} class="cursor-pointer">
						<div class="flex flex-col">
							<span class="font-medium">{getLocalizedText(kind.title, currentLocale)}</span>
							<span class="text-xs text-muted-foreground"
								>{getLocalizedText(kind.subtitle, currentLocale)}</span
							>
						</div>
					</Select.Item>
				{/each}
			{/each}
		{/if}
	</Select.Content>
</Select.Root>
