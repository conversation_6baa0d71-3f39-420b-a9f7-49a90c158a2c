import type { SpotifySearchResponse } from '../../routes/product/types';

const SPOTIFY_API_BASE = 'https://api.spotify.com/v1';
const SPOTIFY_TOKEN_URL = 'https://accounts.spotify.com/api/token';
const CLIENT_ID = '0340647e846649118066a63027143458';
const CLIENT_SECRET = '31e1e04276cb467ab020abc33c1921a1';

let accessToken: string | null = null;
let tokenExpiry: number | null = null;

export async function getAccessToken() {
	if (accessToken && tokenExpiry && Date.now() < tokenExpiry) {
		return accessToken;
	}

	const response = await fetch(SPOTIFY_TOKEN_URL, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
			Authorization: `Basic ${btoa(`${CLIENT_ID}:${CLIENT_SECRET}`)}`
		},
		body: 'grant_type=client_credentials'
	});

	if (!response.ok) {
		throw new Error('Failed to get Spotify access token');
	}

	const data = await response.json();
	accessToken = data.access_token;
	tokenExpiry = Date.now() + data.expires_in * 1000;
	return accessToken;
}

export async function searchSpotify(
	query: string,
	type: 'track' | 'artist'
): Promise<SpotifySearchResponse> {
	const token = await getAccessToken();

	const response = await fetch(
		`${SPOTIFY_API_BASE}/search?q=${encodeURIComponent(query)}&type=${type}&limit=10`,
		{
			headers: {
				Authorization: `Bearer ${token}`
			}
		}
	);

	if (!response.ok) {
		throw new Error('Failed to search Spotify');
	}

	return response.json();
}
