// Extract timezone from event data structure
export function getEventTimezone(event: any): string | null {
	// First check direct landmark
	if (event.landmark?.address) {
		const address = Array.isArray(event.landmark.address)
			? event.landmark.address[0]
			: event.landmark.address;
		if (address?.time_zone) {
			return address.time_zone;
		}
	}

	// Then check space landmark
	if (event.space?.landmark?.address) {
		const spaceLandmark = Array.isArray(event.space.landmark)
			? event.space.landmark[0]
			: event.space.landmark;
		const address = Array.isArray(spaceLandmark?.address)
			? spaceLandmark.address[0]
			: spaceLandmark?.address;
		if (address?.time_zone) {
			return address.time_zone;
		}
	}

	// Fallback to browser timezone if no timezone found
	return null;
}
