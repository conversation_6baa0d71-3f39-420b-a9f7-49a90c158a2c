import type { J<PERSON> } from '$lib/supabase/database.types';
import { getLocale } from '$lib/paraglide/runtime';

// Define type for standard localized text with required English
export type LocalizedText = {
	[key: string]: string;
};

export type LocaleKey = 'en' | 'ja' | 'ko' | 'zh';

const FALLBACK_ORDER: LocaleKey[] = ['en', 'zh', 'ko', 'ja'];

/**
 * Helper function to convert Json data from database to a valid LocalizedText object
 * with standard language keys (en, zh, ja, ko)
 *
 * @param value Source JSON data from database that might be incomplete or null
 * @param defaultValue Optional default value for empty strings (defaults to '')
 * @returns A complete LocalizedText object with all language keys
 */
export function ensureLocalizedText(value?: Json | null, defaultValue = ''): LocalizedText {
	if (!value || typeof value !== 'object') {
		return { en: defaultValue };
	}

	const textObj = value as Record<string, string>;
	return {
		en: textObj.en || defaultValue,
		zh: textObj.zh || defaultValue,
		ja: textObj.ja || defaultValue,
		ko: textObj.ko || defaultValue
	};
}

/**
 * Gets the localized text from a JSONB column based on user's locale
 * @param text The localized text object from JSONB column
 * @param locale The user's preferred locale
 * @returns The localized text string or '--' if not found
 */
export function getLocalizedText(
	text: Json | string | null | undefined,
	locale: LocaleKey = getLocale() as LocaleKey
): string {
	if (!text || typeof text !== 'object' || Array.isArray(text)) {
		return typeof text === 'string' ? text : '--';
	}

	const record = text as Record<string, string>;
	if (record[locale]) return record[locale];

	for (const fallbackLocale of FALLBACK_ORDER) {
		if (record[fallbackLocale]) return record[fallbackLocale];
	}

	return '--';
}
