export interface DateTimeFormatOptions {
	timeZone?: string;
	locale?: string;
}

// Get browser timezone
export function getBrowserTimezone(): string {
	return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

// Format time in specific timezone (e.g., "3:30 PM")
export function formatTime(date: Date | string, options: DateTimeFormatOptions = {}): string {
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	const formatter = new Intl.DateTimeFormat(options.locale || 'en-US', {
		hour: 'numeric',
		minute: '2-digit',
		hour12: true,
		timeZone: options.timeZone
	});

	return formatter.format(dateObj);
}

// Format date and time (e.g., "Sat, Jan 20 • 3:30 PM")
export function formatDateTime(date: Date | string, options: DateTimeFormatOptions = {}): string {
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	const dateFormatter = new Intl.DateTimeFormat(options.locale || 'en-US', {
		weekday: 'short',
		month: 'short',
		day: 'numeric',
		timeZone: options.timeZone
	});

	const timeStr = formatTime(dateObj, options);
	const dateStr = dateFormatter.format(dateObj);

	return `${dateStr} • ${timeStr}`;
}

// Format full date with time range (e.g., "Saturday, January 20, 3:30pm - 4:30pm")
export function formatDateTimeRange(
	startDate: Date | string,
	endDate: Date | string,
	options: DateTimeFormatOptions = {}
): string {
	const startObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
	const endObj = typeof endDate === 'string' ? new Date(endDate) : endDate;

	const dateFormatter = new Intl.DateTimeFormat(options.locale || 'en-US', {
		weekday: 'long',
		month: 'long',
		day: 'numeric',
		timeZone: options.timeZone
	});

	const startTime = formatTime(startObj, options).toLowerCase();
	const endTime = formatTime(endObj, options).toLowerCase();

	return `${dateFormatter.format(startObj)}, ${startTime} - ${endTime}`;
}

// Format date (e.g., "Saturday, January 20, 2024")
export function formatDate(
	date: Date | string,
	options: DateTimeFormatOptions & { includeYear?: boolean } = {}
): string {
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	const formatOptions: Intl.DateTimeFormatOptions = {
		weekday: 'long',
		month: 'long',
		day: 'numeric',
		timeZone: options.timeZone
	};

	if (options.includeYear !== false) {
		formatOptions.year = 'numeric';
	}

	const formatter = new Intl.DateTimeFormat(options.locale || 'en-US', formatOptions);

	return formatter.format(dateObj);
}

// Get timezone offset difference in hours
export function getTimezoneOffsetHours(
	date: Date | string,
	timezone1: string,
	timezone2: string
): number {
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	// Create formatters for both timezones
	const formatter1 = new Intl.DateTimeFormat('en-US', {
		timeZone: timezone1,
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit',
		hour12: false
	});

	const formatter2 = new Intl.DateTimeFormat('en-US', {
		timeZone: timezone2,
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit',
		hour12: false
	});

	// Parse the formatted dates
	const parts1 = formatter1.formatToParts(dateObj);
	const parts2 = formatter2.formatToParts(dateObj);

	// Extract date components
	const getDateFromParts = (parts: Intl.DateTimeFormatPart[]) => {
		const values: { [key: string]: string } = {};
		parts.forEach((part) => {
			if (part.type !== 'literal') {
				values[part.type] = part.value;
			}
		});

		return new Date(
			parseInt(values.year),
			parseInt(values.month) - 1,
			parseInt(values.day),
			parseInt(values.hour),
			parseInt(values.minute)
		);
	};

	const date1 = getDateFromParts(parts1);
	const date2 = getDateFromParts(parts2);

	// Calculate difference in hours
	const diffMs = date2.getTime() - date1.getTime();
	const diffHours = diffMs / (1000 * 60 * 60);

	return Math.round(diffHours);
}

// Get friendly timezone name (e.g., "Pacific Standard Time")
export function getTimezoneName(timezone: string, date: Date | string = new Date()): string {
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	const formatter = new Intl.DateTimeFormat('en-US', {
		timeZone: timezone,
		timeZoneName: 'long'
	});

	const parts = formatter.formatToParts(dateObj);
	const timeZonePart = parts.find((part) => part.type === 'timeZoneName');

	return timeZonePart?.value || timezone;
}

// Get short timezone name (e.g., "PST")
export function getTimezoneAbbreviation(
	timezone: string,
	date: Date | string = new Date()
): string {
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	const formatter = new Intl.DateTimeFormat('en-US', {
		timeZone: timezone,
		timeZoneName: 'short'
	});

	const parts = formatter.formatToParts(dateObj);
	const timeZonePart = parts.find((part) => part.type === 'timeZoneName');

	return timeZonePart?.value || timezone;
}

// Check if two timezones are different
export function isTimezoneDifferent(timezone1: string, timezone2: string): boolean {
	// Normalize timezone names to handle edge cases
	const tz1 = timezone1.trim();
	const tz2 = timezone2.trim();

	return tz1 !== tz2;
}

// Format time difference in a human-readable way
export function formatTimeDifference(hours: number): string {
	const absHours = Math.abs(hours);
	const direction = hours > 0 ? 'ahead' : 'behind';

	if (absHours === 0) return 'Same time';
	if (absHours === 1) return `1 hour ${direction}`;
	return `${absHours} hours ${direction}`;
}

// Format date with day of week (e.g., "Fri, June 20")
export function formatDateShort(date: Date | string, options: DateTimeFormatOptions = {}): string {
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	const formatter = new Intl.DateTimeFormat(options.locale || 'en-US', {
		weekday: 'short',
		month: 'long',
		day: 'numeric',
		timeZone: options.timeZone
	});

	return formatter.format(dateObj);
}

// Format full date and time for timezone comparison (e.g., "Friday, June 20 at 7:00 PM")
export function formatDateTimeFull(
	date: Date | string,
	options: DateTimeFormatOptions = {}
): string {
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	const dateFormatter = new Intl.DateTimeFormat(options.locale || 'en-US', {
		weekday: 'long',
		month: 'long',
		day: 'numeric',
		timeZone: options.timeZone
	});

	const timeFormatter = new Intl.DateTimeFormat(options.locale || 'en-US', {
		hour: 'numeric',
		minute: '2-digit',
		hour12: true,
		timeZone: options.timeZone
	});

	return `${dateFormatter.format(dateObj)} at ${timeFormatter.format(dateObj)}`;
}

// Semantic date/time formatting (e.g., "Tomorrow night", "In 3 hours", "Yesterday noon")
export function formatSemanticDateTime(
	date: Date | string,
	options: DateTimeFormatOptions & {
		now?: Date;
		includeTime?: boolean;
		includeRelative?: boolean;
	} = {}
): string {
	const dateObj = typeof date === 'string' ? new Date(date) : date;
	const now = options.now || new Date();
	const { timeZone, locale = 'en-US', includeTime = true, includeRelative = true } = options;

	// Adjust for timezone if provided
	let targetDate = dateObj;
	let compareDate = now;

	if (timeZone) {
		// Get the date in the target timezone
		const formatter = new Intl.DateTimeFormat(locale, {
			timeZone,
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			hour12: false
		});

		const parts = formatter.formatToParts(dateObj);
		const dateParts: Record<string, number> = {};
		parts.forEach((part) => {
			if (part.type !== 'literal') {
				dateParts[part.type] = parseInt(part.value);
			}
		});

		targetDate = new Date(
			dateParts.year,
			dateParts.month - 1,
			dateParts.day,
			dateParts.hour,
			dateParts.minute
		);

		// Also adjust 'now' to the same timezone for comparison
		const nowParts = formatter.formatToParts(now);
		const nowDateParts: Record<string, number> = {};
		nowParts.forEach((part) => {
			if (part.type !== 'literal') {
				nowDateParts[part.type] = parseInt(part.value);
			}
		});

		compareDate = new Date(
			nowDateParts.year,
			nowDateParts.month - 1,
			nowDateParts.day,
			nowDateParts.hour,
			nowDateParts.minute
		);
	}

	// Calculate differences
	const diffMs = targetDate.getTime() - compareDate.getTime();
	const diffMins = Math.round(diffMs / (1000 * 60));
	const diffHours = Math.round(diffMs / (1000 * 60 * 60));

	// Calculate calendar day difference
	const targetDateOnly = new Date(
		targetDate.getFullYear(),
		targetDate.getMonth(),
		targetDate.getDate()
	);
	const compareDateOnly = new Date(
		compareDate.getFullYear(),
		compareDate.getMonth(),
		compareDate.getDate()
	);
	const diffDays = Math.round(
		(targetDateOnly.getTime() - compareDateOnly.getTime()) / (1000 * 60 * 60 * 24)
	);

	// Get time period
	const timeOfDay = getTimeOfDay(targetDate);
	const timeStr = includeTime ? formatTime(dateObj, { timeZone, locale }) : '';

	// Check if same day
	if (isSameDay(targetDate, compareDate)) {
		// For today, show relative time if within reasonable range
		if (includeRelative) {
			// Within 12 hours, show relative time
			const absDiffHours = Math.abs(diffHours);
			if (absDiffHours <= 12) {
				if (diffMins === 0) return 'now';
				if (diffMins > 0) {
					if (diffMins < 60) return `in ${diffMins} min${diffMins !== 1 ? 's' : ''}`;
					return `in ${diffHours} hour${diffHours !== 1 ? 's' : ''}`;
				}
				if (diffMins < 0) {
					if (Math.abs(diffMins) < 60)
						return `${Math.abs(diffMins)} min${Math.abs(diffMins) !== 1 ? 's' : ''} ago`;
					return `${Math.abs(diffHours)} hour${Math.abs(diffHours) !== 1 ? 's' : ''} ago`;
				}
			}
			// For times further away today, show time of day
			return `this ${timeOfDay}`;
		}
		return 'Today';
	}

	// Check if tomorrow
	const tomorrow = new Date(compareDate);
	tomorrow.setDate(tomorrow.getDate() + 1);
	if (isSameDay(targetDate, tomorrow)) {
		if (includeTime) {
			return `Tomorrow ${timeOfDay}${timeStr ? ` at ${timeStr}` : ''}`;
		}
		return 'Tomorrow';
	}

	// Check if yesterday
	const yesterday = new Date(compareDate);
	yesterday.setDate(yesterday.getDate() - 1);
	if (isSameDay(targetDate, yesterday)) {
		if (includeTime) {
			return `Yesterday ${timeOfDay}${timeStr ? ` at ${timeStr}` : ''}`;
		}
		return 'Yesterday';
	}

	// Within a week - show day name with context
	if (Math.abs(diffDays) <= 7) {
		const dayName = new Intl.DateTimeFormat(locale, {
			weekday: 'long',
			timeZone
		}).format(dateObj);

		// Add context based on how far away it is
		if (diffDays > 0) {
			// Future
			if (diffDays === 2) {
				return includeTime ? `The day after tomorrow ${timeOfDay}` : 'The day after tomorrow';
			}
			if (diffDays <= 3) {
				return includeTime ? `This ${dayName} ${timeOfDay}` : `This ${dayName}`;
			}
			return includeTime ? `Next ${dayName} ${timeOfDay}` : `Next ${dayName}`;
		} else {
			// Past
			if (diffDays === -2) {
				return includeTime ? `The day before yesterday ${timeOfDay}` : 'The day before yesterday';
			}
			if (diffDays >= -3) {
				return includeTime ? `This past ${dayName} ${timeOfDay}` : `This past ${dayName}`;
			}
			return includeTime ? `Last ${dayName} ${timeOfDay}` : `Last ${dayName}`;
		}
	}

	// Otherwise show full date
	const dateStr = formatDateShort(dateObj, { timeZone, locale });
	if (includeTime) {
		return `${dateStr} at ${timeStr}`;
	}
	return dateStr;
}

// Get semantic time of day
function getTimeOfDay(date: Date): string {
	const hours = date.getHours();

	if (hours >= 4 && hours < 12) return 'morning';
	if (hours >= 12 && hours < 17) return 'afternoon';
	if (hours >= 17 && hours < 21) return 'evening';
	return 'night';
}

// Check if two dates are the same day
function isSameDay(date1: Date, date2: Date): boolean {
	return (
		date1.getFullYear() === date2.getFullYear() &&
		date1.getMonth() === date2.getMonth() &&
		date1.getDate() === date2.getDate()
	);
}

// Get relative time description (e.g., "5 mins ago", "in 2 hours")
export function getRelativeTime(
	date: Date | string,
	options: DateTimeFormatOptions & { now?: Date } = {}
): string {
	const dateObj = typeof date === 'string' ? new Date(date) : date;
	const now = options.now || new Date();

	const diffMs = dateObj.getTime() - now.getTime();
	const absDiffMs = Math.abs(diffMs);

	// Less than a minute
	if (absDiffMs < 60 * 1000) {
		return 'now';
	}

	// Less than an hour
	if (absDiffMs < 60 * 60 * 1000) {
		const mins = Math.round(absDiffMs / (60 * 1000));
		if (diffMs > 0) return `in ${mins} min${mins !== 1 ? 's' : ''}`;
		return `${mins} min${mins !== 1 ? 's' : ''} ago`;
	}

	// Less than a day
	if (absDiffMs < 24 * 60 * 60 * 1000) {
		const hours = Math.round(absDiffMs / (60 * 60 * 1000));
		if (diffMs > 0) return `in ${hours} hour${hours !== 1 ? 's' : ''}`;
		return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
	}

	// Less than a week
	if (absDiffMs < 7 * 24 * 60 * 60 * 1000) {
		const days = Math.round(absDiffMs / (24 * 60 * 60 * 1000));
		if (diffMs > 0) return `in ${days} day${days !== 1 ? 's' : ''}`;
		return `${days} day${days !== 1 ? 's' : ''} ago`;
	}

	// More than a week
	const weeks = Math.round(absDiffMs / (7 * 24 * 60 * 60 * 1000));
	if (diffMs > 0) return `in ${weeks} week${weeks !== 1 ? 's' : ''}`;
	return `${weeks} week${weeks !== 1 ? 's' : ''} ago`;
}
