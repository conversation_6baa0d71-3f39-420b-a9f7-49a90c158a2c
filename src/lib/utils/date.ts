import {
	format,
	addDays,
	startOfWeek,
	endOfWeek,
	eachDayOfInterval,
	addWeeks,
	isSameMonth
} from 'date-fns';
import type { WeekDate } from '$lib/types/class';

export function generateWeekDates(currentDate: Date): WeekDate[] {
	// Get start and end dates for two weeks
	const start = startOfWeek(currentDate);
	const end = endOfWeek(addWeeks(start, 1));

	// Generate all dates in the interval
	return eachDayOfInterval({ start, end }).map((date) => ({
		date: date.getDate(),
		day: format(date, 'EEE'),
		isToday: format(date, 'yyyy-MM-dd') === format(currentDate, 'yyyy-MM-dd'),
		hasEvents: [10, 11, 12, 13, 14, 17, 18, 19, 20, 21].includes(date.getDate()),
		isCurrentMonth: isSameMonth(date, currentDate)
	}));
}

export function formatMonthYear(date: Date): string {
	return format(date, 'MMM yyyy');
}
