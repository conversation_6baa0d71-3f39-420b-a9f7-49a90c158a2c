/**
 * Format a currency value
 */
export function formatCurrency(value: number | null | undefined, currencyCode = 'USD'): string {
	if (value === null || value === undefined) return '-';

	return new Intl.NumberFormat('en-US', {
		style: 'currency',
		currency: currencyCode
	}).format(value);
}

/**
 * Format a date as YYYY-MM-DD
 */
export function formatDate(date: string | Date | null | undefined): string {
	if (!date) return '-';

	const d = typeof date === 'string' ? new Date(date) : date;
	return d.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'short',
		day: 'numeric'
	});
}

/**
 * Format a date with time
 */
export function formatDateTime(date: string | Date | null | undefined): string {
	if (!date) return '-';

	const d = typeof date === 'string' ? new Date(date) : date;
	return d.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'short',
		day: 'numeric',
		hour: '2-digit',
		minute: '2-digit'
	});
}
