// Extract unique semantic colors from event data structure
export function getEventSemanticColors(event: any): string[] {
	const colors = new Set<string>();

	// Extract colors from all products and their product_prices
	if (event.products && Array.isArray(event.products)) {
		for (const eventProduct of event.products) {
			const product = eventProduct.product;
			if (product?.product_prices && Array.isArray(product.product_prices)) {
				for (const productPrice of product.product_prices) {
					if (
						productPrice.color_primary_semantic &&
						typeof productPrice.color_primary_semantic === 'string'
					) {
						colors.add(productPrice.color_primary_semantic.toLowerCase().trim());
					}
				}
			}
		}
	}

	return Array.from(colors).filter(Boolean);
}

// Generate modern glow box-shadow based on semantic colors
export function getEventGlowClasses(colors: string[]): string {
	if (colors.length === 0) {
		// Default subtle glow
		return 'shadow-[0_4px_14px_0_rgba(156,163,175,0.3)] hover:shadow-[0_6px_20px_rgba(156,163,175,0.2)]';
	}

	if (colors.length === 1) {
		const color = colors[0];
		// Create modern glow effect with color-specific shadows
		switch (color) {
			case 'blue':
				return 'shadow-[0_4px_14px_0_rgba(59,130,246,0.4)] hover:shadow-[0_6px_20px_rgba(59,130,246,0.3)]';
			case 'red':
				return 'shadow-[0_4px_14px_0_rgba(239,68,68,0.4)] hover:shadow-[0_6px_20px_rgba(239,68,68,0.3)]';
			case 'green':
				return 'shadow-[0_4px_14px_0_rgba(34,197,94,0.4)] hover:shadow-[0_6px_20px_rgba(34,197,94,0.3)]';
			case 'yellow':
				return 'shadow-[0_4px_14px_0_rgba(234,179,8,0.4)] hover:shadow-[0_6px_20px_rgba(234,179,8,0.3)]';
			case 'purple':
				return 'shadow-[0_4px_14px_0_rgba(147,51,234,0.4)] hover:shadow-[0_6px_20px_rgba(147,51,234,0.3)]';
			case 'pink':
				return 'shadow-[0_4px_14px_0_rgba(236,72,153,0.4)] hover:shadow-[0_6px_20px_rgba(236,72,153,0.3)]';
			case 'indigo':
				return 'shadow-[0_4px_14px_0_rgba(99,102,241,0.4)] hover:shadow-[0_6px_20px_rgba(99,102,241,0.3)]';
			case 'cyan':
				return 'shadow-[0_4px_14px_0_rgba(6,182,212,0.4)] hover:shadow-[0_6px_20px_rgba(6,182,212,0.3)]';
			case 'teal':
				return 'shadow-[0_4px_14px_0_rgba(20,184,166,0.4)] hover:shadow-[0_6px_20px_rgba(20,184,166,0.3)]';
			case 'orange':
				return 'shadow-[0_4px_14px_0_rgba(249,115,22,0.4)] hover:shadow-[0_6px_20px_rgba(249,115,22,0.3)]';
			case 'amber':
				return 'shadow-[0_4px_14px_0_rgba(245,158,11,0.4)] hover:shadow-[0_6px_20px_rgba(245,158,11,0.3)]';
			case 'lime':
				return 'shadow-[0_4px_14px_0_rgba(132,204,22,0.4)] hover:shadow-[0_6px_20px_rgba(132,204,22,0.3)]';
			case 'emerald':
				return 'shadow-[0_4px_14px_0_rgba(16,185,129,0.4)] hover:shadow-[0_6px_20px_rgba(16,185,129,0.3)]';
			case 'sky':
				return 'shadow-[0_4px_14px_0_rgba(14,165,233,0.4)] hover:shadow-[0_6px_20px_rgba(14,165,233,0.3)]';
			case 'violet':
				return 'shadow-[0_4px_14px_0_rgba(139,92,246,0.4)] hover:shadow-[0_6px_20px_rgba(139,92,246,0.3)]';
			case 'fuchsia':
				return 'shadow-[0_4px_14px_0_rgba(217,70,239,0.4)] hover:shadow-[0_6px_20px_rgba(217,70,239,0.3)]';
			case 'rose':
				return 'shadow-[0_4px_14px_0_rgba(244,63,94,0.4)] hover:shadow-[0_6px_20px_rgba(244,63,94,0.3)]';
			case 'slate':
				return 'shadow-[0_4px_14px_0_rgba(100,116,139,0.4)] hover:shadow-[0_6px_20px_rgba(100,116,139,0.3)]';
			case 'zinc':
				return 'shadow-[0_4px_14px_0_rgba(113,113,122,0.4)] hover:shadow-[0_6px_20px_rgba(113,113,122,0.3)]';
			case 'neutral':
				return 'shadow-[0_4px_14px_0_rgba(115,115,115,0.4)] hover:shadow-[0_6px_20px_rgba(115,115,115,0.3)]';
			case 'stone':
				return 'shadow-[0_4px_14px_0_rgba(120,113,108,0.4)] hover:shadow-[0_6px_20px_rgba(120,113,108,0.3)]';
			default:
				// Fallback for unknown colors
				return 'shadow-[0_4px_14px_0_rgba(156,163,175,0.4)] hover:shadow-[0_6px_20px_rgba(156,163,175,0.3)]';
		}
	}

	// For multiple colors, create a multi-color glow effect
	if (colors.length >= 2) {
		// Use a beautiful multi-color glow that combines colors
		return 'shadow-[0_4px_14px_0_rgba(99,102,241,0.3),0_4px_14px_0_rgba(236,72,153,0.2)] hover:shadow-[0_6px_20px_rgba(99,102,241,0.2),0_6px_20px_rgba(236,72,153,0.15)]';
	}

	return 'shadow-[0_4px_14px_0_rgba(156,163,175,0.3)] hover:shadow-[0_6px_20px_rgba(156,163,175,0.2)]';
}

// Generate Tailwind gradient classes based on semantic colors with dark/light mode support
export function getEventGradientClasses(colors: string[]): string {
	if (colors.length === 0) {
		// Default gray gradient - responsive to light/dark mode
		return 'bg-gradient-to-br from-gray-200 via-gray-300 to-gray-400 hover:from-gray-300 hover:via-gray-400 hover:to-gray-500 dark:from-gray-600 dark:via-gray-700 dark:to-gray-800 dark:hover:from-gray-500 dark:hover:via-gray-600 dark:hover:to-gray-700';
	}

	if (colors.length === 1) {
		const color = colors[0];
		// Single color with adaptive intensity for light/dark modes
		switch (color) {
			case 'blue':
				return 'bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 hover:from-blue-500 hover:via-blue-600 hover:to-blue-700 dark:from-blue-500 dark:via-blue-600 dark:to-blue-700 dark:hover:from-blue-400 dark:hover:via-blue-500 dark:hover:to-blue-600';
			case 'red':
				return 'bg-gradient-to-br from-red-400 via-red-500 to-red-600 hover:from-red-500 hover:via-red-600 hover:to-red-700 dark:from-red-500 dark:via-red-600 dark:to-red-700 dark:hover:from-red-400 dark:hover:via-red-500 dark:hover:to-red-600';
			case 'green':
				return 'bg-gradient-to-br from-green-400 via-green-500 to-green-600 hover:from-green-500 hover:via-green-600 hover:to-green-700 dark:from-green-500 dark:via-green-600 dark:to-green-700 dark:hover:from-green-400 dark:hover:via-green-500 dark:hover:to-green-600';
			case 'yellow':
				return 'bg-gradient-to-br from-yellow-300 via-yellow-400 to-yellow-500 hover:from-yellow-400 hover:via-yellow-500 hover:to-yellow-600 dark:from-yellow-500 dark:via-yellow-600 dark:to-yellow-700 dark:hover:from-yellow-400 dark:hover:via-yellow-500 dark:hover:to-yellow-600';
			case 'purple':
				return 'bg-gradient-to-br from-purple-400 via-purple-500 to-purple-600 hover:from-purple-500 hover:via-purple-600 hover:to-purple-700 dark:from-purple-500 dark:via-purple-600 dark:to-purple-700 dark:hover:from-purple-400 dark:hover:via-purple-500 dark:hover:to-purple-600';
			case 'pink':
				return 'bg-gradient-to-br from-pink-400 via-pink-500 to-pink-600 hover:from-pink-500 hover:via-pink-600 hover:to-pink-700 dark:from-pink-500 dark:via-pink-600 dark:to-pink-700 dark:hover:from-pink-400 dark:hover:via-pink-500 dark:hover:to-pink-600';
			case 'indigo':
				return 'bg-gradient-to-br from-indigo-400 via-indigo-500 to-indigo-600 hover:from-indigo-500 hover:via-indigo-600 hover:to-indigo-700 dark:from-indigo-500 dark:via-indigo-600 dark:to-indigo-700 dark:hover:from-indigo-400 dark:hover:via-indigo-500 dark:hover:to-indigo-600';
			case 'cyan':
				return 'bg-gradient-to-br from-cyan-400 via-cyan-500 to-cyan-600 hover:from-cyan-500 hover:via-cyan-600 hover:to-cyan-700 dark:from-cyan-500 dark:via-cyan-600 dark:to-cyan-700 dark:hover:from-cyan-400 dark:hover:via-cyan-500 dark:hover:to-cyan-600';
			case 'teal':
				return 'bg-gradient-to-br from-teal-400 via-teal-500 to-teal-600 hover:from-teal-500 hover:via-teal-600 hover:to-teal-700 dark:from-teal-500 dark:via-teal-600 dark:to-teal-700 dark:hover:from-teal-400 dark:hover:via-teal-500 dark:hover:to-teal-600';
			case 'orange':
				return 'bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 hover:from-orange-500 hover:via-orange-600 hover:to-orange-700 dark:from-orange-500 dark:via-orange-600 dark:to-orange-700 dark:hover:from-orange-400 dark:hover:via-orange-500 dark:hover:to-orange-600';
			case 'amber':
				return 'bg-gradient-to-br from-amber-300 via-amber-400 to-amber-500 hover:from-amber-400 hover:via-amber-500 hover:to-amber-600 dark:from-amber-500 dark:via-amber-600 dark:to-amber-700 dark:hover:from-amber-400 dark:hover:via-amber-500 dark:hover:to-amber-600';
			case 'lime':
				return 'bg-gradient-to-br from-lime-300 via-lime-400 to-lime-500 hover:from-lime-400 hover:via-lime-500 hover:to-lime-600 dark:from-lime-500 dark:via-lime-600 dark:to-lime-700 dark:hover:from-lime-400 dark:hover:via-lime-500 dark:hover:to-lime-600';
			case 'emerald':
				return 'bg-gradient-to-br from-emerald-400 via-emerald-500 to-emerald-600 hover:from-emerald-500 hover:via-emerald-600 hover:to-emerald-700 dark:from-emerald-500 dark:via-emerald-600 dark:to-emerald-700 dark:hover:from-emerald-400 dark:hover:via-emerald-500 dark:hover:to-emerald-600';
			case 'sky':
				return 'bg-gradient-to-br from-sky-400 via-sky-500 to-sky-600 hover:from-sky-500 hover:via-sky-600 hover:to-sky-700 dark:from-sky-500 dark:via-sky-600 dark:to-sky-700 dark:hover:from-sky-400 dark:hover:via-sky-500 dark:hover:to-sky-600';
			case 'violet':
				return 'bg-gradient-to-br from-violet-400 via-violet-500 to-violet-600 hover:from-violet-500 hover:via-violet-600 hover:to-violet-700 dark:from-violet-500 dark:via-violet-600 dark:to-violet-700 dark:hover:from-violet-400 dark:hover:via-violet-500 dark:hover:to-violet-600';
			case 'fuchsia':
				return 'bg-gradient-to-br from-fuchsia-400 via-fuchsia-500 to-fuchsia-600 hover:from-fuchsia-500 hover:via-fuchsia-600 hover:to-fuchsia-700 dark:from-fuchsia-500 dark:via-fuchsia-600 dark:to-fuchsia-700 dark:hover:from-fuchsia-400 dark:hover:via-fuchsia-500 dark:hover:to-fuchsia-600';
			case 'rose':
				return 'bg-gradient-to-br from-rose-400 via-rose-500 to-rose-600 hover:from-rose-500 hover:via-rose-600 hover:to-rose-700 dark:from-rose-500 dark:via-rose-600 dark:to-rose-700 dark:hover:from-rose-400 dark:hover:via-rose-500 dark:hover:to-rose-600';
			case 'slate':
				return 'bg-gradient-to-br from-slate-400 via-slate-500 to-slate-600 hover:from-slate-500 hover:via-slate-600 hover:to-slate-700 dark:from-slate-500 dark:via-slate-600 dark:to-slate-700 dark:hover:from-slate-400 dark:hover:via-slate-500 dark:hover:to-slate-600';
			case 'zinc':
				return 'bg-gradient-to-br from-zinc-400 via-zinc-500 to-zinc-600 hover:from-zinc-500 hover:via-zinc-600 hover:to-zinc-700 dark:from-zinc-500 dark:via-zinc-600 dark:to-zinc-700 dark:hover:from-zinc-400 dark:hover:via-zinc-500 dark:hover:to-zinc-600';
			case 'neutral':
				return 'bg-gradient-to-br from-neutral-400 via-neutral-500 to-neutral-600 hover:from-neutral-500 hover:via-neutral-600 hover:to-neutral-700 dark:from-neutral-500 dark:via-neutral-600 dark:to-neutral-700 dark:hover:from-neutral-400 dark:hover:via-neutral-500 dark:hover:to-neutral-600';
			case 'stone':
				return 'bg-gradient-to-br from-stone-400 via-stone-500 to-stone-600 hover:from-stone-500 hover:via-stone-600 hover:to-stone-700 dark:from-stone-500 dark:via-stone-600 dark:to-stone-700 dark:hover:from-stone-400 dark:hover:via-stone-500 dark:hover:to-stone-600';
			default:
				// Fallback for unknown colors
				return 'bg-gradient-to-br from-gray-400 via-gray-500 to-gray-600 hover:from-gray-500 hover:via-gray-600 hover:to-gray-700 dark:from-gray-500 dark:via-gray-600 dark:to-gray-700 dark:hover:from-gray-400 dark:hover:via-gray-500 dark:hover:to-gray-600';
		}
	}

	// Multiple colors - create elegant transitions with dark mode support
	if (colors.length === 2) {
		const [color1, color2] = colors;
		return `bg-gradient-to-r from-${color1}-400 to-${color2}-400 hover:from-${color1}-500 hover:to-${color2}-500 dark:from-${color1}-600 dark:to-${color2}-600 dark:hover:from-${color1}-500 dark:hover:to-${color2}-500`;
	}

	if (colors.length === 3) {
		const [color1, color2, color3] = colors;
		return `bg-gradient-to-r from-${color1}-400 via-${color2}-400 to-${color3}-400 hover:from-${color1}-500 hover:via-${color2}-500 hover:to-${color3}-500 dark:from-${color1}-600 dark:via-${color2}-600 dark:to-${color3}-600 dark:hover:from-${color1}-500 dark:hover:via-${color2}-500 dark:hover:to-${color3}-500`;
	}

	// For more than 3 colors, use a rainbow-like effect with the first 3
	const [color1, color2, color3] = colors.slice(0, 3);
	return `bg-gradient-to-r from-${color1}-400 via-${color2}-400 to-${color3}-400 hover:from-${color1}-500 hover:via-${color2}-500 hover:to-${color3}-500 dark:from-${color1}-600 dark:via-${color2}-600 dark:to-${color3}-600 dark:hover:from-${color1}-500 dark:hover:via-${color2}-500 dark:hover:to-${color3}-500`;
}

// Get text color classes that work well with the gradient background
export function getEventTextClasses(colors: string[]): string {
	// For light colors in light mode and all colors in dark mode, use white text
	// For very light colors (yellow, lime, amber), use darker text in light mode
	if (colors.length === 0) {
		return 'text-gray-700 dark:text-white';
	}

	const lightColors = ['yellow', 'lime', 'amber'];
	const hasLightColor = colors.some((color) => lightColors.includes(color));

	if (hasLightColor && colors.length === 1) {
		// For single light colors, use dark text in light mode, white in dark mode
		return 'text-gray-800 dark:text-white';
	}

	// For all other colors, use white text with good drop shadow for contrast
	return 'text-white';
}
