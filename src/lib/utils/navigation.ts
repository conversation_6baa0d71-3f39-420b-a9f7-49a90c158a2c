import {
	Bot,
	ShoppingCart,
	MapPin,
	Package,
	Calendar,
	Settings,
	CalendarDays,
	Users,
	Receipt,
	GitPullRequest,
	LayoutDashboard,
	Shield,
	LogIn
} from '@lucide/svelte';
import type { IconType } from '$lib/types/navigation';

export const ICON_MAP: Record<IconType, typeof Bot> = {
	Bot,
	ShoppingCart,
	MapPin,
	Package,
	Calendar,
	Settings,
	CalendarDays,
	Users,
	Receipt,
	GitPullRequest,
	LayoutDashboard,
	Shield,
	LogIn
} as const;

export const ALWAYS_AVAILABLE_ITEMS = [
	// {
	// 	title: 'AI',
	// 	url: '/private/ai',
	// 	icon: 'Bot' as IconType,
	// 	features: []
	// },
	{
		title: 'Upcoming',
		url: '/event',
		icon: 'CalendarDays' as IconType,
		features: []
	},
	{
		title: 'My Orders',
		url: '/private/my-order',
		icon: 'Receipt' as IconType,
		features: []
	}
] as const;
