declare module '@event-calendar/core' {
	import type { SvelteComponent } from 'svelte';

	export type Content = string | { html: string } | HTMLElement;
	export type Plugin = any;
	export type Duration = {
		years?: number;
		months?: number;
		days?: number;
		milliseconds?: number;
		weeks?: number;
		minutes?: number;
		hours?: number;
		seconds?: number;
	};

	export interface Event {
		id: string;
		title: string;
		start: string;
		end: string;
		allDay?: boolean;
		url?: string;
		backgroundColor?: string;
		borderColor?: string;
		textColor?: string;
		classNames?: string[] | string;
		editable?: boolean;
		startEditable?: boolean;
		durationEditable?: boolean;
		resourceId?: string;
		location?: string;
	}

	export interface Resource {
		id: string;
		title: string;
		eventBackgroundColor?: string;
		eventBorderColor?: string;
		eventTextColor?: string;
		eventClassNames?: string[] | string;
		[key: string]: any;
	}

	export interface View {
		type: string;
		title: string;
		activeStart: Date;
		activeEnd: Date;
		currentStart: Date;
		currentEnd: Date;
	}

	export interface DatesSetArg {
		start: Date;
		end: Date;
		startStr: string;
		endStr: string;
		view: View;
	}

	export interface SelectInfo {
		start: Date;
		end: Date;
		startStr: string;
		endStr: string;
		allDay: boolean;
		resource?: any;
		jsEvent: MouseEvent;
		view: View;
	}

	export interface EventDropInfo {
		event: Event;
		oldEvent: Event;
		delta: Duration;
		revert: () => void;
		jsEvent: MouseEvent | TouchEvent;
		view: View;
	}

	export interface EventResizeInfo {
		event: Event;
		oldEvent: Event;
		endDelta: Duration;
		revert: () => void;
		jsEvent: MouseEvent | TouchEvent;
		view: View;
	}

	export interface CalendarOptions {
		allDayContent?: Content | ((arg: { text: string }) => Content);
		allDaySlot?: boolean;
		buttonText?: Record<string, string>;
		customButtons?: Record<
			string,
			{
				text: Content;
				click: (mouseEvent: MouseEvent) => void;
				active?: boolean;
			}
		>;
		date?: string | Date;
		dateClick?: (info: {
			date: Date;
			dateStr: string;
			allDay: boolean;
			resource?: Resource;
			dayEl: HTMLElement;
			jsEvent: MouseEvent;
			view: View;
		}) => void;
		datesAboveResources?: boolean;
		datesSet?: (arg: DatesSetArg) => void;
		dayCellFormat?: object | ((date: Date) => Content);
		dayHeaderAriaLabelFormat?: object | ((date: Date) => string);
		dayHeaderFormat?: object | ((date: Date) => Content);
		dayMaxEvents?: boolean;
		dayPopoverFormat?: object | ((date: Date) => Content);
		displayEventEnd?: boolean;
		dragScroll?: boolean;
		duration?: Duration;
		editable?: boolean;
		eventAllUpdated?: (info: { view: View }) => void;
		events?: Event[];
		eventBackgroundColor?: string;
		eventTextColor?: string;
		eventClassNames?: string[] | ((info: { event: Event; view: View }) => string[]);
		eventClick?: (info: { el: HTMLElement; event: Event; jsEvent: MouseEvent; view: View }) => void;
		eventColor?: string;
		eventContent?: Content | ((info: { event: Event; timeText: string; view: View }) => Content);
		eventDidMount?: (info: { el: HTMLElement; event: Event; timeText: string; view: View }) => void;
		eventDragMinDistance?: number;
		eventDragStart?: (info: { event: Event; jsEvent: MouseEvent | TouchEvent; view: View }) => void;
		eventDragStop?: (info: { event: Event; jsEvent: MouseEvent | TouchEvent; view: View }) => void;
		eventDrop?: (info: EventDropInfo) => void;
		eventDurationEditable?: boolean;
		eventLongPressDelay?: number;
		eventMouseEnter?: (info: {
			el: HTMLElement;
			event: Event;
			jsEvent: MouseEvent;
			view: View;
		}) => void;
		eventMouseLeave?: (info: {
			el: HTMLElement;
			event: Event;
			jsEvent: MouseEvent;
			view: View;
		}) => void;
		eventResize?: (info: EventResizeInfo) => void;
		eventResizeStart?: (info: {
			event: Event;
			jsEvent: MouseEvent | TouchEvent;
			view: View;
		}) => void;
		eventResizeStop?: (info: {
			event: Event;
			jsEvent: MouseEvent | TouchEvent;
			view: View;
		}) => void;
		eventSources?: Array<{
			url?: string;
			method?: string;
			extraParams?: Record<string, any>;
			events?: (
				fetchInfo: {
					start: Date;
					end: Date;
					startStr: string;
					endStr: string;
				},
				successCallback: (events: Event[]) => void,
				failureCallback: (error: any) => void
			) => void | Event[] | Promise<Event[]>;
		}>;
		eventStartEditable?: boolean;
		eventTimeFormat?: object | ((start: Date, end: Date) => Content);
		filterEventsWithResources?: boolean;
		filterResourcesWithEvents?: boolean;
		firstDay?: number;
		flexibleSlotTimeLimits?:
			| boolean
			| {
					eventFilter: (event: Event) => boolean;
			  };
		headerToolbar?: {
			start?: string;
			center?: string;
			end?: string;
		};
		height?: string;
		hiddenDays?: number[];
		highlightedDates?: Array<string | Date>;
		lazyFetching?: boolean;
		listDayFormat?: object | ((date: Date) => Content);
		listDaySideFormat?: object | ((date: Date) => Content);
		loading?: (isLoading: boolean) => void;
		locale?: string;
		longPressDelay?: number;
		moreLinkContent?: Content | ((arg: { num: number; text: string }) => Content);
		noEventsClick?: (info: { jsEvent: MouseEvent; view: View }) => void;
		noEventsContent?: Content | (() => Content);
		nowIndicator?: boolean;
		pointer?: boolean;
		resources?: Resource[];
		resourceLabelContent?: Content | ((info: { resource: Resource; date?: Date }) => Content);
		resourceLabelDidMount?: (info: { el: HTMLElement; resource: Resource; date?: Date }) => void;
		select?: (info: SelectInfo) => void;
		selectable?: boolean;
		selectBackgroundColor?: string;
		selectLongPressDelay?: number;
		selectMinDistance?: number;
		slotDuration?: string;
		slotEventOverlap?: boolean;
		slotHeight?: string | number;
		slotLabelFormat?: object | ((date: Date) => Content);
		slotMaxTime?: string;
		slotMinTime?: string;
		slotWidth?: string | number;
		scrollTime?: string;
		theme?: Record<string, string>;
		titleFormat?: object | ((date: Date) => Content);
		unselect?: (jsEvent?: MouseEvent) => void;
		unselectAuto?: boolean;
		unselectCancel?: string;
		view?: string;
		viewDidMount?: (info: { view: View }) => void;
		views?: Record<string, any>;
	}

	export interface CalendarProps {
		plugins?: Plugin[];
		options?: CalendarOptions;
	}

	export default class Calendar extends SvelteComponent<CalendarProps> {}
}

declare module '@event-calendar/time-grid' {
	import { Plugin } from '@event-calendar/core';
	const TimeGrid: Plugin;
	export default TimeGrid;
}

declare module '@event-calendar/interaction' {
	import { Plugin } from '@event-calendar/core';
	const Interaction: Plugin;
	export default Interaction;
}

declare module '@event-calendar/list' {
	import type { Plugin } from '@event-calendar/core';
	const List: Plugin;
	export default List;
}
