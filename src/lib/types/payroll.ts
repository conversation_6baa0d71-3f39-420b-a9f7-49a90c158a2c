// Types for payroll system

export type PayrollProfileData = {
	id: string;
	profile_id: string;
	creator_id: string;
	title: string | null;
	level: string | null;
	enrolled_at: string;
	unenrolled_at: string | null;
	accountant_notes: string | null;
	currency_code: string;
	created_at: string;
	updated_at: string;
};

export type PayrollPeriodData = {
	id: string;
	brand_id: string;
	name: string;
	start_at: string;
	end_at: string;
	status: 'draft' | 'calculating' | 'approved' | 'paid';
	creator_id: string;
	created_at: string;
	updated_at: string;
};

export type PayrollFormulaData = {
	id: string;
	brand_id: string;
	name: string;
	description: string | null;
	earning_source_kind: 'event' | 'product' | 'profile';
	product_kind: string | null;
	event_kind: string | null;
	formula_js: string;
	is_default: boolean;
	is_active: boolean;
	created_at: string;
	updated_at: string;
};

export type PayrollFormulaAssignmentData = {
	id: string;
	payroll_profile_id: string;
	payroll_formula_id: string;
	auto_payroll_formula_earning_source_kind: string;
	created_at: string;
	updated_at: string;
};

export type PayrollEventFormulaInputs = {
	payee_enrolled_at: string;
	payee_title: string | null;
	payee_level: string | null;
	payee_event_role: string | null;
	payee_event_role_count: number;
	event_minutes: number;
	event_start_at: string;
	event_space_id: string;
	event_consumer_total_cost: number;
	event_consumer_registered: number;
	event_consumer_checked_in: number;
};

export type PayrollProductFormulaInputs = {
	payee_enrolled_at: string;
	payee_title: string | null;
	payee_level: string | null;
	product_id: string;
	product_kind: string;
	product_sale_count: number;
	product_total_revenue: number;
	product_cost: number;
};

export type PayrollProfileFormulaInputs = {
	payee_enrolled_at: string;
	payee_title: string | null;
	payee_level: string | null;
	payee_total_event_count: number;
	payee_total_student_count: number;
	payee_total_product_sales: number;
	payee_total_hours: number;
};

export type PayrollEntryData = {
	id: string;
	payroll_period_id: string;
	payroll_profile_id: string;
	payroll_formula_id: string;
	entry_type: 'event' | 'product' | 'profile';
	entry_for_event_id: string | null;
	entry_for_product_id: string | null;
	entry_for_profile_id: string | null;
	formula_amount: number;
	adjustment_amount: number;
	adjustment_reason: string | null;
	adjustment_by: string | null;
	final_amount: number;
	event_formula_inputs: PayrollEventFormulaInputs | null;
	product_formula_inputs: PayrollProductFormulaInputs | null;
	profile_formula_inputs: PayrollProfileFormulaInputs | null;
	calculation_details: Record<string, any>;
	created_at: string;
	updated_at: string;
};
