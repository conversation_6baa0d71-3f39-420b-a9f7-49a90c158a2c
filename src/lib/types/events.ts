import type { Database } from '$lib/supabase/database.types';

// Common types
export type LocalizedText = {
	[key: string]: string;
};

// Base event row from the database
type EventRow = Database['public']['Tables']['event']['Row'];

// Extended event type with all metadata for use in components
export interface EventWithMetadata extends EventRow {
	metadata: {
		id: string;
		title: LocalizedText;
		auto_final_title: LocalizedText;
		auto_final_subtitle: LocalizedText;
		metadata_wikipage?: {
			wikipage: {
				id: string;
				title: LocalizedText;
				auto_final_title: LocalizedText;
			};
			relation: string;
		}[];
		metadata_track?: {
			track: {
				id: string;
				genre: string;
				auto_final_title: LocalizedText;
				title: LocalizedText;
			};
		}[];
	};
	space?: {
		id: string;
		title_short: LocalizedText;
		landmark?: {
			id: string;
			title_short: LocalizedText;
			address?: {
				city: string;
				auto_normalized_address_local: string | null;
				timeZone?: {
					name: string;
				};
			};
		};
	};
	landmark?: {
		id: string;
		title_short: LocalizedText;
		address?: {
			city: string;
			auto_normalized_address_local: string | null;
			timeZone?: {
				name: string;
			};
		};
	};
	attendee_count?: number;
	product?: {
		title: LocalizedText;
		description?: LocalizedText;
	};
}
