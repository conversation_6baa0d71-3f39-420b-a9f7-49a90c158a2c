import type { LocalizedText } from '$lib/utils/localization';

export type IconType =
	| 'Bot'
	| 'ShoppingCart'
	| 'MapPin'
	| 'Package'
	| 'Calendar'
	| 'Settings'
	| 'CalendarDays'
	| 'Users'
	| 'Receipt'
	| 'GitPullRequest'
	| 'LayoutDashboard'
	| 'Shield'
	| 'LogIn';

export type NavigationItem = {
	title: string;
	url: string;
	icon: IconType;
	features?: string[];
	settings?: Record<string, unknown>;
	items?: NavigationSubItem[];
};

export type NavigationSubItem = {
	title: string;
	url: string;
	icon: IconType;
};

export type GroupNavigationItem = Omit<NavigationItem, 'features' | 'settings'> & {
	id: string;
};

export type BrandFeatureGroup = {
	brand_id: string;
	brand_name: string;
	features: NavigationItem[];
};
