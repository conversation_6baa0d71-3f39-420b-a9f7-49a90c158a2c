import { writable, derived, type Writable } from 'svelte/store';
import { browser } from '$app/environment';
import { goto } from '$app/navigation';

// Define our own message type to match Mastra's expected format
export type Message = {
    role: 'user' | 'assistant' | 'system';
    content: string;
};

// Define chat state type
export type ChatState = {
    messages: Message[];
    error: Error | null;
    isLoading: boolean;
    input: string;
};

/**
 * Custom hook to handle chat interactions with the Mastra agent
 * This provides a Svelte-friendly interface for interacting with Mastra agents
 */
export function useAgentChat() {
    // Create stores for state management
    const state: Writable<ChatState> = writable<ChatState>({
        messages: [],
        error: null,
        isLoading: false,
        input: '',
    });

    // Derived stores for easier access to specific parts of state
    const messages = derived(state, $state => $state.messages);
    const error = derived(state, $state => $state.error);
    const isLoading = derived(state, $state => $state.isLoading);
    
    // Create a two-way binding for the input
    const input = {
        subscribe: derived(state, $state => $state.input).subscribe,
        set: (value: string) => state.update(s => ({ ...s, input: value }))
    };

    /**
     * Handle authentication errors by redirecting to login
     */
    const handleAuthError = () => {
        if (browser) {
            // Redirect to login page
            goto('/auth/login?error=session_expired&redirect=/private/agent');
        }
    };

    /**
     * Submit a message to the agent and handle the streaming response
     */
    const handleSubmit = async (e?: { preventDefault: () => void }) => {
        if (e) e.preventDefault();
        
        // Get current state value
        let currentState: ChatState = { messages: [], error: null, isLoading: false, input: '' };
        state.subscribe(s => { currentState = s; })();
        
        // Don't do anything if already loading or input is empty
        if (currentState.isLoading || !currentState.input.trim()) {
            return;
        }

        // Create new user message
        const userMessage: Message = {
            role: 'user',
            content: currentState.input,
        };

        // Update state with new message and reset input
        state.update(s => ({
            ...s,
            messages: [...s.messages, userMessage],
            isLoading: true,
            input: '',
            error: null,
        }));

        try {
            // Only proceed if we're in the browser
            if (!browser) return;

            // Call our agent API endpoint with streaming enabled
            const response = await fetch('/api/agent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream',
                },
                body: JSON.stringify({
                    messages: [...currentState.messages, userMessage],
                }),
            });

            // Handle authentication errors specifically
            if (response.status === 401) {
                handleAuthError();
                throw new Error('Authentication failed. Please log in again.');
            }

            if (!response.ok) {
                const errorData = await response.text();
                
                // Try to parse the error to get a more helpful message
                try {
                    const parsedError = JSON.parse(errorData);
                    throw new Error(parsedError.error || `Failed to send message: ${errorData}`);
                } catch (parseError) {
                    throw new Error(`Failed to send message: ${errorData}`);
                }
            }

            // Create new assistant message
            const assistantMessage: Message = {
                role: 'assistant',
                content: '',
            };

            // Add empty assistant message to state
            state.update(s => ({
                ...s,
                messages: [...s.messages, assistantMessage],
            }));

            // Process the streaming response
            const reader = response.body?.getReader();
            if (!reader) throw new Error('Failed to get response stream');

            const decoder = new TextDecoder();
            let done = false;

            while (!done) {
                const { value, done: readerDone } = await reader.read();
                done = readerDone;

                if (done) break;

                const decodedValue = decoder.decode(value, { stream: true });
                const dataLines = decodedValue.split('\n');

                for (const line of dataLines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            
                            // Check if this is an error message
                            if (data.error) {
                                throw new Error(data.error);
                            }
                            
                            // Update the assistant message with the new chunk
                            state.update(s => {
                                const updatedMessages = [...s.messages];
                                const lastMessage = updatedMessages[updatedMessages.length - 1];
                                
                                if (lastMessage && lastMessage.role === 'assistant') {
                                    lastMessage.content += data.text || '';
                                }
                                
                                return {
                                    ...s,
                                    messages: updatedMessages,
                                };
                            });
                        } catch (e) {
                            console.error('Error parsing SSE data:', e);
                            
                            // If this is a formatted error message from the server, use it
                            if (e instanceof Error && e.message !== 'Unexpected end of JSON input') {
                                throw e;
                            }
                        }
                    }
                }
            }
        } catch (e) {
            // Handle errors
            console.error('Chat error:', e);
            
            // Add system message indicating error
            state.update(s => {
                const errorMessage: Message = {
                    role: 'system',
                    content: `Error: ${e instanceof Error ? e.message : 'Unknown error occurred'}`
                };
                
                // Only add error message if last message isn't already a system error
                const lastMessage = s.messages[s.messages.length - 1];
                if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.content) {
                    // Replace empty assistant message with error
                    const updatedMessages = [...s.messages];
                    updatedMessages[updatedMessages.length - 1] = errorMessage;
                    return {
                        ...s,
                        messages: updatedMessages,
                        error: e as Error,
                    };
                } else {
                    // Add new error message
                    return {
                        ...s,
                        messages: [...s.messages, errorMessage],
                        error: e as Error,
                    };
                }
            });
        } finally {
            // Mark loading as finished
            state.update(s => ({
                ...s,
                isLoading: false,
            }));
        }
    };

    return {
        messages,
        input,
        handleSubmit,
        isLoading,
        error,
        state,
    };
} 