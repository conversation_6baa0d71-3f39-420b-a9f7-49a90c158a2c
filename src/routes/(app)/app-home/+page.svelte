<script lang="ts">
	import { fly } from 'svelte/transition';
	import { Button } from '$lib/components/ui/button';
	import { ChevronRight, Calendar, MapPin, Users } from '@lucide/svelte';
	import type { PageData } from '../home/<USER>';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();
</script>

<div class="relative min-h-screen">
	<!-- Hero Section -->
	<section class="from-background via-muted/10 to-background relative bg-gradient-to-b">
		<div class="container mx-auto px-4 py-20 text-center">
			<h1
				in:fly={{ y: 20, duration: 800, delay: 200 }}
				class="text-5xl font-bold tracking-tight sm:text-6xl lg:text-7xl"
			>
				<span class="from-primary to-primary/70 bg-gradient-to-r bg-clip-text text-transparent">
					Discover Events
				</span>
				<br />
				<span class="text-foreground">Across All Communities</span>
			</h1>

			<p
				in:fly={{ y: 20, duration: 800, delay: 400 }}
				class="text-muted-foreground mx-auto mt-6 max-w-2xl text-lg"
			>
				Explore workshops, classes, and experiences from multiple brands and instructors all in one
				place.
			</p>

			<div in:fly={{ y: 20, duration: 800, delay: 600 }} class="mt-10 flex justify-center gap-4">
				<Button href="/event" size="lg" class="group">
					Browse All Events
					<ChevronRight class="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
				</Button>
				<Button href="/brands" variant="outline" size="lg">Explore Brands</Button>
			</div>
		</div>
	</section>

	<!-- Features Section -->
	<section class="container mx-auto px-4 py-20">
		<div class="grid gap-8 md:grid-cols-3">
			<div class="text-center">
				<div
					class="bg-primary/10 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full"
				>
					<Calendar class="text-primary h-8 w-8" />
				</div>
				<h3 class="mb-2 text-xl font-semibold">All Events in One Place</h3>
				<p class="text-muted-foreground">
					Find workshops, classes, and experiences from multiple brands without switching between
					sites.
				</p>
			</div>

			<div class="text-center">
				<div
					class="bg-primary/10 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full"
				>
					<MapPin class="text-primary h-8 w-8" />
				</div>
				<h3 class="mb-2 text-xl font-semibold">Location-Based Discovery</h3>
				<p class="text-muted-foreground">
					Easily find events happening near you or explore activities in new areas.
				</p>
			</div>

			<div class="text-center">
				<div
					class="bg-primary/10 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full"
				>
					<Users class="text-primary h-8 w-8" />
				</div>
				<h3 class="mb-2 text-xl font-semibold">Multiple Communities</h3>
				<p class="text-muted-foreground">
					Connect with various communities and discover new interests across different brands.
				</p>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="bg-muted/30">
		<div class="container mx-auto px-4 py-16 text-center">
			<h2 class="mb-4 text-3xl font-bold">Ready to Explore?</h2>
			<p class="text-muted-foreground mb-8 text-lg">
				Join thousands discovering new experiences every day.
			</p>
			<Button href="/event" size="lg">Start Browsing Events</Button>
		</div>
	</section>
</div>
