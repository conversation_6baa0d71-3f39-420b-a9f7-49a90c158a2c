<script lang="ts">
	import type { SuperValidated, Infer } from 'sveltekit-superforms';
	import type { SignUpSchema } from './schema';
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form/index.js';
	import { page } from '$app/state';

	import { Loader2, AlertCircle, Info } from '@lucide/svelte';
	import { superForm } from 'sveltekit-superforms/client';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { signUpSchema } from './schema';
	import * as Card from '$lib/components/ui/card';
	import { Field, Control, Label, FieldErrors } from 'formsnap';
	import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
	import * as m from '$lib/paraglide/messages.js';
	import ShineBorder from '../../components/ShineBorder.svelte';

	interface Props {
		data: {
			form: SuperValidated<Infer<SignUpSchema>>;
		};
	}

	let props: Props = $props();
	const eventId = $derived(page.url.searchParams.get('eid'));

	const form = superForm(props.data.form, {
		validators: zodClient(signUpSchema),
		dataType: 'json',
		delayMs: 300,
		taintedMessage: null
	});

	const { submitting, errors, allErrors, enhance, form: formData } = form;
</script>

<Card.Root class="relative overflow-hidden">
	<ShineBorder duration={12} borderWidth={1} />
	<Card.Header class="space-y-1">
		<Card.Title>{m.auth_sign_up_title()}</Card.Title>
		<Card.Description>{m.auth_sign_up_description()}</Card.Description>
	</Card.Header>
	<Card.Content>
		{#if eventId}
			<Alert class="mb-4">
				<Info class="size-4" />
				<AlertTitle>Event Registration</AlertTitle>
				<AlertDescription>
					Please create an account to register for the event. After creating your account, you'll be
					redirected to complete your registration.
				</AlertDescription>
			</Alert>
		{/if}

		{#if $allErrors.length > 0 && $allErrors.find((err) => err.path === '')}
			<Alert variant="destructive" class="mb-4">
				<AlertCircle class="size-4" />
				<AlertDescription>
					{$allErrors.find((err) => err.path === '')?.messages[0] || 'An error occurred'}
				</AlertDescription>
			</Alert>
		{:else if $errors._errors && $errors._errors.length > 0}
			<Alert variant="destructive" class="mb-4">
				<AlertCircle class="size-4" />
				<AlertDescription>{$errors._errors[0]}</AlertDescription>
			</Alert>
		{/if}

		<form method="POST" class="grid gap-4" use:enhance>
			{#if eventId}
				<input type="hidden" name="eventId" value={eventId} />
			{/if}

			<div class="grid grid-cols-2 gap-4">
				<Field {form} name="given_name">
					<Control>
						{#snippet children({ props })}
							<div class="grid gap-2">
								<Label>{m.auth_sign_up_given_name_label()}</Label>
								<Input
									{...props}
									type="text"
									bind:value={$formData.given_name}
									placeholder={m.auth_sign_up_given_name_placeholder()}
									aria-invalid={$errors.given_name ? 'true' : undefined}
								/>
								<FieldErrors class="text-sm text-destructive" />
							</div>
						{/snippet}
					</Control>
				</Field>

				<Field {form} name="family_name">
					<Control>
						{#snippet children({ props })}
							<div class="grid gap-2">
								<Label>{m.auth_sign_up_family_name_label()}</Label>
								<Input
									{...props}
									type="text"
									bind:value={$formData.family_name}
									placeholder={m.auth_sign_up_family_name_placeholder()}
									aria-invalid={$errors.family_name ? 'true' : undefined}
								/>
								<FieldErrors class="text-sm text-destructive" />
							</div>
						{/snippet}
					</Control>
				</Field>
			</div>

			<Field {form} name="email">
				<Control>
					{#snippet children({ props })}
						<div class="grid gap-2">
							<Label>{m.auth_sign_up_email_label()}</Label>
							<Input
								{...props}
								type="email"
								bind:value={$formData.email}
								placeholder={m.auth_sign_up_email_placeholder()}
								aria-invalid={$errors.email ? 'true' : undefined}
							/>
							<FieldErrors class="text-sm text-destructive" />
						</div>
					{/snippet}
				</Control>
			</Field>

			<Field {form} name="password">
				<Control>
					{#snippet children({ props })}
						<div class="grid gap-2">
							<Label>{m.auth_sign_up_password_label()}</Label>
							<Input
								{...props}
								type="password"
								bind:value={$formData.password}
								placeholder={m.auth_sign_up_password_placeholder()}
								aria-invalid={$errors.password ? 'true' : undefined}
							/>
							<FieldErrors class="text-sm text-destructive" />
						</div>
					{/snippet}
				</Control>
			</Field>

			<Field {form} name="confirmPassword">
				<Control>
					{#snippet children({ props })}
						<div class="grid gap-2">
							<Label>{m.auth_sign_up_confirm_password_label()}</Label>
							<Input
								{...props}
								type="password"
								bind:value={$formData.confirmPassword}
								placeholder={m.auth_sign_up_confirm_password_placeholder()}
								aria-invalid={$errors.confirmPassword ? 'true' : undefined}
							/>
							<FieldErrors class="text-sm text-destructive" />
						</div>
					{/snippet}
				</Control>
			</Field>

			<Form.Button type="submit" class="mt-4 w-full" disabled={$submitting}>
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
					{m.auth_sign_up_creating_account()}
				{:else}
					{m.auth_sign_up_continue_to_email_verification()}
				{/if}
			</Form.Button>
		</form>
	</Card.Content>
	<Card.Footer class="flex flex-col space-y-4">
		<p class="px-6 text-center text-xs text-muted-foreground">
			{m.auth_sign_up_terms_prefix()}
			<a href="/legal/terms" class="text-foreground hover:text-foreground/90"
				>{m.auth_sign_up_terms_of_service()}</a
			>
			{m.auth_sign_up_terms_middle()}
			<a href="/legal/privacy" class="text-foreground hover:text-foreground/90"
				>{m.auth_sign_up_privacy_policy()}</a
			>.
		</p>
	</Card.Footer>
</Card.Root>

<div class="mt-4 text-center text-sm">
	<span class="text-muted-foreground">{m.auth_sign_up_already_have_account()}</span>
	{' '}
	<a
		href="/auth/sign-in{eventId ? `?eid=${eventId}` : ''}"
		class="text-foreground hover:text-foreground/90">{m.auth_sign_up_sign_in()}</a
	>
</div>
