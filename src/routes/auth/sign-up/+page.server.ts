import { redirect, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { fail } from '@sveltejs/kit';
import { superValidate, setError } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { signUpSchema } from './schema';

export const load = (async ({ locals: { supabase } }) => {
	const {
		data: { user }
	} = await supabase.auth.getUser();

	if (user) {
		throw redirect(303, '/event');
	}

	const form = await superValidate(zod(signUpSchema));

	return {
		form
	};
}) satisfies PageServerLoad;

export const actions = {
	default: async ({ request, locals: { supabase, brand }, url }) => {
		const formData = await request.formData();
		const eventId = formData.get('eventId') as string | null;

		const form = await superValidate(formData, zod(signUpSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const { data: authData, error: signUpError } = await supabase.auth.signUp({
			email: form.data.email,
			password: form.data.password,
			options: {
				data: {
					sign_up_given_name: form.data.given_name,
					sign_up_family_name: form.data.family_name
					// sign_up_instagram_id: form.data.instagram_id || '',
					// sign_up_wechat_id: form.data.wechat_id || ''
				},
				emailRedirectTo: `${url.origin}/auth/verify-account${eventId ? `?eid=${eventId}` : ''}`
			}
		});

		if (signUpError) {
			// Email already exists
			if (signUpError.message.includes('User already registered')) {
				return setError(form, 'email', 'This email is already registered. Please sign in instead.');
			}

			// Default error
			return setError(form, '', signUpError.message);
		}

		// Upsert brand_managed_profile to track this user under the current brand
		if (authData.user) {
			const { error: upsertError } = await supabase.from('brand_managed_profile').upsert(
				{
					brand_id: brand.id,
					managed_profile_id: authData.user.id,
					updated_at: new Date().toISOString()
				},
				{
					onConflict: 'brand_id,managed_profile_id',
					ignoreDuplicates: true
				}
			);

			if (upsertError) {
				console.error('[sign-up] Failed to upsert brand_managed_profile:', upsertError);
				// Don't fail the sign-up, just log the error
			}
		}

		// Redirect to verify account page with eventId if provided
		if (eventId) {
			redirect(303, `/auth/verify-account?email=${form.data.email}&eid=${eventId}`);
		} else {
			redirect(303, `/auth/verify-account?email=${form.data.email}`);
		}
	}
} satisfies Actions;
