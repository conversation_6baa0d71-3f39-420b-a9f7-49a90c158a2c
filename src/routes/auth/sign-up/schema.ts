import { z } from 'zod';

export const signUpSchema = z
	.object({
		given_name: z
			.string({ required_error: 'Given name is required' })
			.trim()
			.min(1, 'Given name is required')
			.max(50, 'Given name must be less than 50 characters'),
		family_name: z
			.string({ required_error: 'Family name is required' })
			.trim()
			.min(1, 'Family name is required')
			.max(50, 'Family name must be less than 50 characters'),
		email: z
			.string({ required_error: 'Email is required' })
			.trim()
			.toLowerCase()
			.email('Please enter a valid email')
			.min(1, 'Email is required'),
		password: z
			.string({ required_error: 'Password is required' })
			.trim()
			.min(8, 'Password must be at least 8 characters')
			.regex(/[a-z]/, 'Password must contain at least one lowercase letter')
			.regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
			.regex(/[0-9]/, 'Password must contain at least one number'),
		confirmPassword: z
			.string({ required_error: 'Please confirm your password' })
			.trim()
			.min(1, 'Please confirm your password')
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords don't match",
		path: ['confirmPassword']
	});

export type SignUpSchema = typeof signUpSchema;
