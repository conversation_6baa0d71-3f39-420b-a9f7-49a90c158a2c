<script lang="ts">
	import type { LayoutData } from './$types';
	import AuroraBackground from './components/AuroraBackground.svelte';
	import AuthFooter from './components/AuthFooter.svelte';
	import ThemeToggle from './components/ThemeToggle.svelte';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { AlertCircle, ArrowLeft } from '@lucide/svelte';
	import { slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import LanguagePicker from './components/LanguagePicker.svelte';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import { onNavigate } from '$app/navigation';
	import { browser } from '$app/environment';

	interface Props {
		data: LayoutData;
		children: any;
	}

	let props: Props = $props();
	let error = $state('');

	// Add support for View Transitions API
	if (browser) {
		onNavigate((navigation) => {
			// Only apply transitions for auth page navigations
			if (!navigation.to?.url.pathname.startsWith('/auth')) {
				return;
			}

			// Skip transitions if the browser doesn't support them
			if (!document.startViewTransition) {
				return;
			}

			return new Promise((resolve) => {
				document.startViewTransition(async () => {
					resolve();
					await navigation.complete;
				});
			});
		});
	}
</script>

<div class="relative h-dvh overflow-auto">
	<AuroraBackground class="fixed inset-0">{''}</AuroraBackground>
	<div class="relative grid h-full w-full grid-rows-[auto_1fr_auto] gap-4">
		<header class="z-10 flex justify-end p-3">
			<div class="relative flex space-x-2">
				<LanguagePicker />
				<ThemeToggle />
			</div>
		</header>

		<main class="flex flex-col items-center justify-center px-4">
			<a
				href="/event"
				class="group -m-2 flex flex-col items-center justify-center space-y-2 rounded-lg p-2 transition-all duration-200 hover:scale-105 focus:scale-105 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2 focus:ring-offset-background active:scale-95"
				aria-label="Go back to home page"
			>
				<div
					class="view-transition-logo relative size-10 overflow-hidden rounded-xl transition-opacity duration-200 group-hover:opacity-80 group-active:opacity-70"
				>
					<img src="/logo/1024.png" alt="Hana" class="size-full object-contain dark:invert" />
				</div>
				<div class="flex flex-col items-center space-y-1">
					<h1
						class="view-transition-brand-name bg-linear-to-b from-neutral-400 to-neutral-700 bg-clip-text text-2xl font-bold tracking-tight text-transparent transition-opacity duration-200 group-hover:opacity-80 group-active:opacity-70 dark:from-neutral-100 dark:to-neutral-400"
					>
						{getLocalizedText(props.data.brand.name_full, getLocale())}
					</h1>
					<!-- Always visible hint text for mobile -->
					<span
						class="text-xs text-muted-foreground/60 transition-colors duration-200 group-hover:text-muted-foreground group-active:text-muted-foreground"
					>
						← Back to home
					</span>
				</div>
			</a>

			<div class="mt-6 flex w-full flex-1 items-center justify-center">
				<div class="view-transition-card mx-auto w-full max-w-[400px] space-y-6">
					{#if error}
						<div transition:slide|local={{ duration: 200, easing: quintOut }} class="mb-6">
							<Alert variant="destructive">
								<AlertCircle class="size-4" />
								<AlertDescription>{error}</AlertDescription>
							</Alert>
						</div>
					{/if}
					{@render props.children({ error: (message: string) => (error = message) })}
				</div>
			</div>
		</main>

		<AuthFooter />
	</div>
</div>

<style>
	/* View Transitions API named elements */
	.view-transition-logo {
		view-transition-name: auth-logo;
	}

	.view-transition-brand-name {
		view-transition-name: auth-brand-name;
	}

	.view-transition-card {
		view-transition-name: auth-card;
	}

	/* Define how transitions animate */
	::view-transition-old(auth-card) {
		animation: 300ms cubic-bezier(0.4, 0, 0.2, 1) both fade-out;
	}

	::view-transition-new(auth-card) {
		animation: 500ms cubic-bezier(0.4, 0, 0.2, 1) both fade-in;
	}

	@keyframes fade-in {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes fade-out {
		from {
			opacity: 1;
			transform: translateY(0);
		}
		to {
			opacity: 0;
			transform: translateY(-20px);
		}
	}
</style>
