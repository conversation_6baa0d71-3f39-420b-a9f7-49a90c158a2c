<script lang="ts">
	import type { SuperValidated, Infer } from 'sveltekit-superforms';
	import type { ForgotPasswordSchema } from './schema';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Loader2 } from '@lucide/svelte';
	import { Field, Control, Label, FieldErrors } from 'formsnap';
	import { superForm } from 'sveltekit-superforms/client';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { forgotPasswordSchema } from './schema';
	import { goto } from '$app/navigation';
	import * as Card from '$lib/components/ui/card';
	import { toast } from 'svelte-sonner';
	import ShineBorder from '../../components/ShineBorder.svelte';
	import AuroraText from '../../components/AuroraText.svelte';

	interface Props {
		data: {
			form: SuperValidated<Infer<ForgotPasswordSchema>>;
			email: string;
			success?: boolean;
		};
	}

	let { data }: Props = $props();

	const formHandler = superForm(data.form, {
		validators: zodClient(forgotPasswordSchema),
		dataType: 'json',
		onResult: ({ result }) => {
			if (result.type === 'success' && result.data?.success) {
				toast.success('Password reset link sent to your email');
			}
		}
	});

	const { form, enhance, submitting, errors, message } = formHandler;

	// Set email from URL if available
	$effect(() => {
		if (data.email) {
			$form.email = data.email;
		}
	});

	function handleNavigate() {
		goto('/auth/sign-in');
	}
</script>

<Card.Root class="relative overflow-hidden">
	<ShineBorder duration={12} borderWidth={1} />
	<Card.Header class="space-y-1">
		<Card.Title>Forgot Password</Card.Title>
		<Card.Description>Enter your email below to reset your password</Card.Description>
	</Card.Header>
	<form id="forgot-password-form" method="POST" use:enhance>
		<Card.Content class="grid gap-4">
			{#if $message}
				<div class="text-sm text-destructive">{$message}</div>
			{/if}

			<Field form={formHandler} name="email">
				<Control>
					{#snippet children({ props })}
						<div class="grid gap-2">
							<Label>Email</Label>
							<Input
								{...props}
								type="email"
								bind:value={$form.email}
								placeholder="<EMAIL>"
							/>
							<FieldErrors class="text-sm text-destructive" />
						</div>
					{/snippet}
				</Control>
			</Field>
		</Card.Content>
		<Card.Footer class="flex flex-col gap-4">
			<Button type="submit" class="w-full" disabled={$submitting}>
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
					Sending Reset Link...
				{:else}
					Send Reset Link
				{/if}
			</Button>
		</Card.Footer>
	</form>
</Card.Root>

<div class="mt-4 text-center text-sm">
	<a href="/auth/sign-in" class="text-foreground hover:text-foreground/90">Back to Sign In</a>
</div>

<style>
</style>
