import { redirect, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { fail } from '@sveltejs/kit';
import { superValidate, setError } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { forgotPasswordSchema } from './schema';

export const load: PageServerLoad = async ({ url }) => {
	const form = await superValidate(zod(forgotPasswordSchema));
	const email = url.searchParams.get('email') ?? '';

	return {
		form,
		email
	};
};

export const actions = {
	default: async ({ request, locals: { supabase }, url }) => {
		const form = await superValidate(request, zod(forgotPasswordSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		const { error } = await supabase.auth.resetPasswordForEmail(form.data.email, {
			redirectTo: `${url.origin}/auth/reset-password`
		});

		if (error) {
			return setError(form, 'email', error.message);
		}

		return {
			form,
			success: true
		};
	}
} satisfies Actions;
