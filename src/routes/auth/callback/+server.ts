import { redirect } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET = (async ({ url, locals: { supabase } }) => {
	const code = url.searchParams.get('code');
	const next = url.searchParams.get('next') ?? '/';
	const eventId = url.searchParams.get('eid');

	if (code) {
		const { error } = await supabase.auth.exchangeCodeForSession(code);
		if (!error) {
			// Check if we have an event ID to redirect to
			if (eventId) {
				redirect(303, `/event/${eventId}`);
			} else {
				redirect(303, next);
			}
		}
	}

	// If there's an error or no code, redirect to error page with event ID if available
	redirect(303, `/auth/auth-callback-error${eventId ? `?eid=${eventId}` : ''}`);
}) satisfies RequestHandler;
