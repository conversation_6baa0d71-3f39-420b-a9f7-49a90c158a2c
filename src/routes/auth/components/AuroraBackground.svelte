<!-- Aurora background component -->
<script lang="ts">
	import { cn } from '$lib/utils';
	import type { Snippet } from 'svelte';

	interface Props {
		showRadialGradient?: boolean;
		class?: string;
		children: Snippet;
	}

	let { showRadialGradient = true, class: className, children }: Props = $props();
</script>

<div
	class={cn(
		'transition-bg relative flex h-screen w-full flex-col items-center justify-center bg-zinc-50 text-slate-950 dark:bg-zinc-900',
		className
	)}
>
	<div class="absolute inset-0 w-full overflow-hidden">
		<div
			class={cn(
				'pointer-events-none absolute -inset-[10px] w-[calc(100%+20px)] opacity-50 blur-[10px] invert filter will-change-transform dark:invert-0',
				'[--white-gradient:repeating-linear-gradient(100deg,var(--white)_0%,var(--white)_7%,var(--transparent)_10%,var(--transparent)_12%,var(--white)_16%)]',
				'[--dark-gradient:repeating-linear-gradient(100deg,var(--black)_0%,var(--black)_7%,var(--transparent)_10%,var(--transparent)_12%,var(--black)_16%)]',
				'[--aurora:repeating-linear-gradient(100deg,var(--blue-500)_10%,var(--indigo-300)_15%,var(--blue-300)_20%,var(--violet-200)_25%,var(--blue-400)_30%)]',
				'[background-image:var(--white-gradient),var(--aurora)]',
				'dark:[background-image:var(--dark-gradient),var(--aurora)]',
				'bg-size-[300%_200%]',
				'bg-position-[50%_50%,50%_50%]',
				'after:content-[""]',
				'after:absolute',
				'after:inset-0',
				'after:[background-image:var(--white-gradient),var(--aurora)]',
				'after:dark:[background-image:var(--dark-gradient),var(--aurora)]',
				'after:bg-size-[200%_100%]',
				'after:animate-aurora',
				'after:bg-fixed',
				'after:mix-blend-difference',
				showRadialGradient &&
					'mask-[radial-gradient(ellipse_at_100%_0%,black_10%,var(--transparent)_70%)]'
			)}
		></div>
	</div>
	<div class="relative z-10 w-full max-w-sm">
		{@render children()}
	</div>
</div>

<style>
	:global(:root) {
		--transparent: transparent;
		--white: white;
		--black: black;
		--blue-300: rgb(147, 197, 253);
		--blue-400: rgb(96, 165, 250);
		--blue-500: rgb(59, 130, 246);
		--indigo-300: rgb(165, 180, 252);
		--violet-200: rgb(221, 214, 254);
	}

	@keyframes aurora {
		from {
			background-position:
				50% 50%,
				50% 50%;
		}
		to {
			background-position:
				350% 50%,
				350% 50%;
		}
	}
</style>
