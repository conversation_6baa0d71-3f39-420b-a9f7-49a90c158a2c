<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { buttonVariants } from '$lib/components/ui/button/index.js';
	import { Languages } from '@lucide/svelte';
	import { locales, getLocale, setLocale } from '$lib/paraglide/runtime.js';
	import { page } from '$app/state';
	import * as m from '$lib/paraglide/messages.js';

	interface Language {
		code: string;
		label: string;
	}

	const languages: Language[] = [
		{ code: 'en', label: 'English' },
		{ code: 'zh', label: '中文' },
		{ code: 'ko', label: '한국어' },
		{ code: 'ja', label: '日本語' }
	];

	let currentLang = $derived(getLocale());

	function handleLanguageSelect(lang: string) {
		setLocale(lang);
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger class={buttonVariants({ variant: 'ghost', size: 'icon' })}>
		<Languages class="h-[1.2rem] w-[1.2rem]" />
		<span class="sr-only">Toggle language</span>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end">
		<DropdownMenu.RadioGroup value={currentLang} onValueChange={handleLanguageSelect}>
			{#each languages as { code, label }}
				<DropdownMenu.RadioItem value={code} class="cursor-pointer">
					{label}
				</DropdownMenu.RadioItem>
			{/each}
		</DropdownMenu.RadioGroup>
	</DropdownMenu.Content>
</DropdownMenu.Root>
