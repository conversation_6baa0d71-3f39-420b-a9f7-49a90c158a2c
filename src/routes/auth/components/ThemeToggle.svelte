<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { buttonVariants } from '$lib/components/ui/button/index.js';
	import { Moon, Sun } from '@lucide/svelte';
	import { mode, setMode, resetMode, userPrefersMode } from 'mode-watcher';

	type Theme = 'light' | 'dark' | 'system';
	let currentTheme = $state<Theme>('system');
	let displayTheme = $state<'light' | 'dark'>('light');

	$effect(() => {
		displayTheme = mode.current as 'light' | 'dark';
	});

	$effect(() => {
		if (!userPrefersMode.current) {
			currentTheme = 'system';
		} else {
			currentTheme = userPrefersMode.current;
		}
	});

	function updateTheme(value: string) {
		const theme = value as Theme;
		currentTheme = theme;
		if (theme === 'system') {
			resetMode(); // Reset to system preference
		} else {
			setMode(theme); // Set to light or dark
		}
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger class={buttonVariants({ variant: 'ghost', size: 'icon' })}>
		{#if displayTheme === 'dark'}
			<Moon class="h-[1.2rem] w-[1.2rem]" />
		{:else}
			<Sun class="h-[1.2rem] w-[1.2rem]" />
		{/if}
		<span class="sr-only">Toggle theme</span>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end">
		<DropdownMenu.RadioGroup value={currentTheme} onValueChange={updateTheme}>
			<DropdownMenu.RadioItem value="light">Light</DropdownMenu.RadioItem>
			<DropdownMenu.RadioItem value="dark">Dark</DropdownMenu.RadioItem>
			<DropdownMenu.RadioItem value="system">System</DropdownMenu.RadioItem>
		</DropdownMenu.RadioGroup>
	</DropdownMenu.Content>
</DropdownMenu.Root>
