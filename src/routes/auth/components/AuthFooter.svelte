<script lang="ts">
	import { Separator } from '$lib/components/ui/separator';
	import { Badge } from '$lib/components/ui/badge';
	import { cn } from '$lib/utils';
	import * as m from '$lib/paraglide/messages.js';

	let { class: className = '' } = $props<{
		class?: string;
	}>();

	const currentYear = new Date().getFullYear();
</script>

<footer class={cn('mt-4 w-full px-6 pb-4', className)}>
	<div class="flex flex-col items-center gap-4 md:flex-row md:items-center">
		<div class="flex flex-1 justify-start">
			<Badge variant="secondary" class="text-xs font-normal">{m.auth_footer_powered_by()}</Badge>
		</div>

		<div class="hidden min-w-[400px] flex-1 justify-center md:flex">
			<div class="flex items-center gap-x-4 text-xs text-muted-foreground/60">
				<a href="/help" class="hover:text-muted-foreground">{m.auth_footer_help()}</a>
				<Separator orientation="vertical" class="h-4"></Separator>
				<a href="/legal/privacy" class="hover:text-muted-foreground">{m.auth_footer_privacy()}</a>
				<Separator orientation="vertical" class="h-4"></Separator>
				<a href="/legal/terms" class="hover:text-muted-foreground">{m.auth_footer_terms()}</a>
				<Separator orientation="vertical" class="h-4"></Separator>
				<a href="/contact" class="hover:text-muted-foreground">{m.auth_footer_contact()}</a>
			</div>
		</div>

		<div class="hidden flex-1 justify-end text-[11px] text-muted-foreground/60 md:flex">
			<span>{m.auth_footer_made_with_love()} {m.auth_footer_copyright({ year: currentYear })}</span>
		</div>

		<!-- Mobile layout -->
		<div class="flex flex-col items-center gap-4 md:hidden">
			<div
				class="flex flex-wrap items-center justify-center gap-x-4 gap-y-1 text-xs text-muted-foreground/60"
			>
				<a href="/help" class="hover:text-muted-foreground">{m.auth_footer_help()}</a>
				<Separator orientation="vertical" class="h-4"></Separator>
				<a href="/legal/privacy" class="hover:text-muted-foreground">{m.auth_footer_privacy()}</a>
				<Separator orientation="vertical" class="h-4"></Separator>
				<a href="/legal/terms" class="hover:text-muted-foreground">{m.auth_footer_terms()}</a>
				<Separator orientation="vertical" class="h-4"></Separator>
				<a href="/contact" class="hover:text-muted-foreground">{m.auth_footer_contact()}</a>
			</div>
			<div
				class="flex flex-col items-center gap-1 text-center text-[11px] text-muted-foreground/60"
			>
				<p>{m.auth_footer_made_with_love()}</p>
				<p>{m.auth_footer_copyright({ year: currentYear })}</p>
			</div>
		</div>
	</div>
</footer>
