import { redirect, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { fail } from '@sveltejs/kit';
import { superValidate, setError } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { signInSchema } from './schema';
import { dev } from '$app/environment';
import { DEV_EMAIL, DEV_PASSWORD } from '$env/static/private';

const isConnectionError = (error: any) => {
	const message = error?.message || '';
	return (
		message.includes('ERR_TLS_CERT_ALTNAME_INVALID') ||
		message.includes('fetch failed') ||
		message.includes('Failed to fetch') ||
		message.includes('Network Error') ||
		message.includes('ECONNREFUSED') ||
		message.includes('ETIMEDOUT') ||
		message.includes('socket hang up')
	);
};

export const load = (async ({ request, locals: { supabase } }) => {
	const form = await superValidate(request, zod(signInSchema));

	const {
		data: { user },
		error: authError
	} = await supabase.auth.getUser();

	// Only handle non-auth errors
	if (authError && !authError.message?.includes('Auth session missing')) {
		if (isConnectionError(authError)) {
			setError(form, '', 'auth_connection_error');
			return { form };
		}
		setError(form, '', authError.message);
		return { form };
	}

	if (user) {
		redirect(303, '/event');
	}

	return { form };
}) satisfies PageServerLoad;

export const actions = {
	default: async ({ request, locals: { supabase, brand } }) => {
		const formData = await request.formData();
		const eventId = formData.get('eventId') as string | null;

		const form = await superValidate(formData, zod(signInSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const { data: authData, error: signInError } = await supabase.auth.signInWithPassword({
			email: form.data.email,
			password: form.data.password
		});

		if (signInError) {
			// Connection errors
			if (isConnectionError(signInError)) {
				return setError(form, '', 'auth_connection_error');
			}

			// Invalid login credentials
			if (signInError.message.includes('Invalid login credentials')) {
				return setError(form, 'email', 'The email or password you entered is incorrect');
			}

			// Default error case
			return setError(form, '', signInError.message);
		}

		// Upsert brand_managed_profile to track this user under the current brand
		if (authData.user) {
			const { error: upsertError } = await supabase.from('brand_managed_profile').upsert(
				{
					brand_id: brand.id,
					managed_profile_id: authData.user.id,
					updated_at: new Date().toISOString()
				},
				{
					onConflict: 'brand_id,managed_profile_id',
					ignoreDuplicates: true
				}
			);

			if (upsertError) {
				console.error('[sign-in] Failed to upsert brand_managed_profile:', upsertError);
				// Don't fail the sign-in, just log the error
			}
		}

		// Redirect is handled by Supabase Auth
		return { form, eventId };
	}
} satisfies Actions;
