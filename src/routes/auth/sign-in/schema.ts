import { z } from 'zod';

export const signInSchema = z.object({
	email: z
		.string({ required_error: 'Email is required' })
		.trim()
		.toLowerCase()
		.email('Please enter a valid email address'),
	password: z
		.string({ required_error: 'Password is required' })
		.trim()
		.min(8, 'Password must be at least 8 characters')
		.max(64, 'Password must be less than 64 characters')
});

export type SignInSchema = typeof signInSchema;
