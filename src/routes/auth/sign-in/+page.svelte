<script lang="ts">
	import type { SuperValidated, Infer } from 'sveltekit-superforms';
	import type { SignInSchema } from './schema';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import type { Database } from '$lib/supabase/database.types';
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form/index.js';
	import { page } from '$app/state';

	import { Loader2 } from '@lucide/svelte';
	import { Field, Control, Label, FieldErrors } from 'formsnap';
	import { superForm } from 'sveltekit-superforms/client';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { signInSchema } from './schema';
	import * as Card from '$lib/components/ui/card';
	import * as m from '$lib/paraglide/messages.js';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
	import { AlertCircle, Info } from '@lucide/svelte';
	import ShineBorder, { SHINE_COLORS } from '../../components/ShineBorder.svelte';
	import AuroraText from '../../components/AuroraText.svelte';

	interface Props {
		data: {
			form: SuperValidated<Infer<SignInSchema>>;
			supabase: SupabaseClient<Database>;
			brand: {
				name_short: LocalizedText;
			};
		};
		error: (message: string) => void;
	}

	let props: Props = $props();
	const eventId = $derived(page.url.searchParams.get('eid'));

	const form = superForm(props.data.form, {
		validators: zodClient(signInSchema),
		dataType: 'json',
		errorSelector: '.auth-form-error-message',
		delayMs: 300,
		taintedMessage: null,
		onError: ({ result }) => {
			const errorMessage = result.error?.message || '';
			if (errorMessage === 'auth_connection_error') {
				props.error(m.auth_connection_error());
			} else {
				props.error(errorMessage || 'An unexpected error occurred');
			}
		},
		onUpdated: ({ form }) => {
			// Handle form-level errors
			const errors = form.errors._errors;
			if (errors && errors.length > 0) {
				const errorMessage = errors[0];
				if (errorMessage === 'auth_connection_error') {
					props.error(m.auth_connection_error());
				} else {
					props.error(errorMessage);
				}
			}

			// Also handle field-specific errors
			if (form.errors.email && form.errors.email.length > 0) {
				props.error(form.errors.email[0]);
			}
		}
	});

	const { submitting, errors, message, form: formData, allErrors, enhance } = form;
</script>

<Card.Root class="relative overflow-hidden">
	<ShineBorder duration={12} borderWidth={1} />
	<Card.Header class="space-y-1">
		<Card.Title>
			{m.auth_sign_in_title()}
		</Card.Title>
		<Card.Description>
			{m.auth_sign_in_description({
				brand: getLocalizedText(props.data.brand.name_short, getLocale())
			})}
		</Card.Description>
	</Card.Header>
	<Card.Content>
		{#if eventId}
			<Alert class="mb-4">
				<Info class="size-4" />
				<AlertTitle>Event Registration</AlertTitle>
				<AlertDescription>
					Please sign in to register for the event. After signing in, you'll be redirected to
					complete your registration.
				</AlertDescription>
			</Alert>
		{/if}

		{#if $allErrors.length > 0 && $allErrors.find((err) => err.path === '')}
			<Alert variant="destructive" class="auth-form-error-message mb-4">
				<AlertCircle class="size-4" />
				<AlertDescription>
					{#if $allErrors.find((err) => err.path === '')?.messages[0] === 'auth_connection_error'}
						{m.auth_connection_error()}
					{:else}
						{$allErrors.find((err) => err.path === '')?.messages[0] || 'An error occurred'}
					{/if}
				</AlertDescription>
			</Alert>
		{:else if $errors._errors && $errors._errors.length > 0}
			<Alert variant="destructive" class="auth-form-error-message mb-4">
				<AlertCircle class="size-4" />
				<AlertDescription>
					{$errors._errors[0] === 'auth_connection_error'
						? m.auth_connection_error()
						: $errors._errors[0]}
				</AlertDescription>
			</Alert>
		{:else if $message}
			<Alert variant="destructive" class="mb-4">
				<AlertCircle class="size-4" />
				<AlertDescription>{$message}</AlertDescription>
			</Alert>
		{/if}

		<form method="POST" class="grid gap-4" use:enhance>
			{#if eventId}
				<input type="hidden" name="eventId" value={eventId} />
			{/if}

			<div class="grid gap-4">
				<Field {form} name="email">
					<Control>
						{#snippet children({ props })}
							<div class="grid gap-2">
								<Label>{m.auth_sign_in_email_label()}</Label>
								<Input
									{...props}
									type="email"
									autocomplete="email"
									bind:value={$formData.email}
									placeholder={m.auth_sign_in_email_placeholder()}
									aria-invalid={$errors.email ? 'true' : undefined}
								/>
								<FieldErrors class="auth-form-field-error text-sm text-destructive" />
							</div>
						{/snippet}
					</Control>
				</Field>

				<Field {form} name="password">
					<Control>
						{#snippet children({ props })}
							<div class="grid gap-2">
								<Label>{m.auth_sign_in_password_label()}</Label>
								<Input
									{...props}
									type="password"
									autocomplete="current-password"
									bind:value={$formData.password}
									placeholder={m.auth_sign_in_password_placeholder()}
									aria-invalid={$errors.password ? 'true' : undefined}
								/>
								<FieldErrors class="auth-form-field-error text-sm text-destructive" />
								<div class="flex justify-end text-sm">
									<a
										href="/auth/forgot-password?email={encodeURIComponent($formData.email)}{eventId
											? `&eid=${eventId}`
											: ''}"
										class="text-muted-foreground hover:text-foreground"
									>
										{m.auth_sign_in_forgot_password()}
									</a>
								</div>
							</div>
						{/snippet}
					</Control>
				</Field>
			</div>

			<Form.Button type="submit" class="mt-4 w-full" disabled={$submitting}>
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
					{m.auth_sign_in_signing_in()}
				{:else}
					{m.auth_sign_in_sign_in()}
				{/if}
			</Form.Button>
		</form>
	</Card.Content>
	<Card.Footer class="flex flex-col space-y-4">
		<p class="px-6 text-center text-xs text-muted-foreground">
			By signing in, you agree to our
			<a href="/legal/terms" class="text-foreground hover:text-foreground/90">Terms of Service</a>
			and
			<a href="/legal/privacy" class="text-foreground hover:text-foreground/90">Privacy Policy</a>.
		</p>
	</Card.Footer>
</Card.Root>

<div class="mt-4 text-center text-sm">
	<span class="text-muted-foreground">{m.auth_sign_in_no_account()}</span>
	{' '}
	<a
		href="/auth/sign-up{eventId ? `?eid=${eventId}` : ''}"
		class="text-foreground hover:text-foreground/90">{m.auth_sign_in_sign_up()}</a
	>
</div>
