import { fail, redirect } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import type { ServerLoad } from '@sveltejs/kit';
import type { Actions } from '@sveltejs/kit';
import { verifyAccountSchema } from './schema';
import type { SupabaseClient } from '@supabase/supabase-js';

interface Locals {
	supabase: SupabaseClient;
}

export const load = (async ({ locals: { supabase }, url }: { locals: Locals; url: URL }) => {
	const {
		data: { session }
	} = await supabase.auth.getSession();

	// Only redirect if the session exists AND the email is confirmed
	if (session && session.user && session.user.email_confirmed_at) {
		throw redirect(303, '/event');
	}

	const token = url.searchParams.get('token');
	if (!token && import.meta.env.PROD) {
		throw redirect(303, '/auth/sign-in');
	}

	const form = superValidate(zod(verifyAccountSchema), {
		id: 'verify-account',
		defaults: {
			code: ''
		}
	});
	return { form, token };
}) satisfies ServerLoad;

export const actions = {
	default: async ({
		request,
		locals: { supabase },
		url
	}: {
		request: Request;
		locals: Locals;
		url: URL;
	}) => {
		const form = await superValidate(request, zod(verifyAccountSchema));

		if (!form.valid) {
			return message(form, 'Please enter a valid 6-digit code', { status: 400 });
		}

		const token = url.searchParams.get('token');
		if (!token && import.meta.env.PROD) {
			return message(form, 'Invalid verification link. Please try again.', {
				status: 400
			});
		}

		try {
			if (!token) {
				return message(form, 'Verification token is required', { status: 400 });
			}

			if (!form.data.code || form.data.code.length !== 6) {
				return message(form, 'Please enter a valid 6-digit code', { status: 400 });
			}

			const { error } = await supabase.auth.verifyOtp({
				token_hash: token,
				type: 'email'
			});

			if (error) {
				return message(form, error.message, { status: 400 });
			}

			throw redirect(303, '/event');
		} catch (error) {
			if (error instanceof Error) {
				return message(form, error.message, { status: 400 });
			}
			return message(form, 'Failed to verify account. Please try again.', { status: 400 });
		}
	}
} satisfies Actions;
