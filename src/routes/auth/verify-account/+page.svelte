<script lang="ts">
	import type { SuperValidated, Infer } from 'sveltekit-superforms';
	import type { VerifyAccountSchema } from './schema';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { AlertCircle, Loader2 } from '@lucide/svelte';
	import { Button } from '$lib/components/ui/button';
	import * as InputOTP from '$lib/components/ui/input-otp';
	import { superForm } from 'sveltekit-superforms/client';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { verifyAccountSchema } from './schema';
	import { Field, Control, FieldErrors } from 'formsnap';
	import { slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import type { Database } from '$lib/supabase/database.types';
	import * as Card from '$lib/components/ui/card';
	import * as m from '$lib/paraglide/messages.js';
	import { page } from '$app/state';

	interface Props {
		data: {
			form: SuperValidated<Infer<VerifyAccountSchema>>;
			supabase: SupabaseClient<Database>;
			brand: any;
		};
	}

	let props: Props = $props();
	const { data } = props;
	const { supabase } = data;
	const email = $derived(page.url.searchParams.get('email') || '');
	const eventId = $derived(page.url.searchParams.get('eid'));
	let error = $state('');

	const form = superForm(data.form, {
		validators: zodClient(verifyAccountSchema),
		dataType: 'json',
		onError: ({ result }) => {
			error = result.error.message;
		},
		onSubmit: ({ formData }) => {
			const code = formData.get('code') as string;
			if (!code || code.length !== 6) {
				error = 'Please enter a valid 6-digit code';
				return false;
			}
		}
	});
	const { form: formData, enhance, submitting } = form;
</script>

<div class="auth-form">
	<div class="auth-form-header">
		<h1 class="auth-form-title">Verify Account</h1>
		<p class="auth-form-description">Enter the verification code sent to your email</p>
	</div>

	{#if error}
		<div transition:slide|local={{ duration: 200, easing: quintOut }}>
			<Alert variant="destructive" class="auth-form-error">
				<AlertCircle class="size-4" />
				<AlertDescription>{error}</AlertDescription>
			</Alert>
		</div>
	{/if}

	<div class="auth-form-container">
		<form method="POST" use:enhance>
			<div class="space-y-4">
				<Field {form} name="code">
					<Control>
						{#snippet children({ props })}
							<div class="flex justify-center">
								<InputOTP.Root
									maxlength={6}
									value={$formData.code ?? ''}
									onValueChange={(value) => ($formData.code = value)}
								>
									{#snippet children({ cells })}
										<InputOTP.Group>
											{#each cells.slice(0, 3) as cell}
												<InputOTP.Slot {cell} />
											{/each}
										</InputOTP.Group>
										<InputOTP.Separator>·</InputOTP.Separator>
										<InputOTP.Group>
											{#each cells.slice(3, 6) as cell}
												<InputOTP.Slot {cell} />
											{/each}
										</InputOTP.Group>
									{/snippet}
								</InputOTP.Root>
							</div>
							<FieldErrors class="mt-2 text-center text-xs text-destructive" />
						{/snippet}
					</Control>
				</Field>

				<div class="auth-form-footer">
					<Button type="submit" class="auth-form-submit" disabled={$submitting}>
						{#if $submitting}
							<Loader2 class="mr-2 size-4 animate-spin" />
							Verifying...
						{:else}
							Verify Account
						{/if}
					</Button>
					<a href="/auth/sign-in{eventId ? `?eid=${eventId}` : ''}" class="auth-form-link"
						>Back to Sign In</a
					>
				</div>
			</div>
		</form>
	</div>
</div>

<div class="mt-4 text-center text-sm">
	<span class="text-muted-foreground">{m.auth_verify_already_verified()}</span>
	{' '}
	<a
		href="/auth/sign-in{eventId ? `?eid=${eventId}` : ''}"
		class="text-foreground hover:text-foreground/90">{m.auth_verify_sign_in()}</a
	>
</div>

<style>
</style>
