<script lang="ts">
	import type { SuperValidated, Infer } from 'sveltekit-superforms';
	import type { ResetPasswordSchema } from './schema';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { AlertCircle, Loader2 } from '@lucide/svelte';
	import { Field, Control, Label, FieldErrors } from 'formsnap';
	import { superForm } from 'sveltekit-superforms/client';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { resetPasswordSchema } from './schema';
	import { slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import * as Card from '$lib/components/ui/card';
	import { toast } from 'svelte-sonner';
	import ShineBorder from '../../components/ShineBorder.svelte';
	import AuroraText from '../../components/AuroraText.svelte';

	interface Props {
		data: {
			form: SuperValidated<Infer<ResetPasswordSchema>>;
		};
	}

	let { data }: Props = $props();

	const formHandler = superForm(data.form, {
		validators: zodClient(resetPasswordSchema),
		dataType: 'json',
		onError: ({ result }) => {
			toast.error(result.error?.message || 'An error occurred');
		}
	});

	const { form, enhance, submitting, errors, message } = formHandler;
</script>

<Card.Root class="relative overflow-hidden">
	<ShineBorder duration={12} borderWidth={1} />
	<Card.Header class="space-y-1">
		<Card.Title>Reset Password</Card.Title>
		<Card.Description>Enter your new password below</Card.Description>
	</Card.Header>
	<form id="reset-password-form" method="POST" use:enhance>
		<Card.Content class="grid gap-4">
			{#if $message}
				<div class="text-sm text-destructive">{$message}</div>
			{/if}

			<Field form={formHandler} name="password">
				<Control>
					{#snippet children({ props })}
						<div class="grid gap-2">
							<Label>New Password</Label>
							<Input
								{...props}
								type="password"
								bind:value={$form.password}
								placeholder="••••••••"
							/>
							<FieldErrors class="text-sm text-destructive" />
						</div>
					{/snippet}
				</Control>
			</Field>

			<Field form={formHandler} name="confirmPassword">
				<Control>
					{#snippet children({ props })}
						<div class="grid gap-2">
							<Label>Confirm Password</Label>
							<Input
								{...props}
								type="password"
								bind:value={$form.confirmPassword}
								placeholder="••••••••"
							/>
							<FieldErrors class="text-sm text-destructive" />
						</div>
					{/snippet}
				</Control>
			</Field>
		</Card.Content>
		<Card.Footer class="flex flex-col gap-4">
			<Button type="submit" class="w-full" disabled={$submitting}>
				{#if $submitting}
					<Loader2 class="mr-2 size-4 animate-spin" />
					Resetting Password...
				{:else}
					Reset Password
				{/if}
			</Button>
		</Card.Footer>
	</form>
</Card.Root>

<div class="mt-4 text-center text-sm">
	<a href="/auth/sign-in" class="text-foreground hover:text-foreground/90">Back to Sign In</a>
</div>

<style>
</style>
