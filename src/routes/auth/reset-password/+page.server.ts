import { redirect, type Actions } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { fail } from '@sveltejs/kit';
import { superValidate, setError } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { resetPasswordSchema } from './schema';

export const load = (({ locals: { supabase } }) => {
	const form = superValidate(zod(resetPasswordSchema), { errors: false });
	return { form };
}) satisfies PageServerLoad;

export const actions = {
	default: async ({ request, locals: { supabase } }) => {
		const form = await superValidate(request, zod(resetPasswordSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		const { error } = await supabase.auth.updateUser({
			password: form.data.password
		});

		if (error) {
			return setError(form, 'password', error.message);
		}

		throw redirect(303, '/auth/sign-in?reset=success');
	}
} satisfies Actions;
