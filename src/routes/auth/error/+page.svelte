<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import ShineBorder from '../../components/ShineBorder.svelte';
	import AuroraText from '../../components/AuroraText.svelte';
</script>

<Card.Root class="relative overflow-hidden">
	<ShineBorder duration={12} borderWidth={1} />
	<Card.Header>
		<Card.Title>Authentication Error</Card.Title>
		<Card.Description>There was a problem with the authentication process.</Card.Description>
	</Card.Header>
	<Card.Content>
		<p>Please try again or contact support if the problem persists.</p>
	</Card.Content>
	<Card.Footer>
		<Button href="/auth" class="w-full">Return to Login</Button>
	</Card.Footer>
</Card.Root>
