<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Badge } from '$lib/components/ui/badge';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar';
	import { Search, Users, Key, ShieldCheck } from '@lucide/svelte';
	import { format } from 'date-fns';
	import { toast } from 'svelte-sonner';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { LocaleKey } from '$lib/utils/localization';
	import type { Database } from '$lib/supabase/database.types';

	// Types
	type ProductRow = Database['public']['Tables']['product']['Row'];
	type ProductFeatureAccessRow = Database['public']['Tables']['product_feature_access']['Row'];
	type FeatureAccessRow = Database['public']['Tables']['feature_access']['Row'];
	type OrderProductRow = Database['public']['Tables']['order_product']['Row'];
	type LocalizedText = {
		[key: string]: string;
	};

	interface Consumer {
		id: string;
		func_given_name_en_first?: string;
		func_family_name_en_first?: string;
		auto_user_email?: string;
	}

	interface OrderProduct extends Partial<OrderProductRow> {
		id: string;
		consumer: Consumer;
		order: {
			id: string;
			created_at: string;
		};
	}

	interface FeatureAccess extends Partial<FeatureAccessRow> {
		id: string;
		name: LocalizedText;
		description: LocalizedText;
		scope: string;
	}

	interface ProductFeatureAccess extends Partial<ProductFeatureAccessRow> {
		id: string;
		feature_access_id: string;
		feature_access_settings: Record<string, any>;
		feature_access: FeatureAccess;
	}

	interface ProductWithFeatureAccess extends Partial<ProductRow> {
		id: string;
		title: LocalizedText;
		product_feature_access: ProductFeatureAccess[];
		order_products: OrderProduct[];
	}

	interface Props {
		product: ProductWithFeatureAccess;
		featureAccessList: FeatureAccess[];
		currentLocale: LocaleKey;
		supabase: SupabaseClient;
	}

	// Props and state
	let { product, featureAccessList, currentLocale, supabase }: Props = $props();

	let searchQuery = $state('');
	let loading = $state(false);

	// Derived values
	let filteredConsumers = $derived(
		product.order_products
			.filter((orderProduct) => {
				if (!searchQuery) return true;

				const query = searchQuery.toLowerCase();
				const name = `${orderProduct.consumer.func_given_name_en_first || ''} ${
					orderProduct.consumer.func_family_name_en_first || ''
				}`.toLowerCase();
				const email = orderProduct.consumer.auto_user_email?.toLowerCase() || '';

				return name.includes(query) || email.includes(query);
			})
			.sort((a, b) => {
				// Sort by name
				const nameA = `${a.consumer.func_given_name_en_first || ''} ${
					a.consumer.func_family_name_en_first || ''
				}`.toLowerCase();
				const nameB = `${b.consumer.func_given_name_en_first || ''} ${
					b.consumer.func_family_name_en_first || ''
				}`.toLowerCase();
				return nameA.localeCompare(nameB);
			})
	);

	// Get product title
	function getProductTitle(): string {
		return (
			String(getLocalizedText(product.title as LocalizedText, currentLocale)) || 'Untitled Product'
		);
	}

	// Get user's full name
	function getFullName(consumer: Consumer): string {
		const firstName = consumer.func_given_name_en_first || '';
		const lastName = consumer.func_family_name_en_first || '';
		return `${firstName} ${lastName}`.trim() || 'Unknown User';
	}

	// Get user's initials for avatar
	function getInitials(consumer: Consumer): string {
		const firstName = consumer.func_given_name_en_first || '';
		const lastName = consumer.func_family_name_en_first || '';
		return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || 'U';
	}

	// Format date
	function formatDate(dateString: string): string {
		return format(new Date(dateString), 'MMM d, yyyy');
	}
</script>

<div class="space-y-4">
	<!-- Product Header -->
	<div class="flex flex-col space-y-1">
		<div class="flex items-center gap-2">
			<h2 class="text-xl font-semibold">
				{getProductTitle()}
			</h2>
			<Badge variant="outline" class="ml-auto">
				{product.order_products.length} Users
			</Badge>
		</div>
		<div class="flex flex-wrap gap-1 py-1">
			{#each product.product_feature_access as access}
				<Badge variant="secondary" class="flex items-center gap-1">
					<Key class="h-3.5 w-3.5" />
					<span>
						{getLocalizedText(access.feature_access.name as LocalizedText, currentLocale)}
					</span>
				</Badge>
			{/each}
		</div>
	</div>

	<!-- Search -->
	<div class="relative w-full">
		<Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
		<Input
			type="search"
			placeholder="Search users..."
			class="w-full pl-8"
			value={searchQuery}
			oninput={(e) => (searchQuery = e.currentTarget.value)}
		/>
	</div>

	<!-- Consumer List -->
	{#if loading && product.order_products.length === 0}
		<div class="space-y-2">
			{#each Array(5) as _}
				<Skeleton class="h-16 w-full" />
			{/each}
		</div>
	{:else if filteredConsumers.length === 0}
		<div class="flex flex-col items-center justify-center py-8 text-center">
			<Users class="mb-2 h-12 w-12 text-muted-foreground" />
			{#if searchQuery}
				<h3 class="text-lg font-medium">No matching users</h3>
				<p class="mt-1 text-sm text-muted-foreground">Try a different search term</p>
			{:else}
				<h3 class="text-lg font-medium">No users found</h3>
				<p class="mt-1 text-sm text-muted-foreground">
					There are no users with access to this product
				</p>
			{/if}
		</div>
	{:else}
		<div class="space-y-2">
			{#each filteredConsumers as orderProduct}
				<div class="flex items-center justify-between rounded-md border border-border p-4">
					<div class="flex items-center space-x-4">
						<Avatar>
							<AvatarImage src="" alt={getFullName(orderProduct.consumer)} />
							<AvatarFallback>{getInitials(orderProduct.consumer)}</AvatarFallback>
						</Avatar>
						<div>
							<p class="font-medium">{getFullName(orderProduct.consumer)}</p>
							<p class="text-sm text-muted-foreground">
								{orderProduct.consumer.auto_user_email || 'No email'}
							</p>
						</div>
					</div>
					<div class="flex items-center gap-4">
						<div class="flex flex-col items-end">
							<Badge variant="outline" class="flex items-center gap-1">
								<ShieldCheck class="h-3.5 w-3.5" />
								<span>Access granted</span>
							</Badge>
							<span class="mt-1 text-xs text-muted-foreground">
								Since {formatDate(orderProduct.created_at || new Date().toISOString())}
							</span>
						</div>
					</div>
				</div>
			{/each}
		</div>
	{/if}

	<p class="text-sm text-muted-foreground">
		{filteredConsumers.length} users
	</p>
</div>
