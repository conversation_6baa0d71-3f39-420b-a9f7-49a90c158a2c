<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import { Separator } from '$lib/components/ui/separator';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { Badge } from '$lib/components/ui/badge';
	import { Search, Package, Users } from '@lucide/svelte';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { LocaleKey } from '$lib/utils/localization';

	type LocalizedText = {
		[key: string]: string;
	};

	interface ProductFeatureAccess {
		id: string;
		feature_access_id: string;
		feature_access_settings: Record<string, any>;
		feature_access: {
			id: string;
			name: LocalizedText;
			description: LocalizedText;
			scope: string;
		};
	}

	interface Product {
		id: string;
		title: LocalizedText;
		product_feature_access: ProductFeatureAccess[];
		order_products: {
			id: string;
			consumer: {
				id: string;
				func_given_name_en_first?: string;
				func_family_name_en_first?: string;
				auto_user_email?: string;
			};
		}[];
	}

	interface Props {
		products: Product[];
		selectedProductId: string;
		onProductChange: (productId: string) => void;
		loading: boolean;
	}

	let { products, selectedProductId, onProductChange, loading }: Props = $props();

	let searchQuery = $state('');
	let currentLocale = $state<LocaleKey>('en');

	// Derived values
	let filteredProducts = $derived(
		products.filter((product) => {
			if (!searchQuery) return true;

			const query = searchQuery.toLowerCase();
			const title =
				getLocalizedText(product.title as LocalizedText, currentLocale)?.toLowerCase() || '';

			return title.includes(query);
		})
	);

	// Get product title
	function getProductTitle(product: Product): string {
		return (
			String(getLocalizedText(product.title as LocalizedText, currentLocale)) || 'Untitled Product'
		);
	}

	// Handle product selection
	function handleProductClick(productId: string): void {
		onProductChange(productId);
	}

	// Get visible feature access badges
	function getVisibleFeatureAccess(product: Product): ProductFeatureAccess[] {
		return product.product_feature_access.slice(0, 3);
	}

	// Get overflow count
	function getOverflowCount(product: Product): number {
		return Math.max(0, product.product_feature_access.length - 3);
	}
</script>

<div class="space-y-4">
	<!-- Search -->
	<div class="relative w-full">
		<Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
		<Input
			type="search"
			placeholder="Search products..."
			class="w-full pl-8"
			value={searchQuery}
			oninput={(e) => (searchQuery = e.currentTarget.value)}
		/>
	</div>

	<!-- Product List -->
	{#if loading && products.length === 0}
		<div class="space-y-2">
			{#each Array(5) as _}
				<Skeleton class="h-16 w-full" />
			{/each}
		</div>
	{:else if filteredProducts.length === 0}
		<div class="flex flex-col items-center justify-center py-8 text-center">
			<Package class="mb-2 h-12 w-12 text-muted-foreground" />
			{#if searchQuery}
				<h3 class="text-lg font-medium">No matching products</h3>
				<p class="mt-1 text-sm text-muted-foreground">Try a different search term</p>
			{:else}
				<h3 class="text-lg font-medium">No products found</h3>
				<p class="mt-1 text-sm text-muted-foreground">There are no products with feature access</p>
			{/if}
		</div>
	{:else}
		<div class="space-y-2">
			{#each filteredProducts as product}
				<button
					class="group w-full rounded-md border p-3 text-left transition-all hover:bg-secondary
					{selectedProductId === product.id ? 'border-primary bg-secondary/50' : 'border-border'}"
					onclick={() => handleProductClick(product.id)}
				>
					<div class="flex justify-between">
						<div class="min-w-0 flex-1">
							<h3 class="truncate font-medium">{getProductTitle(product)}</h3>
							<div class="mt-1 flex items-center gap-2">
								<div class="flex items-center text-sm text-muted-foreground">
									<Users class="mr-1 h-3.5 w-3.5" />
									<span>{product.order_products.length} Users</span>
								</div>

								<div class="flex items-center gap-1">
									{#each getVisibleFeatureAccess(product) as access}
										<Badge variant="outline" class="text-xs">
											{getLocalizedText(access.feature_access.name as LocalizedText, currentLocale)}
										</Badge>
									{/each}

									{#if getOverflowCount(product) > 0}
										<Badge variant="outline" class="text-xs">
											+{getOverflowCount(product)} more
										</Badge>
									{/if}
								</div>
							</div>
						</div>
					</div>
				</button>
			{/each}
		</div>
	{/if}

	<p class="text-sm text-muted-foreground">
		{filteredProducts.length} products
	</p>

	<Separator />
</div>
