import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { supabase, user, brand } }) => {
	// Check if user is authenticated
	if (!user) {
		throw redirect(303, '/auth/signin');
	}

	// Check if user has a brand context
	if (!brand?.id) {
		console.error('No brand context available');
		return {
			products: [],
			featureAccessList: []
		};
	}

	// Fetch all feature access types (for reference/display)
	const { data: featureAccessList, error: featureAccessError } = await supabase
		.from('feature_access')
		.select('id, name, description, scope')
		.order('id');

	if (featureAccessError) {
		console.error('Error fetching feature access list:', featureAccessError);
	}

	// Fetch all products under current brand that have feature_access associations
	const { data: products, error: productsError } = await supabase
		.from('product')
		.select(
			`
			id,
			title,
			kind,
			auto_stock_available,
			product_feature_access!inner (
				id,
				feature_access_id,
				feature_access_settings,
				feature_access:feature_access_id (
					id,
					name,
					description,
					scope
				)
			)
		`
		)
		.eq('brand_id', brand.id)
		.order('created_at', { ascending: false });

	if (productsError) {
		console.error('Error fetching products with feature access:', productsError);
	}

	// For each product, fetch the associated order_products and consumers
	const productsWithOrders = [];

	if (products && products.length > 0) {
		for (const product of products) {
			const { data: orderProducts, error: orderProductsError } = await supabase
				.from('order_product')
				.select(
					`
					id,
					created_at,
					canceled_at,
					consumer:consumer_profile_id (
						id, 
						func_given_name_en_first,
						func_family_name_en_first,
						auto_user_email
					),
					order:order_id (
						id,
						created_at
					)
				`
				)
				.eq('auto_product_price_product_id', product.id)
				.is('canceled_at', null);

			if (orderProductsError) {
				console.error(
					`Error fetching order products for product ${product.id}:`,
					orderProductsError
				);
			}

			productsWithOrders.push({
				...product,
				order_products: orderProducts || []
			});
		}
	}

	return {
		products: productsWithOrders,
		featureAccessList: featureAccessList || []
	};
};
