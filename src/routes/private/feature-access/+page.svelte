<script lang="ts">
	import { PageContainer } from '$lib/components/layout';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { LocaleKey } from '$lib/utils/localization';
	import type { PageData } from './$types';
	import type { Database } from '$lib/supabase/database.types';
	import ProductSelector from './ProductSelector.svelte';
	import PermissionConsumerList from './PermissionConsumerList.svelte';

	// Types
	type ProductRow = Database['public']['Tables']['product']['Row'];
	type ProductFeatureAccessRow = Database['public']['Tables']['product_feature_access']['Row'];
	type FeatureAccessRow = Database['public']['Tables']['feature_access']['Row'];
	type OrderProductRow = Database['public']['Tables']['order_product']['Row'];
	type ProfileRow = Database['public']['Tables']['profile']['Row'];
	type LocalizedText = {
		[key: string]: string;
	};

	interface Consumer extends Partial<ProfileRow> {
		id: string;
		func_given_name_en_first?: string;
		func_family_name_en_first?: string;
		auto_user_email?: string;
	}

	interface OrderProduct extends Partial<OrderProductRow> {
		id: string;
		consumer: Consumer;
		order: {
			id: string;
			created_at: string;
		};
	}

	interface FeatureAccess extends Partial<FeatureAccessRow> {
		id: string;
		name: LocalizedText;
		description: LocalizedText;
		scope: string;
	}

	interface ProductFeatureAccess extends Partial<ProductFeatureAccessRow> {
		id: string;
		feature_access_id: string;
		feature_access_settings: Record<string, any>;
		feature_access: FeatureAccess;
	}

	interface ProductWithFeatureAccess extends Partial<ProductRow> {
		id: string;
		title: LocalizedText;
		product_feature_access: ProductFeatureAccess[];
		order_products: OrderProduct[];
	}

	interface Props {
		data: PageData;
	}

	// Props and state initialization
	let { data }: Props = $props();

	let currentLocale = $state<LocaleKey>('en');
	let selectedProductId = $state('');
	let loading = $state(false);
	let products = $state<ProductWithFeatureAccess[]>(data.products);
	let featureAccessList = $state<FeatureAccess[]>(data.featureAccessList);

	// Derived values
	let selectedProduct = $derived(
		products.find((product) => product.id === selectedProductId) as
			| ProductWithFeatureAccess
			| undefined
	);

	// Functions
	function handleProductChange(productId: string): void {
		selectedProductId = productId;
	}

	// Initialize with first product if available
	$effect(() => {
		if (products.length > 0 && !selectedProductId) {
			selectedProductId = products[0].id;
		}
	});
</script>

{#snippet actions()}
	<div class="flex items-center gap-2">
		<!-- No actions needed in the header -->
	</div>
{/snippet}

{#snippet content()}
	<div class="space-y-6 md:grid md:grid-cols-12 md:gap-6 md:space-y-0">
		<!-- Product Selector -->
		<div class="md:col-span-4 lg:col-span-4 xl:col-span-3">
			<h3 class="mb-2 text-lg font-medium">Products with Feature Access</h3>
			<ProductSelector
				{products}
				{selectedProductId}
				onProductChange={handleProductChange}
				{loading}
			/>
		</div>

		<!-- Permission Consumer List -->
		<div class="md:col-span-8 lg:col-span-8 xl:col-span-9">
			{#if selectedProduct}
				<PermissionConsumerList
					product={selectedProduct}
					{featureAccessList}
					{currentLocale}
					supabase={data.supabase}
				/>
			{:else if products.length > 0}
				<div class="flex h-40 items-center justify-center">
					<p class="text-muted-foreground">Select a product to view permissions</p>
				</div>
			{:else}
				<div class="flex h-40 items-center justify-center">
					<p class="text-muted-foreground">No products with feature access available</p>
				</div>
			{/if}
		</div>
	</div>
{/snippet}

<PageContainer
	title="Feature Access Management"
	description="Manage user permissions by product-based feature access"
	{actions}
	{content}
/>
