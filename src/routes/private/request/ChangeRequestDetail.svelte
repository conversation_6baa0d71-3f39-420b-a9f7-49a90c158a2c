<script lang="ts">
	import type { Database } from '$lib/supabase/database.types';
	import { format } from 'date-fns';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import { Button } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Separator } from '$lib/components/ui/separator';
	import { Badge } from '$lib/components/ui/badge';
	import { Card } from '$lib/components/ui/card';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { Check, Edit, ChevronDown, Package, Calendar, Users, FileText } from '@lucide/svelte';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import { goto, invalidateAll } from '$app/navigation';
	import DeleteOfferingButton from '$lib/components/shared/DeleteOfferingButton.svelte';

	interface Props {
		request: Database['public']['Tables']['change_request']['Row'];
		supabase: SupabaseClient;
	}

	let { request, supabase }: Props = $props();

	// Types
	type LocalizedText = {
		[key: string]: string;
	};

	type ChangeProductRow = Database['public']['Tables']['change_product']['Row'] & {
		product?: Database['public']['Tables']['product']['Row'];
	};

	type ChangeMetadataRow = Database['public']['Tables']['change_metadata_wikipage']['Row'] & {
		metadata_wikipage?: Database['public']['Tables']['metadata_wikipage']['Row'];
		wikipage?: Database['public']['Tables']['wikipage']['Row'];
	};

	type ChangeEventRow = Database['public']['Tables']['change_event']['Row'] & {
		event?: Database['public']['Tables']['event']['Row'];
	};

	// State
	let changeProducts = $state<ChangeProductRow[]>([]);
	let changeMetadata = $state<ChangeMetadataRow[]>([]);
	let changeEvents = $state<ChangeEventRow[]>([]);
	let creationMetadata = $state<any[]>([]);
	let loading = $state(true);
	let changeActor = $state<any | null>(null);
	let changeReason = $state<any | null>(null);
	let publishingStates = $state<any[]>([]);
	let currentLocale = $derived(getLocale());

	// Fetch all related data
	async function loadRequestDetails() {
		loading = true;
		try {
			// Fetch all change request related data in parallel
			const [
				{ data: products, error: productsError },
				{ data: metadata, error: metadataError },
				{ data: events, error: eventsError },
				{ data: creationData, error: creationError },
				{ data: actorWithReason, error: actorError },
				{ data: states, error: statesError }
			] = await Promise.all([
				supabase
					.from('change_product')
					.select('*, product:apply_to_product_id(*)')
					.eq('change_request_id', request.id),
				supabase
					.from('change_metadata_wikipage')
					.select(
						'*, metadata_wikipage:apply_to_metadata_wikipage_id(*), wikipage:override_wikipage_id(*)'
					)
					.eq('change_request_id', request.id),
				supabase
					.from('change_event')
					.select('*, event:apply_to_event_id(*)')
					.eq('change_request_id', request.id),
				supabase.from('metadata').select('*').eq('creation_request_id', request.id),
				supabase
					.from('profile')
					.select('*, change_reason:change_request(change_reason(*))')
					.eq('id', request.actor_profile_id)
					.single(),
				supabase.from('publishing_state').select('*')
			]);

			if (productsError) throw productsError;
			if (metadataError) throw metadataError;
			if (eventsError) throw eventsError;
			if (creationError) throw creationError;
			if (actorError) throw actorError;
			if (statesError) throw statesError;

			changeProducts = products || [];
			changeMetadata = metadata || [];
			changeEvents = events || [];
			creationMetadata = creationData || [];
			changeActor = actorWithReason;
			changeReason = actorWithReason?.change_reason?.[0]?.change_reason || null;
			publishingStates = states || [];
		} catch (error) {
			console.error('Error loading change request details:', error);
		} finally {
			loading = false;
		}
	}

	async function publishChangeRequest(state: string) {
		try {
			const { error } = await supabase
				.from('change_request')
				.update({ status: state })
				.eq('id', request.id);

			if (error) throw error;

			// Update the local request object
			request = { ...request, status: state };
		} catch (error) {
			console.error('Error publishing change request:', error);
		}
	}

	// Navigate to appropriate edit page
	function handleEdit(type: string, id?: string) {
		if (type === 'creation') {
			goto(`/private/request/offering/create?rid=${request.id}&mode=edit`);
		} else if (type === 'product' && id) {
			goto(`/private/product/${id}/edit?change_request=${request.id}`);
		} else if (type === 'event' && id) {
			goto(`/private/event/${id}/edit?change_request=${request.id}`);
		}
	}

	// Duplicate the request
	function handleDuplicate() {
		goto(`/private/request/offering/create?rid=${request.id}&mode=duplicate`);
	}

	// Add handler for successful deletion
	async function handleDeleteSuccess() {
		// Refresh the page data
		await invalidateAll();
		// Could also navigate away or show a message
		goto('/private/request');
	}

	// Load data when request changes
	$effect(() => {
		if (request?.id) {
			loadRequestDetails();
		}
	});
</script>

<div class="flex flex-col gap-6">
	<!-- Header with metadata -->
	<div class="flex flex-col gap-2">
		<div class="flex items-center justify-between">
			<h2 class="text-xl font-bold">
				<a href={`/private/request/${request.id}`} class="hover:underline">
					Change Request #{request.id.substring(0, 8)}
				</a>
			</h2>

			<div class="flex items-center gap-2">
				<Badge variant={request.status === 'published' ? 'default' : 'outline'}>
					{request.status}
				</Badge>

				<DropdownMenu.Root>
					<DropdownMenu.Trigger>
						<Button variant="outline" size="sm">
							Publish <ChevronDown class="ml-1 h-4 w-4" />
						</Button>
					</DropdownMenu.Trigger>
					<DropdownMenu.Content>
						{#each publishingStates as state}
							<DropdownMenu.Item onclick={() => publishChangeRequest(state.id)}>
								{state.id}
							</DropdownMenu.Item>
						{/each}
					</DropdownMenu.Content>
				</DropdownMenu.Root>
			</div>
		</div>

		<div class="text-muted-foreground grid grid-cols-2 gap-4 text-sm">
			<div>
				<span class="font-medium">Created:</span>
				{format(new Date(request.created_at), 'PPpp')}
			</div>
			<div>
				<span class="font-medium">Updated:</span>
				{format(new Date(request.updated_at), 'PPpp')}
			</div>
			<div>
				<span class="font-medium">Actor:</span>
				{loading ? 'Loading...' : changeActor?.email || 'Unknown'}
			</div>
			<div>
				<span class="font-medium">Role:</span>
				{request.auto_change_actor_role_id || 'creator'}
			</div>
			{#if request.change_reason_id || request.change_reason_message}
				<div class="col-span-2">
					<span class="font-medium">Reason:</span>
					{request.change_reason_id
						? getLocalizedText(changeReason?.title as LocalizedText, currentLocale) ||
							request.change_reason_id
						: 'Product Creation'}
				</div>
			{/if}
		</div>
	</div>

	<Separator />

	{#if loading}
		<div class="space-y-3">
			{#each Array(3) as _}
				<Card class="p-4">
					<Skeleton class="mb-3 h-6 w-1/3" />
					<Skeleton class="mb-2 h-4 w-full" />
					<Skeleton class="h-4 w-2/3" />
				</Card>
			{/each}
		</div>
	{:else}
		<!-- Creations Section -->
		{#if creationMetadata.length > 0}
			<div class="space-y-3">
				<div class="flex items-center justify-between">
					<h3 class="text-lg font-medium">Created Metadata</h3>
					<div class="flex gap-2">
						<Button size="sm" variant="outline" onclick={() => handleEdit('creation')}>
							<Edit class="mr-2 h-4 w-4" />
							Edit Creation
						</Button>
						<Button size="sm" variant="outline" onclick={handleDuplicate}>
							<Package class="mr-2 h-4 w-4" />
							Duplicate
						</Button>
						<DeleteOfferingButton
							creationRequestId={request.id}
							onSuccess={handleDeleteSuccess}
							variant="outline"
							size="sm"
							buttonText="Delete Offering"
						/>
					</div>
				</div>
				{#each creationMetadata as metadata}
					<Card class="p-4">
						<div class="flex items-start justify-between">
							<div class="space-y-2">
								<div class="flex items-center gap-2">
									<Package class="text-muted-foreground h-5 w-5" />
									<h4 class="font-medium">
										{getLocalizedText(metadata.title as LocalizedText, currentLocale) || 'Untitled'}
									</h4>
									<Badge variant="outline">{metadata.kind}</Badge>
								</div>
								{#if metadata.subtitle}
									<p class="text-muted-foreground text-sm">
										{getLocalizedText(metadata.subtitle as LocalizedText, currentLocale)}
									</p>
								{/if}
								<div class="text-muted-foreground flex items-center gap-4 text-xs">
									<span>ID: {metadata.id.substring(0, 8)}</span>
									{#if metadata.custom_attribute?.program_id}
										<span>Program: {metadata.custom_attribute.program_id.substring(0, 8)}</span>
									{/if}
									{#if metadata.custom_attribute?.product_form_type}
										<span>Type: {metadata.custom_attribute.product_form_type}</span>
									{/if}
								</div>
							</div>
						</div>
					</Card>
				{/each}
			</div>
		{/if}

		<!-- Product Changes Section -->
		{#if changeProducts.length > 0}
			<div class="space-y-3">
				<div class="flex items-center justify-between">
					<h3 class="text-lg font-medium">Product Changes</h3>
				</div>
				{#each changeProducts as productChange}
					<Card class="p-4">
						<div class="flex items-start justify-between">
							<div class="space-y-2">
								<div class="flex items-center gap-2">
									<Package class="text-muted-foreground h-5 w-5" />
									<h4 class="font-medium">
										Product: {productChange.product?.id || 'Unknown'}
									</h4>
								</div>
								{#if productChange.override_title}
									<p class="text-sm">
										<span class="font-medium">Title:</span>
										{getLocalizedText(productChange.override_title as LocalizedText, currentLocale)}
									</p>
								{/if}
								{#if productChange.override_subtitle}
									<p class="text-sm">
										<span class="font-medium">Subtitle:</span>
										{getLocalizedText(
											productChange.override_subtitle as LocalizedText,
											currentLocale
										)}
									</p>
								{/if}
								{#if productChange.override_publishing_state}
									<p class="text-sm">
										<span class="font-medium">Publishing state:</span>
										<Badge variant="outline">{productChange.override_publishing_state}</Badge>
									</p>
								{/if}
							</div>
							<Button
								size="sm"
								variant="outline"
								onclick={() => handleEdit('product', productChange.apply_to_product_id)}
							>
								<Edit class="mr-2 h-4 w-4" />
								Edit
							</Button>
						</div>
					</Card>
				{/each}
			</div>
		{/if}

		<!-- Event Changes Section -->
		{#if changeEvents.length > 0}
			<div class="space-y-3">
				<div class="flex items-center justify-between">
					<h3 class="text-lg font-medium">Event Changes</h3>
				</div>
				{#each changeEvents as eventChange}
					<Card class="p-4">
						<div class="flex items-start justify-between">
							<div class="space-y-2">
								<div class="flex items-center gap-2">
									<Calendar class="text-muted-foreground h-5 w-5" />
									<h4 class="font-medium">
										Event: {eventChange.event?.id || 'Unknown'}
									</h4>
								</div>
								{#if eventChange.override_start_at}
									<p class="text-sm">
										<span class="font-medium">Start time:</span>
										{format(new Date(eventChange.override_start_at), 'PPpp')}
									</p>
								{/if}
								{#if eventChange.override_duration_minute}
									<p class="text-sm">
										<span class="font-medium">Duration:</span>
										{eventChange.override_duration_minute} minutes
									</p>
								{/if}
								{#if eventChange.override_publishing_state}
									<p class="text-sm">
										<span class="font-medium">Publishing state:</span>
										<Badge variant="outline">{eventChange.override_publishing_state}</Badge>
									</p>
								{/if}
							</div>
							<Button
								size="sm"
								variant="outline"
								onclick={() => handleEdit('event', eventChange.apply_to_event_id)}
							>
								<Edit class="mr-2 h-4 w-4" />
								Edit
							</Button>
						</div>
					</Card>
				{/each}
			</div>
		{/if}

		<!-- Metadata Changes Section -->
		{#if changeMetadata.length > 0}
			<div class="space-y-3">
				<div class="flex items-center justify-between">
					<h3 class="text-lg font-medium">Metadata Changes</h3>
				</div>
				{#each changeMetadata as metadataChange}
					<Card class="p-4">
						<div class="flex items-start justify-between">
							<div class="space-y-2">
								<div class="flex items-center gap-2">
									<Users class="text-muted-foreground h-5 w-5" />
									<h4 class="font-medium">
										Metadata Wikipage: {metadataChange.metadata_wikipage?.id || 'Unknown'}
									</h4>
								</div>
								{#if metadataChange.override_relation}
									<p class="text-sm">
										<span class="font-medium">Relation:</span>
										{metadataChange.override_relation}
									</p>
								{/if}
								{#if metadataChange.override_wikipage_id}
									<p class="text-sm">
										<span class="font-medium">Wikipage:</span>
										{metadataChange.override_wikipage_id}
									</p>
								{/if}
							</div>
						</div>
					</Card>
				{/each}
			</div>
		{/if}

		<!-- Reviews Section -->
		{#if false}
			<div class="space-y-3">
				<h3 class="text-lg font-medium">Reviews</h3>
				<Card class="p-4">
					<div class="text-muted-foreground flex h-20 items-center justify-center text-sm">
						No reviews yet
					</div>
				</Card>
			</div>
		{/if}
	{/if}
</div>
