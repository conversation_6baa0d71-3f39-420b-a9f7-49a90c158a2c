<script lang="ts">
	import { PageContainer } from '$lib/components/layout';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { Edit, Check, X, ArrowLeft } from '@lucide/svelte';
	import { goto, invalidateAll } from '$app/navigation';
	import type { PageData } from './$types';
	import { format } from 'date-fns';
	import { toast } from 'svelte-sonner';

	import CreationRequestDetail from './components/CreationRequestDetail.svelte';
	import ChangeRequestDetail from './components/ChangeRequestDetail.svelte';
	import RequestHeader from './components/RequestHeader.svelte';
	import ReviewsSection from './components/ReviewsSection.svelte';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	let isProcessing = $state(false);

	// Format request ID for display
	const requestIdShort = $derived(data.changeRequest.id.substring(0, 8));

	// Navigate to edit page
	function handleEdit() {
		if (data.isCreationRequest) {
			goto(`/private/request/offering/create?rid=${data.changeRequest.id}&mode=edit`);
		} else {
			// Navigate to appropriate change edit page based on type
			toast.info('Edit functionality for change requests coming soon');
		}
	}

	// Update request status
	async function updateRequestStatus(status: string) {
		isProcessing = true;
		try {
			const response = await fetch(`/api/change-request/${data.changeRequest.id}/status`, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ status })
			});

			if (!response.ok) throw new Error('Failed to update status');

			toast.success(`Request ${status === 'published' ? 'approved' : 'rejected'}`);
			await invalidateAll();
		} catch (error) {
			console.error('Error updating request status:', error);
			toast.error('Failed to update request status');
		} finally {
			isProcessing = false;
		}
	}
</script>

{#snippet actions()}
	<div class="flex items-center gap-2">
		<Button variant="ghost" onclick={() => goto('/private/request')}>
			<ArrowLeft class="mr-2 h-4 w-4" />
			Back to Requests
		</Button>

		<div class="ml-auto flex items-center gap-2">
			{#if data.canEdit}
				<Button variant="outline" onclick={handleEdit}>
					<Edit class="mr-2 h-4 w-4" />
					Edit
				</Button>
			{/if}

			{#if data.canApprove && data.changeRequest.status === 'ready_for_review'}
				<Button
					variant="outline"
					onclick={() => updateRequestStatus('hidden')}
					disabled={isProcessing}
				>
					<X class="mr-2 h-4 w-4" />
					Reject
				</Button>
				<Button
					variant="default"
					onclick={() => updateRequestStatus('published')}
					disabled={isProcessing}
				>
					<Check class="mr-2 h-4 w-4" />
					Approve
				</Button>
			{/if}
		</div>
	</div>
{/snippet}

{#snippet content()}
	<div class="space-y-6">
		<!-- Request Header -->
		<RequestHeader
			request={data.changeRequest}
			actor={data.changeRequest.actor_profile_id}
			reason={data.changeRequest.change_reason_id}
			isCreationRequest={data.isCreationRequest}
		/>

		<!-- Main Content -->
		{#if data.isCreationRequest}
			<CreationRequestDetail
				changeRequest={data.changeRequest}
				eventProducts={data.eventProducts}
			/>
		{:else}
			<ChangeRequestDetail changeRequest={data.changeRequest} />
		{/if}

		<!-- Reviews Section -->
		{#if data.changeRequest.change_request_review && data.changeRequest.change_request_review.length > 0}
			<ReviewsSection reviews={data.changeRequest.change_request_review} />
		{/if}
	</div>
{/snippet}

<PageContainer
	title={data.isCreationRequest ? 'Creation Request' : 'Change Request'}
	description={`Request #${requestIdShort} • ${format(new Date(data.changeRequest.created_at), 'PPP')}`}
	{actions}
	{content}
/>
