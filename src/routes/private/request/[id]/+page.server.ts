import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import type { Tables } from '$lib/supabase/database.types';

// Define types for the query result with relationships
type ChangeRequestWithRelations = Tables<'change_request'> & {
	actor_profile_id: Tables<'profile'> | null;
	change_reason_id: Tables<'change_reason'> | null;
	metadata: Tables<'metadata'>[] | null;
	product:
		| (Tables<'product'> & {
				metadata_id: Tables<'metadata'> | null;
				product_price:
					| (Tables<'product_price'> & {
							price: Tables<'price'> | null;
					  })[]
					| null;
				product_kind: Tables<'product_kind'> | null;
				consumer_group_id: Tables<'group'> | null;
		  })[]
		| null;
	event:
		| (Tables<'event'> & {
				metadata_id: Tables<'metadata'> | null;
				event_kind: Tables<'event_kind'> | null;
				space_id:
					| (Tables<'space'> & {
							landmark_id:
								| (Tables<'landmark'> & {
										address_id: Tables<'address'> | null;
								  })
								| null;
					  })
					| null;
		  })[]
		| null;
	change_product:
		| (Tables<'change_product'> & {
				apply_to_product_id:
					| (Tables<'product'> & {
							metadata_id: Tables<'metadata'> | null;
					  })
					| null;
		  })[]
		| null;
	change_event:
		| (Tables<'change_event'> & {
				apply_to_event_id:
					| (Tables<'event'> & {
							metadata_id: Tables<'metadata'> | null;
							space_id:
								| (Tables<'space'> & {
										landmark_id:
											| (Tables<'landmark'> & {
													address_id: Tables<'address'> | null;
											  })
											| null;
								  })
								| null;
					  })
					| null;
		  })[]
		| null;
	change_metadata_wikipage:
		| (Tables<'change_metadata_wikipage'> & {
				apply_to_metadata_wikipage_id:
					| (Tables<'metadata_wikipage'> & {
							metadata_id: Tables<'metadata'> | null;
							wikipage_id: Tables<'wikipage'> | null;
					  })
					| null;
		  })[]
		| null;
	change_order_product:
		| (Tables<'change_order_product'> & {
				apply_to_order_product_id:
					| (Tables<'order_product'> & {
							product:
								| (Tables<'product'> & {
										metadata_id: Tables<'metadata'> | null;
								  })
								| null;
							order_id: Tables<'order'> | null;
					  })
					| null;
		  })[]
		| null;
	change_order_price:
		| (Tables<'change_order_price'> & {
				apply_to_order_price_id:
					| (Tables<'order_price'> & {
							price: Tables<'price'> | null;
							order_id: Tables<'order'> | null;
					  })
					| null;
		  })[]
		| null;
	change_request_review:
		| (Tables<'change_request_review'> & {
				reviewer_profile_id: Tables<'profile'> | null;
		  })[]
		| null;
};

// Extended metadata type with relations
type MetadataWithRelations = Tables<'metadata'> & {
	metadata_wikipage:
		| (Tables<'metadata_wikipage'> & {
				wikipage_id: Tables<'wikipage'> | null;
				metadata_wikipage_relation: Tables<'metadata_wikipage_relation'> | null;
		  })[]
		| null;
	metadata_track:
		| (Tables<'metadata_track'> & {
				track_id: Tables<'track'> | null;
		  })[]
		| null;
};

// Event product type
type EventProductWithRelations = Tables<'event_product'> & {
	product:
		| (Tables<'product'> & {
				metadata_id: Tables<'metadata'> | null;
		  })
		| null;
	event_product_relation: Tables<'event_product_relation'> | null;
};

export const load = (async ({ params, locals: { supabase, user, brand } }) => {
	if (!user) {
		throw error(401, 'Unauthorized');
	}

	const { id } = params;

	// Fetch change request with all relationships using raw naming
	const { data: changeRequest, error: requestError } = await supabase
		.from('change_request')
		.select(
			`
			*,
			actor_profile_id(
				id,
				username,
				avatar,
				auto_user_email
			),
			change_reason_id(
				id,
				title,
				requires_message
			),
			metadata!creation_request_id(
				*,
				metadata_wikipage(
					*,
					wikipage_id(
						id,
						title,
						brief
					),
					metadata_wikipage_relation(*)
				),
				metadata_track(
					*,
					track_id(
						id,
						title
					)
				)
			),
			product!creation_request_id(
				*,
				metadata_id(*),
				product_price(
					*,
					price!product_price_price_id_fkey(*)
				),
				product_kind(*),
				consumer_group_id(
					id,
					name
				)
			),
			event!creation_request_id(
				*,
				metadata_id(*),
				event_kind(*),
				space_id(
					id,
					name_full,
					name_short,
					landmark_id(
						id,
						address_id(
							id,
							street,
							city,
							state,
							postal_code,
							country
						)
					)
				)
			),
			change_product!change_request_id(
				*,
				apply_to_product_id(
					*,
					metadata_id(*)
				)
			),
			change_event!change_request_id(
				*,
				apply_to_event_id(
					*,
					metadata_id(*),
					space_id(
						id,
						name_full,
						name_short,
						landmark_id(
							id,
							address_id(
								id,
								street,
								city,
								state,
								postal_code,
								country
							)
						)
					)
				)
			),
			change_metadata_wikipage!change_request_id(
				*,
				apply_to_metadata_wikipage_id(
					*,
					metadata_id(*),
					wikipage_id(*)
				)
			),
			change_order_product!change_request_id(
				*,
				apply_to_order_product_id(
					*,
					product(
						*,
						metadata_id(*)
					),
					order_id(*)
				)
			),
			change_order_price!change_request_id(
				*,
				apply_to_order_price_id(
					*,
					price:auto_price_option_price_id(*),
					order_id(*)
				)
			),
			change_request_review!change_request_id(
				*,
				reviewer_profile_id(
					id,
					username,
					avatar,
					auto_user_email
				)
			)
		`
		)
		.eq('id', id)
		.single<ChangeRequestWithRelations>();

	if (requestError || !changeRequest) {
		console.error('Error fetching change request:', requestError);
		throw error(404, 'Change request not found');
	}

	// Check if user has access to this brand's requests
	if (changeRequest.brand_id !== brand.id) {
		throw error(403, 'Access denied');
	}

	// Determine if this is a creation request or change request
	const isCreationRequest = !changeRequest.change_reason_id;

	// For creation requests, fetch event_products separately
	let eventProducts: EventProductWithRelations[] = [];
	if (isCreationRequest && changeRequest.event && changeRequest.event.length > 0) {
		const eventIds = changeRequest.event.map((e) => e.id);
		const { data } = await supabase
			.from('event_product')
			.select(
				`
				*,
				product(
					id,
					metadata_id(title)
				),
				event_product_relation(*)
			`
			)
			.in('event_id', eventIds);
		eventProducts = data || [];
	}

	// Check if current user can edit
	const canEdit = changeRequest.actor_profile_id === user.id;
	const canApprove = user.id === brand.owner_profile_id; // Or check for admin role

	return {
		changeRequest,
		eventProducts,
		isCreationRequest,
		canEdit,
		canApprove,
		user,
		brand
	};
}) satisfies PageServerLoad;
