<script lang="ts">
	import { Badge } from '$lib/components/ui/badge';
	import * as Avatar from '$lib/components/ui/avatar';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import { format } from 'date-fns';
	import type { Tables, Json } from '$lib/supabase/database.types';

	interface Props {
		request: Tables<'change_request'>;
		actor: Tables<'profile'> | null;
		reason: Tables<'change_reason'> | null;
		isCreationRequest: boolean;
	}

	let { request, actor, reason, isCreationRequest }: Props = $props();

	// Status badge variant
	const statusVariant = $derived(() => {
		switch (request.status) {
			case 'published':
				return 'default';
			case 'ready_for_review':
				return 'secondary';
			case 'hidden':
			case 'rejected':
				return 'destructive';
			default:
				return 'outline';
		}
	});

	const currentLocale = $derived(getLocale());
</script>

<div class="bg-card rounded-lg border p-6">
	<div class="flex items-start justify-between">
		<div class="space-y-4">
			<!-- Request Type and Status -->
			<div class="flex items-center gap-3">
				<h2 class="text-2xl font-semibold">
					{isCreationRequest ? 'Creation Request' : 'Change Request'}
				</h2>
				<Badge variant={statusVariant()}>
					{request.status.replace(/_/g, ' ')}
				</Badge>
			</div>

			<!-- Request Info -->
			<div class="text-muted-foreground space-y-2 text-sm">
				<div class="flex items-center gap-2">
					<span class="font-medium">ID:</span>
					<code class="bg-muted rounded px-2 py-0.5 font-mono text-xs">
						{request.id}
					</code>
				</div>

				{#if !isCreationRequest && reason}
					<div class="flex items-start gap-2">
						<span class="font-medium">Reason:</span>
						<span>
							{getLocalizedText(reason.title as Json, currentLocale)}
							{#if request.change_reason_message}
								<span class="text-muted-foreground">
									- {request.change_reason_message}
								</span>
							{/if}
						</span>
					</div>
				{/if}

				<div class="flex items-center gap-2">
					<span class="font-medium">Created:</span>
					<span>{format(new Date(request.created_at), 'PPpp')}</span>
				</div>

				{#if request.updated_at !== request.created_at}
					<div class="flex items-center gap-2">
						<span class="font-medium">Updated:</span>
						<span>{format(new Date(request.updated_at), 'PPpp')}</span>
					</div>
				{/if}
			</div>
		</div>

		<!-- Actor Info -->
		{#if actor}
			<div class="flex items-center gap-3">
				<div class="text-right">
					<p class="text-sm font-medium">
						{actor.username || 'Unknown User'}
					</p>
					<p class="text-muted-foreground text-xs">{actor.auto_user_email || 'No email'}</p>
					<p class="text-muted-foreground text-xs capitalize">
						{request.auto_change_actor_role_id?.replace(/_/g, ' ') || 'Creator'}
					</p>
				</div>
				<Avatar.Root class="h-10 w-10">
					{#if actor.avatar}
						<Avatar.Image src={actor.avatar} alt={actor.username || ''} />
					{/if}
					<Avatar.Fallback>
						{(actor.username || actor.auto_user_email || '?').charAt(0).toUpperCase()}
					</Avatar.Fallback>
				</Avatar.Root>
			</div>
		{/if}
	</div>
</div>
