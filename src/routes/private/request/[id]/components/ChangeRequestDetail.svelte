<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import {
		Package,
		Calendar,
		FileText,
		ArrowRight,
		Clock,
		MapPin,
		ShoppingCart,
		DollarSign
	} from '@lucide/svelte';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import { format } from 'date-fns';
	import type { Tables, Json } from '$lib/supabase/database.types';

	// Type definitions for extended relationships
	type ChangeProductWithRelations = Tables<'change_product'> & {
		apply_to_product_id?:
			| (Tables<'product'> & {
					metadata_id?: Tables<'metadata'> | null;
			  })
			| null;
	};

	type ChangeEventWithRelations = Tables<'change_event'> & {
		apply_to_event_id?:
			| (Tables<'event'> & {
					metadata_id?: Tables<'metadata'> | null;
					space_id?: Tables<'space'> | null;
					event_kind?: Tables<'event_kind'> | null;
			  })
			| null;
	};

	type ChangeOrderProductWithRelations = Tables<'change_order_product'> & {
		apply_to_order_product_id?:
			| (Tables<'order_product'> & {
					product?:
						| (Tables<'product'> & {
								metadata_id?: Tables<'metadata'> | null;
						  })
						| null;
					order_id?: Tables<'order'> | null;
			  })
			| null;
	};

	type ChangeOrderPriceWithRelations = Tables<'change_order_price'> & {
		apply_to_order_price_id?:
			| (Tables<'order_price'> & {
					price?: Tables<'price'> | null;
					order_id?: Tables<'order'> | null;
			  })
			| null;
	};

	type ChangeMetadataWikipageWithRelations = Tables<'change_metadata_wikipage'> & {
		apply_to_metadata_wikipage_id?:
			| (Tables<'metadata_wikipage'> & {
					metadata_id?: Tables<'metadata'> | null;
					wikipage_id?: Tables<'wikipage'> | null;
			  })
			| null;
	};

	type ChangeRequestWithRelations = Tables<'change_request'> & {
		change_product?: ChangeProductWithRelations[] | null;
		change_event?: ChangeEventWithRelations[] | null;
		change_order_product?: ChangeOrderProductWithRelations[] | null;
		change_order_price?: ChangeOrderPriceWithRelations[] | null;
		change_metadata_wikipage?: ChangeMetadataWikipageWithRelations[] | null;
	};

	interface Props {
		changeRequest: ChangeRequestWithRelations;
	}

	let { changeRequest }: Props = $props();

	const currentLocale = $derived(getLocale());

	// Helper to access nested data
	const changeProducts = $derived(changeRequest.change_product || []);
	const changeEvents = $derived(changeRequest.change_event || []);
	const changeOrderProducts = $derived(changeRequest.change_order_product || []);
	const changeOrderPrices = $derived(changeRequest.change_order_price || []);
	const changeMetadataWikipages = $derived(changeRequest.change_metadata_wikipage || []);

	// Format currency
	function formatCurrency(amount: number): string {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD'
		}).format(amount / 100);
	}

	// Format duration
	function formatDuration(minutes: number): string {
		const hours = Math.floor(minutes / 60);
		const mins = minutes % 60;
		return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
	}
</script>

<div class="space-y-6">
	<!-- Product Changes -->
	{#if changeProducts && changeProducts.length > 0}
		<div class="space-y-4">
			<div class="flex items-center gap-2">
				<Package class="h-5 w-5" />
				<h3 class="text-lg font-semibold">Product Changes</h3>
				<Badge variant="secondary">{changeProducts.length}</Badge>
			</div>

			{#each changeProducts as change}
				<Card.Root>
					<Card.Header>
						<div class="flex items-start justify-between">
							<div>
								<Card.Title class="text-base">
									{getLocalizedText(
										change.apply_to_product_id?.metadata_id?.title as Json,
										currentLocale
									) || 'Unknown Product'}
								</Card.Title>
								<p class="text-muted-foreground text-sm">
									Product ID: <code class="text-xs"
										>{change.apply_to_product_id?.id?.substring(0, 8) || 'N/A'}</code
									>
								</p>
							</div>
							{#if change.apply_to_product_id?.publishing_state}
								<Badge variant="outline">{change.apply_to_product_id.publishing_state}</Badge>
							{/if}
						</div>
					</Card.Header>

					<Card.Content class="space-y-3">
						{#if change.override_title}
							<div class="rounded-lg border p-3">
								<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">Title Change</p>
								<div class="flex items-center gap-2">
									<span class="line-through opacity-60">
										{getLocalizedText(
											change.apply_to_product_id?.metadata_id?.title as Json,
											currentLocale
										)}
									</span>
									<ArrowRight class="h-4 w-4 shrink-0" />
									<span class="font-medium">
										{getLocalizedText(change.override_title as Json, currentLocale)}
									</span>
								</div>
							</div>
						{/if}

						{#if change.override_subtitle}
							<div class="rounded-lg border p-3">
								<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">
									Subtitle Change
								</p>
								<div class="flex items-center gap-2">
									<span class="line-through opacity-60">
										{getLocalizedText(
											change.apply_to_product_id?.metadata_id?.subtitle as Json,
											currentLocale
										) || 'None'}
									</span>
									<ArrowRight class="h-4 w-4 shrink-0" />
									<span class="font-medium">
										{getLocalizedText(change.override_subtitle as Json, currentLocale)}
									</span>
								</div>
							</div>
						{/if}

						{#if change.override_publishing_state}
							<div class="rounded-lg border p-3">
								<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">
									Publishing State Change
								</p>
								<div class="flex items-center gap-2">
									<Badge variant="secondary">{change.apply_to_product_id?.publishing_state}</Badge>
									<ArrowRight class="h-4 w-4 shrink-0" />
									<Badge variant="default">{change.override_publishing_state}</Badge>
								</div>
							</div>
						{/if}
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	{/if}

	<!-- Event Changes -->
	{#if changeEvents && changeEvents.length > 0}
		<div class="space-y-4">
			<div class="flex items-center gap-2">
				<Calendar class="h-5 w-5" />
				<h3 class="text-lg font-semibold">Event Changes</h3>
				<Badge variant="secondary">{changeEvents.length}</Badge>
			</div>

			{#each changeEvents as change}
				<Card.Root>
					<Card.Header>
						<div class="flex items-start justify-between">
							<div>
								<Card.Title class="text-base">
									Event on {format(new Date(change.apply_to_event_id?.start_at || ''), 'PPP')}
								</Card.Title>
								<p class="text-muted-foreground text-sm">
									{getLocalizedText(
										change.apply_to_event_id?.metadata_id?.title as Json,
										currentLocale
									) || 'Unknown Event'}
								</p>
							</div>
							<Badge variant="outline">{change.apply_to_event_id?.event_kind?.id || 'Event'}</Badge>
						</div>
					</Card.Header>

					<Card.Content class="space-y-3">
						{#if change.override_start_at}
							<div class="rounded-lg border p-3">
								<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">Time Change</p>
								<div class="flex items-center gap-2">
									<span class="line-through opacity-60">
										{format(new Date(change.apply_to_event_id?.start_at || ''), 'PPpp')}
									</span>
									<ArrowRight class="h-4 w-4 shrink-0" />
									<span class="font-medium">
										{format(new Date(change.override_start_at), 'PPpp')}
									</span>
								</div>
							</div>
						{/if}

						{#if change.override_duration_minute}
							<div class="rounded-lg border p-3">
								<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">
									Duration Change
								</p>
								<div class="flex items-center gap-2">
									<span class="line-through opacity-60">
										{formatDuration(change.apply_to_event_id?.duration_minute || 0)}
									</span>
									<ArrowRight class="h-4 w-4 shrink-0" />
									<span class="font-medium">
										{formatDuration(change.override_duration_minute)}
									</span>
								</div>
							</div>
						{/if}

						{#if change.override_publishing_state}
							<div class="rounded-lg border p-3">
								<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">
									Publishing State Change
								</p>
								<div class="flex items-center gap-2">
									<Badge variant="secondary">{change.apply_to_event_id?.publishing_state}</Badge>
									<ArrowRight class="h-4 w-4 shrink-0" />
									<Badge variant="default">{change.override_publishing_state}</Badge>
								</div>
							</div>
						{/if}
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	{/if}

	<!-- Order Product Changes -->
	{#if changeOrderProducts && changeOrderProducts.length > 0}
		<div class="space-y-4">
			<div class="flex items-center gap-2">
				<ShoppingCart class="h-5 w-5" />
				<h3 class="text-lg font-semibold">Order Product Changes</h3>
				<Badge variant="secondary">{changeOrderProducts.length}</Badge>
			</div>

			{#each changeOrderProducts as change}
				<Card.Root>
					<Card.Header>
						<Card.Title class="text-base">
							Order #{change.apply_to_order_product_id?.order_id?.id.substring(0, 8) || 'N/A'} -
							{getLocalizedText(
								change.apply_to_order_product_id?.product?.metadata_id?.title as Json,
								currentLocale
							)}
						</Card.Title>
					</Card.Header>

					<Card.Content class="space-y-3">
						{#if change.override_purchased_count}
							<div class="rounded-lg border p-3">
								<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">
									Quantity Change
								</p>
								<div class="flex items-center gap-2">
									<span class="font-medium">
										{change.override_purchased_count} units
									</span>
								</div>
							</div>
						{/if}
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	{/if}

	<!-- Order Price Changes -->
	{#if changeOrderPrices && changeOrderPrices.length > 0}
		<div class="space-y-4">
			<div class="flex items-center gap-2">
				<DollarSign class="h-5 w-5" />
				<h3 class="text-lg font-semibold">Order Price Changes</h3>
				<Badge variant="secondary">{changeOrderPrices.length}</Badge>
			</div>

			{#each changeOrderPrices as change}
				<Card.Root>
					<Card.Header>
						<Card.Title class="text-base">
							Order #{change.apply_to_order_price_id?.order_id?.id.substring(0, 8) || 'N/A'} -
							{getLocalizedText(
								change.apply_to_order_price_id?.price?.title as Json,
								currentLocale
							)}
						</Card.Title>
					</Card.Header>

					<Card.Content class="space-y-3">
						{#if change.override_unit}
							<div class="rounded-lg border p-3">
								<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">Unit Change</p>
								<div class="flex items-center gap-2">
									<span class="font-medium">
										{change.override_unit} units
									</span>
								</div>
							</div>
						{/if}

						{#if change.override_expire_at}
							<div class="rounded-lg border p-3">
								<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">
									Expiration Date Change
								</p>
								<div class="flex items-center gap-2">
									<span class="line-through opacity-60">
										{change.apply_to_order_price_id?.auto_price_option_expire_at
											? format(
													new Date(change.apply_to_order_price_id.auto_price_option_expire_at),
													'PP'
												)
											: 'No expiration'}
									</span>
									<ArrowRight class="h-4 w-4 shrink-0" />
									<span class="font-medium">
										{format(new Date(change.override_expire_at), 'PP')}
									</span>
								</div>
							</div>
						{/if}
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	{/if}

	<!-- Metadata Wikipage Changes -->
	{#if changeMetadataWikipages && changeMetadataWikipages.length > 0}
		<div class="space-y-4">
			<div class="flex items-center gap-2">
				<FileText class="h-5 w-5" />
				<h3 class="text-lg font-semibold">Metadata Wikipage Changes</h3>
				<Badge variant="secondary">{changeMetadataWikipages.length}</Badge>
			</div>

			{#each changeMetadataWikipages as change}
				<Card.Root>
					<Card.Header>
						<Card.Title class="text-base">
							{getLocalizedText(
								change.apply_to_metadata_wikipage_id?.metadata_id?.title as Json,
								currentLocale
							) || 'Unknown Metadata'}
						</Card.Title>
					</Card.Header>

					<Card.Content class="space-y-3">
						{#if change.override_wikipage_id}
							<div class="rounded-lg border p-3">
								<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">
									Wikipage Change
								</p>
								<div class="flex items-center gap-2">
									<span class="line-through opacity-60">
										{getLocalizedText(
											change.apply_to_metadata_wikipage_id?.wikipage_id?.title as Json,
											currentLocale
										) || 'None'}
									</span>
									<ArrowRight class="h-4 w-4 shrink-0" />
									<span class="font-medium">
										{change.override_wikipage_id.substring(0, 8)}
									</span>
								</div>
							</div>
						{/if}

						{#if change.override_relation}
							<div class="rounded-lg border p-3">
								<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">
									Relation Change
								</p>
								<div class="flex items-center gap-2">
									<Badge variant="secondary"
										>{change.apply_to_metadata_wikipage_id?.relation || 'None'}</Badge
									>
									<ArrowRight class="h-4 w-4 shrink-0" />
									<Badge variant="default">{change.override_relation}</Badge>
								</div>
							</div>
						{/if}
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	{/if}
</div>
