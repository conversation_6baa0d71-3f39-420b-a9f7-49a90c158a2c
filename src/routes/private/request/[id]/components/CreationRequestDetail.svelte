<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import {
		Package,
		Calendar,
		FileText,
		Tag,
		Clock,
		MapPin,
		DollarSign,
		Users,
		Link,
		Hash
	} from '@lucide/svelte';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import { format } from 'date-fns';
	import type { Tables } from '$lib/supabase/database.types';
	import type { Json } from '$lib/supabase/database.types';

	// Type for extended relationships
	type MetadataWithRelations = Tables<'metadata'> & {
		metadata_wikipage?:
			| (Tables<'metadata_wikipage'> & {
					wikipage_id?: Tables<'wikipage'> | null;
					metadata_wikipage_relation?: Tables<'metadata_wikipage_relation'> | null;
			  })[]
			| null;
		metadata_track?:
			| (Tables<'metadata_track'> & {
					track_id?: Tables<'track'> | null;
			  })[]
			| null;
	};

	type ProductWithRelations = Tables<'product'> & {
		metadata_id?: Tables<'metadata'> | null;
		product_price?:
			| (Tables<'product_price'> & {
					price?: Tables<'price'> | null;
			  })[]
			| null;
		product_kind?: Tables<'product_kind'> | null;
		consumer_group_id?: Tables<'group'> | null;
	};

	type EventWithRelations = Tables<'event'> & {
		metadata_id?: Tables<'metadata'> | null;
		event_kind?: Tables<'event_kind'> | null;
		space_id?:
			| (Tables<'space'> & {
					landmark_id?:
						| (Tables<'landmark'> & {
								address_id?: Tables<'address'> | null;
						  })
						| null;
			  })
			| null;
	};

	type EventProductWithRelations = Tables<'event_product'> & {
		product?:
			| (Tables<'product'> & {
					metadata_id?: Tables<'metadata'> | null;
			  })
			| null;
		event_product_relation?: Tables<'event_product_relation'> | null;
	};

	type ChangeRequestWithRelations = Tables<'change_request'> & {
		metadata?: MetadataWithRelations[] | null;
		product?: ProductWithRelations[] | null;
		event?: EventWithRelations[] | null;
	};

	interface Props {
		changeRequest: ChangeRequestWithRelations;
		eventProducts: EventProductWithRelations[];
	}

	let { changeRequest, eventProducts }: Props = $props();

	const currentLocale = $derived(getLocale());

	// Helper to access nested data
	const metadata = $derived(changeRequest.metadata || []);
	const products = $derived(changeRequest.product || []);
	const events = $derived(changeRequest.event || []);

	// Group events by products they're connected to
	const productEventMap: Map<string, EventWithRelations[]> = $derived(
		(() => {
			const map = new Map<string, EventWithRelations[]>();

			eventProducts?.forEach((ep) => {
				const productId = ep.product?.id;
				if (productId) {
					if (!map.has(productId)) {
						map.set(productId, []);
					}
					const event = events?.find((e) => e.id === ep.event_id);
					if (event) {
						map.get(productId)?.push(event);
					}
				}
			});

			return map;
		})()
	);

	// Format currency
	function formatCurrency(amount: number): string {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD'
		}).format(amount / 100);
	}

	// Format duration
	function formatDuration(minutes: number): string {
		const hours = Math.floor(minutes / 60);
		const mins = minutes % 60;
		return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
	}

	// Get standalone events (not connected to products)
	const standaloneEvents = $derived(
		events?.filter((e) => !eventProducts?.some((ep) => ep.event_id === e.id)) || []
	);
</script>

<div class="space-y-8">
	<!-- Metadata Overview -->
	{#if metadata && metadata.length > 0}
		<div class="space-y-4">
			<div class="flex items-center gap-2">
				<FileText class="h-5 w-5" />
				<h3 class="text-lg font-semibold">Metadata</h3>
				<Badge variant="secondary">{metadata.length}</Badge>
			</div>

			{#each metadata as meta}
				<Card.Root class="bg-card">
					<Card.Content class="p-6">
						<div class="space-y-4">
							<div class="flex items-start justify-between">
								<div class="space-y-1">
									<h4 class="text-2xl font-semibold">
										{getLocalizedText(meta.auto_final_title as Json, currentLocale) || '--'}
									</h4>
									{#if meta.auto_final_subtitle}
										<p class="text-muted-foreground">
											{getLocalizedText(meta.auto_final_subtitle as Json, currentLocale) || '--'}
										</p>
									{/if}
								</div>
								<div class="flex items-center gap-2">
									<Badge variant="outline">{meta.kind}</Badge>
									<code class="text-muted-foreground text-xs">
										{meta.id.substring(0, 8)}
									</code>
								</div>
							</div>

							{#if meta.desc}
								<div>
									<p class="text-muted-foreground mb-1 text-xs font-medium uppercase">
										Description
									</p>
									<p class="text-sm">{getLocalizedText(meta.desc as Json, currentLocale)}</p>
								</div>
							{/if}

							{#if meta.metadata_wikipage && meta.metadata_wikipage.length > 0}
								<div>
									<p class="text-muted-foreground mb-2 text-xs font-medium uppercase">Wikipages</p>
									<div class="flex flex-wrap gap-2">
										{#each meta.metadata_wikipage as mw}
											<Badge variant="secondary" class="gap-1">
												<Link class="h-3 w-3" />
												{getLocalizedText(
													mw.metadata_wikipage_relation?.title as Json,
													currentLocale
												) || 'relation'}:
												{getLocalizedText(mw.wikipage_id?.title as Json, currentLocale) ||
													'Unknown'}
											</Badge>
										{/each}
									</div>
								</div>
							{/if}

							{#if meta.metadata_track && meta.metadata_track.length > 0}
								<div>
									<p class="text-muted-foreground mb-2 text-xs font-medium uppercase">Tracks</p>
									<div class="flex flex-wrap gap-2">
										{#each meta.metadata_track as mt}
											<Badge variant="secondary" class="gap-1">
												<Tag class="h-3 w-3" />
												{getLocalizedText(mt.track_id?.title as Json, currentLocale) || 'Unknown'}
											</Badge>
										{/each}
									</div>
								</div>
							{/if}
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	{/if}

	<!-- Products and Events -->
	{#if products && products.length > 0}
		<div class="space-y-4">
			<div class="flex items-center gap-2">
				<Package class="h-5 w-5" />
				<h3 class="text-lg font-semibold">Products & Events</h3>
				<div class="flex gap-2">
					<Badge variant="secondary">{products.length} products</Badge>
					<Badge variant="secondary">{events?.length || 0} events</Badge>
				</div>
			</div>

			<div class="space-y-6">
				{#each products as product}
					{@const connectedEvents = productEventMap.get(product.id) || []}

					<Card.Root class="bg-card">
						<Card.Content class="p-6">
							<div class="space-y-4">
								<!-- Product Header -->
								<div class="flex items-start justify-between">
									<div class="space-y-1">
										<div class="flex items-center gap-3">
											<Package class="h-5 w-5" />
											<h4 class="text-2xl font-semibold">
												{getLocalizedText(
													product.metadata_id?.auto_final_title as Json,
													currentLocale
												) || '--'}
											</h4>
										</div>
										{#if product.metadata_id?.auto_final_subtitle}
											<p class="text-muted-foreground ml-8">
												{getLocalizedText(
													product.metadata_id.auto_final_subtitle as Json,
													currentLocale
												) || '--'}
											</p>
										{/if}
									</div>

									<div class="space-y-2 text-right">
										<Badge
											variant={product.publishing_state === 'published' ? 'default' : 'outline'}
										>
											{product.publishing_state}
										</Badge>
										<div class="flex items-center gap-2">
											<Badge variant="secondary" class="gap-1">
												<Hash class="h-3 w-3" />
												{product.stock} stock
											</Badge>
											{#if product.consumer_group_id}
												<Badge variant="secondary" class="gap-1">
													<Users class="h-3 w-3" />
													{product.consumer_group_id.name}
												</Badge>
											{/if}
										</div>
									</div>
								</div>

								<!-- Product Prices -->
								{#if product.product_price && product.product_price.length > 0}
									<div>
										<p class="text-muted-foreground mb-3 text-xs font-medium uppercase">Pricing</p>
										<div class="space-y-2">
											{#each product.product_price as pp}
												<div class="bg-muted/50 flex items-center justify-between rounded-lg p-4">
													<div class="flex items-center gap-3">
														<DollarSign class="text-muted-foreground h-5 w-5" />
														<div>
															<p class="text-lg font-medium">
																{formatCurrency(pp.cost_units)} per unit
															</p>
															<p class="text-muted-foreground text-sm">
																{pp.price
																	? getLocalizedText(pp.price.title as Json, currentLocale)
																	: pp.price_id}
															</p>
														</div>
													</div>
													<div class="text-right">
														<p class="text-muted-foreground text-sm">
															{format(new Date(pp.start_at), 'MMM d, yyyy')}
															{#if pp.end_at}
																- {format(new Date(pp.end_at), 'MMM d, yyyy')}
															{/if}
														</p>
														{#if pp.stock}
															<p class="text-muted-foreground text-xs">
																Limited: {pp.stock} units
															</p>
														{/if}
													</div>
												</div>
											{/each}
										</div>
									</div>
								{/if}

								<!-- Connected Events -->
								{#if connectedEvents.length > 0}
									<div>
										<p class="text-muted-foreground mb-3 text-xs font-medium uppercase">
											Connected Events ({connectedEvents.length})
										</p>
										<div class="space-y-3">
											{#each connectedEvents as event}
												<div class="bg-muted/30 rounded-lg border p-4">
													<div class="flex items-start justify-between">
														<div class="space-y-1">
															<div class="flex items-center gap-2">
																<Calendar class="text-muted-foreground h-4 w-4" />
																<p class="font-medium">
																	{format(new Date(event.start_at), 'PPP')}
																</p>
															</div>
															<div class="text-muted-foreground flex items-center gap-4 text-sm">
																<span class="flex items-center gap-1">
																	<Clock class="h-3 w-3" />
																	{format(new Date(event.start_at), 'p')} • {formatDuration(
																		event.duration_minute
																	)}
																</span>
																{#if event.space_id}
																	<span class="flex items-center gap-1">
																		<MapPin class="h-3 w-3" />
																		{getLocalizedText(
																			event.space_id.name_full as Json,
																			currentLocale
																		)}
																	</span>
																{/if}
															</div>
														</div>
														<Badge variant="outline" class="text-xs">
															{event.event_kind?.id || 'Event'}
														</Badge>
													</div>
												</div>
											{/each}
										</div>
									</div>
								{/if}
							</div>
						</Card.Content>
					</Card.Root>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Standalone Events (not connected to products) -->
	{#if standaloneEvents.length > 0}
		<div class="space-y-4">
			<div class="flex items-center gap-2">
				<Calendar class="h-5 w-5" />
				<h3 class="text-lg font-semibold">Standalone Events</h3>
				<Badge variant="secondary">{standaloneEvents.length}</Badge>
			</div>

			<div class="grid gap-3 md:grid-cols-2">
				{#each standaloneEvents as event}
					<Card.Root>
						<Card.Header>
							<div class="flex items-start justify-between">
								<div class="space-y-1">
									<Card.Title class="text-base">
										{format(new Date(event.start_at), 'PPP')}
									</Card.Title>
									<p class="text-muted-foreground text-sm">
										{format(new Date(event.start_at), 'p')} • {formatDuration(
											event.duration_minute
										)}
									</p>
								</div>
								<Badge variant="outline" class="text-xs">
									{event.event_kind?.id || 'Event'}
								</Badge>
							</div>
						</Card.Header>
						{#if event.space_id}
							<Card.Content>
								<div class="flex items-center gap-2 text-sm">
									<MapPin class="text-muted-foreground h-4 w-4" />
									<span>{getLocalizedText(event.space_id.name_full as Json, currentLocale)}</span>
								</div>
							</Card.Content>
						{/if}
					</Card.Root>
				{/each}
			</div>
		</div>
	{/if}
</div>
