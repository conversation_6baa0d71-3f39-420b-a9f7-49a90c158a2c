<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import * as Avatar from '$lib/components/ui/avatar';
	import { Badge } from '$lib/components/ui/badge';
	import { CheckCircle, XCircle, Clock } from '@lucide/svelte';
	import { format } from 'date-fns';
	import type { Tables } from '$lib/supabase/database.types';

	type ReviewWithRelations = Tables<'change_request_review'> & {
		reviewer_profile_id?: Tables<'profile'> | null;
	};

	interface Props {
		reviews: ReviewWithRelations[];
	}

	let { reviews }: Props = $props();

	function getStatusIcon(status: string) {
		switch (status) {
			case 'approve':
				return CheckCircle;
			case 'reject':
				return XCircle;
			default:
				return Clock;
		}
	}

	function getStatusVariant(status: string): 'default' | 'destructive' | 'secondary' {
		switch (status) {
			case 'approve':
				return 'default';
			case 'reject':
				return 'destructive';
			default:
				return 'secondary';
		}
	}
</script>

<Card.Root>
	<Card.Header>
		<Card.Title>Reviews</Card.Title>
		<Card.Description>Review history for this request</Card.Description>
	</Card.Header>

	<Card.Content class="space-y-4">
		{#each reviews as review}
			{@const StatusIcon = getStatusIcon(review.status)}

			<div class="flex items-start space-x-4">
				<Avatar.Root class="h-8 w-8">
					{#if review.reviewer_profile_id?.avatar}
						<Avatar.Image
							src={review.reviewer_profile_id.avatar}
							alt={review.reviewer_profile_id.username || 'Reviewer'}
						/>
					{/if}
					<Avatar.Fallback>
						{(review.reviewer_profile_id?.username || 'R').charAt(0).toUpperCase()}
					</Avatar.Fallback>
				</Avatar.Root>

				<div class="flex-1 space-y-1">
					<div class="flex items-center gap-2">
						<p class="text-sm font-medium">
							{review.reviewer_profile_id?.username || 'Unknown Reviewer'}
						</p>
						<Badge variant={getStatusVariant(review.status)} class="gap-1">
							<StatusIcon class="h-3 w-3" />
							{review.status}
						</Badge>
						<span class="text-muted-foreground text-xs">
							{format(new Date(review.created_at), 'PPp')}
						</span>
					</div>

					{#if review.message}
						<p class="text-muted-foreground text-sm">{review.message}</p>
					{/if}
				</div>
			</div>
		{/each}
	</Card.Content>
</Card.Root>
