import { fail, redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import type { Database } from '$lib/supabase/database.types';

type OrderInput = {
	payer_id: string;
	consumer_profiles: Array<{
		consumer_profile_id: string;
		product_count: number;
		product_prices: Array<{
			product_price_id: string;
			fill_up_with_price_option_combinations?: Array<{
				price_option_id: string;
				quantity: number;
			}>;
			order_requirement_passcode?: string;
		}>;
	}>;
};

export const load: PageServerLoad = async ({ locals: { supabase, session, brand, security } }) => {
	if (!session) {
		redirect(303, '/auth/sign-in');
	}

	// Check permissions
	const hasOrderWrite = security.hasPermission('brand_order_write');
	const hasOrderReview = security.hasPermission('brand_order_review');

	// If user has neither permission, or only review permission, redirect
	if (!hasOrderWrite) {
		redirect(303, '/private/access-denied');
	}

	// ProfilePicker fetches profiles via its own API endpoint, no need to load them here

	// Fetch upcoming events with product information
	const startDate = new Date();
	const endDate = new Date();
	endDate.setMonth(endDate.getMonth() + 3); // 3 months ahead

	const { data: events, error: eventsError } = await supabase
		.from('event')
		.select(
			`
			id,
			brand_id,
			title,
			start_at,
			duration_minute,
			event_product!inner (
				id,
				product:product_id (
					id,
					title,
					product_price (
						id,
						cost_units,
						price:price_id (
							id,
							title,
							unit_kind,
							price_unit_kind:unit_kind (
								id,
								quantity_forms,
								quantity_forms_short
							),
							price_option (
								id,
								title,
								units,
								money_int
							)
						)
					)
				)
			),
			space:space_id (
				id,
				name_short,
				landmark:landmark_id (
					id,
					title_short
				)
			),
			landmark:landmark_id (
				id,
				title_short
			),
			metadata:metadata_id (
				id,
				auto_final_title,
				auto_final_subtitle,
				metadata_wikipage (
					wikipage:wikipage_id (
						id,
						title
					),
					relation
				)
			)
		`
		)
		.gte('start_at', startDate.toISOString())
		.lte('start_at', endDate.toISOString())
		.eq('brand_id', brand.id)
		.order('start_at', { ascending: true });

	if (eventsError) {
		console.error('Error fetching events:', eventsError);
	}

	return {
		events: events || [],
		permissions: {
			hasOrderWrite,
			hasOrderReview
		},
		brand
	};
};

export const actions: Actions = {
	createOrder: async ({ request, locals: { supabase, brand, security, user } }) => {
		if (!user) {
			return fail(401, { error: 'Unauthorized' });
		}

		const hasOrderWrite = security.hasPermission('brand_order_write');
		const hasOrderReview = security.hasPermission('brand_order_review');

		if (!hasOrderWrite) {
			return fail(403, { error: 'You do not have permission to create orders' });
		}

		const formData = await request.formData();

		// Extract form data
		const payerId = formData.get('payer_id') as string;
		const consumerId = formData.get('consumer_id') as string;
		const eventId = formData.get('event_id') as string;
		const productCount = parseInt(formData.get('product_count') as string) || 1;
		const productPriceId = formData.get('product_price_id') as string;
		const priceOptionId = formData.get('price_option_id') as string;

		// Payment info (optional)
		const createPayment = formData.get('create_payment') === 'true';
		const paymentAmount = parseFloat(formData.get('payment_amount') as string) || 0;
		const paymentSource = (formData.get('payment_source') as string) || 'cash';
		const currencyCode = (formData.get('currency_code') as string) || 'USD';
		const paymentNote = formData.get('payment_note') as string;

		// Validate required fields
		if (!payerId || !consumerId || !eventId || !productPriceId) {
			return fail(400, { error: 'Missing required fields' });
		}

		// Build order input
		const productPriceConfig: any = {
			product_price_id: productPriceId
		};

		// Add price option if selected
		if (priceOptionId) {
			productPriceConfig.fill_up_with_price_option_combinations = [
				{
					price_option_id: priceOptionId,
					quantity: 1
				}
			];
		}

		const orderInput: OrderInput = {
			payer_id: payerId,
			consumer_profiles: [
				{
					consumer_profile_id: consumerId,
					product_count: productCount,
					product_prices: [productPriceConfig]
				}
			]
		};

		try {
			// If user has both permissions, create order directly
			if (hasOrderWrite && hasOrderReview) {
				// Call client_new_order with perform_action = true
				// NOTE: The function doesn't support dry run yet, so we call it directly
				const { data: orderResult, error: orderError } = await supabase.rpc('client_new_order', {
					payer_id: orderInput.payer_id,
					consumer_profiles: orderInput.consumer_profiles
				});

				if (orderError) {
					console.error('Error creating order:', orderError);
					return fail(500, { error: orderError.message });
				}

				// If payment requested, create order_price_payment using the RPC function
				if (createPayment && paymentAmount > 0 && orderResult?.order_id) {
					try {
						const { data: paymentResult, error: paymentError } = await supabase.rpc(
							'new_order_price_payment',
							{
								input_payment_intent_id: null, // null for manual payments
								input_payment_source: paymentSource,
								input_currency_code: currencyCode,
								input_money_received_int: Math.round(paymentAmount * 100), // Convert to cents
								input_order_id: orderResult.order_id
							}
						);

						if (paymentError) {
							console.error('Error creating payment record:', paymentError);
							// The function validates the amount internally
						} else {
							console.log('Payment record created successfully:', paymentResult);
						}
					} catch (error) {
						console.error('Unexpected error creating payment:', error);
						// Don't fail the whole operation since the order was created successfully
					}
				}

				return {
					success: true,
					orderId: orderResult?.order_id,
					message: 'Order created successfully'
				};
			}
			// If user only has write permission, create a change request
			else if (hasOrderWrite && !hasOrderReview) {
				// Create change request for review
				const { data: changeRequest, error: changeRequestError } = await supabase
					.from('change_request')
					.insert({
						actor_profile_id: user.id,
						auto_change_actor_role_id: 'admin',
						brand_id: brand.id,
						status: 'pending_review',
						form_data: {
							type: 'create_order',
							order_input: orderInput,
							payment: createPayment
								? {
										amount: paymentAmount,
										source: paymentSource,
										currency_code: currencyCode,
										creator_id: user.id,
										note: paymentNote || null
									}
								: null
						}
					})
					.select()
					.single();

				if (changeRequestError) {
					console.error('Error creating change request:', changeRequestError);
					return fail(500, { error: changeRequestError.message });
				}

				return {
					success: true,
					changeRequestId: changeRequest.id,
					message: 'Order request created and sent for review'
				};
			}
		} catch (error) {
			console.error('Unexpected error:', error);
			return fail(500, { error: 'An unexpected error occurred' });
		}
	},

	createPriceOrder: async ({ request, locals: { supabase, brand, security, user } }) => {
		if (!user) {
			return fail(401, { error: 'Unauthorized' });
		}

		const hasOrderWrite = security.hasPermission('brand_order_write');
		const hasOrderReview = security.hasPermission('brand_order_review');

		if (!hasOrderWrite) {
			return fail(403, { error: 'You do not have permission to create orders' });
		}

		const formData = await request.formData();

		// Extract form data
		const payerId = formData.get('payer_id') as string;
		const consumerId = formData.get('consumer_id') as string;
		const priceOptionId = formData.get('price_option_id') as string;

		// Payment info (optional)
		const createPayment = formData.get('create_payment') === 'true';
		const paymentAmount = parseFloat(formData.get('payment_amount') as string) || 0;
		const paymentSource = (formData.get('payment_source') as string) || 'cash';
		const currencyCode = (formData.get('currency_code') as string) || 'USD';
		const paymentNote = formData.get('payment_note') as string;

		// Validate required fields
		if (!payerId || !consumerId || !priceOptionId) {
			return fail(400, { error: 'Missing required fields' });
		}

		try {
			// If user has both permissions, create order directly
			if (hasOrderWrite && hasOrderReview) {
				// Generate order ID
				const orderId = crypto.randomUUID();

				// Call client_new_order_price RPC
				const { data: orderPriceId, error: orderError } = await supabase.rpc(
					'client_new_order_price',
					{
						input_order_id: orderId,
						input_payer_id: payerId,
						input_consumer_profile_id: consumerId,
						input_price_option_id: priceOptionId,
						input_price_option_count: 1,
						input_open_for_minute: 30
					}
				);

				if (orderError) {
					console.error('Error creating price order:', orderError);
					return fail(500, { error: orderError.message });
				}

				// If payment requested, create order_price_payment using the RPC function
				if (createPayment && paymentAmount > 0 && orderId) {
					try {
						const { data: paymentResult, error: paymentError } = await supabase.rpc(
							'new_order_price_payment',
							{
								input_payment_intent_id: null, // null for manual payments
								input_payment_source: paymentSource,
								input_currency_code: currencyCode,
								input_money_received_int: Math.round(paymentAmount * 100), // Convert to cents
								input_order_id: orderId
							}
						);

						if (paymentError) {
							console.error('Error creating payment record:', paymentError);
							// The function validates the amount internally
						} else {
							console.log('Payment record created successfully:', paymentResult);
						}
					} catch (error) {
						console.error('Unexpected error creating payment:', error);
						// Don't fail the whole operation since the order was created successfully
					}
				}

				return {
					success: true,
					orderId: orderId,
					message: 'Class pass order created successfully'
				};
			}
			// If user only has write permission, create a change request
			else if (hasOrderWrite && !hasOrderReview) {
				// Create change request for review
				const { data: changeRequest, error: changeRequestError } = await supabase
					.from('change_request')
					.insert({
						actor_profile_id: user.id,
						auto_change_actor_role_id: 'admin',
						brand_id: brand.id,
						status: 'pending_review',
						form_data: {
							type: 'create_price_order',
							payer_id: payerId,
							consumer_id: consumerId,
							price_option_id: priceOptionId,
							payment: createPayment
								? {
										amount: paymentAmount,
										source: paymentSource,
										currency_code: currencyCode,
										creator_id: user.id,
										note: paymentNote || null
									}
								: null
						}
					})
					.select()
					.single();

				if (changeRequestError) {
					console.error('Error creating change request:', changeRequestError);
					return fail(500, { error: changeRequestError.message });
				}

				return {
					success: true,
					changeRequestId: changeRequest.id,
					message: 'Class pass order request created and sent for review'
				};
			}
		} catch (error) {
			console.error('Unexpected error:', error);
			return fail(500, { error: 'An unexpected error occurred' });
		}
	}
};
