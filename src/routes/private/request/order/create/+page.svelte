<script lang="ts">
	import type { PageData, ActionData } from './$types';
	import { PageContainer } from '$lib/components/layout';
	import { Button } from '$lib/components/ui/button';
	import { Card } from '$lib/components/ui/card';
	import { Label } from '$lib/components/ui/label';
	import { Input } from '$lib/components/ui/input';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import * as Select from '$lib/components/ui/select';
	import * as Popover from '$lib/components/ui/popover';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import ProfilePicker from '$lib/components/shared/ProfilePicker.svelte';
	import PriceOptionPicker from '$lib/components/shared/PriceOptionPicker.svelte';
	import EventSelector from '../../../check-in/EventSelector.svelte';
	import { RangeCalendar } from '$lib/components/ui/range-calendar';
	import {
		ArrowLeft,
		ShoppingCart,
		User,
		Calendar,
		Package,
		CreditCard,
		AlertCircle,
		CheckCircle,
		Loader2,
		Info,
		Plus,
		Hash,
		CalendarIcon,
		X,
		RefreshCw,
		Coins
	} from '@lucide/svelte';
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { EventWithMetadata } from '../../../check-in/event-utils';
	import { cn } from '$lib/utils';
	import {
		CalendarDate,
		DateFormatter,
		getLocalTimeZone,
		type DateValue
	} from '@internationalized/date';
	import type { DateRange } from 'bits-ui';
	import { format } from 'date-fns';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Tabs from '$lib/components/ui/tabs';

	interface Props {
		data: PageData;
		form: ActionData;
	}

	let { data, form }: Props = $props();

	// Date formatter for compact display
	const df = new DateFormatter('en-US', {
		month: 'short',
		day: 'numeric'
	});

	// State
	let orderMode = $state<'product' | 'price'>('product');
	let payerId = $state('');
	let consumerId = $state('');
	let selectedEventId = $state('');
	let selectedProductId = $state('');
	let productCount = $state(1);
	let selectedProductPriceId = $state('');
	let selectedPriceOptionId = $state('');
	let createPayment = $state(true); // Default to true for payment
	let paymentAmount = $state<number>(0);
	let paymentSource = $state('cash');
	let currencyCode = $state('USD');
	let paymentNote = $state('');
	let submitting = $state(false);
	let dateRangePopoverOpen = $state(false);
	let eventsLoading = $state(false);
	let selectedPriceOption = $state<any>(null);

	// Initialize date range to show next 3 months
	const today = new Date();
	const defaultStartDate = new CalendarDate(
		today.getFullYear(),
		today.getMonth() + 1,
		today.getDate()
	);
	const defaultEndDate = new CalendarDate(
		today.getFullYear(),
		today.getMonth() + 4, // 3 months ahead
		today.getDate()
	);

	// Date range state
	let dateRange: DateRange = $state({
		start: defaultStartDate,
		end: defaultEndDate
	});

	// Events state
	let events = $state<EventWithMetadata[]>((data.events || []) as unknown as EventWithMetadata[]);

	// Sync consumer to payer when payer is selected and consumer is empty
	$effect(() => {
		if (payerId && !consumerId) {
			consumerId = payerId;
		}
	});

	// Load events when date range changes
	async function loadEvents(): Promise<void> {
		if (!dateRange?.start || !dateRange?.end) return;

		eventsLoading = true;

		try {
			// Get local timezone
			const localTimeZone = getLocalTimeZone();

			// Create start date in local timezone at beginning of day
			const startLocalDate = dateRange.start.toDate(localTimeZone);
			const startISO = startLocalDate.toISOString();

			// Create end date in local timezone at end of day
			const endLocalDate = dateRange.end.toDate(localTimeZone);
			endLocalDate.setHours(23, 59, 59, 999);
			const endISO = endLocalDate.toISOString();

			// Fetch events for the selected date range
			const response = await fetch(
				`/api/events?start=${encodeURIComponent(startISO)}&end=${encodeURIComponent(endISO)}`
			);

			if (response.ok) {
				const result = await response.json();
				events = result.events || [];
			}
		} catch (error) {
			console.error('Error loading events:', error);
		} finally {
			eventsLoading = false;
			dateRangePopoverOpen = false;
		}
	}

	// Reset to default date range
	function resetDateRange(): void {
		dateRange = {
			start: defaultStartDate,
			end: defaultEndDate
		};
		loadEvents();
	}

	// Payment method options
	const paymentMethods = [
		{ value: 'cash', label: 'Cash' },
		{ value: 'card', label: 'Card (In Person)' },
		{ value: 'bank_transfer', label: 'Bank Transfer' },
		{ value: 'other', label: 'Other' }
	];

	// Derived values
	let selectedEvent = $derived(
		events.find((event) => event.id === selectedEventId) as EventWithMetadata | undefined
	);

	let availableProducts = $derived(() => {
		if (!selectedEvent || !selectedEvent.event_product) return [];

		return selectedEvent.event_product.map((ep: any) => ep.product).filter(Boolean);
	});

	let selectedProduct = $derived(() => {
		const products = availableProducts();
		return products.find((p: any) => p.id === selectedProductId);
	});

	let availableProductPrices = $derived(() => {
		if (!selectedProduct()) return [];
		return (selectedProduct() as any).product_price || [];
	});

	// Get the price_id from selected product price
	let selectedPriceId = $derived(() => {
		if (!selectedProductPriceId) return undefined;
		const productPrice = availableProductPrices().find(
			(pp: any) => pp.id === selectedProductPriceId
		);
		return productPrice?.price?.id;
	});

	// Handle event selection
	function handleEventChange(eventId: string) {
		selectedEventId = eventId;
		// Reset product selections when event changes
		selectedProductId = '';
		selectedProductPriceId = '';
		selectedPriceOptionId = '';
		selectedPriceOption = null;

		// Only auto-select in product mode
		if (orderMode === 'product') {
			// Auto-select product if only one available
			const event = events.find((e) => e.id === eventId);
			if (event?.event_product?.length === 1) {
				const product = event.event_product[0].product;
				if (product) {
					selectedProductId = product.id;
					// Check if this product has only one price
					if (product.product_price?.length === 1) {
						selectedProductPriceId = product.product_price[0].id;
					}
				}
			}
		}
	}

	// Handle product selection
	function handleProductChange(productId: string | undefined) {
		if (productId) {
			selectedProductId = productId;
			// Reset price selection when product changes
			selectedProductPriceId = '';
			selectedPriceOptionId = '';
			selectedPriceOption = null;

			// Auto-select price if only one available
			const products = availableProducts();
			const product = products.find((p: any) => p.id === productId);
			if (product?.product_price?.length === 1) {
				selectedProductPriceId = product.product_price[0].id;
			}
		}
	}

	// Watch for product price changes to reset price option
	$effect(() => {
		if (selectedProductPriceId) {
			// Reset price option when product price changes
			selectedPriceOptionId = '';
			selectedPriceOption = null;
		}
	});

	// Watch for order mode changes
	$effect(() => {
		if (orderMode === 'price') {
			// Clear product-related selections
			selectedProductId = '';
			selectedProductPriceId = '';
		}
		// Always clear price option when mode changes
		selectedPriceOptionId = '';
		selectedPriceOption = null;
	});

	// Get event display title
	function getEventDisplayTitle(event: EventWithMetadata | undefined): string {
		if (!event) return 'No event selected';
		return String(
			getLocalizedText(event.metadata?.auto_final_title as LocalizedText) ||
				event.title ||
				'Untitled Event'
		);
	}

	// Get product display name
	function getProductDisplayName(product: any): string {
		// First try metadata auto_final_title, then regular title
		if (product.metadata?.auto_final_title) {
			return getLocalizedText(product.metadata.auto_final_title as LocalizedText);
		}
		return getLocalizedText(product.title as LocalizedText) || 'Untitled Product';
	}

	// Get price display
	function getPriceDisplay(productPrice: any): string {
		if (!productPrice.price) return 'Unknown Price';

		const priceName = getLocalizedText(productPrice.price.title as LocalizedText);
		const unitName = productPrice.price.price_unit_kind
			? getLocalizedText(
					productPrice.price.price_unit_kind.quantity_forms_short as LocalizedText
				) || productPrice.price.unit_kind
			: productPrice.price.unit_kind;
		const cost = productPrice.cost_units;

		return `${cost} ${unitName} - ${priceName}`;
	}

	// Calculate total price based on selected product price
	let totalCost = $derived(() => {
		const productPrice = availableProductPrices().find(
			(pp: any) => pp.id === selectedProductPriceId
		);
		if (!productPrice) return 0;

		return (productPrice.cost_units || 0) * productCount;
	});

	// Permission message
	let permissionMessage = $derived(() => {
		if (data.permissions.hasOrderWrite && data.permissions.hasOrderReview) {
			return {
				type: 'info' as const,
				text: 'You have full permissions to create and approve orders immediately.'
			};
		} else if (data.permissions.hasOrderWrite && !data.permissions.hasOrderReview) {
			return {
				type: 'warning' as const,
				text: 'Your order will be submitted for review before being processed.'
			};
		}
		return null;
	});

	// Load events on mount
	$effect(() => {
		loadEvents();
	});

	// Update currency and payment amount when price option changes
	$effect(() => {
		if (selectedPriceOption) {
			currencyCode = selectedPriceOption.currency_code || 'USD';
			paymentAmount = selectedPriceOption.money_int / 100; // Convert cents to dollars
		}
	});
</script>

{#snippet actions()}
	<div class="flex items-center gap-2">
		<Button variant="ghost" onclick={() => goto('/private/order')}>
			<ArrowLeft class="mr-2 h-4 w-4" />
			Back to Orders
		</Button>
	</div>
{/snippet}

{#snippet content()}
	<form
		method="POST"
		action={orderMode === 'product' ? '?/createOrder' : '?/createPriceOrder'}
		use:enhance={() => {
			submitting = true;
			return async ({ update }) => {
				submitting = false;
				await update();
			};
		}}
	>
		<div class="mx-auto max-w-4xl space-y-6">
			<!-- Permission Notice -->
			{#if permissionMessage()}
				<Alert variant={permissionMessage()?.type === 'warning' ? 'default' : 'default'}>
					<Info class="h-4 w-4" />
					<AlertDescription>{permissionMessage()?.text}</AlertDescription>
				</Alert>
			{/if}

			<!-- Error Display -->
			{#if form?.error}
				<Alert variant="destructive">
					<AlertCircle class="h-4 w-4" />
					<AlertDescription>{form.error}</AlertDescription>
				</Alert>
			{/if}

			<!-- Success Display -->
			{#if form?.success}
				<Alert>
					<CheckCircle class="h-4 w-4" />
					<AlertDescription>
						{form.message}
						{#if form.orderId}
							<Button
								variant="link"
								class="ml-2 h-auto p-0"
								onclick={() => goto(`/private/order/${form.orderId}`)}
							>
								View Order
							</Button>
						{/if}
					</AlertDescription>
				</Alert>
			{/if}

			<!-- Customer Information -->
			<Card class="p-6">
				<h2 class="mb-4 flex items-center gap-2 text-lg font-semibold">
					<User class="h-5 w-5" />
					Customer Information
				</h2>

				<div class="space-y-4">
					<div>
						<Label for="payer">Payer</Label>
						<input type="hidden" name="payer_id" value={payerId} />
						<ProfilePicker bind:selectedProfileId={payerId} placeholder="Select payer" />
						<p class="mt-1 text-sm text-muted-foreground">The person responsible for payment</p>
					</div>

					<div>
						<Label for="consumer">Consumer</Label>
						<input type="hidden" name="consumer_id" value={consumerId} />
						<ProfilePicker bind:selectedProfileId={consumerId} placeholder="Select consumer" />
						<p class="mt-1 text-sm text-muted-foreground">
							The person who will use the product/service
						</p>
					</div>
				</div>
			</Card>

			<!-- Order Type Selection -->
			<Card class="p-6">
				<h2 class="mb-4 flex items-center gap-2 text-lg font-semibold">
					<Package class="h-5 w-5" />
					Order Type
				</h2>

				<Tabs.Root bind:value={orderMode}>
					<Tabs.List class="grid w-full grid-cols-2">
						<Tabs.Trigger value="product">Product Registration</Tabs.Trigger>
						<Tabs.Trigger value="price">Class Pass Only</Tabs.Trigger>
					</Tabs.List>
					<Tabs.Content value="product" class="mt-4">
						<p class="text-sm text-muted-foreground">
							Register for a specific product/service and optionally purchase a class pass
						</p>
					</Tabs.Content>
					<Tabs.Content value="price" class="mt-4">
						<p class="text-sm text-muted-foreground">
							Purchase a class pass without registering for a specific product
						</p>
					</Tabs.Content>
				</Tabs.Root>
			</Card>

			<!-- Event & Product Selection (for Product mode) or Price Selection (for Price mode) -->
			<Card class="p-6">
				{#if orderMode === 'product'}
					<h2 class="mb-4 flex items-center gap-2 text-lg font-semibold">
						<Calendar class="h-5 w-5" />
						Event & Product Selection
					</h2>

					<div class="space-y-4">
						<!-- Date Range Selector -->
						<div>
							<Label>Date Range</Label>
							<div class="relative">
								<Popover.Root bind:open={dateRangePopoverOpen}>
									<Popover.Trigger class="w-full">
										{#snippet child({ props })}
											<Button
												variant="outline"
												class="relative w-full justify-start text-left font-normal"
												{...props}
												disabled={eventsLoading}
											>
												<CalendarIcon class="mr-2 h-4 w-4" />
												{#if dateRange && dateRange.start}
													{#if dateRange.end}
														{df.format(dateRange.start.toDate(getLocalTimeZone()))} - {df.format(
															dateRange.end.toDate(getLocalTimeZone())
														)}
													{:else}
														{df.format(dateRange.start.toDate(getLocalTimeZone()))}
													{/if}
												{:else}
													Select date range
												{/if}
												{#if eventsLoading}
													<RefreshCw class="ml-auto h-4 w-4 animate-spin" />
												{/if}
											</Button>
										{/snippet}
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" align="start">
										<RangeCalendar
											bind:value={dateRange}
											numberOfMonths={2}
											onValueChange={() => {
												if (dateRange.start && dateRange.end) {
													loadEvents();
												}
											}}
										/>
									</Popover.Content>
								</Popover.Root>

								{#if !eventsLoading}
									<Button
										variant="ghost"
										size="icon"
										class="absolute right-2 top-1/2 h-7 w-7 -translate-y-1/2"
										onclick={resetDateRange}
										title="Reset to default date range"
									>
										<X class="h-4 w-4" />
									</Button>
								{/if}
							</div>
							<p class="mt-1 text-sm text-muted-foreground">Select the date range to load events</p>
						</div>

						<!-- Event Selection -->
						{#if !selectedEventId}
							<div>
								<Label for="event">Event</Label>
								<input type="hidden" name="event_id" value={selectedEventId} />
								<EventSelector
									{events}
									{selectedEventId}
									onEventChange={handleEventChange}
									loading={eventsLoading}
								/>
							</div>
						{:else}
							<!-- Selected Event Display -->
							<div>
								<Label>Selected Event</Label>
								<div class="flex items-center justify-between rounded-md border p-3">
									<div class="flex-1">
										<p class="font-medium">
											{getEventDisplayTitle(selectedEvent)}
										</p>
										<p class="text-sm text-muted-foreground">
											{selectedEvent
												? format(new Date(selectedEvent.start_at), 'EEEE, MMMM d, h:mm a')
												: ''}
										</p>
									</div>
									<Button
										variant="ghost"
										size="sm"
										onclick={() => {
											selectedEventId = '';
											selectedProductId = '';
											selectedProductPriceId = '';
											selectedPriceOptionId = '';
										}}
									>
										Change
									</Button>
								</div>
								<input type="hidden" name="event_id" value={selectedEventId} />
							</div>

							{#if availableProducts().length > 0}
								<!-- Product Selection -->
								<div>
									<Label for="product">Product</Label>
									<Select.Root
										type="single"
										bind:value={selectedProductId}
										onValueChange={handleProductChange}
									>
										<Select.Trigger>
											{#if selectedProductId}
												{getProductDisplayName(
													availableProducts().find((p) => p.id === selectedProductId)
												)}
											{:else}
												Select a product
											{/if}
										</Select.Trigger>
										<Select.Content>
											{#each availableProducts() as product}
												<Select.Item value={product.id} label={getProductDisplayName(product)}>
													<div class="flex flex-col gap-0.5">
														<div class="flex items-center gap-2">
															<Package class="h-4 w-4" />
															<span class="font-medium">
																{#if product.metadata?.auto_final_title}
																	{getLocalizedText(
																		product.metadata.auto_final_title as LocalizedText
																	)}
																{:else if product.title}
																	{getLocalizedText(product.title as LocalizedText)}
																{:else}
																	Untitled Product
																{/if}
															</span>
														</div>
														{#if product.metadata?.auto_final_subtitle}
															<span class="ml-6 text-xs text-muted-foreground">
																{getLocalizedText(
																	product.metadata.auto_final_subtitle as LocalizedText
																)}
															</span>
														{/if}
													</div>
												</Select.Item>
											{/each}
										</Select.Content>
									</Select.Root>
								</div>

								<!-- Price Selection -->
								{#if selectedProductId && availableProducts().find((p) => p.id === selectedProductId)}
									<div>
										<Label for="price">Buying Option</Label>
										<input type="hidden" name="product_price_id" value={selectedProductPriceId} />
										<Select.Root type="single" bind:value={selectedProductPriceId}>
											<Select.Trigger>
												{#if selectedProductPriceId}
													{getPriceDisplay(
														availableProductPrices().find(
															(pp: any) => pp.id === selectedProductPriceId
														)
													)}
												{:else}
													Select a price option
												{/if}
											</Select.Trigger>
											<Select.Content>
												{#each availableProductPrices() as productPrice}
													<Select.Item
														value={productPrice.id}
														label={getPriceDisplay(productPrice)}
													>
														{getPriceDisplay(productPrice)}
													</Select.Item>
												{/each}
											</Select.Content>
										</Select.Root>
									</div>

									{#if selectedProductPriceId && selectedPriceId()}
										<PriceOptionPicker
											priceId={selectedPriceId()}
											bind:selectedPriceOptionId
											bind:selectedPriceOption
											label="Price Option"
											required={true}
										/>
									{/if}

									<div>
										<Label for="quantity">Quantity</Label>
										<div class="flex items-center gap-2">
											<Input
												type="number"
												name="product_count"
												bind:value={productCount}
												min="1"
												max="99"
												class="w-24"
											/>
											<span class="text-sm text-muted-foreground">
												Total: {totalCost()} units
											</span>
										</div>
									</div>
								{/if}
							{:else}
								<Alert>
									<AlertCircle class="h-4 w-4" />
									<AlertDescription>No products available for this event</AlertDescription>
								</Alert>
							{/if}
						{/if}
					</div>
				{:else}
					<!-- Price Only Mode -->
					<h2 class="mb-4 flex items-center gap-2 text-lg font-semibold">
						<Coins class="h-5 w-5" />
						Select Class Pass
					</h2>

					<div class="space-y-4">
						<PriceOptionPicker
							brandId={data.brand.id}
							bind:selectedPriceOptionId
							bind:selectedPriceOption
							label="Class Pass Options"
							required={true}
							mode="all-prices"
						/>
					</div>
				{/if}
			</Card>

			<!-- Payment -->
			<Card class="p-6">
				<div class="mb-4">
					<h2 class="flex items-center gap-2 text-lg font-semibold">
						<CreditCard class="h-5 w-5" />
						Payment
					</h2>
					<p class="mt-1 text-sm text-muted-foreground">Record a payment for this order</p>
				</div>

				<div class="space-y-4">
					<div class="flex items-center gap-2">
						<Checkbox id="create-payment" bind:checked={createPayment} />
						<Label for="create-payment" class="cursor-pointer">Record payment now</Label>
					</div>
					<!-- Hidden input to submit the checkbox value with the form -->
					<input type="hidden" name="create_payment" value={createPayment ? 'true' : 'false'} />

					{#if createPayment}
						<div class="space-y-4 rounded-lg border p-4">
							<div class="grid gap-4 md:grid-cols-2">
								<div>
									<Label for="payment-amount">Payment Amount ({currencyCode})</Label>
									<Input
										type="number"
										name="payment_amount"
										bind:value={paymentAmount}
										min="0"
										step="0.01"
										placeholder="0.00"
									/>
									{#if selectedPriceOption}
										<p class="mt-1 text-sm text-muted-foreground">
											Order total: {currencyCode}
											{(selectedPriceOption.money_int / 100).toFixed(2)}
										</p>
									{/if}
								</div>

								<div>
									<Label for="payment-source">Payment Method</Label>
									<Select.Root type="single" bind:value={paymentSource}>
										<Select.Trigger>
											{paymentMethods.find((m) => m.value === paymentSource)?.label ||
												'Select payment method'}
										</Select.Trigger>
										<Select.Content>
											{#each paymentMethods as method}
												<Select.Item value={method.value}>
													{method.label}
												</Select.Item>
											{/each}
										</Select.Content>
									</Select.Root>
									<input type="hidden" name="payment_source" value={paymentSource} />
								</div>
							</div>

							<input type="hidden" name="currency_code" value={currencyCode} />

							<div>
								<Label for="payment-note">Note (Optional)</Label>
								<Textarea
									id="payment-note"
									name="payment_note"
									bind:value={paymentNote}
									placeholder="Add any additional notes about this payment..."
									rows={3}
								/>
							</div>
						</div>
					{/if}
				</div>
			</Card>

			<!-- Form Actions -->
			<div class="flex justify-end gap-3">
				<Button
					type="button"
					variant="outline"
					onclick={() => goto('/private/order')}
					disabled={submitting}
				>
					Cancel
				</Button>

				<Button
					type="submit"
					disabled={submitting ||
						!payerId ||
						!consumerId ||
						!selectedPriceOptionId ||
						(orderMode === 'product' && (!selectedEventId || !selectedProductPriceId))}
				>
					{#if submitting}
						<Loader2 class="mr-2 h-4 w-4 animate-spin" />
						Creating...
					{:else}
						<ShoppingCart class="mr-2 h-4 w-4" />
						Create Order
					{/if}
				</Button>
			</div>

			<input type="hidden" name="order_mode" value={orderMode} />
		</div>
	</form>
{/snippet}

<PageContainer
	title="Create Order"
	description="Create a new order on behalf of a customer"
	{actions}
	{content}
/>
