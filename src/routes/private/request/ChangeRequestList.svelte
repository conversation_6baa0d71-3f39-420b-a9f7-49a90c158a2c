<script lang="ts">
	import { formatDistanceToNow, format } from 'date-fns';
	import type { Database } from '$lib/supabase/database.types';
	import { Badge } from '$lib/components/ui/badge';
	import { Card } from '$lib/components/ui/card';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import { Package, Calendar, FileText, Search } from '@lucide/svelte';

	interface Props {
		changeRequests: Database['public']['Tables']['change_request']['Row'][];
		selectedRequestId: string;
		requestDetails: Map<string, any>;
		onRequestChange: (id: string) => void;
		loading: boolean;
	}

	let { changeRequests, selectedRequestId, requestDetails, onRequestChange, loading }: Props =
		$props();

	// Types
	type ChangeRequestRow = Database['public']['Tables']['change_request']['Row'];
	type LocalizedText = {
		[key: string]: string;
	};

	// State
	let searchQuery = $state('');
	let currentLocale = $derived(getLocale());

	// Derived state
	let filteredRequests = $derived(
		searchQuery
			? changeRequests.filter(
					(request) =>
						request.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
						(request.change_reason_id &&
							request.change_reason_id.toLowerCase().includes(searchQuery.toLowerCase())) ||
						(request.change_reason_message &&
							request.change_reason_message.toLowerCase().includes(searchQuery.toLowerCase())) ||
						(requestDetails.get(request.id)?.title &&
							requestDetails
								.get(request.id)
								.title.toLowerCase()
								.includes(searchQuery.toLowerCase()))
				)
			: changeRequests
	);

	// Get request type and title
	function getRequestInfo(request: ChangeRequestRow): {
		type: string;
		title: string;
		subtitle: string;
		icon: any;
		iconClass: string;
		typeClass: string;
	} {
		const details = requestDetails.get(request.id);

		if (details?.metadata) {
			// This is a creation request
			return {
				type: 'Product Creation',
				title:
					getLocalizedText(details.metadata.title as LocalizedText, currentLocale) || 'New Product',
				subtitle: 'New product request',
				icon: Package,
				iconClass: 'text-blue-600 dark:text-blue-400',
				typeClass: 'text-blue-600 dark:text-blue-400'
			};
		} else if (details?.product) {
			// This is a product change
			const productTitle = getLocalizedText(
				details.product.metadata?.title as LocalizedText,
				currentLocale
			);
			return {
				type: 'Product Change',
				title: productTitle || `Product ${details.product.id.substring(0, 8)}`,
				subtitle: 'Product modification',
				icon: Package,
				iconClass: 'text-purple-600 dark:text-purple-400',
				typeClass: 'text-purple-600 dark:text-purple-400'
			};
		} else if (details?.event) {
			// This is an event change or cancellation
			const isCancel =
				request.change_reason_id?.includes('cancel') ||
				request.change_reason_id?.includes('unavailable') ||
				request.change_reason_message?.toLowerCase().includes('cancel');

			const eventTitle = getLocalizedText(
				details.event.metadata?.title as LocalizedText,
				currentLocale
			);

			return {
				type: isCancel ? 'Event Cancellation' : 'Event Change',
				title: details.event.start_at
					? format(new Date(details.event.start_at), 'MMM d, yyyy h:mm a')
					: `Event ${details.event.id.substring(0, 8)}`,
				subtitle:
					eventTitle ||
					request.change_reason_message ||
					request.change_reason_id?.replace(/_/g, ' ') ||
					'Event update',
				icon: Calendar,
				iconClass: isCancel
					? 'text-red-600 dark:text-red-400'
					: 'text-orange-600 dark:text-orange-400',
				typeClass: isCancel
					? 'text-red-600 dark:text-red-400'
					: 'text-orange-600 dark:text-orange-400'
			};
		} else if (request.change_reason_id) {
			// Generic change with reason
			return {
				type: 'Change Request',
				title: request.change_reason_id.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
				subtitle: request.change_reason_message || 'General update',
				icon: FileText,
				iconClass: 'text-gray-600 dark:text-gray-400',
				typeClass: 'text-gray-600 dark:text-gray-400'
			};
		}

		return {
			type: 'Change Request',
			title: `Request ${request.id.substring(0, 8)}`,
			subtitle: 'Update request',
			icon: FileText,
			iconClass: 'text-gray-600 dark:text-gray-400',
			typeClass: 'text-gray-600 dark:text-gray-400'
		};
	}

	function getStatusBadge(status: string): { label: string; class: string } {
		switch (status) {
			case 'draft':
				return {
					label: 'Draft',
					class: 'bg-blue-50 text-blue-700 dark:bg-blue-950/50 dark:text-blue-300'
				};
			case 'ready_for_review':
				return {
					label: 'Ready',
					class: 'bg-amber-50 text-amber-700 dark:bg-amber-950/50 dark:text-amber-300'
				};
			case 'approved':
				return {
					label: 'Approved',
					class: 'bg-green-50 text-green-700 dark:bg-green-950/50 dark:text-green-300'
				};
			case 'rejected':
				return {
					label: 'Rejected',
					class: 'bg-red-50 text-red-700 dark:bg-red-950/50 dark:text-red-300'
				};
			case 'published':
				return {
					label: 'Published',
					class: 'bg-purple-50 text-purple-700 dark:bg-purple-950/50 dark:text-purple-300'
				};
			default:
				return {
					label: status,
					class: 'bg-gray-50 text-gray-700 dark:bg-gray-800/50 dark:text-gray-300'
				};
		}
	}
</script>

<div class="flex flex-col gap-3">
	<!-- Search input -->
	<div class="relative">
		<Search class="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
		<input
			type="text"
			placeholder="Search requests..."
			class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring w-full rounded-lg border py-2 pr-3 pl-9 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
			bind:value={searchQuery}
		/>
	</div>

	<!-- Request list -->
	<div
		class="flex flex-col gap-2 overflow-y-auto px-1 pt-1"
		style="max-height: calc(100vh - 280px);"
	>
		{#if loading}
			{#each Array(5) as _, i}
				<div class="rounded-lg border p-3">
					<div class="flex items-start gap-3">
						<Skeleton class="h-8 w-8 rounded" />
						<div class="flex-1 space-y-2">
							<div class="flex items-center justify-between">
								<Skeleton class="h-4 w-32" />
								<Skeleton class="h-4 w-20" />
							</div>
							<Skeleton class="h-5 w-3/4" />
							<Skeleton class="h-4 w-1/2" />
						</div>
					</div>
				</div>
			{/each}
		{:else if filteredRequests.length === 0}
			<div
				class="text-muted-foreground flex h-32 items-center justify-center rounded-lg border border-dashed p-4 text-center text-sm"
			>
				{#if searchQuery}
					<div class="space-y-1">
						<p>No results found for "{searchQuery}"</p>
						<p class="text-xs">Try adjusting your search</p>
					</div>
				{:else}
					No change requests found
				{/if}
			</div>
		{:else}
			{#each filteredRequests as request (request.id)}
				{@const info = getRequestInfo(request)}
				{@const status = getStatusBadge(request.status)}
				<button
					onclick={() => onRequestChange(request.id)}
					class="hover:bg-accent/30 focus:ring-ring w-full rounded-lg border p-3 text-left transition-all focus:ring-2 focus:ring-offset-2 focus:outline-none {selectedRequestId ===
					request.id
						? 'border-primary bg-accent/50 shadow-sm'
						: 'hover:shadow-sm'}"
				>
					<div class="flex items-start gap-3">
						<!-- Icon -->
						<div class="bg-muted/50 flex h-8 w-8 shrink-0 items-center justify-center rounded">
							<info.icon class="h-4 w-4 {info.iconClass}" />
						</div>

						<!-- Content -->
						<div class="min-w-0 flex-1">
							<!-- Header: Type and Time -->
							<div class="mb-1 flex items-center justify-between gap-2">
								<span class="text-xs font-medium {info.typeClass}">
									{info.type}
								</span>
								<span class="text-muted-foreground text-xs">
									{formatDistanceToNow(new Date(request.updated_at), { addSuffix: true })}
								</span>
							</div>

							<!-- Title -->
							<h4 class="mb-1 line-clamp-1 leading-tight font-medium">
								{info.title}
							</h4>

							<!-- Footer: Subtitle and Status -->
							<div class="flex items-center justify-between gap-2">
								<p class="text-muted-foreground line-clamp-1 text-sm">
									{info.subtitle}
								</p>
								<Badge variant="secondary" class="h-5 px-2 text-xs font-normal {status.class}">
									{status.label}
								</Badge>
							</div>
						</div>
					</div>
				</button>
			{/each}
		{/if}
	</div>
</div>
