import { redirect, fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import type { PageServerLoad, Actions } from './$types';
import type { LocalizedText } from '$lib/utils/localization';
import { fetchEvents, transformEventData } from '../../check-in/event-utils';

const formSchema = z.object({
	eventId: z.string().min(1, 'Event selection is required'),
	reasonId: z.string().min(1, 'Cancellation reason is required'),
	comments: z.string().optional()
});

export const load = (async ({ locals: { supabase, brand } }) => {
	// Get current date at start of day
	const today = new Date();
	today.setHours(0, 0, 0, 0);

	// Set end date to 3 months from now
	const threeMonthsLater = new Date(today);
	threeMonthsLater.setMonth(threeMonthsLater.getMonth() + 3);

	// Load upcoming events using shared utility
	const eventsData = await fetchEvents({
		supabase,
		startDate: today,
		endDate: threeMonthsLater,
		brandId: brand.id
	});

	// Transform data for EventSelector compatibility
	const events = transformEventData(eventsData);

	// Load cancellation reasons
	const { data: reasons } = await supabase
		.from('change_reason')
		.select('*')
		.order('created_at', { ascending: true });

	return {
		events,
		reasons: reasons || [],
		form: await superValidate(zod(formSchema))
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	default: async ({ request, locals }) => {
		const { supabase, user, brand } = locals;

		// Validate form data
		const form = await superValidate(request, zod(formSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			const { eventId, reasonId, comments } = form.data;

			// Debug log
			console.log('Submitted form data:', form.data);

			// Verify user is authenticated
			if (!user) {
				return fail(401, { form, message: 'Not authenticated' });
			}

			// Get the event data - fix the query to remove the relationship that doesn't exist
			const { data: event, error: eventError } = await supabase
				.from('event')
				.select('*') // Removed the relationship query that was causing the error
				.eq('id', eventId)
				.single();

			if (eventError) {
				console.error('Event not found error:', eventError);
				console.error('Searching for event ID:', eventId);
				return fail(400, { form, message: `Event not found. Details: ${eventError.message}` });
			}

			// 2. Create a change request
			const { data: changeRequest, error: changeRequestError } = await supabase
				.from('change_request')
				.insert({
					actor_profile_id: user.id,
					auto_change_actor_role_id: 'brand_admin',
					change_reason_id: reasonId,
					change_reason_message: comments || `Cancellation: ${reasonId}`,
					status: 'ready_for_review',
					form_data: { notifyCustomers: true }
				})
				.select()
				.single();

			if (changeRequestError) {
				console.error('Failed to create change request:', changeRequestError);
				return fail(500, {
					form,
					message: `Failed to create change request: ${changeRequestError.message}`
				});
			}

			// 3. Create a change event entry
			// Based on the table schema, novu_transaction_id is required
			const changeEventData = {
				change_request_id: changeRequest.id,
				apply_to_event_id: eventId,
				override_publishing_state: 'hidden',
				novu_transaction_id: `manual-${Date.now()}` // Adding the required field
			};

			console.log('Creating change_event with data:', changeEventData);

			const { error: changeEventError } = await supabase
				.from('change_event')
				.insert(changeEventData);

			if (changeEventError) {
				console.error('Failed to create change event:', changeEventError);
				console.error('Change event data:', changeEventData);
				return fail(500, {
					form,
					message: `Failed to create change event: ${changeEventError.message}. Code: ${changeEventError.code}`
				});
			}

			// The user will handle notifications through Postgres hooks, so we don't need Trigger.dev

			return { form, success: true };
		} catch (error) {
			console.error('Error processing event cancellation:', error);
			return fail(500, { form, message: 'An unexpected error occurred' });
		}
	}
};
