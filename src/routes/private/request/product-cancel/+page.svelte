<script lang="ts">
	import { PageContainer } from '$lib/components/layout';
	import { Button } from '$lib/components/ui/button';
	import { Card } from '$lib/components/ui/card';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Separator } from '$lib/components/ui/separator';
	import { ArrowLeft } from '@lucide/svelte';
	import * as Select from '$lib/components/ui/select';
	import { goto } from '$app/navigation';
	import { getLocale } from '$lib/paraglide/runtime';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { Database } from '$lib/supabase/database.types';
	import type { PageData } from './$types';
	import { browser } from '$app/environment';
	import { superForm } from 'sveltekit-superforms';
	import { toast } from 'svelte-sonner';
	import { format } from 'date-fns';
	import EventSelector from '../../check-in/EventSelector.svelte';
	import type { EventWithMetadata } from '../../check-in/event-utils';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	// Types
	type ChangeReason = Database['public']['Tables']['change_reason']['Row'];

	// State
	let loading = $state(false);
	let events = $state<EventWithMetadata[]>(data.events as EventWithMetadata[]);
	let selectedEventId = $state('');
	let affectedCustomers = $state<any[]>([]);
	let currentLocale = $derived(getLocale());

	// Change reasons from the server
	let changeReasons = $state<ChangeReason[]>(data.reasons || []);

	// Form
	const { form, enhance, submitting, errors, message } = superForm(data.form, {
		onSubmit: () => {
			loading = true;
		},
		onResult: ({ result }) => {
			loading = false;

			if (result.type === 'success') {
				toast.success('Event cancellation request submitted');
				setTimeout(() => {
					window.location.href = '/private/request';
				}, 500);
			} else if (result.type === 'failure') {
				toast.error(result.data?.message || 'Failed to submit cancellation request');
			}
		}
	});

	// Functions
	async function loadAffectedCustomers(eventId: string) {
		if (!browser || !eventId) return;

		try {
			// Get customers who booked this event
			const { data: customers, error } = await data.supabase
				.from('event_member')
				.select('*, profile:member_profile_id(*)')
				.eq('event_id', eventId)
				.eq('role', 'consumer');

			if (error) throw error;
			affectedCustomers = customers || [];
		} catch (error) {
			console.error('Error loading affected customers:', error);
		}
	}

	function handleSelectEvent(eventId: string) {
		selectedEventId = eventId;
		form.update((f) => {
			f.eventId = eventId;
			return f;
		});
		loadAffectedCustomers(eventId);
	}
</script>

{#snippet actions()}
	<div class="flex items-center gap-2">
		<Button variant="outline" onclick={() => goto('/private/request')}>
			<ArrowLeft class="mr-2 size-4" />
			Back to Change Requests
		</Button>
	</div>
{/snippet}

{#snippet content()}
	<div class="space-y-6 md:grid md:grid-cols-12 md:gap-6 md:space-y-0">
		<!-- Event List (sidebar) -->
		<div class="md:col-span-4 lg:col-span-4 xl:col-span-3">
			<h3 class="mb-4 text-lg font-medium">Upcoming Events</h3>
			<EventSelector
				{events}
				{selectedEventId}
				onEventChange={handleSelectEvent}
				{loading}
				showAttendeeCount={true}
			/>
		</div>

		<div class="hidden md:block">
			<Separator orientation="vertical" />
		</div>

		<!-- Cancellation Form -->
		<div class="md:col-span-8 lg:col-span-7 xl:col-span-8">
			<h3 class="mb-4 text-lg font-medium">Event Cancellation</h3>

			{#if !selectedEventId}
				<div class="flex h-40 items-center justify-center rounded-md border border-dashed">
					<p class="text-muted-foreground">Select an event to continue</p>
				</div>
			{:else}
				<Card class="p-6">
					<form method="POST" class="space-y-6" use:enhance>
						<div>
							<label for="eventId" class="text-sm font-medium">Selected Event</label>
							<Select.Root type="single" disabled={true}>
								<Select.Trigger class="w-full">
									<span>
										{events.find((e) => e.id === selectedEventId)?.metadata
											? getLocalizedText(
													events.find((e) => e.id === selectedEventId)?.metadata
														?.auto_final_title as LocalizedText,
													currentLocale
												)
											: 'Unknown Event'} - {format(
											new Date(events.find((e) => e.id === selectedEventId)?.start_at || ''),
											'PPp'
										)}
									</span>
								</Select.Trigger>
								<Select.Content>
									{#each events as event (event.id)}
										<Select.Item value={event.id}>
											{event.metadata
												? getLocalizedText(
														event.metadata.auto_final_title as LocalizedText,
														currentLocale
													)
												: 'Unknown Event'} - {format(new Date(event.start_at), 'PPp')}
										</Select.Item>
									{/each}
								</Select.Content>
							</Select.Root>
							<input type="hidden" name="eventId" bind:value={$form.eventId} />
							<p class="mt-1 text-sm text-muted-foreground">The event that will be cancelled</p>
							{#if $errors.eventId}
								<p class="mt-1 text-sm text-destructive">{$errors.eventId}</p>
							{/if}
						</div>

						<div>
							<label for="reasonId" class="text-sm font-medium">Cancellation Reason</label>
							<Select.Root
								type="single"
								onValueChange={(value) => {
									form.update((f) => {
										f.reasonId = value;
										return f;
									});
								}}
							>
								<Select.Trigger class="w-full">
									<span>
										{$form.reasonId
											? getLocalizedText(
													changeReasons.find((r) => r.id === $form.reasonId)
														?.title as LocalizedText,
													currentLocale
												)
											: 'Select a reason'}
									</span>
								</Select.Trigger>
								<Select.Content>
									{#each changeReasons as reason (reason.id)}
										<Select.Item value={reason.id}>
											{getLocalizedText(reason.title as LocalizedText, currentLocale)}
										</Select.Item>
									{/each}
								</Select.Content>
							</Select.Root>
							<input type="hidden" name="reasonId" bind:value={$form.reasonId} />
							<p class="mt-1 text-sm text-muted-foreground">Reason for cancelling this event</p>
							{#if $errors.reasonId}
								<p class="mt-1 text-sm text-destructive">{$errors.reasonId}</p>
							{/if}
						</div>

						<div>
							<label for="comments" class="text-sm font-medium">Additional Comments</label>
							<Textarea
								id="comments"
								name="comments"
								placeholder="Add any additional information about this cancellation"
								rows={3}
								bind:value={$form.comments}
							/>
							<p class="mt-1 text-sm text-muted-foreground">
								These comments will be visible to internal team members only
							</p>
							{#if $errors.comments}
								<p class="mt-1 text-sm text-destructive">{$errors.comments}</p>
							{/if}
						</div>

						{#if affectedCustomers.length > 0}
							<div class="rounded-md border bg-muted/30 p-4">
								<p class="flex items-center text-sm">
									All {affectedCustomers.length} customers will be automatically notified about this
									cancellation.
								</p>
							</div>

							<div>
								<h4 class="mb-2 text-sm font-medium">
									Affected Customers ({affectedCustomers.length})
								</h4>
								<div class="max-h-48 overflow-y-auto rounded-md border p-2">
									{#each affectedCustomers as booking (booking.id)}
										<div class="flex items-center justify-between border-b py-2 last:border-0">
											<div class="font-medium">
												{getLocalizedText(
													booking.profile?.given_name as LocalizedText,
													currentLocale
												)}
												{getLocalizedText(
													booking.profile?.family_name as LocalizedText,
													currentLocale
												)}
											</div>
										</div>
									{/each}
								</div>
							</div>
						{:else}
							<div class="rounded-md border bg-muted/30 p-4">
								<p class="flex items-center text-sm">
									No customers are currently affected by this cancellation.
								</p>
							</div>
						{/if}

						<div class="flex justify-end pt-4">
							<Button type="submit" disabled={$submitting} variant="destructive">
								{$submitting ? 'Processing...' : 'Submit Cancellation Request'}
							</Button>
						</div>
					</form>
				</Card>
			{/if}
		</div>
	</div>
{/snippet}

<PageContainer
	title="Event Cancellation"
	description="Cancel an event and manage affected customers"
	{actions}
	{content}
/>
