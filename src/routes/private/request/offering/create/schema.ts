import { z } from 'zod';

// Input types matching the RPC function structure
const productPriceSchema = z.object({
	product_price_id: z.string().optional(),
	product_price_cost_units: z.number().min(0.1),
	product_price_start_at: z.string(),
	product_price_end_at: z.string().nullable().optional(),
	product_price_stock: z.number().int().min(0).optional(),
	product_price_order_requirement_id: z.string().optional(),
	product_price_reward_price_id: z.string().optional(),
	product_price_reward_price_units: z.number().optional(),
	product_price_price_id: z.string().min(1, 'Price is required')
});

const metadataWikipageSchema = z
	.object({
		metadata_wikipage_id: z.string().optional(),
		metadata_wikipage_relation: z.string().min(1, 'Relation is required'),
		wikipage_id: z.string().optional() // Make this optional since it can be empty initially
	})
	.refine(
		(data) => {
			// Only require wikipage_id if the relation is set
			if (data.metadata_wikipage_relation && data.metadata_wikipage_relation !== '') {
				return data.wikipage_id && data.wikipage_id !== '';
			}
			return true;
		},
		{
			message: 'Wikipage selection is required for this relation',
			path: ['wikipage_id']
		}
	);

const metadataTrackSchema = z.object({
	metadata_track_id: z.string().optional(),
	track_id: z.string().min(1, 'Track is required')
});

const metadataSchema = z.object({
	metadata_id: z.string().min(1, 'Metadata ID is required'),
	metadata_kind: z.string().min(1, 'Metadata kind is required'),
	metadata_title: z.record(z.string()).optional(),
	metadata_subtitle: z.record(z.string()).optional(), // Make this optional since it might be empty
	metadata_desc: z.record(z.string()).optional(),
	metadata_message: z.record(z.string()).optional(),
	metadata_custom_attribute: z.any().optional(),
	metadata_promo_message: z.record(z.string()).optional(), // Make this optional since it might be empty
	metadata_promo_image_url: z.string().optional(),
	metadata_promo_video_url: z.string().optional(),
	metadata_promo_webpage_url: z.string().optional(),
	metadata_wikipages: z.array(metadataWikipageSchema),
	metadata_tracks: z.array(metadataTrackSchema),
	// Global publishing state for all events and products
	publishing_state: z
		.enum(['draft', 'hidden', 'published', 'ready_for_review'])
		.default('ready_for_review')
});

const eventSchema = z.object({
	event_id: z.string().optional(),
	event_kind: z.string().min(1, 'Event kind is required'),
	event_start_at: z.string().min(1, 'Start time is required'),
	event_duration_minute: z.number().int().min(1, 'Duration must be at least 1 minute'),
	event_space_id: z.string().optional(),
	event_publishing_state: z.string().min(1, 'Publishing state is required'),
	event_metadata_id: z.string().min(1, 'Event metadata reference is required')
});

const productSchema = z.object({
	product_id: z.string().optional(),
	product_kind: z.string().min(1, 'Product kind is required'),
	product_stock: z.number().int().min(0, 'Stock must be non-negative'),
	product_publishing_state: z.string().min(1, 'Publishing state is required'),
	product_open_to_buy_at: z.string().min(1, 'Open date is required'),
	product_close_to_buy_at: z.string().nullable().optional(), // Can be null
	product_prices: z.array(productPriceSchema).min(1, 'At least one price is required'),
	product_metadata_id: z.string().min(1, 'Product metadata reference is required'),
	// Optional custom fields for UI
	custom_title: z.record(z.string()).optional()
});

const eventProductSchema = z.object({
	event_product_id: z.string().optional(),
	event_product_relation: z.string().min(1, 'Relation is required'),
	event_id: z.string().min(1, 'Event ID is required'),
	product_id: z.string().min(1, 'Product ID is required')
});

export const productCreationSchema = z.object({
	program_id: z.string().min(1, 'Program is required'),

	// Single metadata for the offering
	metadata: metadataSchema,

	// Arrays matching RPC input structure
	events: z.array(eventSchema),
	products: z.array(productSchema).min(1, 'At least one product is required'),
	event_products: z.array(eventProductSchema),

	// Internal notes
	notes: z.string().optional()
});

export type ProductCreationFormData = z.infer<typeof productCreationSchema>;

// Helper types
export type ProductPrice = z.infer<typeof productPriceSchema>;
export type MetadataWikipage = z.infer<typeof metadataWikipageSchema>;
export type Event = z.infer<typeof eventSchema>;
export type Product = z.infer<typeof productSchema>;
export type EventProduct = z.infer<typeof eventProductSchema>;
