<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import * as Tabs from '$lib/components/ui/tabs';
	import { Clock } from '@lucide/svelte';
	import type { SuperForm } from 'sveltekit-superforms';
	import type { ProductCreationFormData } from '../schema';
	import type { Database } from '$lib/supabase/database.types';
	import type { SupabaseClient } from '@supabase/supabase-js';

	import EventCard from './EventCard.svelte';
	import AddEventForm from './AddEventForm.svelte';
	import { format, startOfWeek, endOfWeek, subWeeks, addWeeks } from 'date-fns';
	import { DateFormatter, type DateValue, CalendarDate } from '@internationalized/date';

	// Type for Program with relations
	type Program = Database['public']['Tables']['program']['Row'] & {
		product_kind: Database['public']['Tables']['product_kind']['Row'] | null;
		event_kind: Database['public']['Tables']['event_kind']['Row'] | null;
		program_price: Array<
			Database['public']['Tables']['program_price']['Row'] & {
				price: Database['public']['Tables']['price']['Row'] | null;
			}
		>;
		program_metadata_wikipage: Array<
			Database['public']['Tables']['program_metadata_wikipage']['Row'] & {
				metadata_wikipage_relation:
					| Database['public']['Tables']['metadata_wikipage_relation']['Row']
					| null;
			}
		>;
	};

	interface Props {
		form: SuperForm<ProductCreationFormData>;
		program: Program;
		supabase: SupabaseClient<Database>;
		brand: any;
	}

	let { form, program, supabase, brand }: Props = $props();

	const { form: formData, errors } = form;

	// Constants
	const WEEKDAYS = [
		{ id: 'Monday', short: 'Mon', dayNum: 1 },
		{ id: 'Tuesday', short: 'Tue', dayNum: 2 },
		{ id: 'Wednesday', short: 'Wed', dayNum: 3 },
		{ id: 'Thursday', short: 'Thu', dayNum: 4 },
		{ id: 'Friday', short: 'Fri', dayNum: 5 },
		{ id: 'Saturday', short: 'Sat', dayNum: 6 },
		{ id: 'Sunday', short: 'Sun', dayNum: 0 }
	];

	const df = new DateFormatter('en-US', {
		dateStyle: 'long'
	});

	// State
	let selectedTab = $state('Monday');
	let isLoadingEvents = $state(false);
	let suggestedSlots = $state<any[]>([]);
	let isInitialized = $state(false);

	// Single add event form state
	let dateValue = $state<DateValue | undefined>();
	let customDate = $state('');
	let customStartTime = $state('10:00');
	let customDurationNumber = $state(60);
	let customSpaceId = $state('');

	let customError = $state<string | null>(null);
	let datePickerRef = $state<HTMLElement | null>(null);

	// Get current event kind from metadata
	let currentEventKind = $derived(
		$formData.metadata.metadata_custom_attribute?.event_kind || 'dance_class'
	);

	// Get current publishing state from metadata
	let currentPublishingState = $derived($formData.metadata.publishing_state || 'ready_for_review');

	// Helper functions
	function generateEventId(): string {
		return crypto.randomUUID();
	}

	function getNextWeekdayDate(weekday: string): Date {
		const today = new Date();
		const targetDay = WEEKDAYS.find((d) => d.id === weekday)?.dayNum || 0;
		const currentDay = today.getDay();

		let daysToAdd = targetDay - currentDay;
		if (daysToAdd <= 0) daysToAdd += 7;

		const targetDate = new Date(today);
		targetDate.setDate(today.getDate() + daysToAdd);
		targetDate.setHours(0, 0, 0, 0);

		return targetDate;
	}

	// Get the Monday of the previous week
	function getPrevWeekMonday(): Date {
		const today = new Date();
		const thisWeekMonday = startOfWeek(today, { weekStartsOn: 1 });
		return subWeeks(thisWeekMonday, 1);
	}

	// Get the Sunday of the current week
	function getCurrentWeekSunday(): Date {
		const today = new Date();
		return endOfWeek(today, { weekStartsOn: 1 });
	}

	// Fetch past events as reference
	async function fetchPastEvents() {
		if (!brand?.id) return;

		isLoadingEvents = true;
		try {
			const startDate = getPrevWeekMonday().toISOString();
			const endDate = getCurrentWeekSunday().toISOString();

			const { data, error } = await supabase
				.from('event')
				.select(
					`
					id,
					start_at,
					duration_minute,
					space_id
				`
				)
				.eq('brand_id', brand.id)
				.gte('start_at', startDate)
				.lte('start_at', endDate)
				.order('start_at');

			if (error) throw error;

			// Process events into suggested time slots
			const slots = data.map((event) => {
				const startDate = new Date(event.start_at);
				const weekday = format(startDate, 'EEEE');

				return {
					weekday,
					time: format(startDate, 'HH:mm'),
					duration: event.duration_minute,
					space_id: event.space_id || ''
				};
			});

			// Deduplicate by weekday and time
			const uniqueSlots = new Map();
			slots.forEach((slot) => {
				const key = `${slot.weekday}-${slot.time}`;
				if (!uniqueSlots.has(key)) {
					uniqueSlots.set(key, slot);
				}
			});

			suggestedSlots = Array.from(uniqueSlots.values());
		} catch (error) {
			console.error('Error fetching events:', error);
		} finally {
			isLoadingEvents = false;
		}
	}

	// Get suggested slots for a specific weekday
	function getSuggestedSlotsForDay(weekday: string) {
		return suggestedSlots.filter((slot) => slot.weekday === weekday);
	}

	// Get suggestion count for visual indicator
	function getSuggestionCount(weekday: string): number {
		return getSuggestedSlotsForDay(weekday).length;
	}

	// Get all added events sorted by date
	function getSortedEvents() {
		return [...$formData.events].sort((a, b) => {
			return new Date(a.event_start_at).getTime() - new Date(b.event_start_at).getTime();
		});
	}

	// Pre-fill form with suggested slot
	function useSuggestedSlot(slot: any, weekday: string) {
		const nextDate = getNextWeekdayDate(weekday);
		customDate = nextDate.toISOString().split('T')[0];

		// Convert to DateValue for the calendar
		const [year, month, day] = customDate.split('-').map(Number);
		dateValue = new CalendarDate(year, month, day);

		customStartTime = slot.time;
		customDurationNumber = slot.duration;
		if (slot.space_id) {
			customSpaceId = slot.space_id;
		}
		customError = null;
	}

	// Remove an event
	function removeEvent(eventId: string) {
		formData.update(($form) => ({
			...$form,
			events: $form.events.filter((e) => e.event_id !== eventId)
		}));
	}

	// Navigate date by weeks
	function adjustDateByWeeks(weeks: number) {
		const currentDate = customDate ? new Date(customDate) : new Date();
		const newDate = addWeeks(currentDate, weeks);
		customDate = newDate.toISOString().split('T')[0];

		// Update DateValue for calendar
		const [year, month, day] = customDate.split('-').map(Number);
		dateValue = new CalendarDate(year, month, day);
	}

	// Save multiple events (unified function)
	function saveEvents(
		events: Array<{
			date: string;
			startTime: string;
			duration: number;
			spaceId: string;
		}>
	) {
		// Validate all events first
		for (const event of events) {
			if (!event.date || !event.startTime || !event.duration) {
				customError = 'All events must have date, time, and duration';
				return;
			}

			if (!event.spaceId) {
				customError = 'All events must have a space selected';
				return;
			}

			const duration = event.duration;
			if (isNaN(duration) || duration <= 0) {
				customError = 'Duration must be a positive number for all events';
				return;
			}

			// Validate duration constraints
			if (program.event_kind) {
				if (duration < program.event_duration_minute_min) {
					customError = `Duration must be at least ${program.event_duration_minute_min} minutes for all events`;
					return;
				}
				if (program.event_duration_minute_max && duration > program.event_duration_minute_max) {
					customError = `Duration cannot exceed ${program.event_duration_minute_max} minutes for all events`;
					return;
				}
			}
		}

		// Create all events
		const newEvents = events.map((event) => {
			const startDateTime = new Date(`${event.date}T${event.startTime}:00`);

			return {
				event_id: generateEventId(),
				event_kind: currentEventKind,
				event_start_at: startDateTime.toISOString(),
				event_duration_minute: event.duration,
				event_space_id: event.spaceId,
				event_publishing_state: currentPublishingState,
				event_metadata_id: $formData.metadata.metadata_id || '' // Will be set properly on submission
			};
		});

		// Add all events to form data
		formData.update(($form) => ({
			...$form,
			events: [...$form.events, ...newEvents]
		}));

		// Clear error
		customError = null;

		// Update form to prepare for next event (set to the day after the last created event)
		if (newEvents.length > 0) {
			const lastEvent = newEvents[newEvents.length - 1];
			const lastEventDate = new Date(lastEvent.event_start_at);
			const nextEventDate = new Date(lastEventDate);
			nextEventDate.setDate(nextEventDate.getDate() + 7);

			customDate = nextEventDate.toISOString().split('T')[0];
			const [year, month, day] = customDate.split('-').map(Number);
			dateValue = new CalendarDate(year, month, day);
		}
	}

	// Initialize data on mount - runs only once
	$effect(() => {
		if (isInitialized) return;

		// Always fetch past events for suggestions
		fetchPastEvents();

		// Set initial form values
		const lastEvent =
			$formData.events.length > 0 ? $formData.events[$formData.events.length - 1] : null;

		if (lastEvent) {
			// Use last event as template
			const lastEventDate = new Date(lastEvent.event_start_at);
			// Add one week to the last event date
			const nextEventDate = new Date(lastEventDate);
			nextEventDate.setDate(nextEventDate.getDate() + 7);

			customDate = nextEventDate.toISOString().split('T')[0];
			const [year, month, day] = customDate.split('-').map(Number);
			dateValue = new CalendarDate(year, month, day);

			// Copy time from last event
			customStartTime = format(lastEventDate, 'HH:mm');
			customDurationNumber = lastEvent.event_duration_minute;
			customSpaceId = lastEvent.event_space_id || '';
		} else {
			// Default values for first event
			const nextDate = getNextWeekdayDate(selectedTab);
			customDate = nextDate.toISOString().split('T')[0];
			const [year, month, day] = customDate.split('-').map(Number);
			dateValue = new CalendarDate(year, month, day);
			customDurationNumber = program.event_duration_minute_min || 60;
		}

		isInitialized = true;
	});

	// Update customDate when dateValue changes
	$effect(() => {
		if (dateValue) {
			customDate = `${dateValue.year}-${String(dateValue.month).padStart(2, '0')}-${String(dateValue.day).padStart(2, '0')}`;
		}
	});
</script>

<div class="space-y-6">
	{#if program.event_kind}
		<!-- Time Suggestions Tabs -->
		<div class="mb-6 space-y-3">
			<h3 class="text-sm font-medium">Suggested Times</h3>

			<Tabs.Root bind:value={selectedTab}>
				<Tabs.List class="grid w-full grid-cols-7">
					{#each WEEKDAYS as day}
						{@const suggestionCount = getSuggestionCount(day.id)}
						<Tabs.Trigger value={day.id} class="relative flex items-center justify-center gap-2">
							<span>{day.short}</span>
							{#if suggestionCount > 0}
								<span class="text-muted-foreground text-xs tabular-nums">
									{suggestionCount}
								</span>
							{/if}
						</Tabs.Trigger>
					{/each}
				</Tabs.List>

				{#each WEEKDAYS as day}
					<Tabs.Content value={day.id} class="mt-3">
						{#if isLoadingEvents}
							<div class="text-muted-foreground py-4 text-center text-sm">
								<div
									class="mx-auto h-4 w-4 animate-spin rounded-full border-b-2 border-current"
								></div>
							</div>
						{:else}
							{@const daySuggestions = getSuggestedSlotsForDay(day.id)}
							{#if daySuggestions.length > 0}
								<div class="overflow-x-auto">
									<div class="flex gap-2 pb-2">
										{#each daySuggestions as slot}
											<Button
												type="button"
												size="sm"
												variant="outline"
												onclick={() => useSuggestedSlot(slot, day.id)}
												class="shrink-0"
											>
												{slot.time} ({slot.duration}min)
											</Button>
										{/each}
									</div>
								</div>
							{:else}
								<div class="text-muted-foreground py-4 text-center text-sm">
									No suggestions for {day.id}
								</div>
							{/if}
						{/if}
					</Tabs.Content>
				{/each}
			</Tabs.Root>
		</div>

		<!-- Add Event Form (Singleton) -->
		<AddEventForm
			bind:dateValue
			bind:customStartTime
			bind:customDuration={customDurationNumber}
			bind:customSpaceId
			customError={customError || ''}
			brandId={brand.id}
			eventDurationMin={program.event_duration_minute_min || 5}
			eventDurationMax={program.event_duration_minute_max || 180}
			onAdjustDateWeeks={adjustDateByWeeks}
			onSaveEvents={saveEvents}
		/>

		<!-- All Added Events -->
		{#if $formData.events.length > 0}
			<div class="mt-6 space-y-3">
				<div class="flex items-center justify-between">
					<h3 class="text-sm font-medium">Added Events</h3>
					<span class="text-muted-foreground text-sm tabular-nums">
						{$formData.events.length} event{$formData.events.length !== 1 ? 's' : ''}
					</span>
				</div>
				<div class="space-y-2">
					{#each getSortedEvents() as event}
						{@const displayEvent = {
							id: event.event_id || '',
							start_time: event.event_start_at,
							end_time: new Date(
								new Date(event.event_start_at).getTime() + event.event_duration_minute * 60000
							).toISOString(),
							space_id: event.event_space_id
						}}
						<EventCard event={displayEvent} onRemove={(id) => removeEvent(id)} compact={true} />
					{/each}
				</div>
			</div>
		{/if}
	{:else}
		<div class="text-muted-foreground py-6 text-center">
			<Clock class="mx-auto mb-4 h-12 w-12 opacity-50" />
			<p>No event scheduling required for this product type</p>
		</div>
	{/if}
</div>
