<script lang="ts">
	import { MapPin, Users, Clock, Calendar, X } from '@lucide/svelte';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { format, parseISO, differenceInDays, isSameDay } from 'date-fns';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';

	interface Props {
		event: {
			id: string;
			start_time: string;
			end_time: string;
			space_id?: string;
			instructor_ids?: string[];
			space?: any; // Space details if available
			instructors?: any[]; // Instructor details if available
		};
		onRemove?: (eventId: string) => void;
		selectable?: boolean;
		selected?: boolean;
		onSelect?: (eventId: string) => void;
		compact?: boolean;
		class?: string;
	}

	let {
		event,
		onRemove,
		selectable = false,
		selected = false,
		onSelect,
		compact = false,
		class: className = ''
	}: Props = $props();

	// Format time range (e.g., "10:00 - 11:30")
	function formatTimeRange(start: string, end: string): string {
		return `${format(parseISO(start), 'HH:mm')} - ${format(parseISO(end), 'HH:mm')}`;
	}

	// Get duration in minutes
	function getDuration(start: string, end: string): number {
		return Math.round((new Date(end).getTime() - new Date(start).getTime()) / 60000);
	}

	// Format date with relative context
	function formatEventDate(dateStr: string): { relative: string; exact: string } {
		const date = parseISO(dateStr);
		const now = new Date();
		const daysDiff = differenceInDays(date, now);
		const weekday = format(date, 'EEEE');
		const exactDate = format(date, 'MMM d');

		// Today
		if (isSameDay(date, now)) {
			return { relative: 'Today', exact: exactDate };
		}

		// Tomorrow
		if (daysDiff === 1) {
			return { relative: 'Tomorrow', exact: exactDate };
		}

		// This week (next 7 days)
		if (daysDiff > 0 && daysDiff <= 7) {
			return { relative: `This ${weekday}`, exact: exactDate };
		}

		// Next week
		if (daysDiff > 7 && daysDiff <= 14) {
			return { relative: `Next ${weekday}`, exact: exactDate };
		}

		// In X weeks
		if (daysDiff > 14 && daysDiff <= 28) {
			const weeksAway = Math.floor(daysDiff / 7);
			return { relative: `${weekday} in ${weeksAway} weeks`, exact: exactDate };
		}

		// Past dates
		if (daysDiff < 0 && daysDiff >= -7) {
			return { relative: `Last ${weekday}`, exact: exactDate };
		}

		// Default to full date
		return { relative: format(date, 'MMM d, yyyy'), exact: '' };
	}

	// Get landmark name from space
	function getLandmarkName(): string {
		if (event.space?.landmark?.title) {
			return getLocalizedText(event.space.landmark.title as LocalizedText);
		}
		return 'No location';
	}

	// Get instructor count
	function getInstructorInfo(): string {
		const count = event.instructor_ids?.length || 0;
		if (count === 0) return 'No instructors';
		if (count === 1) return '1 instructor';
		return `${count} instructors`;
	}

	// Handle click
	function handleClick(e: MouseEvent) {
		// Don't trigger if clicking on checkbox
		if ((e.target as HTMLElement).closest('button[role="checkbox"]')) {
			return;
		}
		if (selectable && onSelect) {
			onSelect(event.id);
		}
	}

	// Get formatted date
	let formattedDate = $derived(formatEventDate(event.start_time));
</script>

{#if selectable}
	<div
		class="group hover:bg-accent/5 relative cursor-pointer rounded-lg border transition-all {selected
			? 'border-primary bg-primary/5'
			: ''} {className}"
		onclick={handleClick}
		onkeydown={(e) => {
			if (e.key === 'Enter' || e.key === ' ') {
				e.preventDefault();
				handleClick(e as any);
			}
		}}
		role="button"
		tabindex="0"
	>
		<div class="flex items-start gap-3 p-3">
			<!-- Checkbox -->
			<div class="pt-0.5">
				<Checkbox
					checked={selected}
					onCheckedChange={(checked) => {
						if (onSelect) onSelect(event.id);
					}}
					class="h-4 w-4"
				/>
			</div>

			<!-- Main Content -->
			<div class="flex-1 space-y-2">
				<!-- Date and Time Row -->
				<div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between">
					<div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:gap-3">
						<div class="flex items-center gap-2">
							<Calendar class="text-muted-foreground h-4 w-4" />
							<span class="font-medium">
								{formattedDate.relative}
								{#if formattedDate.exact}
									<span class="text-muted-foreground ml-1 text-sm font-normal">
										({formattedDate.exact})
									</span>
								{/if}
							</span>
						</div>
						<div class="text-muted-foreground flex items-center gap-2 text-sm">
							<Clock class="h-3 w-3" />
							<span>{formatTimeRange(event.start_time, event.end_time)}</span>
						</div>
					</div>
					<Badge variant="secondary" class="mt-1 self-start text-xs sm:mt-0">
						{getDuration(event.start_time, event.end_time)} min
					</Badge>
				</div>

				<!-- Location and Instructors Row -->
				<div class="flex flex-col gap-1 text-sm sm:flex-row sm:items-center sm:gap-4">
					<div class="flex items-center gap-1.5">
						<MapPin class="text-muted-foreground h-3 w-3" />
						<span>{event.space?.name || (event.space_id ? 'Space selected' : 'Space not set')}</span
						>
						{#if event.space?.landmark?.title}
							<Badge variant="outline" class="ml-1 text-xs">
								{getLandmarkName()}
							</Badge>
						{/if}
					</div>
					<div class="flex items-center gap-1.5">
						<Users class="text-muted-foreground h-3 w-3" />
						<span>{getInstructorInfo()}</span>
					</div>
				</div>

				<!-- Instructor names if available and not compact -->
				{#if !compact && event.instructors && event.instructors.length > 0}
					<div class="flex flex-wrap gap-1">
						{#each event.instructors as instructor}
							<Badge variant="secondary" class="text-xs">
								{getLocalizedText(instructor.title as LocalizedText) ||
									instructor.name ||
									'Unknown'}
							</Badge>
						{/each}
					</div>
				{/if}
			</div>
		</div>
	</div>
{:else}
	<div class="group relative rounded-lg border transition-all {className}">
		<div class="flex items-start gap-3 p-3">
			<div class="flex-1 space-y-2">
				<!-- Date and Time Row -->
				<div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:justify-between">
					<div class="flex flex-col gap-1 sm:flex-row sm:items-center sm:gap-3">
						<div class="flex items-center gap-2">
							<Calendar class="text-muted-foreground h-4 w-4" />
							<span class="font-medium">
								{formattedDate.relative}
								{#if formattedDate.exact}
									<span class="text-muted-foreground ml-1 text-sm font-normal">
										({formattedDate.exact})
									</span>
								{/if}
							</span>
						</div>
						<div class="text-muted-foreground flex items-center gap-2 text-sm">
							<Clock class="h-3 w-3" />
							<span>{formatTimeRange(event.start_time, event.end_time)}</span>
						</div>
					</div>
					<div class="flex items-center gap-2">
						<Badge variant="secondary" class="text-xs">
							{getDuration(event.start_time, event.end_time)} min
						</Badge>
						{#if onRemove}
							<Button
								type="button"
								size="icon"
								variant="ghost"
								class="text-destructive hover:text-destructive h-7 w-7 shrink-0"
								onclick={(e) => {
									e.stopPropagation();
									onRemove(event.id);
								}}
							>
								<X class="h-4 w-4" />
							</Button>
						{/if}
					</div>
				</div>

				<!-- Location and Instructors Row -->
				<div class="flex flex-col gap-1 text-sm sm:flex-row sm:items-center sm:gap-4">
					<div class="flex items-center gap-1.5">
						<MapPin class="text-muted-foreground h-3 w-3" />
						<span>{event.space?.name || (event.space_id ? 'Space selected' : 'Space not set')}</span
						>
						{#if event.space?.landmark?.title}
							<Badge variant="outline" class="ml-1 text-xs">
								{getLandmarkName()}
							</Badge>
						{/if}
					</div>
					<div class="flex items-center gap-1.5">
						<Users class="text-muted-foreground h-3 w-3" />
						<span>{getInstructorInfo()}</span>
					</div>
				</div>

				<!-- Instructor names if available and not compact -->
				{#if !compact && event.instructors && event.instructors.length > 0}
					<div class="flex flex-wrap gap-1">
						{#each event.instructors as instructor}
							<Badge variant="secondary" class="text-xs">
								{getLocalizedText(instructor.title as LocalizedText) ||
									instructor.name ||
									'Unknown'}
							</Badge>
						{/each}
					</div>
				{/if}
			</div>
		</div>
	</div>
{/if}
