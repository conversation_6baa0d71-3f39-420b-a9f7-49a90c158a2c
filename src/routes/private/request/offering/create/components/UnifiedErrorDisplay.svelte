<script lang="ts">
	import { AlertCircle, XCircle } from '@lucide/svelte';
	import type { SuperForm } from 'sveltekit-superforms';
	import type { ProductCreationFormData } from '../schema';

	interface Props {
		form: SuperForm<ProductCreationFormData>;
		program: any;
		hasValidationErrors: boolean;
		customErrors?: string[];
	}

	let { form, program, hasValidationErrors = $bindable(), customErrors = [] }: Props = $props();

	const { errors, message, form: formData } = form;

	// Get all validation errors from the form
	let allValidationErrors = $derived(() => {
		const formErrors = $errors as Record<string, any>;
		const errorList: Array<{ field: string; message: string }> = [];

		// Recursively collect all errors from the errors object
		const collectErrors = (obj: any, prefix = '') => {
			for (const [key, value] of Object.entries(obj || {})) {
				const fieldPath = prefix ? `${prefix}.${key}` : key;

				if (Array.isArray(value)) {
					errorList.push({ field: fieldPath, message: value.join(', ') });
				} else if (typeof value === 'string') {
					errorList.push({ field: fieldPath, message: value });
				} else if (typeof value === 'object' && value !== null) {
					collectErrors(value, fieldPath);
				}
			}
		};

		collectErrors(formErrors);
		return errorList;
	});

	// Check program requirements for custom business logic errors
	let programRequirementErrors = $derived(() => {
		const errors: string[] = [];

		if (!program?.event_kind) return errors;

		// Check event count requirements
		const eventCount = $formData.events?.length || 0;
		if (program.event_count_min && eventCount < program.event_count_min) {
			errors.push(
				`Minimum ${program.event_count_min} event${program.event_count_min !== 1 ? 's' : ''} required (currently ${eventCount})`
			);
		}
		if (program.event_count_max && eventCount > program.event_count_max) {
			errors.push(
				`Maximum ${program.event_count_max} event${program.event_count_max !== 1 ? 's' : ''} allowed (currently ${eventCount})`
			);
		}

		// Check event duration requirements
		$formData.events?.forEach((event, index) => {
			if (
				program.event_duration_minute_min &&
				event.event_duration_minute < program.event_duration_minute_min
			) {
				errors.push(
					`Event ${index + 1}: Duration must be at least ${program.event_duration_minute_min} minutes (currently ${event.event_duration_minute})`
				);
			}
			if (
				program.event_duration_minute_max &&
				event.event_duration_minute > program.event_duration_minute_max
			) {
				errors.push(
					`Event ${index + 1}: Duration cannot exceed ${program.event_duration_minute_max} minutes (currently ${event.event_duration_minute})`
				);
			}
		});

		// Check for missing space assignments
		$formData.events?.forEach((event, index) => {
			if (!event.event_space_id) {
				errors.push(`Event ${index + 1}: Space is required`);
			}
		});

		// Check for missing event-product relationships
		if (
			$formData.events?.length > 0 &&
			$formData.products?.length > 0 &&
			$formData.event_products?.length === 0
		) {
			errors.push('At least one event must be assigned to a product');
		}

		// Check for products without events
		$formData.products?.forEach((product, index) => {
			const hasEvents = $formData.event_products?.some(
				(ep) => ep.product_id === product.product_id
			);
			if (!hasEvents) {
				errors.push(`Product ${index + 1}: Must be assigned to at least one event`);
			}
		});

		return errors;
	});

	// Get server message error
	let serverError = $derived($message || null);

	// Calculate validation errors that should prevent submission (excluding server errors)
	let hasValidationErrorsOnly = $derived(
		allValidationErrors().length > 0 ||
			programRequirementErrors().length > 0 ||
			customErrors.length > 0
	);

	// Calculate if there are any errors to display (including server errors for UI)
	let hasAnyErrors = $derived(hasValidationErrorsOnly || serverError !== null);

	// Update the bindable hasValidationErrors (only validation errors, not server errors)
	$effect(() => {
		hasValidationErrors = hasValidationErrorsOnly;
	});

	// Format field names for display
	function formatFieldName(field: string): string {
		return field
			.replace(/\./g, ' → ')
			.replace(/_/g, ' ')
			.replace(/([A-Z])/g, ' $1')
			.toLowerCase()
			.split(' ')
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(' ');
	}
</script>

{#if hasAnyErrors}
	<div class="border-destructive bg-destructive/10 rounded-lg border p-4">
		<div class="flex items-start gap-3">
			<XCircle class="text-destructive mt-0.5 h-5 w-5" />
			<div class="flex-1 space-y-3">
				<h3 class="text-destructive font-medium">
					Please fix the following errors before submitting:
				</h3>

				<!-- Server Error (highest priority) -->
				{#if serverError}
					<div class="space-y-1">
						<h4 class="text-destructive text-sm font-medium">Server Error:</h4>
						<p class="text-destructive text-sm">{serverError}</p>
					</div>
				{/if}

				<!-- Validation Errors -->
				{#if allValidationErrors().length > 0}
					<div class="space-y-1">
						<h4 class="text-destructive text-sm font-medium">Validation Errors:</h4>
						<ul class="space-y-1">
							{#each allValidationErrors() as error}
								<li class="text-destructive text-sm">
									<strong>{formatFieldName(error.field)}:</strong>
									{error.message}
								</li>
							{/each}
						</ul>
					</div>
				{/if}

				<!-- Program Requirement Errors -->
				{#if programRequirementErrors().length > 0}
					<div class="space-y-1">
						<h4 class="text-destructive text-sm font-medium">Program Requirements:</h4>
						<ul class="space-y-1">
							{#each programRequirementErrors() as error}
								<li class="text-destructive text-sm">{error}</li>
							{/each}
						</ul>
					</div>
				{/if}

				<!-- Custom Errors -->
				{#if customErrors.length > 0}
					<div class="space-y-1">
						<h4 class="text-destructive text-sm font-medium">Additional Issues:</h4>
						<ul class="space-y-1">
							{#each customErrors as error}
								<li class="text-destructive text-sm">{error}</li>
							{/each}
						</ul>
					</div>
				{/if}
			</div>
		</div>
	</div>
{/if}
