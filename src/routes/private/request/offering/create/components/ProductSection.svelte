<script lang="ts">
	import { Card } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { Label } from '$lib/components/ui/label';
	import { Input } from '$lib/components/ui/input';
	import * as Select from '$lib/components/ui/select';
	import * as Tabs from '$lib/components/ui/tabs';
	import { DollarSign, Info, Plus, Trash2, Package, Calendar, Users } from '@lucide/svelte';
	import type { SuperForm } from 'sveltekit-superforms';
	import type { ProductCreationFormData } from '../schema';
	import type { Database } from '$lib/supabase/database.types';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import PriceSelector from '../../../../product/components/pricing/PriceSelector.svelte';
	import EventCard from './EventCard.svelte';

	// Type for Program with relations
	type Program = Database['public']['Tables']['program']['Row'] & {
		product_kind: Database['public']['Tables']['product_kind']['Row'] | null;
		event_kind: Database['public']['Tables']['event_kind']['Row'] | null;
		program_price: Array<
			Database['public']['Tables']['program_price']['Row'] & {
				price: Database['public']['Tables']['price']['Row'] | null;
			}
		>;
		program_metadata_wikipage: Array<
			Database['public']['Tables']['program_metadata_wikipage']['Row'] & {
				metadata_wikipage_relation:
					| Database['public']['Tables']['metadata_wikipage_relation']['Row']
					| null;
			}
		>;
	};

	interface Props {
		form: SuperForm<ProductCreationFormData>;
		program: Program;
		prices: Array<
			Database['public']['Tables']['price']['Row'] & {
				price_localized_data: Array<{
					locale: string;
					display_cost: string;
				}>;
			}
		>;
		supabase: SupabaseClient<Database>;
		brandId: string;
	}

	let { form, program, prices, supabase, brandId }: Props = $props();

	const { form: formData, errors } = form;

	// Product form presets for UI convenience
	const PRODUCT_FORM_PRESETS = [
		{
			id: 'bundled',
			name: 'Single Product',
			description: 'One product covering all events',
			icon: Package
		},
		{
			id: 'individual',
			name: 'Per Event',
			description: 'Separate product for each event',
			icon: Calendar
		},
		{
			id: 'trial',
			name: 'First + Rest',
			description: 'First event separate, remaining bundled',
			icon: Users
		},
		{
			id: 'custom',
			name: 'Custom',
			description: 'Start with empty configuration',
			icon: Package
		}
	];

	// State
	let selectedFormPreset = $state<string>('');
	let activeProductIndex = $state(0);

	// Get available program prices
	let availablePrices = $derived(() => {
		const programPriceIds = program.program_price.map((pp) => pp.price_id);
		return prices.filter((p) => programPriceIds.includes(p.id));
	});

	// Get current product kind from metadata
	let currentProductKind = $derived(
		$formData.metadata.metadata_custom_attribute?.product_kind || ''
	);

	// Get current publishing state from metadata
	let currentPublishingState = $derived($formData.metadata.publishing_state || 'ready_for_review');

	// Helper to calculate default cost units based on duration
	function calculateCostUnits(durationMinutes: number): number {
		// 0.5 points per 30 minutes
		return Math.round((durationMinutes / 30) * 0.5 * 100) / 100;
	}

	// Calculate duration for selected events
	function getEventsDuration(eventIds: string[]): number {
		if (!$formData.events || eventIds.length === 0) return 60;
		return eventIds.reduce((total, eventId) => {
			const event = $formData.events.find((e) => e.event_id === eventId);
			if (!event) return total;
			return total + event.event_duration_minute;
		}, 0);
	}

	// Apply product form preset
	function applyProductFormPreset(presetId: string) {
		const defaultPriceId = availablePrices().length > 0 ? availablePrices()[0].id : '';
		const events = $formData.events || [];

		// Clear existing products and relationships
		formData.update(($form) => ({
			...$form,
			products: [],
			event_products: []
		}));

		const defaultOpenDate = new Date().toISOString();
		const defaultCloseDate = null; // Set to null as per user requirement

		switch (presetId) {
			case 'bundled': {
				const productId = crypto.randomUUID();
				const eventIds = events.map((e) => e.event_id!);

				formData.update(($form) => ({
					...$form,
					products: [
						{
							product_id: productId,
							product_kind: currentProductKind,
							product_stock: 100,
							product_publishing_state: currentPublishingState,
							product_open_to_buy_at: defaultOpenDate,
							product_close_to_buy_at: defaultCloseDate,
							product_metadata_id: $form.metadata.metadata_id || '',
							product_prices: [
								{
									product_price_id: crypto.randomUUID(),
									product_price_cost_units: calculateCostUnits(getEventsDuration(eventIds)),
									product_price_start_at: defaultOpenDate,
									product_price_end_at: null,
									product_price_price_id: defaultPriceId
								}
							]
						}
					],
					event_products: eventIds.map((eventId) => ({
						event_product_id: crypto.randomUUID(),
						event_product_relation: 'product_primary',
						event_id: eventId,
						product_id: productId
					}))
				}));
				break;
			}

			case 'individual': {
				const newProducts = events.map((event) => {
					const productId = crypto.randomUUID();
					return {
						product_id: productId,
						product_kind: currentProductKind,
						product_stock: 100,
						product_publishing_state: currentPublishingState,
						product_open_to_buy_at: defaultOpenDate,
						product_close_to_buy_at: defaultCloseDate,
						product_metadata_id: $formData.metadata.metadata_id || '',
						product_prices: [
							{
								product_price_id: crypto.randomUUID(),
								product_price_cost_units: calculateCostUnits(event.event_duration_minute),
								product_price_start_at: defaultOpenDate,
								product_price_end_at: null,
								product_price_price_id: defaultPriceId
							}
						]
					};
				});

				const newEventProducts = events.map((event, index) => ({
					event_product_id: crypto.randomUUID(),
					event_product_relation: 'product_primary',
					event_id: event.event_id!,
					product_id: newProducts[index].product_id!
				}));

				formData.update(($form) => ({
					...$form,
					products: newProducts,
					event_products: newEventProducts
				}));
				break;
			}

			case 'trial': {
				if (events.length === 0) break;

				const firstProductId = crypto.randomUUID();
				const restProductId = crypto.randomUUID();

				const newProducts = [
					{
						product_id: firstProductId,
						product_kind: currentProductKind,
						product_stock: 100,
						product_publishing_state: currentPublishingState,
						product_open_to_buy_at: defaultOpenDate,
						product_close_to_buy_at: defaultCloseDate,
						product_metadata_id: $formData.metadata.metadata_id || '',
						custom_title: { en: 'Trial Class' },
						product_prices: [
							{
								product_price_id: crypto.randomUUID(),
								product_price_cost_units: calculateCostUnits(events[0].event_duration_minute),
								product_price_start_at: defaultOpenDate,
								product_price_end_at: null,
								product_price_price_id: defaultPriceId
							}
						]
					}
				];

				const newEventProducts = [
					{
						event_product_id: crypto.randomUUID(),
						event_product_relation: 'product_primary',
						event_id: events[0].event_id!,
						product_id: firstProductId
					}
				];

				if (events.length > 1) {
					const restEventIds = events.slice(1).map((e) => e.event_id!);
					newProducts.push({
						product_id: restProductId,
						product_kind: currentProductKind,
						product_stock: 100,
						product_publishing_state: currentPublishingState,
						product_open_to_buy_at: defaultOpenDate,
						product_close_to_buy_at: defaultCloseDate,
						product_metadata_id: $formData.metadata.metadata_id || '',
						custom_title: { en: 'Remaining Classes' },
						product_prices: [
							{
								product_price_id: crypto.randomUUID(),
								product_price_cost_units: calculateCostUnits(getEventsDuration(restEventIds)),
								product_price_start_at: defaultOpenDate,
								product_price_end_at: null,
								product_price_price_id: defaultPriceId
							}
						]
					});

					newEventProducts.push(
						...restEventIds.map((eventId) => ({
							event_product_id: crypto.randomUUID(),
							event_product_relation: 'product_primary',
							event_id: eventId,
							product_id: restProductId
						}))
					);
				}

				formData.update(($form) => ({
					...$form,
					products: newProducts,
					event_products: newEventProducts
				}));
				break;
			}

			case 'custom':
				// Start with empty
				break;
		}

		// Reset to first tab
		activeProductIndex = 0;
	}

	// Add a new product
	function addProduct() {
		const defaultPriceId = availablePrices().length > 0 ? availablePrices()[0].id : '';
		const productId = crypto.randomUUID();

		const newProduct = {
			product_id: productId,
			product_kind: currentProductKind,
			product_stock: 100,
			product_publishing_state: currentPublishingState,
			product_open_to_buy_at: new Date().toISOString(),
			product_close_to_buy_at: null,
			product_metadata_id: $formData.metadata.metadata_id || '',
			product_prices: [
				{
					product_price_id: crypto.randomUUID(),
					product_price_cost_units: 1,
					product_price_start_at: new Date().toISOString(),
					product_price_end_at: null,
					product_price_price_id: defaultPriceId
				}
			]
		};

		formData.update(($form) => ({
			...$form,
			products: [...$form.products, newProduct]
		}));

		activeProductIndex = $formData.products.length - 1;
		selectedFormPreset = 'custom';
	}

	// Remove a product
	function removeProduct(index: number) {
		if ($formData.products.length <= 1) return;

		const productToRemove = $formData.products[index];

		formData.update(($form) => ({
			...$form,
			products: $form.products.filter((_, i) => i !== index),
			event_products: $form.event_products.filter(
				(ep) => ep.product_id !== productToRemove.product_id
			)
		}));

		// Adjust active index if needed
		if (activeProductIndex >= $formData.products.length) {
			activeProductIndex = $formData.products.length - 1;
		}
		selectedFormPreset = 'custom';
	}

	// Add a price option to a product
	function addPrice(productIndex: number) {
		const defaultPriceId = availablePrices().length > 0 ? availablePrices()[0].id : '';
		if ($formData.products[productIndex]) {
			$formData.products[productIndex].product_prices.push({
				product_price_id: crypto.randomUUID(),
				product_price_cost_units: 1,
				product_price_start_at: new Date().toISOString(),
				product_price_end_at: null,
				product_price_price_id: defaultPriceId
			});
			$formData = $formData; // Trigger reactivity
		}
	}

	// Remove a price option
	function removePrice(productIndex: number, priceIndex: number) {
		if ($formData.products[productIndex]?.product_prices.length > 1) {
			$formData.products[productIndex].product_prices.splice(priceIndex, 1);
			$formData = $formData; // Trigger reactivity
		}
	}

	// Get display title for a product
	function getProductDisplayTitle(product: any, index: number): string {
		const customTitle = getLocalizedText(product.custom_title as LocalizedText);
		return customTitle && customTitle !== '--' && customTitle.trim()
			? customTitle
			: `Product ${index + 1}`;
	}

	// Get events linked to a product
	function getProductEvents(productId: string) {
		return $formData.event_products
			.filter((ep) => ep.product_id === productId)
			.map((ep) => $formData.events.find((e) => e.event_id === ep.event_id))
			.filter(Boolean);
	}

	// Toggle event-product relationship
	function toggleEventProduct(productId: string, eventId: string) {
		const existingRelation = $formData.event_products.find(
			(ep) => ep.product_id === productId && ep.event_id === eventId
		);

		if (existingRelation) {
			// Remove relationship
			formData.update(($form) => ({
				...$form,
				event_products: $form.event_products.filter(
					(ep) => !(ep.product_id === productId && ep.event_id === eventId)
				)
			}));
		} else {
			// Add relationship
			formData.update(($form) => ({
				...$form,
				event_products: [
					...$form.event_products,
					{
						event_product_id: crypto.randomUUID(),
						event_product_relation: 'product_primary',
						event_id: eventId,
						product_id: productId
					}
				]
			}));
		}

		selectedFormPreset = 'custom';
	}

	// Initialize if empty
	$effect(() => {
		if (!$formData.products || $formData.products.length === 0) {
			// Set default preset
			selectedFormPreset = 'bundled';
			applyProductFormPreset('bundled');
		}
	});
</script>

<div class="space-y-6">
	{#if program.program_price.length > 0}
		<!-- Product Form Preset Selection -->
		<div class="mb-6 space-y-2">
			<Label>Product Configuration Template</Label>
			<Select.Root
				type="single"
				value={selectedFormPreset}
				onValueChange={(value: string | undefined) => {
					selectedFormPreset = value || '';
					if (value) applyProductFormPreset(value);
				}}
			>
				<Select.Trigger class="w-full">
					{PRODUCT_FORM_PRESETS.find((p) => p.id === selectedFormPreset)?.name ||
						'Select a product configuration template'}
				</Select.Trigger>
				<Select.Content>
					{#each PRODUCT_FORM_PRESETS as preset}
						{@const Icon = preset.icon}
						<Select.Item value={preset.id}>
							<div class="flex items-center gap-2">
								<Icon class="h-4 w-4" />
								<div>
									<div class="font-medium">{preset.name}</div>
									<div class="text-muted-foreground text-xs">{preset.description}</div>
								</div>
							</div>
						</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>

		<!-- Info Box -->
		<div class="bg-muted/50 mb-6 space-y-2 rounded-lg p-4">
			<div class="flex items-center gap-2">
				<Info class="h-4 w-4" />
				<h4 class="text-sm font-medium">Product Configuration Tips</h4>
			</div>
			<ul class="text-muted-foreground space-y-1 text-sm">
				<li>• Each tab represents a separate product that customers can purchase</li>
				<li>• Select events to include in each product</li>
				<li>• Configure pricing options for each product</li>
				<li>• Cost units are initially calculated at 0.5 points per 30 minutes</li>
			</ul>
		</div>

		<!-- Products Tabs -->
		{#if $formData.products.length > 0}
			<Tabs.Root value={`product-${activeProductIndex}`}>
				<div class="mb-4 overflow-hidden">
					<div class="overflow-x-auto">
						<Tabs.List class="w-max min-w-full justify-start">
							{#each $formData.products as product, index}
								<Tabs.Trigger
									value={`product-${index}`}
									onclick={() => (activeProductIndex = index)}
									class="relative shrink-0"
								>
									{getProductDisplayTitle(product, index)}
									{@const linkedEvents = getProductEvents(product.product_id!)}
									{#if linkedEvents.length === 0}
										<span class="text-muted-foreground ml-1 text-xs">•</span>
									{/if}
								</Tabs.Trigger>
							{/each}
							<Button
								type="button"
								size="sm"
								variant="ghost"
								class="ml-2 shrink-0"
								onclick={() => addProduct()}
							>
								<Plus class="h-4 w-4" />
							</Button>
						</Tabs.List>
					</div>
				</div>

				{#each $formData.products as product, productIndex}
					<Tabs.Content value={`product-${productIndex}`}>
						<Card class="py-4">
							<div class="space-y-4 px-4">
								<!-- Product Actions -->
								<div class="flex items-center justify-between">
									<h3 class="text-sm font-medium">
										{getProductDisplayTitle(product, productIndex)}
									</h3>
									<div class="flex gap-1">
										{#if $formData.products.length > 1}
											<Button
												type="button"
												size="sm"
												variant="ghost"
												onclick={() => removeProduct(productIndex)}
											>
												<Trash2 class="mr-1 h-3 w-3" />
												Remove
											</Button>
										{/if}
									</div>
								</div>

								<!-- Product Content -->
								<div class="space-y-4">
									<!-- Custom Title -->
									<LocalizedTextControl
										{form}
										name="products[{productIndex}].custom_title"
										label="Product Title"
									/>

									<!-- Product Stock -->
									<div class="space-y-1">
										<Label class="text-xs"
											>Product Stock<span class="text-destructive ml-1">*</span></Label
										>
										<Input
											type="number"
											min="0"
											name={`products[${productIndex}].product_stock`}
											bind:value={product.product_stock}
											class="h-9 w-full sm:max-w-xs"
											placeholder="Available quantity"
										/>
										<p class="text-muted-foreground text-xs">
											Total quantity available for this product
										</p>
									</div>

									<!-- Event Selection -->
									<div class="space-y-1">
										<Label class="text-xs">
											Included Events
											{@const linkedEvents = getProductEvents(product.product_id!)}
											{#if linkedEvents.length === 0}
												<span class="text-destructive ml-1">*</span>
											{/if}
										</Label>
										{#if $formData.events && $formData.events.length > 0}
											<div class="space-y-2">
												{#each $formData.events as event}
													{@const isLinked = $formData.event_products.some(
														(ep) =>
															ep.product_id === product.product_id && ep.event_id === event.event_id
													)}
													{@const displayEvent = {
														id: event.event_id || '',
														start_time: event.event_start_at,
														end_time: new Date(
															new Date(event.event_start_at).getTime() +
																event.event_duration_minute * 60000
														).toISOString(),
														space_id: event.event_space_id
													}}
													<EventCard
														event={displayEvent}
														selectable={true}
														selected={isLinked}
														onSelect={() =>
															toggleEventProduct(product.product_id!, event.event_id!)}
														compact={true}
													/>
												{/each}
											</div>
										{:else}
											<div
												class="rounded-lg bg-amber-50 p-3 text-sm text-amber-800 dark:bg-amber-900/20 dark:text-amber-200"
											>
												<div class="flex items-start gap-2">
													<Info class="mt-0.5 h-4 w-4 shrink-0" />
													<p>Please add events in the Schedule section first</p>
												</div>
											</div>
										{/if}
									</div>

									<!-- Pricing Options -->
									<div class="space-y-3">
										<div class="flex items-center justify-between">
											<Label class="text-xs">Pricing Options</Label>
											<Button
												type="button"
												size="sm"
												variant="outline"
												onclick={() => addPrice(productIndex)}
											>
												<Plus class="mr-2 h-3 w-3" />
												Add Price
											</Button>
										</div>

										{#each product.product_prices as price, priceIndex}
											<div class="space-y-2">
												{#if product.product_prices.length > 1}
													<div class="flex items-center justify-between">
														<h4 class="text-xs font-medium">Price Option {priceIndex + 1}</h4>
														<Button
															type="button"
															size="sm"
															variant="ghost"
															onclick={() => removePrice(productIndex, priceIndex)}
															disabled={product.product_prices.length <= 1}
														>
															<Trash2 class="h-3 w-3" />
														</Button>
													</div>
												{/if}

												<!-- Price Selection and Cost Units in same row -->
												<div class="grid grid-cols-1 gap-2 sm:grid-cols-3">
													<div class="sm:col-span-1">
														<div class="space-y-1">
															<Label class="text-xs"
																>Price Type<span class="text-destructive ml-1">*</span></Label
															>
															<PriceSelector
																{supabase}
																{brandId}
																selectedPriceId={price.product_price_price_id}
																onSelect={(id: string, name: string) => {
																	price.product_price_price_id = id;
																	$formData = $formData; // Trigger reactivity
																}}
															/>
															<input
																type="hidden"
																name={`products[${productIndex}].product_prices[${priceIndex}].product_price_price_id`}
																value={price.product_price_price_id}
															/>
														</div>
													</div>

													<div class="sm:col-span-1">
														<div class="space-y-1">
															<Label class="text-xs">
																Cost Units
																{@const linkedEvents = getProductEvents(product.product_id!)}
																{#if linkedEvents.length > 0}
																	{@const totalDuration = linkedEvents.reduce(
																		(sum, e) => sum + (e?.event_duration_minute || 0),
																		0
																	)}
																	<span class="text-muted-foreground ml-1">
																		({totalDuration} min)
																	</span>
																{/if}
															</Label>
															<Input
																type="number"
																min="0.1"
																step="0.1"
																name={`products[${productIndex}].product_prices[${priceIndex}].product_price_cost_units`}
																value={price.product_price_cost_units}
																oninput={(e) => {
																	price.product_price_cost_units =
																		parseFloat(e.currentTarget.value) || 0;
																	$formData = $formData; // Trigger reactivity
																}}
																class="h-9"
															/>
														</div>
													</div>

													<div class="sm:col-span-1">
														<div class="space-y-1">
															<Label class="text-xs">Stock Limit</Label>
															<Input
																type="number"
																min="0"
																name={`products[${productIndex}].product_prices[${priceIndex}].product_price_stock`}
																value={price.product_price_stock || ''}
																placeholder="Unlimited"
																oninput={(e) => {
																	const val = e.currentTarget.value;
																	price.product_price_stock =
																		val === '' ? undefined : parseInt(val) || 0;
																	$formData = $formData; // Trigger reactivity
																}}
																class="h-9"
															/>
														</div>
													</div>
												</div>
											</div>
										{/each}
									</div>
								</div>
							</div>
						</Card>
					</Tabs.Content>
				{/each}
			</Tabs.Root>
		{:else}
			<!-- Empty State -->
			<div class="rounded-lg border border-dashed p-8 text-center">
				<Package class="text-muted-foreground mx-auto mb-4 h-12 w-12" />
				<h3 class="mb-2 text-lg font-semibold">No Products Configured</h3>
				<p class="text-muted-foreground mb-4 text-sm">
					Select a product configuration template above or create a custom configuration
				</p>
				<Button type="button" onclick={() => addProduct()}>
					<Plus class="mr-2 h-4 w-4" />
					Add First Product
				</Button>
			</div>
		{/if}
	{:else}
		<div class="text-muted-foreground py-8 text-center">
			<DollarSign class="mx-auto mb-4 h-12 w-12 opacity-50" />
			<p>No pricing options configured for this program</p>
			<p class="mt-2 text-sm">Please configure pricing in the program settings first</p>
		</div>
	{/if}
</div>
