<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Card } from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import * as Popover from '$lib/components/ui/popover';
	import { Calendar } from '$lib/components/ui/calendar';
	import * as Select from '$lib/components/ui/select';
	import { Separator } from '$lib/components/ui/separator';
	import SpacePicker from '$lib/components/shared/SpacePicker.svelte';
	import { CalendarIcon, Plus, AlertCircle } from '@lucide/svelte';
	import { buttonVariants } from '$lib/components/ui/button';
	import { cn } from '$lib/utils';
	import { DateFormatter, getLocalTimeZone } from '@internationalized/date';
	import { addWeeks, addYears, format } from 'date-fns';
	import type { DateValue } from '@internationalized/date';

	interface Props {
		dateValue: DateValue | undefined;
		customStartTime: string;
		customDuration: number;
		customSpaceId: string;
		customError: string;
		brandId: string;
		eventDurationMin?: number;
		eventDurationMax?: number;
		onAdjustDateWeeks: (weeks: number) => void;
		onSaveEvents: (
			events: Array<{
				date: string;
				startTime: string;
				duration: number;
				spaceId: string;
			}>
		) => void;
	}

	let {
		dateValue = $bindable(),
		customStartTime = $bindable(),
		customDuration = $bindable(),
		customSpaceId = $bindable(),
		customError,
		brandId,
		eventDurationMin = 5,
		eventDurationMax = 180,
		onAdjustDateWeeks,
		onSaveEvents
	}: Props = $props();

	// Repeat options
	const repeatOptions = [
		{ value: 'single', label: 'Single Event' },
		{ value: 'count', label: 'Repeat Weekly' },
		{ value: 'until', label: 'Repeat Until Date' }
	];

	// Repeat options state
	let repeatMode = $state('single');
	let repeatCount = $state(2);
	let repeatEndDate = $state<DateValue | undefined>();

	const df = new DateFormatter('en-US', {
		weekday: 'short',
		year: 'numeric',
		month: 'short',
		day: 'numeric'
	});

	const shortDf = new DateFormatter('en-US', {
		weekday: 'short',
		month: 'short',
		day: 'numeric'
	});

	// Helper function to format time to 12-hour format with AM/PM
	function formatTime12Hour(timeString: string): string {
		const [hours, minutes] = timeString.split(':').map(Number);
		const date = new Date();
		date.setHours(hours, minutes);
		return date.toLocaleTimeString('en-US', {
			hour: 'numeric',
			minute: '2-digit',
			hour12: true
		});
	}

	// Get the display text for the select trigger
	const triggerContent = $derived(
		repeatOptions.find((option) => option.value === repeatMode)?.label ?? 'Select repeat mode'
	);

	// Calculate max date (2 years from now)
	const maxEndDate = $derived(() => {
		const today = new Date();
		return addYears(today, 2);
	});

	function adjustDuration(minutes: number) {
		const newDuration = customDuration + minutes;
		if (newDuration >= eventDurationMin && newDuration <= eventDurationMax) {
			customDuration = newDuration;
		}
	}

	// Calculate how many events will be created and preview dates
	let eventPreview = $derived(() => {
		if (repeatMode === 'single' || !dateValue) {
			return { count: 1, dates: [] };
		}

		const startDate = dateValue.toDate(getLocalTimeZone());
		const events = [];

		let currentDate = new Date(startDate);
		let eventCount = 0;
		const maxEvents = 100; // Updated safety limit

		while (eventCount < maxEvents) {
			events.push(new Date(currentDate));
			eventCount++;

			// Check end conditions
			if (repeatMode === 'count' && eventCount >= repeatCount) {
				break;
			}

			if (repeatMode === 'until' && repeatEndDate) {
				const endDate = repeatEndDate.toDate(getLocalTimeZone());
				if (currentDate >= endDate) {
					break;
				}
			}

			// Move to next week (7 days)
			currentDate = addWeeks(currentDate, 1);

			// Safety check for infinite loops
			if (repeatMode === 'until' && !repeatEndDate) {
				break;
			}
		}

		// Show first 2, last 2, and skip middle if more than 5 events
		let previewDates = events;
		let showEllipsis = false;

		if (events.length > 5) {
			previewDates = [...events.slice(0, 2), ...events.slice(-2)];
			showEllipsis = true;
		}

		return {
			count: events.length,
			dates: previewDates,
			showEllipsis
		};
	});

	function handleSaveEvent() {
		if (repeatMode === 'single') {
			if (!dateValue) {
				customError = 'Date is required';
				return;
			}
			onSaveEvents([
				{
					date: format(dateValue.toDate(getLocalTimeZone()), 'yyyy-MM-dd'),
					startTime: customStartTime,
					duration: customDuration,
					spaceId: customSpaceId
				}
			]);
			return;
		}

		// Generate all repeating events
		if (!dateValue) return;

		const startDate = dateValue.toDate(getLocalTimeZone());
		const events = [];

		let currentDate = new Date(startDate);
		let eventCount = 0;
		const maxEvents = 100; // Updated safety limit

		while (eventCount < maxEvents) {
			const eventDate = format(currentDate, 'yyyy-MM-dd');
			events.push({
				date: eventDate,
				startTime: customStartTime,
				duration: customDuration,
				spaceId: customSpaceId
			});
			eventCount++;

			// Check end conditions
			if (repeatMode === 'count' && eventCount >= repeatCount) {
				break;
			}

			if (repeatMode === 'until' && repeatEndDate) {
				const endDate = repeatEndDate.toDate(getLocalTimeZone());
				if (currentDate >= endDate) {
					break;
				}
			}

			// Move to next week
			currentDate = addWeeks(currentDate, 1);

			// Safety check for infinite loops
			if (repeatMode === 'until' && !repeatEndDate) {
				break;
			}
		}

		onSaveEvents(events);

		// Reset to single event mode after creating repeating events
		repeatMode = 'single';
		repeatCount = 2;
		repeatEndDate = undefined;
	}
</script>

<Card class="py-4">
	<div class="space-y-4 px-4">
		<h3 class="text-sm font-medium">Add Event</h3>

		{#if customError}
			<div class="bg-destructive/10 text-destructive flex items-center gap-2 rounded p-2 text-sm">
				<AlertCircle class="h-4 w-4" />
				<span>{customError}</span>
			</div>
		{/if}

		<div class="grid gap-2 sm:grid-cols-2">
			<!-- Date -->
			<div class="space-y-1">
				<Label class="text-xs">Date</Label>
				<div class="flex items-center gap-1">
					<Button
						type="button"
						size="icon"
						variant="outline"
						class="h-9 w-9 text-xs"
						onclick={() => onAdjustDateWeeks(-1)}
					>
						-7
					</Button>

					<Popover.Root>
						<Popover.Trigger
							class={cn(
								buttonVariants({
									variant: 'outline',
									class: 'h-9 flex-1 justify-start text-left font-normal'
								}),
								!dateValue && 'text-muted-foreground'
							)}
						>
							<CalendarIcon class="mr-2 h-4 w-4" />
							{dateValue ? df.format(dateValue.toDate(getLocalTimeZone())) : 'Pick a date'}
						</Popover.Trigger>
						<Popover.Content class="w-auto p-0">
							<Calendar type="single" bind:value={dateValue} />
						</Popover.Content>
					</Popover.Root>

					<Button
						type="button"
						size="icon"
						variant="outline"
						class="h-9 w-9 text-xs"
						onclick={() => onAdjustDateWeeks(1)}
					>
						+7
					</Button>
				</div>
			</div>

			<!-- Start Time -->
			<div class="space-y-1">
				<Label class="text-xs">Start Time</Label>
				<Input type="time" bind:value={customStartTime} class="h-9" />
			</div>

			<!-- Duration and Space on same row -->
			<div class="space-y-1">
				<Label class="text-xs">Duration (minutes)</Label>
				<div class="flex items-center gap-1">
					<Button
						type="button"
						size="sm"
						variant="outline"
						class="h-9 px-3 text-xs"
						onclick={() => adjustDuration(-30)}
						disabled={customDuration - 30 < eventDurationMin}
					>
						-30
					</Button>
					<Button
						type="button"
						size="sm"
						variant="outline"
						class="h-9 px-3 text-xs"
						onclick={() => adjustDuration(-5)}
						disabled={customDuration - 5 < eventDurationMin}
					>
						-5
					</Button>
					<Input
						type="number"
						bind:value={customDuration}
						min={eventDurationMin}
						max={eventDurationMax}
						class="h-9 flex-1 text-center text-sm"
					/>
					<Button
						type="button"
						size="sm"
						variant="outline"
						class="h-9 px-3 text-xs"
						onclick={() => adjustDuration(5)}
						disabled={customDuration + 5 > eventDurationMax}
					>
						+5
					</Button>
					<Button
						type="button"
						size="sm"
						variant="outline"
						class="h-9 px-3 text-xs"
						onclick={() => adjustDuration(30)}
						disabled={customDuration + 30 > eventDurationMax}
					>
						+30
					</Button>
				</div>
			</div>

			<!-- Space -->
			<div class="space-y-1">
				<Label class="text-xs">Space<span class="text-destructive ml-1">*</span></Label>
				<SpacePicker
					bind:value={customSpaceId}
					{brandId}
					placeholder="Select a space"
					class="w-full"
				/>
			</div>
		</div>

		<Separator />

		<!-- Repeat Options -->
		<div class="grid gap-2 sm:grid-cols-2">
			<!-- Repeat Mode -->
			<div class="space-y-1">
				<Label class="text-xs">Repeat Options</Label>
				<Select.Root type="single" bind:value={repeatMode}>
					<Select.Trigger class="h-9 w-full">
						{triggerContent}
					</Select.Trigger>
					<Select.Content>
						{#each repeatOptions as option (option.value)}
							<Select.Item value={option.value} label={option.label}>
								{option.label}
							</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>

			<!-- Additional Options Based on Mode -->
			{#if repeatMode === 'count'}
				<div class="space-y-1">
					<Label class="text-xs">Number of Events</Label>
					<Input
						type="number"
						bind:value={repeatCount}
						min="2"
						max="100"
						class="h-9"
						placeholder="2"
					/>
				</div>
			{:else if repeatMode === 'until'}
				<div class="space-y-1">
					<Label class="text-xs">End Date</Label>
					<Popover.Root>
						<Popover.Trigger
							class={cn(
								buttonVariants({
									variant: 'outline',
									class: 'h-9 w-full justify-start text-left font-normal'
								}),
								!repeatEndDate && 'text-muted-foreground'
							)}
						>
							<CalendarIcon class="mr-2 h-4 w-4" />
							{repeatEndDate
								? df.format(repeatEndDate.toDate(getLocalTimeZone()))
								: 'Pick end date'}
						</Popover.Trigger>
						<Popover.Content class="w-auto p-0">
							<Calendar type="single" bind:value={repeatEndDate} />
						</Popover.Content>
					</Popover.Root>
				</div>
			{:else}
				<!-- Empty space to maintain grid alignment -->
				<div></div>
			{/if}
		</div>

		<!-- Preview -->
		{#if eventPreview().count > 1}
			<div class="bg-muted rounded-lg p-3 text-sm">
				<div class="mb-2 font-medium">
					Will create {eventPreview().count} event{eventPreview().count !== 1 ? 's' : ''}
				</div>
				{#if eventPreview().dates.length > 0}
					<div class="space-y-1">
						{#each eventPreview().dates as date, i}
							<div class="text-muted-foreground flex items-center justify-between">
								<span>{shortDf.format(date)}</span>
								<span class="text-xs">{formatTime12Hour(customStartTime)}</span>
							</div>
							{#if eventPreview().showEllipsis && i === 1}
								<div class="flex items-center gap-2 py-2">
									<div class="border-muted-foreground/30 flex-1 border-t border-dotted"></div>
									<span class="text-muted-foreground text-xs">
										{eventPreview().count - 4} more
									</span>
									<div class="border-muted-foreground/30 flex-1 border-t border-dotted"></div>
								</div>
							{/if}
						{/each}
					</div>
				{/if}
			</div>
		{/if}

		<div class="flex justify-end">
			<Button type="button" size="sm" onclick={handleSaveEvent}>
				<Plus class="mr-1 h-3 w-3" />
				{repeatMode === 'single'
					? 'Add Event'
					: `Add ${eventPreview().count} Event${eventPreview().count !== 1 ? 's' : ''}`}
			</Button>
		</div>
	</div>
</Card>
