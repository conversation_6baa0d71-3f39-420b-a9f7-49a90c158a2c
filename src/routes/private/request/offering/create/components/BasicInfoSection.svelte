<script lang="ts">
	import { Label } from '$lib/components/ui/label';
	import * as Select from '$lib/components/ui/select';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Trash2 } from '@lucide/svelte';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import type { SuperForm } from 'sveltekit-superforms';
	import type { ProductCreationFormData } from '../schema';
	import type { Database } from '$lib/supabase/database.types';
	import Combobox from '$lib/components/ui/combobox.svelte';
	import WikipagePicker from '$lib/components/shared/WikipagePicker.svelte';
	import WikipagePickerMulti from '$lib/components/shared/WikipagePickerMulti.svelte';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import PublishingStatePicker from '$lib/components/shared/PublishingStatePicker.svelte';
	import { untrack } from 'svelte';

	// Type for Program with relations
	type Program = Database['public']['Tables']['program']['Row'] & {
		product_kind: Database['public']['Tables']['product_kind']['Row'] | null;
		event_kind: Database['public']['Tables']['event_kind']['Row'] | null;
		program_price: Array<
			Database['public']['Tables']['program_price']['Row'] & {
				price: Database['public']['Tables']['price']['Row'] | null;
			}
		>;
		program_metadata_wikipage: Array<
			Database['public']['Tables']['program_metadata_wikipage']['Row'] & {
				metadata_wikipage_relation:
					| Database['public']['Tables']['metadata_wikipage_relation']['Row']
					| null;
			}
		>;
	};

	interface Props {
		form: SuperForm<ProductCreationFormData>;
		program: Program;
		productKinds: Database['public']['Tables']['product_kind']['Row'][];
		eventKinds: Database['public']['Tables']['event_kind']['Row'][];
		metadataRelations: Database['public']['Tables']['metadata_wikipage_relation']['Row'][];
		brandId: string;
	}

	let { form, program, productKinds, eventKinds, metadataRelations, brandId }: Props = $props();

	const { form: formData, errors } = form;

	// Type for metadata relation
	type MetadataRelation = Database['public']['Tables']['metadata_wikipage_relation']['Row'];

	// Get program metadata requirements - cast properly to handle joined data
	let requiredRelations = $derived(
		program.program_metadata_wikipage.filter((pmw) => pmw.is_required)
	);

	let optionalRelations = $derived(
		program.program_metadata_wikipage.filter((pmw) => !pmw.is_required)
	);

	// Get relation options (optional relations that haven't been added yet)
	let availableRelations = $derived(
		(() => {
			const usedRelations = new Set(
				$formData.metadata.metadata_wikipages.map((mw) => mw.metadata_wikipage_relation)
			);
			return optionalRelations
				.filter((pmw) => {
					const relationId = pmw.metadata_wikipage_relation?.id;
					return relationId && !usedRelations.has(relationId);
				})
				.map((pmw) => {
					const relationId = pmw.metadata_wikipage_relation!.id;
					const relationData = pmw.metadata_wikipage_relation!;
					return {
						value: relationId,
						label: getLocalizedText(relationData.title as LocalizedText) || relationId
					};
				});
		})()
	);

	// Get current product kind and event kind from metadata custom attributes
	let currentProductKind = $derived(
		$formData.metadata.metadata_custom_attribute?.product_kind || ''
	);
	let currentEventKind = $derived($formData.metadata.metadata_custom_attribute?.event_kind || null);

	// Update product kind in metadata
	function updateProductKind(productKind: string) {
		formData.update(($form) => ({
			...$form,
			metadata: {
				...$form.metadata,
				metadata_custom_attribute: {
					...$form.metadata.metadata_custom_attribute,
					product_kind: productKind
				}
			}
		}));
	}

	// Update event kind in metadata
	function updateEventKind(eventKind: string | null) {
		formData.update(($form) => ({
			...$form,
			metadata: {
				...$form.metadata,
				metadata_custom_attribute: {
					...$form.metadata.metadata_custom_attribute,
					event_kind: eventKind
				}
			}
		}));
	}

	// Track previous publishing state to avoid infinite loops
	let previousPublishingState = $state<string | undefined>();

	// Reactivity: Update all event and product publishing states when metadata publishing state changes
	$effect(() => {
		const publishingState = $formData.metadata.publishing_state;

		// Only update if publishing state actually changed
		if (publishingState && publishingState !== previousPublishingState) {
			previousPublishingState = publishingState;

			// Use untrack to avoid creating reactive dependency during update
			untrack(() => {
				formData.update(($form) => ({
					...$form,
					events: $form.events.map((event) => ({
						...event,
						event_publishing_state: publishingState
					})),
					products: $form.products.map((product) => ({
						...product,
						product_publishing_state: publishingState
					}))
				}));
			});
		} else if (publishingState) {
			// Update the tracked state even if we don't trigger an update
			previousPublishingState = publishingState;
		}
	});
</script>

<div class="space-y-6">
	<div class="space-y-4">
		<!-- Metadata Relationships -->
		{#if program.program_metadata_wikipage.length > 0}
			<div class="grid gap-4 sm:grid-cols-2">
				<!-- Required Relations (always shown) -->
				{#each requiredRelations as programRelation, programIndex}
					{@const relationId = programRelation.metadata_wikipage_relation?.id}
					{@const relationData = programRelation.metadata_wikipage_relation}
					{@const relationTitle =
						getLocalizedText(relationData?.title as LocalizedText) || relationId}
					{@const countMax = programRelation.count_max}

					{@const currentEntries = $formData.metadata.metadata_wikipages.filter(
						(mw) => mw.metadata_wikipage_relation === relationId
					)}
					{@const firstEntry = currentEntries[0]}

					<div class="space-y-2">
						<!-- Wikipage Selection -->
						<Label>
							{relationTitle}
							<span class="text-destructive ml-1">*</span>
						</Label>
						{#if countMax === 1}
							<!-- Single selection -->
							<WikipagePicker
								value={firstEntry?.wikipage_id || ''}
								onValueChange={(val) => {
									formData.update(($form) => {
										// Remove existing entries for this relation
										const otherEntries = $form.metadata.metadata_wikipages.filter(
											(mw) => mw.metadata_wikipage_relation !== relationId
										);
										// Add new entry if value is not empty
										const newEntries = val
											? [
													{
														metadata_wikipage_relation: relationId!,
														wikipage_id: val
													}
												]
											: [];
										return {
											...$form,
											metadata: {
												...$form.metadata,
												metadata_wikipages: [...otherEntries, ...newEntries]
											}
										};
									});
								}}
								{brandId}
								supportedKinds={relationData?.supported_wikipage_kinds || []}
								placeholder="Search and select"
								class="w-full"
							/>
							{#if firstEntry}
								<input
									type="hidden"
									name="metadata.metadata_wikipages[{programIndex}].metadata_wikipage_relation"
									value={relationId}
								/>
								<input
									type="hidden"
									name="metadata.metadata_wikipages[{programIndex}].wikipage_id"
									value={firstEntry.wikipage_id}
								/>
							{/if}
						{:else}
							<!-- Multiple selection -->
							{@const currentWikipageIds = currentEntries
								.map((mw) => mw.wikipage_id)
								.filter((id): id is string => id !== '' && id != null)}
							<WikipagePickerMulti
								value={currentWikipageIds}
								onValueChange={(values) => {
									// Update all entries for this relation
									formData.update(($form) => {
										// Remove existing entries for this relation
										const otherEntries = $form.metadata.metadata_wikipages.filter(
											(mw) => mw.metadata_wikipage_relation !== relationId
										);
										// Add new entries for selected wikipages
										const newEntries = values.map((wikipageId) => ({
											metadata_wikipage_relation: relationId!,
											wikipage_id: wikipageId
										}));
										return {
											...$form,
											metadata: {
												...$form.metadata,
												metadata_wikipages: [...otherEntries, ...newEntries]
											}
										};
									});
								}}
								{brandId}
								supportedKinds={relationData?.supported_wikipage_kinds || []}
								placeholder="Search and select"
								maxItems={countMax || undefined}
								class="w-full"
							/>
							{#each currentWikipageIds as wikipageId, wpIndex}
								<input
									type="hidden"
									name="metadata.metadata_wikipages[{programIndex * 10 +
										wpIndex}].metadata_wikipage_relation"
									value={relationId}
								/>
								<input
									type="hidden"
									name="metadata.metadata_wikipages[{programIndex * 10 + wpIndex}].wikipage_id"
									value={wikipageId}
								/>
							{/each}
						{/if}
					</div>
				{/each}

				<!-- Optional Relations (shown if added by user) -->
				{#each optionalRelations as programRelation, programIndex}
					{@const relationId = programRelation.metadata_wikipage_relation?.id}
					{@const relationData = programRelation.metadata_wikipage_relation}
					{@const relationTitle =
						getLocalizedText(relationData?.title as LocalizedText) || relationId}
					{@const countMax = programRelation.count_max}

					{@const currentEntries = $formData.metadata.metadata_wikipages.filter(
						(mw) => mw.metadata_wikipage_relation === relationId
					)}
					{@const hasEntry = currentEntries.length > 0}
					{@const firstEntry = currentEntries[0]}

					{#if hasEntry}
						<div class="space-y-2">
							<!-- Relation Type (with remove button) -->
							<div class="space-y-2">
								<div class="flex items-center justify-between">
									<Label>
										Type<span class="text-destructive ml-1">*</span>
									</Label>
									<Button
										type="button"
										size="icon"
										variant="ghost"
										class="h-6 w-6"
										onclick={() => {
											// Remove all entries for this relation
											formData.update(($form) => ({
												...$form,
												metadata: {
													...$form.metadata,
													metadata_wikipages: $form.metadata.metadata_wikipages.filter(
														(mw) => mw.metadata_wikipage_relation !== relationId
													)
												}
											}));
										}}
									>
										<Trash2 class="h-3 w-3" />
									</Button>
								</div>
								<div class="bg-muted/50 rounded border px-3 py-2 text-sm">
									{relationTitle}
								</div>
							</div>

							<!-- Wikipage Selection -->
							<Label>
								{relationTitle}
							</Label>
							{#if countMax === 1}
								<!-- Single selection -->
								<WikipagePicker
									value={firstEntry?.wikipage_id || ''}
									onValueChange={(val) => {
										formData.update(($form) => {
											// Remove existing entries for this relation
											const otherEntries = $form.metadata.metadata_wikipages.filter(
												(mw) => mw.metadata_wikipage_relation !== relationId
											);
											// Add new entry if value is not empty
											const newEntries = val
												? [
														{
															metadata_wikipage_relation: relationId!,
															wikipage_id: val
														}
													]
												: [];
											return {
												...$form,
												metadata: {
													...$form.metadata,
													metadata_wikipages: [...otherEntries, ...newEntries]
												}
											};
										});
									}}
									{brandId}
									supportedKinds={relationData?.supported_wikipage_kinds || []}
									placeholder="Search and select"
									class="w-full"
								/>
								{#if firstEntry}
									<input
										type="hidden"
										name="metadata.metadata_wikipages[{requiredRelations.length +
											programIndex}].metadata_wikipage_relation"
										value={relationId}
									/>
									<input
										type="hidden"
										name="metadata.metadata_wikipages[{requiredRelations.length +
											programIndex}].wikipage_id"
										value={firstEntry.wikipage_id}
									/>
								{/if}
							{:else}
								<!-- Multiple selection -->
								{@const currentWikipageIds = currentEntries
									.map((mw) => mw.wikipage_id)
									.filter((id): id is string => id !== '' && id != null)}
								<WikipagePickerMulti
									value={currentWikipageIds}
									onValueChange={(values) => {
										// Update all entries for this relation
										formData.update(($form) => {
											// Remove existing entries for this relation
											const otherEntries = $form.metadata.metadata_wikipages.filter(
												(mw) => mw.metadata_wikipage_relation !== relationId
											);
											// Add new entries for selected wikipages
											const newEntries = values.map((wikipageId) => ({
												metadata_wikipage_relation: relationId!,
												wikipage_id: wikipageId
											}));
											return {
												...$form,
												metadata: {
													...$form.metadata,
													metadata_wikipages: [...otherEntries, ...newEntries]
												}
											};
										});
									}}
									{brandId}
									supportedKinds={relationData?.supported_wikipage_kinds || []}
									placeholder="Search and select"
									maxItems={countMax || undefined}
									class="w-full"
								/>
								{#each currentWikipageIds as wikipageId, wpIndex}
									<input
										type="hidden"
										name="metadata.metadata_wikipages[{(requiredRelations.length + programIndex) *
											10 +
											wpIndex}].metadata_wikipage_relation"
										value={relationId}
									/>
									<input
										type="hidden"
										name="metadata.metadata_wikipages[{(requiredRelations.length + programIndex) *
											10 +
											wpIndex}].wikipage_id"
										value={wikipageId}
									/>
								{/each}
							{/if}
						</div>
					{/if}
				{/each}

				{#if availableRelations.length > 0}
					<div class="flex flex-col gap-2">
						<Label>Add Optional Relationship</Label>
						<Combobox
							value=""
							onValueChange={(val) => {
								if (val) {
									// Add an empty entry for the selected relation
									formData.update(($form) => ({
										...$form,
										metadata: {
											...$form.metadata,
											metadata_wikipages: [
												...$form.metadata.metadata_wikipages,
												{
													metadata_wikipage_relation: val,
													wikipage_id: ''
												}
											]
										}
									}));
								}
							}}
							options={availableRelations}
							placeholder="Select type to add"
							class="w-full"
						/>
					</div>
				{/if}
			</div>
		{/if}

		<!-- Subtitle and Subtitle Link on same row on desktop -->
		<div class="grid gap-4 sm:grid-cols-2">
			<LocalizedTextControl {form} name="metadata.metadata_subtitle" label="Subtitle" />

			<div class="flex flex-col justify-end">
				<div class="space-y-2">
					<Label>Subtitle Link</Label>
					<Input
						type="url"
						placeholder="https://example.com"
						bind:value={$formData.metadata.metadata_promo_webpage_url}
						name="metadata.metadata_promo_webpage_url"
					/>
				</div>
			</div>
		</div>

		<!-- Description -->
		<LocalizedTextControl
			{form}
			name="metadata.metadata_promo_message"
			label="Description"
			multiline
		/>

		<!-- Product and Event Kind in a grid -->
		<div class="grid gap-4 sm:grid-cols-2">
			<!-- Product Kind -->
			{#if !program.product_kind_locked}
				<div class="space-y-2">
					<Label>Product Kind<span class="text-destructive ml-1">*</span></Label>
					<Select.Root
						type="single"
						value={currentProductKind}
						onValueChange={(val) => updateProductKind(val || '')}
					>
						<Select.Trigger class="w-full">
							{productKinds.find((k) => k.id === currentProductKind)?.id || 'Select product kind'}
						</Select.Trigger>
						<Select.Content>
							{#each productKinds as kind}
								<Select.Item value={kind.id}>{kind.id}</Select.Item>
							{/each}
						</Select.Content>
					</Select.Root>
				</div>
			{:else}
				<input
					type="hidden"
					name="metadata.metadata_custom_attribute.product_kind"
					value={currentProductKind}
				/>
			{/if}

			<!-- Event Kind -->
			{#if !program.event_kind_locked}
				<div class="space-y-2 {program.product_kind_locked ? 'sm:col-span-2' : ''}">
					<Label>Event Kind</Label>
					<Select.Root
						type="single"
						value={currentEventKind || ''}
						onValueChange={(val) => updateEventKind(val === '' ? null : val)}
					>
						<Select.Trigger class="w-full">
							{eventKinds.find((k) => k.id === currentEventKind)?.id || 'No event kind'}
						</Select.Trigger>
						<Select.Content>
							<Select.Item value="">No event kind</Select.Item>
							{#each eventKinds as kind}
								<Select.Item value={kind.id}>{kind.id}</Select.Item>
							{/each}
						</Select.Content>
					</Select.Root>
				</div>
			{:else}
				<input
					type="hidden"
					name="metadata.metadata_custom_attribute.event_kind"
					value={currentEventKind || ''}
				/>
			{/if}
		</div>

		<!-- Content Status and Publishing State side by side -->
		<div class="grid gap-4 sm:grid-cols-2">
			<!-- Content Status -->
			<div class="space-y-2">
				<Label>Content Status<span class="text-destructive ml-1">*</span></Label>
				<Select.Root
					type="single"
					value={$formData.metadata.metadata_kind}
					onValueChange={(val) => {
						$formData.metadata.metadata_kind = val as 'complete' | 'placeholder';
					}}
				>
					<Select.Trigger class="w-full">
						{$formData.metadata.metadata_kind === 'complete' ? 'Complete' : 'Placeholder'}
					</Select.Trigger>
					<Select.Content>
						<Select.Item value="complete">Complete</Select.Item>
						<Select.Item value="placeholder">Placeholder</Select.Item>
					</Select.Content>
				</Select.Root>
				<input
					type="hidden"
					name="metadata.metadata_kind"
					value={$formData.metadata.metadata_kind}
				/>
				<p class="text-muted-foreground text-xs">
					{#if $formData.metadata.metadata_kind === 'complete'}
						Content is finalized and ready for customers
					{:else}
						Content is temporary and subject to change
					{/if}
				</p>
			</div>

			<!-- Publishing State -->
			<div class="space-y-2">
				<Label>Publishing State<span class="text-destructive ml-1">*</span></Label>
				<PublishingStatePicker
					value={$formData.metadata.publishing_state}
					onValueChange={(val) => {
						$formData.metadata.publishing_state = val as
							| 'draft'
							| 'hidden'
							| 'published'
							| 'ready_for_review';
					}}
					required={true}
					class="w-full"
				/>
				<input
					type="hidden"
					name="metadata.publishing_state"
					value={$formData.metadata.publishing_state}
				/>
				<p class="text-muted-foreground text-xs">Applies to all events and products</p>
			</div>
		</div>
	</div>
</div>
