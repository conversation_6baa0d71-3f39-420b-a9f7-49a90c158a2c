import type { PageServerLoad, Actions } from './$types';
import { error, fail, redirect } from '@sveltejs/kit';
import { superValidate, message, setError } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { productCreationSchema, type ProductCreationFormData } from './schema';
import type { Database } from '$lib/supabase/database.types';

export const load = (async ({ url, locals: { supabase, brand, user } }) => {
	// Check if user has permission to create products
	if (!user) {
		throw error(401, 'Unauthorized');
	}

	// Get params from URL
	const programId = url.searchParams.get('program');
	const requestId = url.searchParams.get('rid'); // request ID for edit/duplicate
	const mode = url.searchParams.get('mode') || 'create'; // create, edit, or duplicate

	// Load existing request data if editing or duplicating
	let existingFormData: ProductCreationFormData | null = null;
	let isEditing = false;
	let isDuplicating = false;

	if (requestId) {
		const { data: changeRequest, error: requestError } = await supabase
			.from('change_request')
			.select('*')
			.eq('id', requestId)
			.single();

		if (requestError || !changeRequest) {
			throw error(404, 'Change request not found');
		}

		// Check permissions
		if (changeRequest.brand_id !== brand.id) {
			throw error(403, 'Access denied');
		}

		// Extract form data
		existingFormData = changeRequest.form_data as ProductCreationFormData;
		isEditing = mode === 'edit';
		isDuplicating = mode === 'duplicate';

		// For duplication, generate new IDs
		if (isDuplicating && existingFormData) {
			// Generate new metadata ID
			const newMetadataId = crypto.randomUUID();
			existingFormData.metadata.metadata_id = newMetadataId;

			// Generate new product IDs
			if (existingFormData.products) {
				existingFormData.products = existingFormData.products.map((product) => ({
					...product,
					product_id: crypto.randomUUID(),
					product_prices: product.product_prices.map((price) => ({
						...price,
						product_price_id: crypto.randomUUID()
					}))
				}));
			}

			// Generate new event IDs
			if (existingFormData.events) {
				existingFormData.events = existingFormData.events.map((event) => ({
					...event,
					event_id: crypto.randomUUID()
				}));
			}

			// Update event-product relationships with new IDs
			if (existingFormData.event_products) {
				existingFormData.event_products = existingFormData.event_products.map((ep) => ({
					...ep,
					event_product_id: crypto.randomUUID()
				}));
			}
		}
	}

	// Fetch available programs for the brand
	const { data: programs, error: programsError } = await supabase
		.from('program')
		.select(
			`
			*,
			product_kind (*),
			event_kind (*),
			program_price (
				*,
				price (*)
			),
			program_metadata_wikipage (
				*,
				metadata_wikipage_relation (*)
			)
		`
		)
		.eq('brand_id', brand.id)
		.order('semantic_order', { ascending: true })
		.order('created_at', { ascending: false });

	if (programsError) {
		console.error('Error loading programs:', programsError);
		throw error(500, 'Failed to load programs');
	}

	// Determine selected program
	let selectedProgram = null;

	// If we have existing form data, use the program from there
	if (existingFormData?.program_id && programs) {
		selectedProgram = programs.find((p) => p.id === existingFormData.program_id);
	}
	// Otherwise, check if program ID is provided in URL
	else if (programId && programs) {
		selectedProgram = programs.find((p) => p.id === programId);
		if (!selectedProgram) {
			throw error(404, 'Program not found');
		}
	}
	// Default to first program if no specific program requested
	else if (!programId && programs && programs.length > 0) {
		selectedProgram = programs[0];
	}

	// Load reference data needed for the form
	const [
		{ data: productKinds },
		{ data: eventKinds },
		{ data: prices },
		{ data: metadataRelations }
	] = await Promise.all([
		supabase.from('product_kind').select('*').order('id'),
		supabase.from('event_kind').select('*').order('id'),
		supabase
			.from('price')
			.select('*')
			.eq('brand_id', brand.id)
			.order('created_at', { ascending: false }),
		supabase.from('metadata_wikipage_relation').select('*').order('id')
	]);

	// Initialize form data
	let formData;

	if (existingFormData) {
		// Use existing form data for editing or duplication
		formData = existingFormData;
	} else if (selectedProgram) {
		// Generate metadata ID upfront for new offerings
		const metadataId = crypto.randomUUID();

		// Create new form data with program defaults
		formData = {
			program_id: selectedProgram.id,
			metadata: {
				metadata_id: metadataId,
				metadata_kind: 'complete',
				metadata_title: {},
				metadata_subtitle: {},
				metadata_promo_message: {},
				metadata_promo_webpage_url: '',
				metadata_custom_attribute: {
					program_id: selectedProgram.id,
					product_kind: selectedProgram.product_kind?.id || '',
					event_kind: selectedProgram.event_kind?.id || null
				},
				metadata_wikipages: selectedProgram.program_metadata_wikipage
					.filter((pmw: any) => pmw.is_required)
					.map((pmw: any) => ({
						metadata_wikipage_relation:
							typeof pmw.metadata_wikipage_relation === 'string'
								? pmw.metadata_wikipage_relation
								: pmw.metadata_wikipage_relation?.id || pmw.metadata_wikipage_relation,
						wikipage_id: ''
					})),
				metadata_tracks: [],
				publishing_state: 'ready_for_review' as const
			},
			events: [],
			products: [],
			event_products: []
		};
	} else {
		formData = undefined;
	}

	// Initialize form with the data
	const form = await superValidate(formData, zod(productCreationSchema));

	console.log('Form validation result:', {
		valid: form.valid,
		errors: form.errors,
		data_metadata_id: form.data.metadata?.metadata_id
	});

	return {
		form,
		programs: programs ?? [],
		selectedProgram,
		productKinds: productKinds ?? [],
		eventKinds: eventKinds ?? [],
		prices: prices ?? [],
		metadataRelations: metadataRelations ?? [],
		existingFormData,
		isEditing,
		isDuplicating,
		requestId
	};
}) satisfies PageServerLoad;

export const actions = {
	default: async ({ request, url, locals: { supabase, brand, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const form = await superValidate(request, zod(productCreationSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Get params for editing
		const requestId = url.searchParams.get('rid');
		const mode = url.searchParams.get('mode') || 'create';
		const isEditing = mode === 'edit' && requestId;

		// Use the metadata ID from form data for new offerings
		const metadataId = form.data.metadata.metadata_id;

		// Prepare metadata input (single metadata for the offering)
		const metadataInputs = [
			{
				...form.data.metadata,
				metadata_id: metadataId
			}
		];

		// Prepare events input - assign event_metadata_id to all events
		const eventInputs = form.data.events.map((event) => ({
			...event,
			event_id: event.event_id || crypto.randomUUID(),
			event_metadata_id: metadataId, // Use event_metadata_id instead of metadata_id
			event_publishing_state: form.data.metadata.publishing_state
		}));

		// Prepare products input - assign product_metadata_id and generate IDs
		const productInputs = form.data.products.map((product) => {
			const productId = product.product_id || crypto.randomUUID();

			// If product has custom title, add it to metadata_custom_attribute
			const customAttribute = { ...form.data.metadata.metadata_custom_attribute };
			if (product.custom_title && Object.keys(product.custom_title).length > 0) {
				customAttribute.product_custom_titles = customAttribute.product_custom_titles || {};
				customAttribute.product_custom_titles[productId] = product.custom_title;
			}

			return {
				...product,
				product_id: productId,
				product_metadata_id: metadataId, // Use product_metadata_id instead of metadata_id
				product_publishing_state: form.data.metadata.publishing_state,
				product_close_to_buy_at: product.product_close_to_buy_at || null,
				product_open_to_buy_at: product.product_open_to_buy_at || new Date().toISOString(),
				product_stock: product.product_stock || 100,
				product_prices: product.product_prices.map((price) => ({
					...price,
					product_price_id: price.product_price_id || crypto.randomUUID(),
					product_price_start_at: price.product_price_start_at || new Date().toISOString(),
					product_price_end_at: price.product_price_end_at || null,
					product_price_stock: price.product_price_stock || null
				}))
			};
		});

		// Update metadata with any custom attributes from products
		if (productInputs.some((p) => productInputs.find((pi) => pi.product_id === p.product_id))) {
			const customAttribute = { ...form.data.metadata.metadata_custom_attribute };
			productInputs.forEach((product) => {
				const originalProduct = form.data.products.find(
					(p) => p.product_id === product.product_id || !p.product_id
				);
				if (originalProduct?.custom_title && Object.keys(originalProduct.custom_title).length > 0) {
					customAttribute.product_custom_titles = customAttribute.product_custom_titles || {};
					customAttribute.product_custom_titles[product.product_id!] = originalProduct.custom_title;
				}
			});
			metadataInputs[0].metadata_custom_attribute = customAttribute;
		}

		// Prepare event-product relationships
		const eventProductInputs = form.data.event_products.map((ep) => ({
			...ep,
			event_product_id: ep.event_product_id || crypto.randomUUID()
		}));

		// Create change request input
		const changeRequestInput = {
			change_request_id: isEditing ? requestId : null,
			change_request_actor_role_id: 'creator',
			change_request_actor_profile_id: user.id,
			change_request_brand_id: brand.id,
			change_request_change_reason_id: null, // Always null for creation requests
			change_request_change_reason_message: form.data.notes || null,
			change_request_status: 'ready_for_review',
			change_request_form_data: form.data
		};

		try {
			// Call the RPC function
			const { data, error: rpcError } = await supabase.rpc('client_upsert_offering', {
				upsert_offering_input_all_event_product: eventProductInputs,
				upsert_offering_input_all_product: productInputs,
				upsert_offering_input_all_event: eventInputs,
				upsert_offering_input_all_metadata: metadataInputs,
				upsert_offering_input_change_request: changeRequestInput,
				upsert_offering_input_perform_action: true
			});

			if (rpcError) {
				console.error('RPC Error:', rpcError);

				// Parse the error message to build comprehensive error message
				const errorMessage = rpcError.message || 'Failed to create product';
				const errorDetails = rpcError.details;
				const errorHint = rpcError.hint;

				// Build a comprehensive error message
				let fullErrorMessage = errorMessage;
				if (errorDetails) {
					fullErrorMessage += ` (${errorDetails})`;
				}
				if (errorHint) {
					fullErrorMessage += ` Hint: ${errorHint}`;
				}

				// For RPC errors, return form with error message
				return message(form, fullErrorMessage, { status: 400 });
			}

			console.log(
				isEditing ? 'Product updated successfully:' : 'Product created successfully:',
				data
			);
		} catch (err) {
			console.error('Unexpected error during RPC call:', err);
			return message(form, 'An unexpected error occurred while processing the request', {
				status: 500
			});
		}

		// Success - redirect to request page (this should be outside the try-catch)
		redirect(303, '/private/request');
	}
} satisfies Actions;
