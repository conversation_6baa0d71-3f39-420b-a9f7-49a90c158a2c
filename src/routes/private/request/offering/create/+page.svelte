<script lang="ts">
	import { dev } from '$app/environment';
	import { PageContainer } from '$lib/components/layout';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import * as Select from '$lib/components/ui/select';
	import { ArrowLeft, Save, Package, Clock, DollarSign, FileText } from '@lucide/svelte';
	import { goto } from '$app/navigation';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { PageData } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import SuperDebug from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { productCreationSchema } from './schema';
	import BasicInfoSection from './components/BasicInfoSection.svelte';
	import ScheduleSection from './components/ScheduleSection.svelte';
	import PricingSection from './components/ProductSection.svelte';
	import type { Database } from '$lib/supabase/database.types';
	import { Textarea } from '$lib/components/ui/textarea';
	import FormSectionHeader from '$lib/components/shared/FormSectionHeader.svelte';
	import UnifiedErrorDisplay from './components/UnifiedErrorDisplay.svelte';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	// Type for Program with relations
	type Program = Database['public']['Tables']['program']['Row'] & {
		product_kind: Database['public']['Tables']['product_kind']['Row'] | null;
		event_kind: Database['public']['Tables']['event_kind']['Row'] | null;
		program_price: Array<
			Database['public']['Tables']['program_price']['Row'] & {
				price: Database['public']['Tables']['price']['Row'] | null;
			}
		>;
		program_metadata_wikipage: Array<
			Database['public']['Tables']['program_metadata_wikipage']['Row'] & {
				metadata_wikipage_relation:
					| Database['public']['Tables']['metadata_wikipage_relation']['Row']
					| null;
			}
		>;
	};

	// Form setup
	const superFormInstance = superForm(data.form, {
		validators: zodClient(productCreationSchema),
		dataType: 'json',
		resetForm: false,
		onError({ result }) {
			// Handle server errors - just log them, the unified error display will show them
			console.error('Form submission error:', result);
		},
		onUpdated({ form }) {
			// Handle successful submission - redirect to request page
			if (form.valid && !form.message) {
				goto('/private/request');
			}
		}
	});

	const { form, errors, enhance, submitting, message } = superFormInstance;

	// Debug: Log initial form data
	$effect(() => {
		console.log('Form data loaded/changed:', {
			metadata_id: $form.metadata?.metadata_id,
			metadata: $form.metadata,
			events_count: $form.events?.length || 0,
			products_count: $form.products?.length || 0,
			event_products_count: $form.event_products?.length || 0
		});
	});

	// State
	let selectedProgram = $state<Program | null>(data.selectedProgram);
	let hasValidationErrors = $state(false);

	// Handle program selection
	function handleProgramSelect(programId: string) {
		const program = data.programs.find((p) => p.id === programId);
		if (!program) return;

		selectedProgram = program;

		// Update form with program defaults
		form.update(($form) => ({
			...$form,
			program_id: program.id,
			metadata: {
				...$form.metadata,
				metadata_custom_attribute: {
					...$form.metadata.metadata_custom_attribute,
					program_id: program.id,
					product_kind: program.product_kind?.id || '',
					event_kind: program.event_kind?.id || null
				},
				metadata_wikipages: [
					// Add all required relationships from program
					...program.program_metadata_wikipage
						.filter((pmw: any) => pmw.is_required)
						.map((pmw: any) => ({
							metadata_wikipage_relation:
								typeof pmw.metadata_wikipage_relation === 'string'
									? pmw.metadata_wikipage_relation
									: pmw.metadata_wikipage_relation?.id || pmw.metadata_wikipage_relation,
							wikipage_id: ''
						})),
					// Keep any existing optional relationships
					...$form.metadata.metadata_wikipages.filter((mw) => {
						// Check if this relation is not a required one from the program
						return !program.program_metadata_wikipage.some((pmw: any) => {
							const pmwRelation =
								typeof pmw.metadata_wikipage_relation === 'string'
									? pmw.metadata_wikipage_relation
									: pmw.metadata_wikipage_relation?.id;
							return pmw.is_required && pmwRelation === mw.metadata_wikipage_relation;
						});
					})
				]
			}
		}));

		// Update URL with program parameter, preserving edit/duplicate mode
		const params = new URLSearchParams();
		params.set('program', program.id);
		if (data.requestId) params.set('rid', data.requestId);
		if (data.isEditing) params.set('mode', 'edit');
		else if (data.isDuplicating) params.set('mode', 'duplicate');

		goto(`/private/request/offering/create?${params.toString()}`, { replaceState: true });
	}

	// Handle form submission
	function handleSubmit(event: Event) {
		// If there are errors, prevent submission
		if (hasValidationErrors) {
			event.preventDefault();
			return false;
		}

		// Let the form submit naturally
		return true;
	}
</script>

{#snippet actions()}
	<div class="flex items-center gap-2">
		<Button variant="ghost" onclick={() => goto('/private/request')}>
			<ArrowLeft class="mr-2 h-4 w-4" />
			Back
		</Button>
	</div>
{/snippet}

{#snippet content()}
	<div class="space-y-6">
		<!-- Program Selection -->
		<div class="space-y-2">
			<Select.Root
				type="single"
				value={selectedProgram?.id}
				onValueChange={(value: string | undefined) => {
					if (value) handleProgramSelect(value);
				}}
			>
				<Select.Trigger class="w-full sm:max-w-md">
					{selectedProgram
						? getLocalizedText(selectedProgram.title as LocalizedText)
						: 'Select a Studio Class Program'}
				</Select.Trigger>
				<Select.Content>
					{#each data.programs as program}
						<Select.Item value={program.id}>
							<div class="flex items-center gap-2">
								<Package class="h-4 w-4" />
								<span>{getLocalizedText(program.title as LocalizedText)}</span>
								{#if program.product_kind}
									<Badge variant="secondary" class="ml-2 text-xs">{program.product_kind.id}</Badge>
								{/if}
							</div>
						</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>

		{#if selectedProgram}
			<form method="POST" use:enhance class="space-y-6">
				<input type="hidden" name="program_id" value={selectedProgram.id} />

				<!-- Basic Information -->
				<div id="basic-info-section">
					<FormSectionHeader icon={Package} title="Basic Information" />
					<BasicInfoSection
						form={superFormInstance}
						program={selectedProgram}
						productKinds={data.productKinds}
						eventKinds={data.eventKinds}
						metadataRelations={data.metadataRelations}
						brandId={data.brand.id}
					/>
				</div>

				<!-- Schedule -->
				<div id="schedule-section">
					<FormSectionHeader icon={Clock} title="Schedule" />
					<ScheduleSection
						form={superFormInstance}
						program={selectedProgram}
						supabase={data.supabase}
						brand={data.brand}
					/>
				</div>

				<!-- Products & Relationships -->
				<div id="pricing-section">
					<FormSectionHeader icon={DollarSign} title="Product Configuration" />
					<PricingSection
						form={superFormInstance}
						program={selectedProgram}
						prices={data.prices}
						supabase={data.supabase}
						brandId={data.brand.id}
					/>
				</div>

				<!-- Internal Notes -->
				<div>
					<FormSectionHeader
						icon={FileText}
						title={data.isEditing ? 'Additional Notes' : 'Internal Notes'}
					/>
					<Textarea
						name="notes"
						bind:value={$form.notes}
						class="min-h-[80px] resize-none sm:min-h-[100px]"
						placeholder={data.isEditing
							? 'Any additional information about this change...'
							: 'Any additional information or notes about this product creation...'}
					/>
				</div>

				<!-- Unified Error Display -->
				<UnifiedErrorDisplay
					form={superFormInstance}
					program={selectedProgram}
					bind:hasValidationErrors
				/>

				<!-- Form Actions -->
				<div class="flex flex-col gap-3 pt-6 sm:flex-row sm:justify-between">
					<Button
						type="button"
						variant="outline"
						onclick={() => goto('/private/request')}
						disabled={$submitting}
					>
						Cancel
					</Button>

					<div class="flex gap-3">
						<Button
							type="submit"
							disabled={$submitting || hasValidationErrors}
							onclick={handleSubmit}
						>
							{#if $submitting}
								<div class="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"></div>
								{data.isEditing ? 'Updating...' : 'Creating...'}
							{:else}
								<Save class="mr-2 h-4 w-4" />
								{data.isEditing
									? 'Update Request'
									: data.isDuplicating
										? 'Create Duplicate'
										: 'Create Product'}
							{/if}
						</Button>
					</div>
				</div>
			</form>
		{:else}
			<div class="bg-muted/50 rounded-lg p-8 text-center">
				<Package class="text-muted-foreground mx-auto mb-4 h-12 w-12" />
				<p class="text-muted-foreground">Select a program to begin creating your product</p>
			</div>
		{/if}

		<!-- SuperDebug for development -->
		<SuperDebug data={$form} label="Form Data" collapsible display={dev} />
	</div>
{/snippet}

<PageContainer
	title={data.isEditing
		? 'Edit Product Request'
		: data.isDuplicating
			? 'Duplicate Product'
			: 'Create New Product'}
	description={data.isEditing
		? 'Update the product configuration and schedule'
		: 'Configure product details and schedule for your offering'}
	{actions}
	{content}
/>
