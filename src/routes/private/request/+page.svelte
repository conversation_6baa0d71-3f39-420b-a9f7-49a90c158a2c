<!-- src/routes/private/request/+page.svelte -->
<script lang="ts">
	import { PageContainer } from '$lib/components/layout';
	import type { PageData } from '../request/$types';
	import type { Database } from '$lib/supabase/database.types';
	import ChangeRequestList from './ChangeRequestList.svelte';
	import ChangeRequestDetail from './ChangeRequestDetail.svelte';
	import { Separator } from '$lib/components/ui/separator';
	import { Button } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { PlusCircle, ChevronDown } from '@lucide/svelte';
	import { goto } from '$app/navigation';
	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	// Types
	type ChangeRequestRow = Database['public']['Tables']['change_request']['Row'];
	type LocalizedText = {
		[key: string]: string;
	};

	// State
	let loading = $state(false);
	let selectedRequestId = $state('');
	let currentLocale = $state('en');
	let changeRequests = $state<ChangeRequestRow[]>([]);
	let requestDetails = $state<Map<string, any>>(new Map());

	// Derived values
	let selectedRequest = $derived(
		changeRequests.find((request) => request.id === selectedRequestId) as
			| ChangeRequestRow
			| undefined
	);

	// Functions
	function handleRequestChange(requestId: string): void {
		selectedRequestId = requestId;
	}

	// Initialize data loading
	async function loadChangeRequests() {
		loading = true;
		try {
			const { data: requests, error } = await data.supabase
				.from('change_request')
				.select('*')
				.eq('brand_id', data.brand.id) // Filter by current brand
				.order('updated_at', { ascending: false });

			if (error) throw error;
			changeRequests = requests;

			// Load additional details for each request
			const detailsMap = new Map();

			// Load all related data in parallel
			const [{ data: creationMetadata }, { data: changeProducts }, { data: changeEvents }] =
				await Promise.all([
					data.supabase
						.from('metadata')
						.select('*')
						.in(
							'creation_request_id',
							requests.map((r) => r.id)
						),
					data.supabase
						.from('change_product')
						.select('*, product:apply_to_product_id!inner(*, metadata:metadata_id(title))')
						.in(
							'change_request_id',
							requests.map((r) => r.id)
						),
					data.supabase
						.from('change_event')
						.select('*, event:apply_to_event_id!inner(*, metadata:metadata_id(title))')
						.in(
							'change_request_id',
							requests.map((r) => r.id)
						)
				]);

			// Map metadata to requests
			creationMetadata?.forEach((metadata) => {
				detailsMap.set(metadata.creation_request_id, { metadata });
			});

			// Map product changes to requests
			changeProducts?.forEach((cp) => {
				if (cp.product) {
					detailsMap.set(cp.change_request_id, {
						product: {
							...cp.product,
							title: cp.product.metadata?.title
						}
					});
				}
			});

			// Map event changes to requests
			changeEvents?.forEach((ce) => {
				if (ce.event) {
					detailsMap.set(ce.change_request_id, {
						event: {
							...ce.event,
							title: ce.event.metadata?.title
						}
					});
				}
			});

			requestDetails = detailsMap;

			if (requests.length > 0 && !selectedRequestId) {
				selectedRequestId = requests[0].id;
			}
		} catch (error) {
			console.error('Error loading change requests:', error);
		} finally {
			loading = false;
		}
	}

	// Load data on mount
	$effect(() => {
		loadChangeRequests();
	});
</script>

{#snippet actions()}
	<div class="flex items-center gap-2">
		<DropdownMenu.Root>
			<DropdownMenu.Trigger>
				<Button variant="default" class="gap-1">
					<PlusCircle class="size-4" />
					New Change Request
					<ChevronDown class="size-4 opacity-70" />
				</Button>
			</DropdownMenu.Trigger>
			<DropdownMenu.Content align="end" class="w-56">
				<DropdownMenu.Group>
					<DropdownMenu.Label>Product & Event Changes</DropdownMenu.Label>
					<DropdownMenu.Item onclick={() => goto('/private/request/offering/create')}>
						Create New Product
					</DropdownMenu.Item>
					<DropdownMenu.Item onclick={() => goto('/private/request/change-offering')}>
						Change Existing Offering
					</DropdownMenu.Item>
					<DropdownMenu.Item onclick={() => goto('/private/request/product-cancel')}>
						Cancel Event
					</DropdownMenu.Item>
				</DropdownMenu.Group>
				<DropdownMenu.Separator />
				<DropdownMenu.Group>
					<DropdownMenu.Label>Order Management</DropdownMenu.Label>
					<DropdownMenu.Item onclick={() => goto('/private/request/order/create')}>
						Create New Order
					</DropdownMenu.Item>
					<DropdownMenu.Item onclick={() => goto('/private/request/change-order')}>
						Change Order Details
					</DropdownMenu.Item>
				</DropdownMenu.Group>
			</DropdownMenu.Content>
		</DropdownMenu.Root>
	</div>
{/snippet}

{#snippet content()}
	<div class="space-y-6 md:grid md:grid-cols-12 md:gap-6 md:space-y-0">
		<!-- Change Request List (sidebar) -->
		<div class="md:col-span-4 lg:col-span-4 xl:col-span-3">
			<h3 class="mb-4 text-lg font-medium">Change Requests</h3>
			<ChangeRequestList
				{changeRequests}
				{selectedRequestId}
				{requestDetails}
				onRequestChange={handleRequestChange}
				{loading}
			/>
		</div>

		<div class="hidden md:block">
			<Separator orientation="vertical" />
		</div>

		<!-- Change Request Details -->
		<div class="md:col-span-8 lg:col-span-7 xl:col-span-8">
			{#if selectedRequest}
				<ChangeRequestDetail request={selectedRequest} supabase={data.supabase} />
			{:else if changeRequests.length > 0}
				<div class="flex h-40 items-center justify-center">
					<p class="text-muted-foreground">Select a change request to view details</p>
				</div>
			{:else}
				<div class="flex h-40 items-center justify-center">
					<p class="text-muted-foreground">No change requests available</p>
				</div>
			{/if}
		</div>
	</div>
{/snippet}

<PageContainer
	title="Change Requests"
	description="Manage and review content changes before publishing"
	{actions}
	{content}
/>
