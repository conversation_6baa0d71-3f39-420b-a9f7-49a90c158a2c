<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Card } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { Label } from '$lib/components/ui/label';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select';
	import {
		ShoppingCart,
		DollarSign,
		AlertCircle,
		ChevronRight,
		Loader2,
		Info,
		Package,
		User
	} from '@lucide/svelte';
	import { goto } from '$app/navigation';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { PageData } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { changeOrderSchema } from './schema';
	import { Field, Control, FieldErrors } from 'formsnap';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	// Form setup
	const superFormInstance = superForm(data.form, {
		validators: zodClient(changeOrderSchema),
		dataType: 'json',
		resetForm: false
	});

	const { form, errors, enhance, submitting, message } = superFormInstance;

	// State
	let selectedOrder = $state<any>(null);
	let expandedProducts = $state<Set<string>>(new Set());
	let expandedPrices = $state<Set<string>>(new Set());

	// Watch for order selection
	$effect(() => {
		if ($form.order_id) {
			selectedOrder = data.orders.find((o) => o.id === $form.order_id);

			// Initialize form arrays if order is selected
			if (selectedOrder && !$form.order_product_changes) {
				$form.order_product_changes = [];
			}
			if (selectedOrder && !$form.order_price_changes) {
				$form.order_price_changes = [];
			}
		}
	});

	// Get change reason that requires message
	let selectedChangeReason = $derived(
		data.changeReasons.find((r) => r.id === $form.change_reason_id)
	);

	// Format currency
	function formatCurrency(cents: number): string {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD'
		}).format(cents / 100);
	}

	// Format date
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('en-US', {
			month: 'short',
			day: 'numeric',
			year: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// Toggle product expansion
	function toggleProductExpansion(productId: string) {
		if (expandedProducts.has(productId)) {
			expandedProducts.delete(productId);
		} else {
			expandedProducts.add(productId);
		}
		expandedProducts = new Set(expandedProducts);
	}

	// Toggle price expansion
	function togglePriceExpansion(priceId: string) {
		if (expandedPrices.has(priceId)) {
			expandedPrices.delete(priceId);
		} else {
			expandedPrices.add(priceId);
		}
		expandedPrices = new Set(expandedPrices);
	}

	// Check if product has changes
	function hasProductChanges(orderProductId: string): boolean {
		return ($form.order_product_changes || []).some(
			(change) => change.order_product_id === orderProductId
		);
	}

	// Check if price has changes
	function hasPriceChanges(orderPriceId: string): boolean {
		return ($form.order_price_changes || []).some(
			(change) => change.order_price_id === orderPriceId
		);
	}

	// Get or create product change
	function getOrCreateProductChange(orderProductId: string) {
		let change = ($form.order_product_changes || []).find(
			(c) => c.order_product_id === orderProductId
		);

		if (!change) {
			change = {
				order_product_id: orderProductId,
				override_product_id: undefined,
				override_publishing_state: undefined,
				override_cost_unit: undefined,
				override_purchased_count: undefined,
				override_canceled_at_should_return_unit: undefined
			};
			$form.order_product_changes = [...($form.order_product_changes || []), change];
		}

		return change;
	}

	// Get or create price change
	function getOrCreatePriceChange(orderPriceId: string) {
		let change = ($form.order_price_changes || []).find((c) => c.order_price_id === orderPriceId);

		if (!change) {
			change = {
				order_price_id: orderPriceId,
				override_expire_at: undefined,
				override_unit: undefined,
				override_money_int: undefined
			};
			$form.order_price_changes = [...($form.order_price_changes || []), change];
		}

		return change;
	}
</script>

<div class="container mx-auto max-w-6xl px-4 py-6 sm:px-6 lg:px-8">
	<div class="mb-6 sm:mb-8">
		<div class="mb-2 flex items-center gap-2 text-sm text-muted-foreground">
			<a href="/private/request" class="hover:text-foreground">Change Requests</a>
			<ChevronRight class="h-4 w-4" />
			<span>Change Order</span>
		</div>
		<h1 class="text-2xl font-bold sm:text-3xl">Change Order Details</h1>
		<p class="mt-1 text-muted-foreground">
			Request changes to existing orders, products, or pricing
		</p>
	</div>

	<form method="POST" use:enhance>
		<div class="space-y-6">
			<!-- Step 1: Select Order -->
			<Card>
				<div class="p-6">
					<h2 class="mb-4 text-xl font-semibold">Select Order</h2>

					<Field form={superFormInstance} name="order_id">
						<Control>
							{#snippet children({ props })}
								<Select.Root
									type="single"
									value={$form.order_id}
									onValueChange={(val) => ($form.order_id = val || '')}
									name={props.name}
								>
									<Select.Trigger class={$errors.order_id ? 'border-destructive' : ''}>
										{#if selectedOrder}
											Order #{selectedOrder.id.slice(0, 8)} - {selectedOrder.profile?.full_name ||
												selectedOrder.profile?.username}
										{:else}
											Select an order
										{/if}
									</Select.Trigger>
									<Select.Content>
										{#each data.orders as order}
											<Select.Item value={order.id}>
												<div class="flex items-center justify-between gap-4">
													<div>
														<div class="font-medium">
															Order #{order.id.slice(0, 8)}
														</div>
														<div class="text-xs text-muted-foreground">
															{order.profile?.full_name || order.profile?.username} • {formatDate(
																order.created_at
															)}
														</div>
													</div>
													<Badge variant="outline">
														{order.order_product?.length || 0} items
													</Badge>
												</div>
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Control>
						<FieldErrors class="mt-2 text-sm text-destructive" />
					</Field>
				</div>
			</Card>

			<!-- Step 2: Change Details -->
			{#if selectedOrder}
				<Card>
					<div class="p-6">
						<h2 class="mb-4 text-xl font-semibold">Change Details</h2>

						<div class="space-y-4">
							<!-- Change Reason -->
							<Field form={superFormInstance} name="change_reason_id">
								<Control>
									{#snippet children({ props })}
										<div class="space-y-2">
											<Label>Reason for Change<span class="ml-1 text-destructive">*</span></Label>
											<Select.Root bind:value={$form.change_reason_id} name={props.name}>
												<Select.Trigger
													class={$errors.change_reason_id ? 'border-destructive' : ''}
												>
													{#if selectedChangeReason}
														{getLocalizedText(selectedChangeReason.title as LocalizedText)}
													{:else}
														Select a reason
													{/if}
												</Select.Trigger>
												<Select.Content>
													{#each data.changeReasons as reason}
														<Select.Item value={reason.id}>
															{getLocalizedText(reason.title as LocalizedText)}
															{#if reason.requires_message}
																<span class="ml-1 text-xs text-muted-foreground"
																	>(details required)</span
																>
															{/if}
														</Select.Item>
													{/each}
												</Select.Content>
											</Select.Root>
										</div>
									{/snippet}
								</Control>
								<FieldErrors class="mt-2 text-sm text-destructive" />
							</Field>

							<!-- Change Reason Message -->
							{#if selectedChangeReason?.requires_message}
								<Field form={superFormInstance} name="change_reason_message">
									<Control>
										{#snippet children({ props })}
											<div class="space-y-2">
												<Label>Details<span class="ml-1 text-destructive">*</span></Label>
												<Textarea
													{...props}
													placeholder="Please provide details about the reason for this change..."
													class="min-h-[100px]"
												/>
											</div>
										{/snippet}
									</Control>
									<FieldErrors class="mt-2 text-sm text-destructive" />
								</Field>
							{/if}
						</div>
					</div>
				</Card>

				<!-- Order Summary -->
				<Card>
					<div class="p-6">
						<h2 class="mb-4 text-xl font-semibold">Order Summary</h2>

						<div class="mb-4 grid gap-4 rounded-lg bg-muted/50 p-4 sm:grid-cols-3">
							<div>
								<p class="text-sm text-muted-foreground">Customer</p>
								<p class="font-medium">
									{selectedOrder.profile?.full_name || selectedOrder.profile?.username}
								</p>
							</div>
							<div>
								<p class="text-sm text-muted-foreground">Order Date</p>
								<p class="font-medium">{formatDate(selectedOrder.created_at)}</p>
							</div>
							<div>
								<p class="text-sm text-muted-foreground">Total Items</p>
								<p class="font-medium">{selectedOrder.order_product?.length || 0} products</p>
							</div>
						</div>

						<!-- Order Products -->
						{#if selectedOrder.order_product && selectedOrder.order_product.length > 0}
							<div class="space-y-4">
								<h3 class="font-medium">Order Products</h3>

								{#each selectedOrder.order_product as orderProduct}
									{@const isExpanded = expandedProducts.has(orderProduct.id)}
									{@const hasChanges = hasProductChanges(orderProduct.id)}

									<Card class="overflow-hidden {hasChanges ? 'ring-2 ring-primary' : ''}">
										<div
											class="flex cursor-pointer items-center justify-between p-4"
											onclick={() => toggleProductExpansion(orderProduct.id)}
										>
											<div class="flex items-center gap-4">
												<Package class="h-5 w-5 text-muted-foreground" />
												<div>
													<p class="font-medium">
														{getLocalizedText(
															orderProduct.product?.metadata?.title as LocalizedText
														)}
													</p>
													<p class="text-sm text-muted-foreground">
														{orderProduct.purchased_count} × {orderProduct.cost_unit} units
													</p>
												</div>
											</div>
											<Button variant="ghost" size="sm">
												{isExpanded ? 'Hide' : 'Edit'}
											</Button>
										</div>

										{#if isExpanded}
											{@const changeIndex =
												$form.order_product_changes?.findIndex(
													(c) => c.order_product_id === orderProduct.id
												) ?? -1}
											{@const change = getOrCreateProductChange(orderProduct.id)}
											<div class="border-t p-4">
												<div class="space-y-4">
													<div class="rounded-lg bg-muted/50 p-3">
														<div class="flex items-start gap-2">
															<Info class="mt-0.5 h-4 w-4 text-muted-foreground" />
															<p class="text-sm text-muted-foreground">
																Leave fields empty to keep the current values
															</p>
														</div>
													</div>

													<!-- Publishing State Override -->
													<div class="space-y-2">
														<Label>New Publishing State</Label>
														<Select.Root
															type="single"
															value={change.override_publishing_state || ''}
															onValueChange={(val) => {
																change.override_publishing_state = val === '' ? undefined : val;
																$form = $form;
															}}
														>
															<Select.Trigger>
																{change.override_publishing_state || 'No change'}
															</Select.Trigger>
															<Select.Content>
																<Select.Item value="">No change</Select.Item>
																{#each data.publishingStates as state}
																	<Select.Item value={state.id}>{state.id}</Select.Item>
																{/each}
															</Select.Content>
														</Select.Root>
													</div>

													<!-- Cost Unit Override -->
													<div class="space-y-2">
														<Label>New Cost Units (Current: {orderProduct.cost_unit})</Label>
														<Input
															type="number"
															step="0.01"
															min="0"
															placeholder="No change"
															value={change.override_cost_unit ?? ''}
															oninput={(e) => {
																change.override_cost_unit = e.currentTarget.value
																	? parseFloat(e.currentTarget.value)
																	: undefined;
																$form = $form;
															}}
														/>
													</div>

													<!-- Purchased Count Override -->
													<div class="space-y-2">
														<Label
															>New Purchased Count (Current: {orderProduct.purchased_count})</Label
														>
														<Input
															type="number"
															min="0"
															placeholder="No change"
															value={change.override_purchased_count ?? ''}
															oninput={(e) => {
																change.override_purchased_count = e.currentTarget.value
																	? parseInt(e.currentTarget.value)
																	: undefined;
																$form = $form;
															}}
														/>
													</div>
												</div>
											</div>
										{/if}
									</Card>
								{/each}
							</div>
						{/if}

						<!-- Order Prices -->
						{#if selectedOrder.order_price && selectedOrder.order_price.length > 0}
							<div class="mt-6 space-y-4">
								<h3 class="font-medium">Order Pricing</h3>

								{#each selectedOrder.order_price as orderPrice}
									{@const isExpanded = expandedPrices.has(orderPrice.id)}
									{@const hasChanges = hasPriceChanges(orderPrice.id)}

									<Card class="overflow-hidden {hasChanges ? 'ring-2 ring-primary' : ''}">
										<div
											class="flex cursor-pointer items-center justify-between p-4"
											onclick={() => togglePriceExpansion(orderPrice.id)}
										>
											<div class="flex items-center gap-4">
												<DollarSign class="h-5 w-5 text-muted-foreground" />
												<div>
													<p class="font-medium">
														{orderPrice.price?.id} - {formatCurrency(orderPrice.money_int || 0)}
													</p>
													<p class="text-sm text-muted-foreground">
														{orderPrice.unit} units • Expires: {orderPrice.expire_at
															? formatDate(orderPrice.expire_at)
															: 'Never'}
													</p>
												</div>
											</div>
											<Button variant="ghost" size="sm">
												{isExpanded ? 'Hide' : 'Edit'}
											</Button>
										</div>

										{#if isExpanded}
											{@const changeIndex =
												$form.order_price_changes?.findIndex(
													(c) => c.order_price_id === orderPrice.id
												) ?? -1}
											{@const change = getOrCreatePriceChange(orderPrice.id)}
											<div class="border-t p-4">
												<div class="space-y-4">
													<!-- Unit Override -->
													<div class="space-y-2">
														<Label>New Units (Current: {orderPrice.unit})</Label>
														<Input
															type="number"
															step="0.01"
															min="0"
															placeholder="No change"
															value={change.override_unit ?? ''}
															oninput={(e) => {
																change.override_unit = e.currentTarget.value
																	? parseFloat(e.currentTarget.value)
																	: undefined;
																$form = $form;
															}}
														/>
													</div>

													<!-- Money Override -->
													<div class="space-y-2">
														<Label>New Amount in Cents (Current: {orderPrice.money_int})</Label>
														<Input
															type="number"
															min="0"
															placeholder="No change"
															value={change.override_money_int ?? ''}
															oninput={(e) => {
																change.override_money_int = e.currentTarget.value
																	? parseInt(e.currentTarget.value)
																	: undefined;
																$form = $form;
															}}
														/>
													</div>

													<!-- Expire At Override -->
													<div class="space-y-2">
														<Label>New Expiration Date</Label>
														<Input
															type="datetime-local"
															value={change.override_expire_at
																? new Date(change.override_expire_at).toISOString().slice(0, 16)
																: ''}
															oninput={(e) => {
																change.override_expire_at = e.currentTarget.value
																	? new Date(e.currentTarget.value).toISOString()
																	: undefined;
																$form = $form;
															}}
														/>
													</div>
												</div>
											</div>
										{/if}
									</Card>
								{/each}
							</div>
						{/if}

						<!-- Additional Notes -->
						<div class="mt-6">
							<Field form={superFormInstance} name="notes">
								<Control>
									{#snippet children({ props })}
										<div class="space-y-2">
											<Label>Additional Notes</Label>
											<Textarea
												{...props}
												placeholder="Any additional information about this change request..."
												class="min-h-[80px]"
											/>
										</div>
									{/snippet}
								</Control>
							</Field>
						</div>
					</div>
				</Card>
			{/if}

			<!-- Error Display -->
			{#if Object.keys($errors).length > 0}
				<div class="rounded-lg bg-destructive/10 p-4">
					<div class="flex items-start gap-2">
						<AlertCircle class="mt-0.5 h-5 w-5 text-destructive" />
						<div>
							<p class="font-medium text-destructive">Please fix the errors before submitting</p>
						</div>
					</div>
				</div>
			{/if}

			<!-- Form Actions -->
			<div class="flex flex-col gap-4 sm:flex-row sm:justify-between">
				<Button type="button" variant="outline" onclick={() => window.history.back()}>
					Cancel
				</Button>
				<Button type="submit" disabled={$submitting || !selectedOrder}>
					{#if $submitting}
						<Loader2 class="mr-2 h-4 w-4 animate-spin" />
						Creating Request...
					{:else}
						Create Change Request
					{/if}
				</Button>
			</div>
		</div>
	</form>
</div>
