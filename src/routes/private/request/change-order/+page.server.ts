import type { PageServerLoad, Actions } from './$types';
import { error, fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { changeOrderSchema, type ChangeOrderFormData } from './schema';
import type { Database } from '$lib/supabase/database.types';

export const load = (async ({ url, locals: { supabase, brand, user } }) => {
	// Check if user has permission
	if (!user) {
		throw error(401, 'Unauthorized');
	}

	// Get order ID from URL if provided
	const orderId = url.searchParams.get('id');

	// Load reference data needed for the form
	const [
		{ data: changeReasons },
		{ data: publishingStates },
		{ data: orders },
		{ data: products }
	] = await Promise.all([
		supabase.from('change_reason').select('*').order('id'),
		supabase.from('publishing_state').select('*').order('id'),
		supabase
			.from('order')
			.select(
				`
				*,
				profile (
					id,
					username,
					full_name
				),
				order_product (
					*,
					product (
						*,
						metadata (
							title
						)
					)
				),
				order_price (
					*,
					price (*)
				)
			`
			)
			.eq('brand_id', brand.id)
			.order('created_at', { ascending: false })
			.limit(100),
		supabase
			.from('product')
			.select(
				`
				*,
				metadata (
					title
				)
			`
			)
			.eq('brand_id', brand.id)
			.order('created_at', { ascending: false })
	]);

	// Initialize form with defaults if order is selected
	const form = await superValidate(
		orderId
			? {
					order_id: orderId,
					change_reason_id: '',
					change_reason_message: ''
				}
			: undefined,
		zod(changeOrderSchema)
	);

	return {
		form,
		changeReasons: changeReasons ?? [],
		publishingStates: publishingStates ?? [],
		orders: orders ?? [],
		products: products ?? []
	};
}) satisfies PageServerLoad;

export const actions = {
	default: async ({ request, locals: { supabase, brand, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const form = await superValidate(request, zod(changeOrderSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			// Create the change request
			const { data: changeRequest, error: changeRequestError } = await supabase
				.from('change_request')
				.insert({
					actor_profile_id: user.id,
					auto_change_actor_role_id: 'creator', // This should be determined based on user role
					brand_id: brand.id,
					change_reason_id: form.data.change_reason_id,
					change_reason_message: form.data.change_reason_message,
					status: 'drafted',
					form_data: form.data
				})
				.select()
				.single();

			if (changeRequestError) throw changeRequestError;

			// Create order product changes if any
			if (form.data.order_product_changes && form.data.order_product_changes.length > 0) {
				const productChanges = form.data.order_product_changes.map((change) => ({
					change_request_id: changeRequest.id,
					apply_to_order_product_id: change.order_product_id,
					novu_transaction_id: crypto.randomUUID(), // This should be generated properly
					override_product_id: change.override_product_id,
					override_publishing_state: change.override_publishing_state,
					override_cost_unit: change.override_cost_unit,
					override_purchased_count: change.override_purchased_count,
					override_canceled_at_should_return_unit: change.override_canceled_at_should_return_unit
				}));

				const { error: productError } = await supabase
					.from('change_order_product')
					.insert(productChanges);

				if (productError) throw productError;
			}

			// Create order price changes if any
			if (form.data.order_price_changes && form.data.order_price_changes.length > 0) {
				const priceChanges = form.data.order_price_changes.map((change) => ({
					change_request_id: changeRequest.id,
					apply_to_order_price_id: change.order_price_id,
					novu_transaction_id: crypto.randomUUID(), // This should be generated properly
					override_expire_at: change.override_expire_at,
					override_unit: change.override_unit,
					override_money_int: change.override_money_int
				}));

				const { error: priceError } = await supabase
					.from('change_order_price')
					.insert(priceChanges);

				if (priceError) throw priceError;
			}

			// Redirect to request list page
			throw redirect(303, '/private/request');
		} catch (err) {
			// Re-throw redirects
			if (err instanceof Response) throw err;

			console.error('Unexpected error:', err);
			return fail(500, {
				form,
				message: err instanceof Error ? err.message : 'An unexpected error occurred'
			});
		}
	}
} satisfies Actions;
