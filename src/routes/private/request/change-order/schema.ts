import { z } from 'zod';

export const changeOrderSchema = z.object({
	// Change request details
	change_reason_id: z.string().min(1, 'Change reason is required'),
	change_reason_message: z.string().optional(),

	// Order selection
	order_id: z.string().uuid('Invalid order ID'),

	// Order product changes
	order_product_changes: z
		.array(
			z.object({
				order_product_id: z.string().uuid(),
				override_product_id: z.string().uuid().optional(), // Replace with different product
				override_publishing_state: z.string().optional(),
				override_cost_unit: z.number().positive().optional(),
				override_purchased_count: z.number().int().min(0).optional(),
				override_canceled_at_should_return_unit: z.number().optional()
			})
		)
		.optional(),

	// Order price changes
	order_price_changes: z
		.array(
			z.object({
				order_price_id: z.string().uuid(),
				override_expire_at: z.string().datetime().optional(),
				override_unit: z.number().optional(),
				override_money_int: z.number().int().optional()
			})
		)
		.optional(),

	// Additional notes
	notes: z.string().optional()
});

export type ChangeOrderFormData = z.infer<typeof changeOrderSchema>;
