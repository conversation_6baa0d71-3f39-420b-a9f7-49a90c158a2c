<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Card } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select';
	import * as RadioGroup from '$lib/components/ui/radio-group';
	import {
		ArrowLeft,
		Package,
		Calendar,
		AlertCircle,
		ChevronRight,
		Loader2,
		Info
	} from '@lucide/svelte';
	import { goto } from '$app/navigation';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { PageData } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { changeOfferingSchema } from './schema';
	import { Field, Control, FieldErrors } from 'formsnap';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	// Form setup
	const superFormInstance = superForm(data.form, {
		validators: zodClient(changeOfferingSchema),
		dataType: 'json',
		resetForm: false
	});

	const { form, errors, enhance, submitting, message } = superFormInstance;

	// State
	let selectedOffering = $state<any>(null);

	// Watch for offering selection
	$effect(() => {
		if ($form.offering_type && $form.offering_id) {
			if ($form.offering_type === 'product') {
				selectedOffering = data.products.find((p) => p.id === $form.offering_id);
			} else if ($form.offering_type === 'event') {
				selectedOffering = data.events.find((e) => e.id === $form.offering_id);
			}
		}
	});

	// Get change reason that requires message
	let selectedChangeReason = $derived(
		data.changeReasons.find((r) => r.id === $form.change_reason_id)
	);

	// Format date for display
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('en-US', {
			weekday: 'short',
			month: 'short',
			day: 'numeric',
			year: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}
</script>

<div class="container mx-auto max-w-6xl px-4 py-6 sm:px-6 lg:px-8">
	<div class="mb-6 sm:mb-8">
		<div class="mb-2 flex items-center gap-2 text-sm text-muted-foreground">
			<a href="/private/request" class="hover:text-foreground">Change Requests</a>
			<ChevronRight class="h-4 w-4" />
			<span>Change Offering</span>
		</div>
		<h1 class="text-2xl font-bold sm:text-3xl">Change Existing Offering</h1>
		<p class="mt-1 text-muted-foreground">
			Request changes to published products or upcoming events
		</p>
	</div>

	<form method="POST" use:enhance>
		<div class="space-y-6">
			<!-- Step 1: Select Offering Type -->
			<Card>
				<div class="p-6">
					<h2 class="mb-4 text-xl font-semibold">What would you like to change?</h2>

					<Field form={superFormInstance} name="offering_type">
						<Control>
							{#snippet children({ props })}
								<RadioGroup.Root
									bind:value={$form.offering_type}
									name={props.name}
									class="grid gap-4 sm:grid-cols-2"
								>
									<label class="cursor-pointer">
										<Card
											class="p-4 transition-all {$form.offering_type === 'product'
												? 'ring-2 ring-primary'
												: 'hover:shadow-md'}"
										>
											<div class="flex items-start gap-3">
												<RadioGroup.Item value="product" class="mt-1" />
												<div class="flex-1">
													<div class="flex items-center gap-2">
														<Package class="h-5 w-5 text-primary" />
														<span class="font-medium">Product</span>
													</div>
													<p class="mt-1 text-sm text-muted-foreground">
														Change product details, pricing, or availability
													</p>
												</div>
											</div>
										</Card>
									</label>

									<label class="cursor-pointer">
										<Card
											class="p-4 transition-all {$form.offering_type === 'event'
												? 'ring-2 ring-primary'
												: 'hover:shadow-md'}"
										>
											<div class="flex items-start gap-3">
												<RadioGroup.Item value="event" class="mt-1" />
												<div class="flex-1">
													<div class="flex items-center gap-2">
														<Calendar class="h-5 w-5 text-primary" />
														<span class="font-medium">Event</span>
													</div>
													<p class="mt-1 text-sm text-muted-foreground">
														Change event schedule, venue, or status
													</p>
												</div>
											</div>
										</Card>
									</label>
								</RadioGroup.Root>
							{/snippet}
						</Control>
						<FieldErrors class="mt-2 text-sm text-destructive" />
					</Field>
				</div>
			</Card>

			<!-- Step 2: Select Specific Offering -->
			{#if $form.offering_type}
				<Card>
					<div class="p-6">
						<h2 class="mb-4 text-xl font-semibold">
							Select {$form.offering_type === 'product' ? 'Product' : 'Event'}
						</h2>

						<Field form={superFormInstance} name="offering_id">
							<Control>
								{#snippet children({ props })}
									<Select.Root bind:value={$form.offering_id} name={props.name}>
										<Select.Trigger class={$errors.offering_id ? 'border-destructive' : ''}>
											{#if selectedOffering}
												{getLocalizedText(selectedOffering.metadata?.title as LocalizedText)}
											{:else}
												Select {$form.offering_type === 'product' ? 'a product' : 'an event'}
											{/if}
										</Select.Trigger>
										<Select.Content>
											{#if $form.offering_type === 'product'}
												{#each data.products as product}
													<Select.Item value={product.id}>
														{getLocalizedText(product.metadata?.title as LocalizedText)}
													</Select.Item>
												{/each}
											{:else}
												{#each data.events as event}
													<Select.Item value={event.id}>
														{getLocalizedText(event.metadata?.title as LocalizedText)} - {formatDate(
															event.start_at
														)}
													</Select.Item>
												{/each}
											{/if}
										</Select.Content>
									</Select.Root>
								{/snippet}
							</Control>
							<FieldErrors class="mt-2 text-sm text-destructive" />
						</Field>
					</div>
				</Card>
			{/if}

			<!-- Step 3: Change Details -->
			{#if selectedOffering}
				<Card>
					<div class="p-6">
						<h2 class="mb-4 text-xl font-semibold">Change Details</h2>

						<div class="space-y-4">
							<!-- Change Reason -->
							<Field form={superFormInstance} name="change_reason_id">
								<Control>
									{#snippet children({ props })}
										<div class="space-y-2">
											<Label>Reason for Change<span class="ml-1 text-destructive">*</span></Label>
											<Select.Root bind:value={$form.change_reason_id} name={props.name}>
												<Select.Trigger
													class={$errors.change_reason_id ? 'border-destructive' : ''}
												>
													{#if selectedChangeReason}
														{getLocalizedText(selectedChangeReason.title as LocalizedText)}
													{:else}
														Select a reason
													{/if}
												</Select.Trigger>
												<Select.Content>
													{#each data.changeReasons as reason}
														<Select.Item value={reason.id}>
															{getLocalizedText(reason.title as LocalizedText)}
															{#if reason.requires_message}
																<span class="ml-1 text-xs text-muted-foreground"
																	>(details required)</span
																>
															{/if}
														</Select.Item>
													{/each}
												</Select.Content>
											</Select.Root>
										</div>
									{/snippet}
								</Control>
								<FieldErrors class="mt-2 text-sm text-destructive" />
							</Field>

							<!-- Change Reason Message -->
							{#if selectedChangeReason?.requires_message}
								<Field form={superFormInstance} name="change_reason_message">
									<Control>
										{#snippet children({ props })}
											<div class="space-y-2">
												<Label>Details<span class="ml-1 text-destructive">*</span></Label>
												<Textarea
													{...props}
													placeholder="Please provide details about the reason for this change..."
													class="min-h-[100px]"
												/>
											</div>
										{/snippet}
									</Control>
									<FieldErrors class="mt-2 text-sm text-destructive" />
								</Field>
							{/if}

							<!-- Product-specific changes -->
							{#if $form.offering_type === 'product'}
								<div class="space-y-4 rounded-lg border p-4">
									<h3 class="font-medium">Product Changes (Optional)</h3>

									<!-- Title Override -->
									<LocalizedTextControl
										form={superFormInstance}
										name="product_changes.override_title"
										label="New Title"
										optional
									/>

									<!-- Subtitle Override -->
									<LocalizedTextControl
										form={superFormInstance}
										name="product_changes.override_subtitle"
										label="New Subtitle"
										optional
									/>

									<!-- Publishing State Override -->
									<Field form={superFormInstance} name="product_changes.override_publishing_state">
										<Control>
											{#snippet children({ props })}
												<div class="space-y-2">
													<Label>New Publishing State</Label>
													<Select.Root
														bind:value={$form.product_changes.override_publishing_state}
														name={props.name}
													>
														<Select.Trigger>
															{$form.product_changes?.override_publishing_state || 'No change'}
														</Select.Trigger>
														<Select.Content>
															<Select.Item value="">No change</Select.Item>
															{#each data.publishingStates as state}
																<Select.Item value={state.id}>{state.id}</Select.Item>
															{/each}
														</Select.Content>
													</Select.Root>
												</div>
											{/snippet}
										</Control>
									</Field>
								</div>
							{/if}

							<!-- Event-specific changes -->
							{#if $form.offering_type === 'event'}
								<div class="space-y-4 rounded-lg border p-4">
									<h3 class="font-medium">Event Changes (Optional)</h3>

									<div class="rounded-lg bg-muted/50 p-3">
										<div class="flex items-start gap-2">
											<Info class="mt-0.5 h-4 w-4 text-muted-foreground" />
											<p class="text-sm text-muted-foreground">
												Leave fields empty to keep the current values
											</p>
										</div>
									</div>

									<!-- New Start Time -->
									<Field form={superFormInstance} name="event_changes.override_start_at">
										<Control>
											{#snippet children({ props })}
												<div class="space-y-2">
													<Label>New Start Time</Label>
													<input
														type="datetime-local"
														{...props}
														class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
													/>
												</div>
											{/snippet}
										</Control>
									</Field>

									<!-- New Duration -->
									<Field form={superFormInstance} name="event_changes.override_duration_minute">
										<Control>
											{#snippet children({ props })}
												<div class="space-y-2">
													<Label>New Duration (minutes)</Label>
													<input
														type="number"
														{...props}
														min="1"
														class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
													/>
												</div>
											{/snippet}
										</Control>
									</Field>

									<!-- Publishing State Override -->
									<Field form={superFormInstance} name="event_changes.override_publishing_state">
										<Control>
											{#snippet children({ props })}
												<div class="space-y-2">
													<Label>New Publishing State</Label>
													<Select.Root
														bind:value={$form.event_changes.override_publishing_state}
														name={props.name}
													>
														<Select.Trigger>
															{$form.event_changes?.override_publishing_state || 'No change'}
														</Select.Trigger>
														<Select.Content>
															<Select.Item value="">No change</Select.Item>
															{#each data.publishingStates as state}
																<Select.Item value={state.id}>{state.id}</Select.Item>
															{/each}
														</Select.Content>
													</Select.Root>
												</div>
											{/snippet}
										</Control>
									</Field>
								</div>
							{/if}

							<!-- Additional Notes -->
							<Field form={superFormInstance} name="notes">
								<Control>
									{#snippet children({ props })}
										<div class="space-y-2">
											<Label>Additional Notes</Label>
											<Textarea
												{...props}
												placeholder="Any additional information about this change request..."
												class="min-h-[80px]"
											/>
										</div>
									{/snippet}
								</Control>
							</Field>
						</div>
					</div>
				</Card>
			{/if}

			<!-- Error Display -->
			{#if Object.keys($errors).length > 0}
				<div class="rounded-lg bg-destructive/10 p-4">
					<div class="flex items-start gap-2">
						<AlertCircle class="mt-0.5 h-5 w-5 text-destructive" />
						<div>
							<p class="font-medium text-destructive">Please fix the errors before submitting</p>
						</div>
					</div>
				</div>
			{/if}

			<!-- Form Actions -->
			<div class="flex flex-col gap-4 sm:flex-row sm:justify-between">
				<Button type="button" variant="outline" onclick={() => window.history.back()}>
					Cancel
				</Button>
				<Button type="submit" disabled={$submitting || !selectedOffering}>
					{#if $submitting}
						<Loader2 class="mr-2 h-4 w-4 animate-spin" />
						Creating Request...
					{:else}
						Create Change Request
					{/if}
				</Button>
			</div>
		</div>
	</form>
</div>
