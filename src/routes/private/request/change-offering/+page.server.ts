import type { PageServerLoad, Actions } from './$types';
import { error, fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { changeOfferingSchema, type ChangeOfferingFormData } from './schema';
import type { Database } from '$lib/supabase/database.types';

export const load = (async ({ url, locals: { supabase, brand, user } }) => {
	// Check if user has permission
	if (!user) {
		throw error(401, 'Unauthorized');
	}

	// Get offering ID and type from URL if provided
	const offeringId = url.searchParams.get('id');
	const offeringType = url.searchParams.get('type') as 'product' | 'event' | null;

	// Load reference data needed for the form
	const [
		{ data: changeReasons },
		{ data: publishingStates },
		{ data: products },
		{ data: events },
		{ data: metadataRelations }
	] = await Promise.all([
		supabase.from('change_reason').select('*').order('id'),
		supabase.from('publishing_state').select('*').order('id'),
		supabase
			.from('product')
			.select(
				`
				*,
				metadata (
					*,
					metadata_wikipage (
						*,
						wikipage (*)
					)
				)
			`
			)
			.eq('brand_id', brand.id)
			.eq('publishing_state', 'published')
			.order('created_at', { ascending: false }),
		supabase
			.from('event')
			.select(
				`
				*,
				metadata (
					*,
					metadata_wikipage (
						*,
						wikipage (*)
					)
				)
			`
			)
			.eq('brand_id', brand.id)
			.eq('publishing_state', 'published')
			.gte('start_at', new Date().toISOString())
			.order('start_at', { ascending: true }),
		supabase.from('metadata_wikipage_relation').select('*').order('id')
	]);

	// Initialize form with defaults if offering is selected
	const form = await superValidate(
		offeringId && offeringType
			? {
					offering_type: offeringType,
					offering_id: offeringId,
					change_reason_id: '',
					change_reason_message: ''
				}
			: undefined,
		zod(changeOfferingSchema)
	);

	return {
		form,
		changeReasons: changeReasons ?? [],
		publishingStates: publishingStates ?? [],
		products: products ?? [],
		events: events ?? [],
		metadataRelations: metadataRelations ?? []
	};
}) satisfies PageServerLoad;

export const actions = {
	default: async ({ request, locals: { supabase, brand, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const form = await superValidate(request, zod(changeOfferingSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			// Create the change request
			const { data: changeRequest, error: changeRequestError } = await supabase
				.from('change_request')
				.insert({
					actor_profile_id: user.id,
					auto_change_actor_role_id: 'creator', // This should be determined based on user role
					brand_id: brand.id,
					change_reason_id: form.data.change_reason_id,
					change_reason_message: form.data.change_reason_message,
					status: 'drafted',
					form_data: form.data
				})
				.select()
				.single();

			if (changeRequestError) throw changeRequestError;

			// Create the specific change records based on offering type
			if (form.data.offering_type === 'product' && form.data.product_changes) {
				const { error: changeProductError } = await supabase.from('change_product').insert({
					change_request_id: changeRequest.id,
					apply_to_product_id: form.data.offering_id,
					novu_transaction_id: crypto.randomUUID(), // This should be generated properly
					override_title: form.data.product_changes.override_title,
					override_subtitle: form.data.product_changes.override_subtitle,
					override_publishing_state: form.data.product_changes.override_publishing_state
				});

				if (changeProductError) throw changeProductError;
			} else if (form.data.offering_type === 'event' && form.data.event_changes) {
				const { error: changeEventError } = await supabase.from('change_event').insert({
					change_request_id: changeRequest.id,
					apply_to_event_id: form.data.offering_id,
					novu_transaction_id: crypto.randomUUID(), // This should be generated properly
					override_start_at: form.data.event_changes.override_start_at,
					override_duration_minute: form.data.event_changes.override_duration_minute,
					override_event_id: form.data.event_changes.override_event_id,
					override_publishing_state: form.data.event_changes.override_publishing_state
				});

				if (changeEventError) throw changeEventError;
			}

			// Create metadata wikipage changes if any
			if (form.data.metadata_wikipage_changes && form.data.metadata_wikipage_changes.length > 0) {
				const wikipageChanges = form.data.metadata_wikipage_changes.map((change) => ({
					change_request_id: changeRequest.id,
					apply_to_metadata_wikipage_id: change.metadata_wikipage_id,
					novu_transaction_id: crypto.randomUUID(), // This should be generated properly
					override_wikipage_id: change.override_wikipage_id,
					override_relation: change.override_relation
				}));

				const { error: wikipageError } = await supabase
					.from('change_metadata_wikipage')
					.insert(wikipageChanges);

				if (wikipageError) throw wikipageError;
			}

			// Redirect to request list page
			throw redirect(303, '/private/request');
		} catch (err) {
			// Re-throw redirects
			if (err instanceof Response) throw err;

			console.error('Unexpected error:', err);
			return fail(500, {
				form,
				message: err instanceof Error ? err.message : 'An unexpected error occurred'
			});
		}
	}
} satisfies Actions;
