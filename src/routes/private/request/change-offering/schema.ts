import { z } from 'zod';

export const changeOfferingSchema = z.object({
	// Change request details
	change_reason_id: z.string().min(1, 'Change reason is required'),
	change_reason_message: z.string().optional(),

	// The offering being changed (product or event)
	offering_type: z.enum(['product', 'event']),
	offering_id: z.string().uuid('Invalid offering ID'),

	// Product changes (when offering_type is 'product')
	product_changes: z
		.object({
			override_title: z.record(z.string()).optional(),
			override_subtitle: z.record(z.string()).optional(),
			override_publishing_state: z.string().optional()
		})
		.optional(),

	// Event changes (when offering_type is 'event')
	event_changes: z
		.object({
			override_start_at: z.string().datetime().optional(),
			override_duration_minute: z.number().int().positive().optional(),
			override_event_id: z.string().uuid().optional(), // For replacement event
			override_publishing_state: z.string().optional()
		})
		.optional(),

	// Metadata wikipage changes (can apply to both products and events)
	metadata_wikipage_changes: z
		.array(
			z.object({
				metadata_wikipage_id: z.string().uuid(),
				override_wikipage_id: z.string().uuid().optional(),
				override_relation: z.string().optional()
			})
		)
		.optional(),

	// Additional notes
	notes: z.string().optional()
});

export type ChangeOfferingFormData = z.infer<typeof changeOfferingSchema>;
