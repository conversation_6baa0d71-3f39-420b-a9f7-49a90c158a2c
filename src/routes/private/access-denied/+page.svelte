<!--
  Access Denied Page
  Shows when a user tries to access a route they don't have permission for
-->
<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { goto } from '$app/navigation';
</script>

<div class="flex min-h-[70vh] flex-col items-center justify-center p-4 text-center">
	<div class="mb-6">
		<svg
			xmlns="http://www.w3.org/2000/svg"
			class="mx-auto h-24 w-24 text-red-500"
			fill="none"
			viewBox="0 0 24 24"
			stroke="currentColor"
		>
			<path
				stroke-linecap="round"
				stroke-linejoin="round"
				stroke-width="2"
				d="M12 15v2m0 0v2m0-2h2m-2 0H9m3-10a1 1 0 110-2 1 1 0 010 2zm-1-4a1 1 0 011-1h2a1 1 0 011 1v3a1 1 0 01-1 1h-2a1 1 0 01-1-1V7z"
			/>
		</svg>
	</div>

	<h1 class="mb-4 text-3xl font-bold">Access Denied</h1>

	<p class="mb-8 max-w-lg text-gray-600">
		You don't have permission to access this page. If you believe this is an error, please contact
		your administrator.
	</p>

	<div class="flex gap-4">
		<Button onclick={() => goto('/event')}>Go to Events</Button>
		<Button variant="outline" onclick={() => history.back()}>Go Back</Button>
	</div>
</div>
