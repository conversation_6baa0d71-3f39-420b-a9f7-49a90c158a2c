<!-- Group Chat View -->
<script lang="ts">
	import { page } from '$app/state';
	import * as Card from '$lib/components/ui/card';
	import * as Button from '$lib/components/ui/button';
	import * as Textarea from '$lib/components/ui/textarea';
	import {
		Send,
		MoreVertical,
		Bot,
		ShoppingCart,
		MapPin,
		Package,
		Calendar,
		FileText
	} from '@lucide/svelte';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import { PageContainer } from '$lib/components/layout';

	interface Props {
		data: {
			group: {
				id: string;
				name: string;
				created_at: string;
			};
			messages: Array<{
				id: string;
				content: string;
				sender_id: string;
				sent_at: string;
				sender: {
					given_name: string;
					username: string;
					avatar_url: string;
				};
			}>;
			supabase: SupabaseClient;
			feature_access: string[];
		};
	}

	let { data }: Props = $props();
	let { group, supabase, feature_access } = $derived(data);
	let groupId = $derived(page.params.id);

	let newMessage = $state('');
	let messages = $state<Props['data']['messages']>(data.messages);
	let messageContainer: HTMLDivElement;
	let textareaHeight = $state('2.75rem');

	const allowedActions = [
		{
			title: 'AI',
			url: `/private/ai?group=${groupId}`,
			icon: Bot,
			features: ['brand_ai_write', 'brand_ai_read']
		},
		{
			title: 'Orders',
			url: `/private/order?group=${groupId}`,
			icon: ShoppingCart,
			features: ['brand_order_write', 'brand_order_read']
		},
		{
			title: 'Locations',
			url: `/private/location?group=${groupId}`,
			icon: MapPin,
			features: ['brand_landmark_write', 'brand_landmark_read']
		},
		{
			title: 'Products',
			url: `/private/product?group=${groupId}`,
			icon: Package,
			features: [
				'brand_product_write',
				'brand_product_read',
				'brand_product_category_write',
				'brand_product_category_read'
			]
		},
		{
			title: 'Time Slots',
			url: `/private/timeslot?group=${groupId}`,
			icon: Calendar,
			features: ['brand_time_slot_write', 'brand_time_slot_read']
		},
		{
			title: 'Wikipages',
			url: `/private/wikipage?group=${groupId}`,
			icon: FileText,
			features: []
		}
	];

	$effect(() => {
		if (messageContainer) {
			messageContainer.scrollTop = messageContainer.scrollHeight;
		}
	});

	$effect(() => {
		// Subscribe to new messages
		const channel = supabase
			.channel('messages')
			.on(
				'postgres_changes',
				{
					event: '*',
					schema: 'public',
					table: 'message',
					filter: `group_id=eq.${groupId}`
				},
				(payload: any) => {
					if (payload.eventType === 'INSERT') {
						messages = [...messages, payload.new];
					} else if (payload.eventType === 'UPDATE') {
						messages = messages.map((msg) =>
							msg.id === payload.new.id ? { ...msg, ...payload.new } : msg
						);
					}
				}
			)
			.subscribe();

		return () => {
			supabase.removeChannel(channel);
		};
	});

	function adjustTextareaHeight(event: Event) {
		const textarea = event.target as HTMLTextAreaElement;
		const lineHeight = 32;
		const minHeight = 44;
		const maxLines = 3;

		textarea.style.height = 'auto';
		const lines = textarea.value.split('\n').length;
		const targetHeight = Math.min(Math.max(lines * lineHeight, minHeight), maxLines * lineHeight);
		textarea.style.height = `${targetHeight}px`;
		textareaHeight = `${targetHeight}px`;
	}

	async function handleSubmit(event: SubmitEvent) {
		event.preventDefault();
		if (!newMessage.trim()) return;

		try {
			const { error } = await supabase.from('message').insert({
				content: newMessage,
				group_id: groupId,
				sent_at: new Date().toISOString()
			});

			if (error) throw error;
			newMessage = '';
		} catch (error) {
			console.error('Error sending message:', error);
		}
	}

	const hasAccess = (item: (typeof allowedActions)[0]) => {
		if (!feature_access || item.features.length === 0) return true;
		return item.features.some((feature) => feature_access.includes(feature));
	};
</script>

{#snippet actions()}
	<div class="flex items-center gap-2 overflow-x-auto">
		{#each allowedActions.filter(hasAccess) as action}
			<Button.Root variant="outline">
				<a href={action.url} class="inline-flex items-center gap-2">
					<action.icon class="h-4 w-4" />
					<span class="hidden sm:inline">{action.title}</span>
				</a>
			</Button.Root>
		{/each}
		<Button.Root variant="ghost" size="icon" class="h-9 w-9">
			<MoreVertical class="h-4 w-4" />
		</Button.Root>
	</div>
{/snippet}

{#snippet content()}
	<div class="flex h-[calc(100vh-16rem)] flex-col overflow-hidden">
		<!-- Messages -->
		<div class="relative flex flex-1 flex-col overflow-hidden">
			<div
				class="absolute inset-0 flex flex-col overflow-y-auto px-4 pb-6 pt-4"
				bind:this={messageContainer}
			>
				{#if messages.length === 0}
					<div class="flex h-full items-center justify-center">
						<div class="text-center text-muted-foreground">
							<p class="font-medium">No messages yet</p>
							<p class="text-sm">Start the conversation by sending a message</p>
						</div>
					</div>
				{:else}
					<div class="flex flex-col gap-6">
						{#each messages as message}
							<div class="group flex gap-3 hover:bg-muted/5">
								<div class="shrink-0 pt-0.5">
									{#if message.sender?.avatar_url}
										<img
											src={message.sender.avatar_url}
											alt={message.sender.given_name}
											class="h-8 w-8 rounded-full object-cover"
										/>
									{:else}
										<div
											class="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary"
										>
											{message.sender?.given_name?.[0] ?? '?'}
										</div>
									{/if}
								</div>
								<div class="flex min-w-0 flex-1 flex-col">
									<div class="flex items-baseline gap-2">
										<span class="truncate font-medium">{message.sender?.given_name}</span>
										<span class="shrink-0 text-xs text-muted-foreground">
											{new Date(message.sent_at).toLocaleTimeString([], {
												hour: '2-digit',
												minute: '2-digit'
											})}
										</span>
									</div>
									<p class="text-sm leading-relaxed">{message.content}</p>
								</div>
							</div>
						{/each}
					</div>
				{/if}
			</div>
		</div>
	</div>
{/snippet}

{#snippet footer()}
	<form class="mx-auto flex max-w-3xl gap-3" onsubmit={handleSubmit}>
		<Textarea.Root
			bind:value={newMessage}
			placeholder="Type a message..."
			class="resize-none border-muted-foreground/20 py-[10px] leading-[1.6] focus-visible:ring-primary/20"
			style={`min-height: ${textareaHeight}; max-height: 96px;`}
			rows={1}
			oninput={adjustTextareaHeight}
		/>
		<Button.Root type="submit" size="icon" class="h-11 w-11 shrink-0">
			<Send class="h-4 w-4" />
		</Button.Root>
	</form>
{/snippet}

<PageContainer title={group.name} description="Group Chat" {actions} {content} {footer} />
