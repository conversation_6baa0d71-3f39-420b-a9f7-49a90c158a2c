import type { PageServerLoad } from './$types';

export const load = (async ({ params, locals: { supabase } }) => {
	const { id } = params;

	// Get group details
	const { data: group, error: groupError } = await supabase
		.from('group')
		.select('*')
		.eq('id', id)
		.single();

	if (groupError) {
		console.error('Error fetching group:', groupError);
		return {
			group: null,
			messages: []
		};
	}

	// Get messages for this group with sender details
	const { data: messages, error: messagesError } = await supabase
		.from('message')
		.select(
			`
			*,
			sender:profile (
				id,
				given_name,
				username,
				avatar_url
			)
			`
		)
		.eq('group_id', id)
		.order('sent_at', { ascending: true });

	if (messagesError) {
		console.error('Error fetching messages:', messagesError);
		return {
			group,
			messages: []
		};
	}

	return {
		group,
		messages: messages || []
	};
}) satisfies PageServerLoad;
