import type { PageServerLoad, Actions } from './$types';
import { fail, error, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { holidayRuleFormSchema } from '../schemas';
import type { HolidayRule, HolidayReference, ScheduleKind, HolidayRuleWithDetails } from '../types';

export const load: PageServerLoad = async ({ locals, url }) => {
	try {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;

		if (!brandId) {
			throw error(400, 'Brand ID is required');
		}

		const year = parseInt(url.searchParams.get('year') || new Date().getFullYear().toString());
		const country_code = url.searchParams.get('country') || '';

		// Load holiday rules with joined data
		let rulesQuery = supabase
			.from('holiday_rule')
			.select(`
				*,
				holiday_reference:holiday_reference_id (
					id,
					name,
					description,
					country_code,
					holiday_date,
					is_national,
					recurrence_rule,
					source_api
				),
				schedule_kind_data:schedule_kind (
					id,
					name,
					description,
					is_default
				),
				holiday_rule_landmark (
					landmark_id,
					landmark:landmark_id (
						id,
						title_short,
						title_full
					)
				)
			`)
			.eq('brand_id', brandId)
			.order('created_at', { ascending: false });

		const { data: holidayRules, error: rulesError } = await rulesQuery;

		if (rulesError) {
			console.error('Error loading holiday rules:', rulesError);
			throw error(500, 'Failed to load holiday rules');
		}

		// Load available holiday references (not yet configured)
		let referencesQuery = supabase
			.from('holiday_reference')
			.select('*')
			.order('holiday_date', { ascending: true });

		// Filter by year if specified
		if (year) {
			referencesQuery = referencesQuery
				.gte('holiday_date', `${year}-01-01`)
				.lte('holiday_date', `${year}-12-31`);
		}

		// Filter by country if specified
		if (country_code) {
			referencesQuery = referencesQuery.eq('country_code', country_code);
		}

		const { data: holidayReferences, error: referencesError } = await referencesQuery;

		if (referencesError) {
			console.error('Error loading holiday references:', referencesError);
			throw error(500, 'Failed to load holiday references');
		}

		// Load schedule kinds
		const { data: scheduleKinds, error: scheduleKindsError } = await supabase
			.from('schedule_kind')
			.select('*')
			.order('is_default', { ascending: false });

		if (scheduleKindsError) {
			console.error('Error loading schedule kinds:', scheduleKindsError);
			throw error(500, 'Failed to load schedule kinds');
		}

		// Load landmarks for the brand
		const { data: landmarks, error: landmarksError } = await supabase
			.from('landmark')
			.select('id, title_short, title_full')
			.eq('brand_id', brandId)
			.order('title_short');

		if (landmarksError) {
			console.error('Error loading landmarks:', landmarksError);
		}

		// Get available countries from holiday references
		const { data: countries, error: countriesError } = await supabase
			.from('holiday_reference')
			.select('country_code')
			.order('country_code');

		const uniqueCountries = Array.from(
			new Set(countries?.map(c => c.country_code) || [])
		).sort();

		// Set up the form
		const holidayRuleForm = await superValidate(zod(holidayRuleFormSchema));

		// Transform rules data for UI
		const rulesWithDetails: HolidayRuleWithDetails[] = (holidayRules || []).map(rule => ({
			...rule,
			holiday_reference: rule.holiday_reference,
			schedule_kind_data: rule.schedule_kind_data,
			upcoming_instances: [], // Will be populated by client-side logic
			affected_landmarks: rule.applies_to_all_landmarks 
				? landmarks || []
				: (rule.holiday_rule_landmark || []).map(hrl => ({
					id: hrl.landmark_id,
					name: hrl.landmark?.title_short || 'Unknown'
				}))
		}));

		return {
			holidayRules: rulesWithDetails,
			holidayReferences: holidayReferences || [],
			scheduleKinds: scheduleKinds || [],
			landmarks: landmarks || [],
			availableCountries: uniqueCountries,
			selectedYear: year,
			selectedCountry: country_code,
			holidayRuleForm
		};
	} catch (e) {
		console.error('Error in holiday rules load:', e);
		throw error(500, 'Failed to load holiday rules data');
	}
};

export const actions: Actions = {
	createRule: async ({ request, locals }) => {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;
		const userId = locals.user?.id;

		if (!brandId) {
			return fail(400, { message: 'Brand ID is required' });
		}

		if (!userId) {
			return fail(400, { message: 'User must be authenticated' });
		}

		const form = await superValidate(request, zod(holidayRuleFormSchema));

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			// Check if rule already exists for this holiday
			const { data: existingRule } = await supabase
				.from('holiday_rule')
				.select('id')
				.eq('brand_id', brandId)
				.eq('holiday_reference_id', form.data.holiday_reference_id)
				.single();

			if (existingRule) {
				form.errors._errors = ['A rule already exists for this holiday'];
				return fail(400, { form });
			}

			// Prepare localized messages
			const customer_message = {
				...(form.data.customer_message_en && { en: form.data.customer_message_en }),
				...(form.data.customer_message_zh && { zh: form.data.customer_message_zh }),
				...(form.data.customer_message_ko && { ko: form.data.customer_message_ko }),
				...(form.data.customer_message_ja && { ja: form.data.customer_message_ja })
			};

			const staff_message = {
				...(form.data.staff_message_en && { en: form.data.staff_message_en }),
				...(form.data.staff_message_zh && { zh: form.data.staff_message_zh }),
				...(form.data.staff_message_ko && { ko: form.data.staff_message_ko }),
				...(form.data.staff_message_ja && { ja: form.data.staff_message_ja })
			};

			// Create the holiday rule
			const { data: newRule, error: ruleError } = await supabase
				.from('holiday_rule')
				.insert({
					brand_id: brandId,
					holiday_reference_id: form.data.holiday_reference_id,
					schedule_kind: form.data.schedule_kind,
					applies_to_all_landmarks: form.data.applies_to_all_landmarks,
					customer_message: Object.keys(customer_message).length > 0 ? customer_message : null,
					staff_message: Object.keys(staff_message).length > 0 ? staff_message : null,
					auto_notify_customers: form.data.auto_notify_customers,
					auto_notify_staff: form.data.auto_notify_staff,
					revenue_impact_notes: form.data.revenue_impact_notes,
					is_active: form.data.is_active
				})
				.select()
				.single();

			if (ruleError) {
				console.error('Holiday rule creation error:', ruleError);
				form.errors._errors = ['Failed to create holiday rule: ' + ruleError.message];
				return fail(400, { form });
			}

			// If not applying to all landmarks, create landmark associations
			if (!form.data.applies_to_all_landmarks && form.data.landmark_ids && form.data.landmark_ids.length > 0) {
				const landmarkAssociations = form.data.landmark_ids.map(landmarkId => ({
					holiday_rule_id: newRule.id,
					landmark_id: landmarkId
				}));

				const { error: landmarkError } = await supabase
					.from('holiday_rule_landmark')
					.insert(landmarkAssociations);

				if (landmarkError) {
					console.error('Error creating landmark associations:', landmarkError);
					// Don't fail the whole operation, just log the error
				}
			}

			return redirect(303, '/private/holiday/rules?success=' + encodeURIComponent('Holiday rule created successfully!'));

		} catch (e) {
			console.error('Error creating holiday rule:', e);
			return fail(500, {
				form,
				message: 'An unexpected error occurred while creating the holiday rule'
			});
		}
	},

	updateRule: async ({ request, locals }) => {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;

		if (!brandId) {
			return fail(400, { message: 'Brand ID is required' });
		}

		const formData = await request.formData();
		const ruleId = formData.get('ruleId') as string;
		const isActive = formData.get('isActive') === 'true';

		if (!ruleId) {
			return fail(400, { message: 'Rule ID is required' });
		}

		try {
			const { error: updateError } = await supabase
				.from('holiday_rule')
				.update({ is_active: isActive })
				.eq('id', ruleId)
				.eq('brand_id', brandId);

			if (updateError) {
				console.error('Holiday rule update error:', updateError);
				return fail(400, { message: 'Failed to update holiday rule: ' + updateError.message });
			}

			return {
				success: true,
				message: `Holiday rule ${isActive ? 'activated' : 'deactivated'} successfully!`
			};
		} catch (e) {
			console.error('Error updating holiday rule:', e);
			return fail(500, {
				message: 'An unexpected error occurred while updating the holiday rule'
			});
		}
	},

	deleteRule: async ({ request, locals }) => {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;

		if (!brandId) {
			return fail(400, { message: 'Brand ID is required' });
		}

		const formData = await request.formData();
		const ruleId = formData.get('ruleId') as string;

		if (!ruleId) {
			return fail(400, { message: 'Rule ID is required' });
		}

		try {
			const { error: deleteError } = await supabase
				.from('holiday_rule')
				.delete()
				.eq('id', ruleId)
				.eq('brand_id', brandId);

			if (deleteError) {
				console.error('Holiday rule deletion error:', deleteError);
				return fail(400, { message: 'Failed to delete holiday rule: ' + deleteError.message });
			}

			return {
				success: true,
				message: 'Holiday rule deleted successfully!'
			};
		} catch (e) {
			console.error('Error deleting holiday rule:', e);
			return fail(500, {
				message: 'An unexpected error occurred while deleting the holiday rule'
			});
		}
	}
};
