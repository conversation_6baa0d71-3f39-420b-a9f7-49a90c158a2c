<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Badge } from '$lib/components/ui/badge';
	import * as Select from '$lib/components/ui/select';
	import * as Card from '$lib/components/ui/card';
	import * as Alert from '$lib/components/ui/alert';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as AlertDialog from '$lib/components/ui/alert-dialog';
	import * as Tabs from '$lib/components/ui/tabs';
	import { PageContainer } from '$lib/components/layout';
	import DataTable from '$lib/components/shared/DataTable.svelte';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { holidayRuleFormSchema, availableCountries } from '../schemas';
	import { Control, Label, Field, FieldErrors } from 'formsnap';
	import type { ColumnDef } from '@tanstack/table-core';
	import {
		createSortableColumn,
		createDateColumn,
		createActionsColumn
	} from '$lib/components/shared/table-utils';
	import { getLocalizedText } from '$lib/utils/localization';
	import CalendarDays from '@lucide/svelte/icons/calendar-days';
	import Plus from '@lucide/svelte/icons/plus';
	import ArrowLeft from '@lucide/svelte/icons/arrow-left';
	import Settings from '@lucide/svelte/icons/settings';
	import Trash2 from '@lucide/svelte/icons/trash-2';
	import Power from '@lucide/svelte/icons/power';
	import PowerOff from '@lucide/svelte/icons/power-off';
	import Globe from '@lucide/svelte/icons/globe';
	import MapPin from '@lucide/svelte/icons/map-pin';
	import type { HolidayRuleWithDetails, HolidayReference, ScheduleKind } from '../types';

	interface Props {
		data: {
			holidayRules: HolidayRuleWithDetails[];
			holidayReferences: HolidayReference[];
			scheduleKinds: ScheduleKind[];
			landmarks: Array<{ id: string; title_short: string; title_full: string }>;
			availableCountries: string[];
			selectedYear: number;
			selectedCountry: string;
			holidayRuleForm: any;
		};
	}

	let { data }: Props = $props();

	// Form handling
	const form = superForm(data.holidayRuleForm, {
		validators: zodClient(holidayRuleFormSchema),
		resetForm: true,
		onResult: ({ result }) => {
			if (result.type === 'redirect') {
				showCreateDialog = false;
			}
		}
	});

	const { form: formData, enhance, errors, submitting } = form;

	// State management
	let showCreateDialog = $state(false);
	let showDeleteDialog = $state(false);
	let selectedYear = $state(data.selectedYear);
	let selectedCountry = $state(data.selectedCountry);
	let ruleToDelete = $state<{ id: string; name: string } | null>(null);
	let message = $state<{ type: 'success' | 'error'; text: string } | null>(null);

	// Handle success message from URL parameters
	$effect(() => {
		const successMessage = page.url.searchParams.get('success');
		if (successMessage) {
			message = { type: 'success', text: decodeURIComponent(successMessage) };
			// Remove the success parameter from URL
			const url = new URL(window.location.href);
			url.searchParams.delete('success');
			window.history.replaceState({}, '', url.toString());
		}
	});

	// Year options
	const currentYear = new Date().getFullYear();
	const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i).map((year) => ({
		value: year.toString(),
		label: year.toString()
	}));

	// Country options
	const countryOptions = [
		{ value: '', label: 'All Countries' },
		...data.availableCountries.map(code => {
			const country = availableCountries.find(c => c.code === code);
			return {
				value: code,
				label: country ? `${country.name} (${code})` : code
			};
		})
	];

	// Get available holiday references (not yet configured)
	const availableHolidayReferences = $derived(
		data.holidayReferences.filter(ref => 
			!data.holidayRules.some(rule => rule.holiday_reference_id === ref.id)
		)
	);

	// Navigation functions
	function goBack() {
		goto('/private/holiday');
	}

	function handleYearChange(value: string | undefined) {
		if (value !== undefined) {
			selectedYear = parseInt(value) || currentYear;
			updateFilters();
		}
	}

	function handleCountryChange(value: string | undefined) {
		selectedCountry = value || '';
		updateFilters();
	}

	function updateFilters() {
		const url = new URL(window.location.href);
		url.searchParams.set('year', selectedYear.toString());
		if (selectedCountry) {
			url.searchParams.set('country', selectedCountry);
		} else {
			url.searchParams.delete('country');
		}
		goto(url.toString(), { replaceState: true });
	}

	function openCreateDialog() {
		// Reset form
		$formData = {
			holiday_reference_id: '',
			schedule_kind: 'closed',
			applies_to_all_landmarks: true,
			landmark_ids: [],
			customer_message_en: '',
			customer_message_zh: '',
			customer_message_ko: '',
			customer_message_ja: '',
			staff_message_en: '',
			staff_message_zh: '',
			staff_message_ko: '',
			staff_message_ja: '',
			auto_notify_customers: false,
			auto_notify_staff: true,
			revenue_impact_notes: '',
			is_active: true
		};
		showCreateDialog = true;
	}

	// Delete functions
	function openDeleteDialog(rule: HolidayRuleWithDetails) {
		ruleToDelete = { 
			id: rule.id, 
			name: getLocalizedText(rule.holiday_reference.name) 
		};
		showDeleteDialog = true;
	}

	function closeDeleteDialog() {
		showDeleteDialog = false;
		ruleToDelete = null;
	}

	// Toggle rule active status
	async function toggleRuleStatus(rule: HolidayRuleWithDetails) {
		const formData = new FormData();
		formData.append('ruleId', rule.id);
		formData.append('isActive', (!rule.is_active).toString());

		const response = await fetch('?/updateRule', {
			method: 'POST',
			body: formData
		});

		if (response.ok) {
			invalidate('supabase:db:holiday_rules');
		}
	}

	// Get schedule kind display name
	function getScheduleKindName(scheduleKindId: string): string {
		const kind = data.scheduleKinds.find(k => k.id === scheduleKindId);
		return kind ? getLocalizedText(kind.name) : scheduleKindId;
	}

	// Get schedule kind badge variant
	function getScheduleKindVariant(scheduleKindId: string): 'default' | 'secondary' | 'destructive' {
		switch (scheduleKindId) {
			case 'closed': return 'destructive';
			case 'reduced': return 'secondary';
			default: return 'default';
		}
	}

	// Table columns
	const columns: ColumnDef<HolidayRuleWithDetails, any>[] = [
		createSortableColumn('holiday_name', 'Holiday', (row) => getLocalizedText(row.holiday_reference.name)),
		createDateColumn('holiday_date', 'Date', (row) => row.holiday_reference.holiday_date),
		{
			accessorKey: 'schedule_kind',
			header: 'Action',
			cell: ({ row }) => {
				const scheduleKind = row.original.schedule_kind;
				return `<div class="flex items-center gap-2">
					<span class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
						scheduleKind === 'closed' ? 'bg-red-100 text-red-800' :
						scheduleKind === 'reduced' ? 'bg-yellow-100 text-yellow-800' :
						'bg-green-100 text-green-800'
					}">
						${getScheduleKindName(scheduleKind)}
					</span>
				</div>`;
			}
		},
		{
			accessorKey: 'scope',
			header: 'Scope',
			cell: ({ row }) => {
				const rule = row.original;
				return rule.applies_to_all_landmarks 
					? '<div class="flex items-center gap-1"><svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path></svg>All Locations</div>'
					: `<div class="flex items-center gap-1"><svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path></svg>${rule.affected_landmarks.length} Location(s)</div>`;
			}
		},
		{
			accessorKey: 'status',
			header: 'Status',
			cell: ({ row }) => {
				const isActive = row.original.is_active;
				return `<span class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
					isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
				}">
					${isActive ? 'Active' : 'Inactive'}
				</span>`;
			}
		},
		{
			id: 'actions',
			header: 'Actions',
			cell: ({ row }) => {
				const rule = row.original;
				return `<div class="flex items-center gap-2">
					<button onclick="toggleRuleStatus('${rule.id}')" class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-8 w-8">
						${rule.is_active ? '<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path></svg>' : '<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>'}
					</button>
					<button onclick="openDeleteDialog('${rule.id}', '${getLocalizedText(rule.holiday_reference.name)}')" class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-8 w-8 text-red-600">
						<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
					</button>
				</div>`;
			}
		}
	];

	// Clear message after 5 seconds
	$effect(() => {
		if (message) {
			const timer = setTimeout(() => {
				message = null;
			}, 5000);
			return () => clearTimeout(timer);
		}
	});
</script>

{#snippet actions()}
	<div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
		<Button variant="outline" onclick={goBack}>
			<ArrowLeft class="mr-2 h-4 w-4" />
			Back to Holidays
		</Button>

		<div class="flex items-center gap-2">
			<Select.Root type="single" value={selectedYear.toString()} onValueChange={handleYearChange}>
				<Select.Trigger class="w-32">
					<Select.Value>{selectedYear}</Select.Value>
				</Select.Trigger>
				<Select.Content>
					{#each yearOptions as year}
						<Select.Item value={year.value}>{year.label}</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>

			<Select.Root type="single" value={selectedCountry} onValueChange={handleCountryChange}>
				<Select.Trigger class="w-40">
					<Select.Value>{selectedCountry || 'All Countries'}</Select.Value>
				</Select.Trigger>
				<Select.Content>
					{#each countryOptions as country}
						<Select.Item value={country.value}>{country.label}</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>

			<Button onclick={openCreateDialog} disabled={availableHolidayReferences.length === 0}>
				<Plus class="mr-2 h-4 w-4" />
				Create Rule
			</Button>
		</div>
	</div>
{/snippet}

{#snippet content()}
	{#if message}
		<Alert.Root class={`mb-4 ${message.type === 'error' ? 'border-red-500' : 'border-green-500'}`}>
			<Alert.Title>{message.type === 'error' ? 'Error' : 'Success'}</Alert.Title>
			<Alert.Description>{message.text}</Alert.Description>
		</Alert.Root>
	{/if}

	{#if availableHolidayReferences.length === 0 && data.holidayRules.length === 0}
		<div class="flex flex-col items-center justify-center py-12 text-center">
			<CalendarDays class="text-muted-foreground mb-4 h-12 w-12" />
			<h3 class="mb-2 text-lg font-semibold">No holiday reference data found</h3>
			<p class="text-muted-foreground mb-4">
				You need to import holiday reference data before you can create business rules.
			</p>
			<Button onclick={() => goto('/private/holiday/reference')}>
				<Globe class="mr-2 h-4 w-4" />
				Import Holiday Data
			</Button>
		</div>
	{:else}
		<div class="rounded-md border">
			<DataTable data={data.holidayRules} {columns} />
		</div>

		{#if data.holidayRules.length === 0}
			<div class="flex flex-col items-center justify-center py-12 text-center">
				<Settings class="text-muted-foreground mb-4 h-12 w-12" />
				<h3 class="mb-2 text-lg font-semibold">No holiday rules configured</h3>
				<p class="text-muted-foreground mb-4">
					Create your first holiday rule to define what your business does on holidays.
				</p>
				<Button onclick={openCreateDialog}>
					<Plus class="mr-2 h-4 w-4" />
					Create Holiday Rule
				</Button>
			</div>
		{/if}
	{/if}
{/snippet}

<PageContainer
	title="Holiday Rules"
	description="Define what your business does for each holiday in {selectedYear}"
	{actions}
	{content}
/>

<!-- Create Rule Dialog -->
<Dialog.Root open={showCreateDialog} onOpenChange={(open) => showCreateDialog = open}>
	<Dialog.Content class="max-w-4xl max-h-[90vh] overflow-y-auto">
		<Dialog.Header>
			<Dialog.Title>Create Holiday Rule</Dialog.Title>
			<Dialog.Description>
				Define what your business does for a specific holiday. This rule will apply automatically each year.
			</Dialog.Description>
		</Dialog.Header>

		<form method="POST" action="?/createRule" use:enhance class="space-y-6">
			{#if $errors._errors && $errors._errors.length > 0}
				<Alert.Root class="border-red-500">
					<Alert.Title>Please fix the following errors:</Alert.Title>
					<Alert.Description>
						<ul class="ml-4 list-disc">
							{#each $errors._errors as error}
								<li>{error}</li>
							{/each}
						</ul>
					</Alert.Description>
				</Alert.Root>
			{/if}

			<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
				<Field {form} name="holiday_reference_id">
					<Control let:attrs>
						<Label>Holiday *</Label>
						<Select.Root type="single" bind:value={$formData.holiday_reference_id}>
							<Select.Trigger class={$errors.holiday_reference_id ? 'border-red-500' : ''}>
								{#if $formData.holiday_reference_id}
									{@const selectedHoliday = availableHolidayReferences.find(h => h.id === $formData.holiday_reference_id)}
									{selectedHoliday ? getLocalizedText(selectedHoliday.name) : 'Select holiday'}
								{:else}
									Select holiday
								{/if}
							</Select.Trigger>
							<Select.Content>
								{#each availableHolidayReferences as holiday}
									<Select.Item value={holiday.id} label={getLocalizedText(holiday.name)}>
										<div class="flex flex-col">
											<span>{getLocalizedText(holiday.name)}</span>
											<span class="text-sm text-muted-foreground">
												{holiday.holiday_date} • {holiday.country_code.toUpperCase()}
											</span>
										</div>
									</Select.Item>
								{/each}
							</Select.Content>
						</Select.Root>
						<input type="hidden" name="holiday_reference_id" bind:value={$formData.holiday_reference_id} />
					</Control>
					<FieldErrors />
				</Field>

				<Field {form} name="schedule_kind">
					<Control let:attrs>
						<Label>What to do *</Label>
						<Select.Root type="single" bind:value={$formData.schedule_kind}>
							<Select.Trigger class={$errors.schedule_kind ? 'border-red-500' : ''}>
								{$formData.schedule_kind ? getScheduleKindName($formData.schedule_kind) : 'Select action'}
							</Select.Trigger>
							<Select.Content>
								{#each data.scheduleKinds as kind}
									<Select.Item value={kind.id} label={getLocalizedText(kind.name)}>
										<div class="flex items-center gap-2">
											<Badge variant={getScheduleKindVariant(kind.id)}>
												{getLocalizedText(kind.name)}
											</Badge>
											{#if kind.description}
												<span class="text-sm text-muted-foreground">
													{getLocalizedText(kind.description)}
												</span>
											{/if}
										</div>
									</Select.Item>
								{/each}
							</Select.Content>
						</Select.Root>
						<input type="hidden" name="schedule_kind" bind:value={$formData.schedule_kind} />
					</Control>
					<FieldErrors />
				</Field>
			</div>

			<div class="space-y-4">
				<div class="flex items-center space-x-2">
					<Checkbox bind:checked={$formData.applies_to_all_landmarks} id="applies_to_all_landmarks" />
					<Label for="applies_to_all_landmarks">Apply to all locations</Label>
				</div>
				<p class="text-muted-foreground text-sm">
					If unchecked, you can select specific locations below
				</p>
				<input type="hidden" name="applies_to_all_landmarks" bind:value={$formData.applies_to_all_landmarks} />

				{#if !$formData.applies_to_all_landmarks}
					<div>
						<Label>Select Locations</Label>
						<div class="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded-md p-2 mt-2">
							{#each data.landmarks as landmark}
								<div class="flex items-center space-x-2">
									<Checkbox
										id="landmark_{landmark.id}"
										checked={$formData.landmark_ids?.includes(landmark.id) || false}
										onCheckedChange={(checked) => {
											if (checked) {
												$formData.landmark_ids = [...($formData.landmark_ids || []), landmark.id];
											} else {
												$formData.landmark_ids = ($formData.landmark_ids || []).filter((id: string) => id !== landmark.id);
											}
										}}
									/>
									<Label for="landmark_{landmark.id}" class="text-sm">
										{landmark.title_short}
									</Label>
								</div>
							{/each}
						</div>
						{#each $formData.landmark_ids || [] as landmarkId}
							<input type="hidden" name="landmark_ids" value={landmarkId} />
						{/each}
					</div>
				{/if}
			</div>

			<Dialog.Footer>
				<Button type="button" variant="outline" onclick={() => showCreateDialog = false}>
					Cancel
				</Button>
				<Button type="submit" disabled={$submitting}>
					{$submitting ? 'Creating...' : 'Create Rule'}
				</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>

<!-- Delete Confirmation Dialog -->
<AlertDialog.Root open={showDeleteDialog} onOpenChange={closeDeleteDialog}>
	<AlertDialog.Content>
		<AlertDialog.Header>
			<AlertDialog.Title>Delete Holiday Rule</AlertDialog.Title>
			<AlertDialog.Description>
				Are you sure you want to delete the rule for "{ruleToDelete?.name}"? This action cannot be undone.
			</AlertDialog.Description>
		</AlertDialog.Header>
		<AlertDialog.Footer>
			<AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
			<form method="POST" action="?/deleteRule" use:enhance class="inline">
				<input type="hidden" name="ruleId" value={ruleToDelete?.id} />
				<AlertDialog.Action type="submit">
					Delete Rule
				</AlertDialog.Action>
			</form>
		</AlertDialog.Footer>
	</AlertDialog.Content>
</AlertDialog.Root>
