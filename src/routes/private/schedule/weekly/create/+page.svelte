<script lang="ts">
	import { goto } from '$app/navigation';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import { Label } from '$lib/components/ui/label';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select';
	import { scheduleSchema } from '../../schemas';
	import ArrowLeft from '@lucide/svelte/icons/arrow-left';
	import Plus from '@lucide/svelte/icons/plus';
	import Trash2 from '@lucide/svelte/icons/trash-2';

	interface Props {
		data: {
			form: any;
			landmarks: Array<{ id: string; name: string }>;
			scheduleKinds: Array<{ id: string; name: Record<string, string> }>;
			daysOfWeek: Array<{ id: number; name_full: Record<string, string>; name_short: Record<string, string> }>;
		};
	}

	const { data }: Props = $props();

	const form = superForm(data.form, {
		validators: zodClient(scheduleSchema),
		resetForm: false
	});

	const { form: formData, enhance, errors, submitting } = form;

	// Schedule periods state
	let periods = $state<Array<{
		day_of_week: number;
		period_name: Record<string, string>;
		start_time: string;
		end_time: string;
	}>>([]);

	function addPeriod(dayOfWeek: number) {
		periods.push({
			day_of_week: dayOfWeek,
			period_name: { en: '' },
			start_time: '09:00',
			end_time: '17:00'
		});
	}

	function removePeriod(index: number) {
		periods.splice(index, 1);
	}

	function goBack() {
		goto('/private/schedule');
	}

	function getLocalizedText(text: Record<string, string>): string {
		return text.en || text.zh || text.ko || text.ja || '';
	}
</script>

<!-- Header -->
<div class="flex items-center gap-4">
	<Button variant="ghost" size="sm" onclick={goBack}>
		<ArrowLeft class="mr-2 h-4 w-4" />
		Back to Schedules
	</Button>
	<div>
		<h1 class="text-3xl font-bold tracking-tight">Create Weekly Schedule</h1>
		<p class="text-muted-foreground">
			Define regular operating hours for each day of the week
		</p>
	</div>
</div>

<div class="space-y-6">

		<form method="POST" use:enhance>
			<div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
				<!-- Schedule Details -->
				<div class="lg:col-span-1">
					<Card.Root>
						<Card.Header>
							<Card.Title>Schedule Details</Card.Title>
							<Card.Description>
								Basic information about this schedule
							</Card.Description>
						</Card.Header>
						<Card.Content class="space-y-4">
							<div class="space-y-2">
								<Label for="landmark_id">Location *</Label>
								<Select.Root type="single" bind:value={$formData.landmark_id} name="landmark_id">
									<Select.Trigger>
										{data.landmarks.find(l => l.id === $formData.landmark_id)?.name ?? "Select location"}
									</Select.Trigger>
									<Select.Content>
										{#each data.landmarks as landmark}
											<Select.Item value={landmark.id} label={landmark.name}>
												{landmark.name}
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<div class="space-y-2">
								<Label for="schedule_kind">Schedule Type *</Label>
								<Select.Root type="single" bind:value={$formData.schedule_kind} name="schedule_kind">
									<Select.Trigger>
										{data.scheduleKinds.find(k => k.id === $formData.schedule_kind) ? getLocalizedText(data.scheduleKinds.find(k => k.id === $formData.schedule_kind)!.name) : "Select type"}
									</Select.Trigger>
									<Select.Content>
										{#each data.scheduleKinds as kind}
											<Select.Item value={kind.id} label={getLocalizedText(kind.name)}>
												{getLocalizedText(kind.name)}
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<div class="space-y-2">
								<Label for="name_en">Schedule Name (English) *</Label>
								<Input id="name_en" name="name.en" bind:value={$formData.name.en} placeholder="e.g., Regular Hours" />
							</div>

							<div class="space-y-2">
								<Label for="description_en">Description (English)</Label>
								<Textarea id="description_en" name="description.en" bind:value={$formData.description.en} placeholder="Optional description" />
							</div>
						</Card.Content>
					</Card.Root>
				</div>

				<!-- Weekly Schedule -->
				<div class="lg:col-span-2">
					<Card.Root>
						<Card.Header>
							<Card.Title>Weekly Schedule</Card.Title>
							<Card.Description>
								Define operating hours for each day of the week
							</Card.Description>
						</Card.Header>
						<Card.Content class="space-y-6">
							{#each data.daysOfWeek as day}
								<div class="space-y-3">
									<div class="flex items-center justify-between">
										<h3 class="text-lg font-medium">{getLocalizedText(day.name_full)}</h3>
										<Button
											type="button"
											variant="outline"
											size="sm"
											onclick={() => addPeriod(day.id)}
										>
											<Plus class="mr-2 h-4 w-4" />
											Add Period
										</Button>
									</div>

									{#if periods.filter(p => p.day_of_week === day.id).length === 0}
										<p class="text-sm text-muted-foreground">No periods defined - location is closed</p>
									{:else}
										<div class="space-y-2">
											{#each periods.filter(p => p.day_of_week === day.id) as period}
												{@const globalIndex = periods.indexOf(period)}
												<div class="flex items-center gap-2 p-3 border rounded-lg">
													<Input
														bind:value={period.period_name.en}
														placeholder="Period name (e.g., Morning)"
														class="flex-1"
													/>
													<Input
														type="time"
														bind:value={period.start_time}
														class="w-32"
													/>
													<span class="text-muted-foreground">to</span>
													<Input
														type="time"
														bind:value={period.end_time}
														class="w-32"
													/>
													<Button
														type="button"
														variant="ghost"
														size="sm"
														onclick={() => removePeriod(globalIndex)}
													>
														<Trash2 class="h-4 w-4" />
													</Button>
												</div>
											{/each}
										</div>
									{/if}
								</div>
							{/each}
						</Card.Content>
					</Card.Root>
				</div>
			</div>

			<!-- Actions -->
			<div class="flex items-center justify-end gap-2">
				<Button type="button" variant="outline" onclick={goBack}>
					Cancel
				</Button>
				<Button type="submit" disabled={$submitting}>
					{$submitting ? 'Creating...' : 'Create Schedule'}
				</Button>
			</div>

			<!-- Hidden fields for periods -->
			{#each periods as period, index}
				<input type="hidden" name="periods[{index}].day_of_week" value={period.day_of_week} />
				<input type="hidden" name="periods[{index}].period_name" value={JSON.stringify(period.period_name)} />
				<input type="hidden" name="periods[{index}].start_time" value={period.start_time} />
				<input type="hidden" name="periods[{index}].end_time" value={period.end_time} />
			{/each}
	</form>
</div>
