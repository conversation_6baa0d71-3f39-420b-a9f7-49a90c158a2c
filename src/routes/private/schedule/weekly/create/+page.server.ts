import type { PageServerLoad, Actions } from './$types';
import { error, fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { scheduleSchema } from '../../schemas';

export const load: PageServerLoad = async ({ locals: { supabase, brand } }) => {
	if (!brand?.id) {
		throw error(400, 'Brand is required');
	}

	try {
		// Load landmarks for this brand
		const { data: landmarks, error: landmarksError } = await supabase
			.from('landmark')
			.select('id, name')
			.eq('brand_id', brand.id)
			.order('name');

		if (landmarksError) {
			console.error('Error loading landmarks:', landmarksError);
			throw error(500, 'Failed to load landmarks');
		}

		// Load schedule kinds
		const { data: scheduleKinds, error: kindsError } = await supabase
			.from('schedule_kind')
			.select('id, name, description')
			.order('is_default', { ascending: false });

		if (kindsError) {
			console.error('Error loading schedule kinds:', kindsError);
		}

		// Load days of week
		const { data: daysOfWeek, error: daysError } = await supabase
			.from('day_of_week')
			.select('id, name_full, name_short, index_sun_first')
			.order('index_sun_first');

		if (daysError) {
			console.error('Error loading days of week:', daysError);
		}

		// Initialize form
		const form = await superValidate(zod(scheduleSchema));

		return {
			form,
			landmarks: landmarks || [],
			scheduleKinds: scheduleKinds || [],
			daysOfWeek: daysOfWeek || []
		};

	} catch (e) {
		console.error('Error in weekly schedule create page load:', e);
		throw error(500, 'Failed to load page data');
	}
};

export const actions: Actions = {
	default: async ({ request, locals: { supabase, brand } }) => {
		if (!brand?.id) {
			return fail(400, { message: 'Brand is required' });
		}

		const formData = await request.formData();
		const form = await superValidate(formData, zod(scheduleSchema));

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			// Parse periods from form data
			const periods: Array<{
				day_of_week: number;
				period_name: Record<string, string>;
				start_time: string;
				end_time: string;
			}> = [];

			// Extract periods from form data
			const periodEntries = Array.from(formData.entries())
				.filter(([key]) => key.startsWith('periods['))
				.reduce((acc, [key, value]) => {
					const match = key.match(/periods\[(\d+)\]\.(.+)/);
					if (match) {
						const index = parseInt(match[1]);
						const field = match[2];
						if (!acc[index]) acc[index] = {};
						if (field === 'period_name') {
							acc[index][field] = JSON.parse(value as string);
						} else if (field === 'day_of_week') {
							acc[index][field] = parseInt(value as string);
						} else {
							acc[index][field] = value as string;
						}
					}
					return acc;
				}, {} as Record<number, any>);

			Object.values(periodEntries).forEach(period => {
				if (period.day_of_week !== undefined) {
					periods.push(period);
				}
			});

			// Create the schedule
			const { data: schedule, error: scheduleError } = await supabase
				.from('schedule')
				.insert({
					landmark_id: form.data.landmark_id,
					schedule_kind: form.data.schedule_kind,
					name: form.data.name,
					description: form.data.description,
					effective_from: form.data.effective_from,
					effective_to: form.data.effective_to
				})
				.select()
				.single();

			if (scheduleError) {
				console.error('Error creating schedule:', scheduleError);
				form.errors._errors = ['Failed to create schedule: ' + scheduleError.message];
				return fail(400, { form });
			}

			// Create schedule periods
			if (periods.length > 0) {
				const periodsToInsert = periods.map(period => ({
					schedule_id: schedule.id,
					day_of_week: period.day_of_week,
					period_name: period.period_name,
					start_time: period.start_time,
					end_time: period.end_time
				}));

				const { error: periodsError } = await supabase
					.from('schedule_period')
					.insert(periodsToInsert);

				if (periodsError) {
					console.error('Error creating schedule periods:', periodsError);
					// Try to clean up the schedule
					await supabase.from('schedule').delete().eq('id', schedule.id);
					form.errors._errors = ['Failed to create schedule periods: ' + periodsError.message];
					return fail(400, { form });
				}
			}

			// Redirect to schedule list with success message
			throw redirect(303, `/private/schedule?success=${encodeURIComponent('Weekly schedule created successfully')}`);

		} catch (e) {
			if (e instanceof Response) throw e; // Re-throw redirect
			console.error('Error creating weekly schedule:', e);
			return fail(500, {
				form,
				message: 'An unexpected error occurred while creating the schedule'
			});
		}
	}
};
