-- =============================================================================
-- COMPREHENSIVE SCHEDULE MANAGEMENT SYSTEM
-- =============================================================================
-- This schema provides unified schedule management for both weekly and holiday schedules.
-- Business owners define semantic rules that apply automatically every year.

-- Note: Country data is managed in separate country.sql file

-- =============================================================================
-- SCHEDULE TYPES
-- =============================================================================

-- Types of schedules that can be defined
create table public.schedule_kind (
  id text not null,
  name jsonb not null,
  description jsonb,
  is_default boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint schedule_kind_pkey primary key (id)
) tablespace pg_default;

-- Insert schedule kinds
insert into public.schedule_kind (id, name, description, is_default) values
('regular', '{"en": "Regular Schedule", "ja": "通常スケジュール", "ko": "일반 일정", "zh": "常规时间表"}', '{"en": "Normal weekly schedule", "ja": "通常の週間スケジュール", "ko": "일반 주간 일정", "zh": "正常的每周时间表"}', true),
('reduced', '{"en": "Reduced Hours", "ja": "短縮営業", "ko": "단축 영업", "zh": "缩短营业时间"}', '{"en": "Limited operating hours", "ja": "営業時間短縮", "ko": "제한된 운영 시간", "zh": "有限的营业时间"}', false),
('closed', '{"en": "Closed", "ja": "休業", "ko": "휴업", "zh": "关闭"}', '{"en": "Business is closed", "ja": "休業", "ko": "휴업", "zh": "营业关闭"}', false)
on conflict (id) do nothing;

-- =============================================================================
-- HOLIDAY REFERENCE DATA
-- =============================================================================

-- Master holiday reference table (generic holidays that can be imported)
create table public.holiday (
  id text primary key,
  name jsonb not null,
  description jsonb,
  country_iso2 char(2) not null references public.country (iso2),
  typical_date text, -- MM-DD format for fixed holidays, or description for variable ones
  is_national boolean not null default true,
  recurrence_type text not null default 'annual', -- 'annual', 'lunar', 'variable'
  source_api text, -- e.g., "nager_date", "manual"
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  unique (country_iso2, name)
) tablespace pg_default;

-- Add a table for holiday instances (yearly occurrences) since we need it for the function
create table public.holiday_day (
  id text primary key, -- Auto-composed: "{year}-{holiday_id}" e.g., "2024-christmas"
  holiday_id text not null references public.holiday (id) on delete cascade,
  year integer not null,
  date date not null, -- Actual date for this year
  is_observed boolean not null default true, -- Whether this holiday is actually observed (some may be moved to adjacent weekdays)
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  unique (holiday_id, year)
) tablespace pg_default;

-- Auto-generate composed ID for holiday_day
create or replace function public.generate_holiday_day_id()
returns trigger
language plpgsql
as $$
begin
  -- Auto-compose ID from year and holiday_id
  new.id := new.year::text || '-' || new.holiday_id;
  return new;
end;
$$;

create trigger trigger_generate_holiday_day_id
  before insert or update on public.holiday_day
  for each row
  execute function public.generate_holiday_day_id();

-- =============================================================================
-- BUSINESS RULES FOR HOLIDAYS
-- =============================================================================

-- What a business does for specific holidays (semantic rules that apply every year)
create table public.holiday_rule (
  id uuid primary key default gen_random_uuid(),
  brand_id uuid not null references public.brand (id) on delete cascade,
  holiday_id text not null references public.holiday (id) on delete cascade,
  schedule_kind text not null references public.schedule_kind (id),
  message_to_consumer jsonb, -- Localized message for customers
  message_to_staff jsonb, -- Localized message for staff
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  unique (brand_id, holiday_id)
) tablespace pg_default;

-- Which landmarks a holiday rule applies to
create table public.holiday_rule_landmark (
  id uuid primary key default gen_random_uuid(),
  holiday_rule_id uuid not null references public.holiday_rule (id) on delete cascade,
  landmark_id uuid not null references public.landmark (id) on delete cascade,
  created_at timestamptz not null default now(),
  unique (holiday_rule_id, landmark_id)
) tablespace pg_default;

-- =============================================================================
-- SCHEDULES
-- =============================================================================

-- Schedules defined for landmarks (both regular weekly and holiday-specific)
create table public.schedule (
  id uuid primary key default gen_random_uuid(),
  landmark_id uuid not null references public.landmark (id) on delete cascade,
  schedule_kind text not null references public.schedule_kind (id),
  holiday_id text references public.holiday (id) on delete cascade, -- If not null, this is a holiday schedule
  name jsonb not null,
  description jsonb,
  effective_from date,
  effective_to date,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  check (effective_to is null or effective_from <= effective_to),
  unique (landmark_id, holiday_id) where holiday_id is not null,
  unique (landmark_id, schedule_kind) where holiday_id is null
) tablespace pg_default;

-- Time periods within schedules
create table public.schedule_period (
  id uuid primary key default gen_random_uuid(),
  schedule_id uuid not null references public.schedule (id) on delete cascade,
  day_of_week integer references public.day_of_week (id), -- null for holiday schedules
  period_name jsonb, -- Customer-facing name, localized
  start_time time,
  end_time time,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  check (start_time < end_time)
) tablespace pg_default;

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Holiday indexes
create index if not exists idx_holiday_country on public.holiday using btree (country_iso2);
create index if not exists idx_holiday_name on public.holiday using gin (name);

-- Holiday day indexes
create index if not exists idx_holiday_day_holiday_year on public.holiday_day using btree (holiday_id, year);
create index if not exists idx_holiday_day_date on public.holiday_day using btree (actual_date);

-- Business rule indexes
create index if not exists idx_holiday_rule_brand on public.holiday_rule using btree (brand_id);
create index if not exists idx_holiday_rule_holiday on public.holiday_rule using btree (holiday_id);

-- Schedule indexes
create index if not exists idx_schedule_landmark on public.schedule using btree (landmark_id);
create index if not exists idx_schedule_landmark_holiday on public.schedule using btree (landmark_id, holiday_id) where holiday_id is not null;
create index if not exists idx_schedule_landmark_weekly on public.schedule using btree (landmark_id, schedule_kind) where holiday_id is null;

-- Schedule period indexes
create index if not exists idx_schedule_period_schedule on public.schedule_period using btree (schedule_id);
create index if not exists idx_schedule_period_day on public.schedule_period using btree (schedule_id, day_of_week) where day_of_week is not null;

-- =============================================================================
-- RLS POLICIES
-- =============================================================================

-- Enable RLS
alter table public.schedule_kind enable row level security;
alter table public.holiday enable row level security;
alter table public.holiday_day enable row level security;
alter table public.holiday_rule enable row level security;
alter table public.holiday_rule_landmark enable row level security;
alter table public.schedule enable row level security;
alter table public.schedule_period enable row level security;

-- Public tables - readable by all
drop policy if exists "Public read access to schedule_kind" on public.schedule_kind;
create policy "Public read access to schedule_kind" on public.schedule_kind
  for select using (true);

drop policy if exists "Public read access to holiday" on public.holiday;
create policy "Public read access to holiday" on public.holiday
  for select using (true);

drop policy if exists "Public read access to holiday_day" on public.holiday_day;
create policy "Public read access to holiday_day" on public.holiday_day
  for select using (true);

-- Business data - brand owner isolation
drop policy if exists "Brand owner isolation for holiday_rule" on public.holiday_rule;
create policy "Brand owner isolation for holiday_rule" on public.holiday_rule
  using (
    exists (
      select 1 from public.brand b
      where b.id = holiday_rule.brand_id
      and b.owner_profile_id = auth.uid()
    )
  );

drop policy if exists "Brand owner isolation for holiday_rule_landmark" on public.holiday_rule_landmark;
create policy "Brand owner isolation for holiday_rule_landmark" on public.holiday_rule_landmark
  using (
    exists (
      select 1 from public.holiday_rule hr
      join public.brand b on b.id = hr.brand_id
      where hr.id = holiday_rule_landmark.holiday_rule_id
      and b.owner_profile_id = auth.uid()
    )
  );

drop policy if exists "Brand owner isolation for schedule" on public.schedule;
create policy "Brand owner isolation for schedule" on public.schedule
  using (
    exists (
      select 1 from public.landmark l
      join public.brand b on b.id = l.brand_id
      where l.id = schedule.landmark_id
      and b.owner_profile_id = auth.uid()
    )
  );

drop policy if exists "Brand owner isolation for schedule_period" on public.schedule_period;
create policy "Brand owner isolation for schedule_period" on public.schedule_period
  using (
    exists (
      select 1 from public.schedule s
      join public.landmark l on l.id = s.landmark_id
      join public.brand b on b.id = l.brand_id
      where s.id = schedule_period.schedule_id
      and b.owner_profile_id = auth.uid()
    )
  );

-- =============================================================================
-- UTILITY FUNCTIONS
-- =============================================================================

-- Get effective schedule for a landmark on a specific date
create or replace function public.client_get_effective_schedule(
  input_landmark_id uuid,
  input_date date
)
returns table (
  schedule_id uuid,
  schedule_kind text,
  period_name jsonb,
  start_time time,
  end_time time,
  source text
)
language plpgsql
security definer
as $$
declare
  var_holiday_rec record;
  var_dow integer;
begin
  -- Get day of week (0=Sunday, 6=Saturday)
  var_dow := extract(dow from input_date)::integer;

  -- Check if there's a holiday on this date that has a rule for this landmark
  select h.id, h.name into var_holiday_rec
  from public.holiday h
  join public.holiday_day hd on hd.holiday_id = h.id
  join public.holiday_rule hr on hr.holiday_id = h.id
  left join public.holiday_rule_landmark hrl on hrl.holiday_rule_id = hr.id
  where hr.brand_id = (select brand_id from public.landmark where id = input_landmark_id)
  and (hrl.landmark_id = input_landmark_id or hrl.landmark_id is null)
  and hd.date = input_date
  and hd.is_observed = true
  limit 1;

  -- If holiday found, return holiday schedule
  if found then
    return query
    select
      s.id as schedule_id,
      s.schedule_kind,
      sp.period_name,
      sp.start_time,
      sp.end_time,
      'holiday'::text as source
    from public.schedule s
    join public.schedule_period sp on sp.schedule_id = s.id
    where s.landmark_id = input_landmark_id
    and s.holiday_id = var_holiday_rec.id
    and (s.effective_from is null or s.effective_from <= input_date)
    and (s.effective_to is null or s.effective_to >= input_date)
    order by sp.start_time, sp.end_time;

    if found then
      return;
    end if;
  end if;

  -- Return regular weekly schedule
  return query
  select
    s.id as schedule_id,
    s.schedule_kind,
    sp.period_name,
    sp.start_time,
    sp.end_time,
    'weekly'::text as source
  from public.schedule s
  join public.schedule_period sp on sp.schedule_id = s.id
  where s.landmark_id = input_landmark_id
  and s.holiday_id is null
  and sp.day_of_week = var_dow
  and (s.effective_from is null or s.effective_from <= input_date)
  and (s.effective_to is null or s.effective_to >= input_date)
  order by sp.start_time, sp.end_time;
end;
$$;

-- Check if landmark is open at specific time
create or replace function public.client_is_landmark_open(
  input_landmark_id uuid,
  input_datetime timestamptz
)
returns boolean
language plpgsql
security definer
as $$
declare
  var_check_date date;
  var_check_time time;
  var_period_rec record;
begin
  var_check_date := input_datetime::date;
  var_check_time := input_datetime::time;

  for var_period_rec in
    select * from public.client_get_effective_schedule(input_landmark_id, var_check_date)
  loop
    if var_check_time >= var_period_rec.start_time and var_check_time <= var_period_rec.end_time then
      return true;
    end if;
  end loop;

  return false;
end;
$$;

-- Get weekly hours for a landmark for the week containing the given datetime (performance optimized)
create or replace function public.client_get_weekly_hours(
  input_landmark_id uuid,
  input_datetime timestamptz default now(), -- Week containing this datetime
  input_week_starts_sunday boolean default true -- true=Sunday first (US), false=Monday first (ISO)
)
returns table (
  day_of_week integer,
  day_name_full jsonb,
  day_name_short jsonb,
  date date,
  periods jsonb, -- Array of {period_name, start_time, end_time, schedule_kind, source}
  is_closed boolean,
  holiday_name jsonb
)
language plpgsql
security definer
as $$
declare
  var_week_start date;
  var_current_date date;
  var_dow integer;
  var_brand_id uuid;
  var_day_name_full jsonb;
  var_day_name_short jsonb;
  var_holiday_info record;
  var_periods jsonb;
  var_is_closed boolean;
  var_loop_start integer;
  var_loop_end integer;
  var_day_index integer;
begin
  -- Calculate week start based on preference
  if input_week_starts_sunday then
    -- US style: Sunday = start of week
    var_week_start := date_trunc('week', input_datetime)::date;
    var_loop_start := 0; -- Sunday
    var_loop_end := 6;   -- Saturday
  else
    -- ISO style: Monday = start of week
    var_week_start := date_trunc('week', input_datetime)::date + interval '1 day';
    var_loop_start := 1; -- Monday
    var_loop_end := 7;   -- Sunday (next week's Sunday)
  end if;

  -- Get brand_id once for performance
  select brand_id into var_brand_id
  from public.landmark
  where id = input_landmark_id;

  -- Loop through each day of the week
  for var_day_index in var_loop_start..var_loop_end loop
    -- Handle day of week calculation
    if input_week_starts_sunday then
      var_dow := var_day_index;
      var_current_date := var_week_start + var_day_index;
    else
      -- For Monday-first, map 1-7 to 1-6,0 (Mon-Sat,Sun)
      var_dow := case when var_day_index = 7 then 0 else var_day_index end;
      var_current_date := var_week_start + (var_day_index - 1);
    end if;

    -- Get day names (localized)
    select name_full, name_short into var_day_name_full, var_day_name_short
    from public.day_of_week
    where index_sun_first = var_dow;

    -- Check for holiday on this date
    select
      h.name as holiday_name,
      hr.schedule_kind
    into var_holiday_info
    from public.holiday h
    join public.holiday_day hd on hd.holiday_id = h.id
    join public.holiday_rule hr on hr.holiday_id = h.id
    left join public.holiday_rule_landmark hrl on hrl.holiday_rule_id = hr.id
    where hr.brand_id = var_brand_id
    and (hrl.landmark_id = input_landmark_id or hrl.landmark_id is null)
    and hd.date = var_current_date
    and hd.is_observed = true
    limit 1;

    -- Get periods for this day
    if var_holiday_info.holiday_name is not null then
      -- Holiday schedule
      select
        coalesce(
          jsonb_agg(
            jsonb_build_object(
              'period_name', sp.period_name,
              'start_time', sp.start_time::text,
              'end_time', sp.end_time::text,
              'schedule_kind', s.schedule_kind,
              'source', 'holiday'
            ) order by sp.start_time, sp.end_time
          ),
          '[]'::jsonb
        ),
        case when s.schedule_kind = 'closed' then true else false end
      into var_periods, var_is_closed
      from public.schedule s
      left join public.schedule_period sp on sp.schedule_id = s.id
      where s.landmark_id = input_landmark_id
      and s.holiday_id = (select holiday_id from public.holiday_day where date = var_current_date limit 1)
      and (s.effective_from is null or s.effective_from <= var_current_date)
      and (s.effective_to is null or s.effective_to >= var_current_date);
    else
      -- Regular weekly schedule
      select
        coalesce(
          jsonb_agg(
            jsonb_build_object(
              'period_name', sp.period_name,
              'start_time', sp.start_time::text,
              'end_time', sp.end_time::text,
              'schedule_kind', s.schedule_kind,
              'source', 'weekly'
            ) order by sp.start_time, sp.end_time
          ),
          '[]'::jsonb
        ),
        case when s.schedule_kind = 'closed' then true else false end
      into var_periods, var_is_closed
      from public.schedule s
      left join public.schedule_period sp on sp.schedule_id = s.id
      where s.landmark_id = input_landmark_id
      and s.holiday_id is null
      and sp.day_of_week = var_dow
      and (s.effective_from is null or s.effective_from <= var_current_date)
      and (s.effective_to is null or s.effective_to >= var_current_date);
    end if;

    -- Default to closed if no schedule found
    if var_periods is null then
      var_periods := '[]'::jsonb;
      var_is_closed := true;
    end if;

    -- Return row for this day
    return next (
      var_dow,
      var_day_name_full,
      var_day_name_short,
      var_current_date,
      var_periods,
      var_is_closed,
      var_holiday_info.holiday_name
    );

    -- Reset variables
    var_holiday_info := null;
    var_periods := null;
    var_is_closed := false;
    var_day_name_full := null;
    var_day_name_short := null;
  end loop;
end;
$$;

-- =============================================================================
-- UNIQUE CONSTRAINTS
-- =============================================================================

-- Ensure unique weekly schedule per landmark and kind (already handled by table constraint)
-- unique (landmark_id, schedule_kind) where holiday_id is null

-- Ensure unique holiday schedule per landmark and holiday (already handled by table constraint)
-- unique (landmark_id, holiday_id) where holiday_id is not null

-- Ensure no duplicate time periods within the same schedule and day
create unique index if not exists idx_schedule_period_no_duplicates
on public.schedule_period (schedule_id, day_of_week, start_time, end_time)
where day_of_week is not null;

-- =============================================================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================================================

comment on table public.holiday is 'Master holiday data populated from external APIs';
comment on table public.holiday_day is 'Yearly instances of holidays with calculated dates';
comment on table public.holiday_rule is 'Business rules defining what to do for specific holidays';
comment on table public.schedule is 'Schedules defined for landmarks (weekly and holiday-specific)';
comment on table public.schedule_period is 'Time periods within schedules for operating hours';
comment on function public.client_get_effective_schedule is 'Returns effective schedule for a landmark on a specific date';
comment on function public.client_is_landmark_open is 'Checks if a landmark is open at a specific time';
comment on function public.client_get_weekly_hours is 'Returns optimized weekly hours display for week containing given datetime, supports Sunday-first or Monday-first weeks';

-- =============================================================================
-- HOLIDAY OBSERVANCE DESIGN EXPLANATION
-- =============================================================================
/*
HOLIDAY OBSERVANCE DESIGN:

GLOBAL LEVEL (holiday_day.is_observed):
- Indicates whether the holiday is officially observed on this date by the government/region
- Handles moved holidays (e.g., Christmas on Sunday observed on Monday)
- Data comes from external APIs (Nager.Date, government sources)
- Shared across ALL businesses - this is factual calendar data

BUSINESS LEVEL (holiday_rule existence):
- Whether a specific business observes this holiday is determined by having a holiday_rule
- No holiday_rule = business ignores this holiday (stays open normally)
- Has holiday_rule = business follows the rule (closed, reduced hours, etc.)

EXAMPLES:
1. Christmas 2024:
   - holiday_day: is_observed=true (government observes it)
   - Business A: has holiday_rule (closes for Christmas)
   - Business B: no holiday_rule (international business, stays open)

2. Presidents Day 2024:
   - holiday_day: is_observed=true (US government observes it)
   - Business A: has holiday_rule (bank, follows federal holidays)
   - Business B: no holiday_rule (restaurant, stays open)

3. Christmas on Sunday (hypothetical):
   - holiday_day '2024-12-22': is_observed=false (actual date, not observed)
   - holiday_day '2024-12-23': is_observed=true (observed on Monday)
   - Business rules apply only to the observed date

LOGIC:
- Functions check: holiday_day.is_observed=true AND holiday_rule exists
- This separates "calendar facts" from "business decisions"
- Clean separation of concerns: global calendar vs business policy

WEEK START OPTIONS (client_get_weekly_hours):
- input_week_starts_sunday=true: US style (Sun, Mon, Tue, Wed, Thu, Fri, Sat)
- input_week_starts_sunday=false: ISO style (Mon, Tue, Wed, Thu, Fri, Sat, Sun)
- Returns days in the specified order for consistent UI display
*/