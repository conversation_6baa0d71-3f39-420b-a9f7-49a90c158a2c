-- =============================================================================
-- COMPREHENSIVE SCHEDULE MANAGEMENT SYSTEM
-- =============================================================================
-- This schema provides unified schedule management for both weekly and holiday schedules.
-- Business owners define semantic rules that apply automatically every year.

-- Note: Country data is managed in separate country.sql file

-- =============================================================================
-- SCHEDULE TYPES
-- =============================================================================

-- Types of schedules that can be defined
create table public.schedule_kind (
  id text not null,
  name jsonb not null,
  description jsonb,
  is_default boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint schedule_kind_pkey primary key (id)
) tablespace pg_default;

-- Insert schedule kinds
insert into public.schedule_kind (id, name, description, is_default) values
('regular', '{"en": "Regular Schedule", "ja": "通常スケジュール", "ko": "일반 일정", "zh": "常规时间表"}', '{"en": "Normal weekly schedule", "ja": "通常の週間スケジュール", "ko": "일반 주간 일정", "zh": "正常的每周时间表"}', true),
('reduced', '{"en": "Reduced Hours", "ja": "短縮営業", "ko": "단축 영업", "zh": "缩短营业时间"}', '{"en": "Limited operating hours", "ja": "営業時間短縮", "ko": "제한된 운영 시간", "zh": "有限的营业时间"}', false),
('closed', '{"en": "Closed", "ja": "休業", "ko": "휴업", "zh": "关闭"}', '{"en": "Business is closed", "ja": "休業", "ko": "휴업", "zh": "营业关闭"}', false)
on conflict (id) do nothing;

-- =============================================================================
-- HOLIDAY REFERENCE DATA
-- =============================================================================

-- Master holiday reference table (generic holidays that can be imported)
create table public.holiday (
  id text primary key,
  name jsonb not null,
  description jsonb,
  country_iso2 char(2) not null references public.country (iso2),
  typical_date text, -- MM-DD format for fixed holidays, or description for variable ones
  is_national boolean not null default true,
  recurrence_type text not null default 'annual', -- 'annual', 'lunar', 'variable'
  source_api text, -- e.g., "nager_date", "manual"
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  unique (country_iso2, name)
) tablespace pg_default;

-- Add a table for holiday instances (yearly occurrences) since we need it for the function
create table public.holiday_day (
  id text primary key, -- Auto-composed: "{year}-{holiday_id}" e.g., "2024-christmas"
  holiday_id text not null references public.holiday (id) on delete cascade,
  year integer not null,
  date date not null, -- Actual date for this year
  is_observed boolean not null default true, -- Whether this holiday is actually observed (some may be moved to adjacent weekdays)
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  unique (holiday_id, year)
) tablespace pg_default;

-- Auto-generate composed ID for holiday_day
create or replace function public.generate_holiday_day_id()
returns trigger
language plpgsql
as $$
begin
  -- Auto-compose ID from year and holiday_id
  new.id := new.year::text || '-' || new.holiday_id;
  return new;
end;
$$;

create trigger trigger_generate_holiday_day_id
  before insert or update on public.holiday_day
  for each row
  execute function public.generate_holiday_day_id();

-- Function to generate schedules from holiday rules for a specific year
create or replace function public.generate_holiday_schedules(
  input_brand_id uuid,
  input_year integer
)
returns table (
  generated_count integer,
  skipped_count integer,
  schedule_ids uuid[]
)
language plpgsql
security definer
as $$
declare
  var_rule record;
  var_holiday_day record;
  var_landmark record;
  var_schedule_id uuid;
  var_generated_count integer := 0;
  var_skipped_count integer := 0;
  var_schedule_ids uuid[] := '{}';
  var_start_date date;
  var_end_date date;
begin
  -- Loop through all active holiday rules for this brand
  for var_rule in
    select hr.*, h.id as holiday_id
    from public.holiday_rule hr
    join public.holiday h on h.id = hr.holiday_id
    where hr.brand_id = input_brand_id
    and hr.is_active = true
    and hr.auto_generate = true
  loop
    -- Get the holiday day for this year
    select * into var_holiday_day
    from public.holiday_day hd
    where hd.holiday_id = var_rule.holiday_id
    and hd.year = input_year
    and hd.is_observed = true;

    if var_holiday_day.id is not null then
      -- Determine which landmarks this rule applies to
      if exists (select 1 from public.holiday_rule_landmark where holiday_rule_id = var_rule.id) then
        -- Rule applies to specific landmarks
        for var_landmark in
          select l.* from public.landmark l
          join public.holiday_rule_landmark hrl on hrl.landmark_id = l.id
          where hrl.holiday_rule_id = var_rule.id
          and l.brand_id = input_brand_id
        loop
          -- Calculate effective dates
          if var_rule.apply_to_full_week then
            var_start_date := date_trunc('week', var_holiday_day.date)::date;
            var_end_date := (date_trunc('week', var_holiday_day.date)::date + interval '6 days')::date;
          else
            var_start_date := var_holiday_day.date - var_rule.days_before_holiday;
            var_end_date := var_holiday_day.date + var_rule.days_after_holiday;
          end if;

          -- Check if schedule already exists
          if not exists (
            select 1 from public.schedule s
            where s.landmark_id = var_landmark.id
            and s.holiday_day_id = var_holiday_day.id
          ) then
            -- Create schedule
            insert into public.schedule (
              landmark_id,
              schedule_kind,
              holiday_day_id,
              holiday_rule_id,
              name,
              description,
              message_to_consumer,
              message_to_staff,
              effective_from,
              effective_to,
              is_generated
            ) values (
              var_landmark.id,
              var_rule.schedule_kind,
              var_holiday_day.id,
              var_rule.id,
              jsonb_build_object('en', var_holiday_day.holiday_id || ' ' || input_year),
              jsonb_build_object('en', 'Generated from holiday rule'),
              var_rule.default_message_to_consumer,
              var_rule.default_message_to_staff,
              var_start_date,
              var_end_date,
              true
            ) returning id into var_schedule_id;

            var_generated_count := var_generated_count + 1;
            var_schedule_ids := var_schedule_ids || var_schedule_id;
          else
            var_skipped_count := var_skipped_count + 1;
          end if;
        end loop;
      else
        -- Rule applies to all landmarks in brand
        for var_landmark in
          select l.* from public.landmark l
          where l.brand_id = input_brand_id
        loop
          -- Calculate effective dates
          if var_rule.apply_to_full_week then
            var_start_date := date_trunc('week', var_holiday_day.date)::date;
            var_end_date := (date_trunc('week', var_holiday_day.date)::date + interval '6 days')::date;
          else
            var_start_date := var_holiday_day.date - var_rule.days_before_holiday;
            var_end_date := var_holiday_day.date + var_rule.days_after_holiday;
          end if;

          -- Check if schedule already exists
          if not exists (
            select 1 from public.schedule s
            where s.landmark_id = var_landmark.id
            and s.holiday_day_id = var_holiday_day.id
          ) then
            -- Create schedule
            insert into public.schedule (
              landmark_id,
              schedule_kind,
              holiday_day_id,
              holiday_rule_id,
              name,
              description,
              message_to_consumer,
              message_to_staff,
              effective_from,
              effective_to,
              is_generated
            ) values (
              var_landmark.id,
              var_rule.schedule_kind,
              var_holiday_day.id,
              var_rule.id,
              jsonb_build_object('en', var_holiday_day.holiday_id || ' ' || input_year),
              jsonb_build_object('en', 'Generated from holiday rule'),
              var_rule.default_message_to_consumer,
              var_rule.default_message_to_staff,
              var_start_date,
              var_end_date,
              true
            ) returning id into var_schedule_id;

            var_generated_count := var_generated_count + 1;
            var_schedule_ids := var_schedule_ids || var_schedule_id;
          else
            var_skipped_count := var_skipped_count + 1;
          end if;
        end loop;
      end if;
    end if;
  end loop;

  return query select var_generated_count, var_skipped_count, var_schedule_ids;
end;
$$;

-- =============================================================================
-- BUSINESS RULES FOR HOLIDAYS
-- =============================================================================

-- Holiday rules as templates/generators for creating yearly schedules
create table public.holiday_rule (
  id uuid primary key default gen_random_uuid(),
  brand_id uuid not null references public.brand (id) on delete cascade,
  holiday_id text not null references public.holiday (id) on delete cascade,
  schedule_kind text not null references public.schedule_kind (id),

  -- Template settings for generating schedules
  days_before_holiday integer default 0, -- How many days before holiday
  days_after_holiday integer default 0,  -- How many days after holiday
  apply_to_full_week boolean default false, -- If true, apply to entire week containing holiday

  -- Default messages (can be overridden in generated schedules)
  default_message_to_consumer jsonb,
  default_message_to_staff jsonb,

  -- Rule metadata
  is_active boolean default true, -- Can be disabled without deleting
  auto_generate boolean default true, -- Automatically generate schedules for new years

  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  unique (brand_id, holiday_id)
) tablespace pg_default;

-- Which landmarks a holiday rule applies to (if null = all landmarks)
create table public.holiday_rule_landmark (
  id uuid primary key default gen_random_uuid(),
  holiday_rule_id uuid not null references public.holiday_rule (id) on delete cascade,
  landmark_id uuid not null references public.landmark (id) on delete cascade,
  created_at timestamptz not null default now(),
  unique (holiday_rule_id, landmark_id)
) tablespace pg_default;

-- =============================================================================
-- SCHEDULES
-- =============================================================================

-- Schedules defined for landmarks (both regular weekly and holiday-specific)
create table public.schedule (
  id uuid primary key default gen_random_uuid(),
  landmark_id uuid not null references public.landmark (id) on delete cascade,
  schedule_kind text not null references public.schedule_kind (id),

  -- Holiday schedule reference (specific yearly instance)
  holiday_day_id text references public.holiday_day (id) on delete cascade, -- If not null, this is a holiday schedule
  holiday_rule_id uuid references public.holiday_rule (id) on delete set null, -- Template that generated this (can be null for manual schedules)

  -- Schedule content
  name jsonb not null,
  description jsonb,
  message_to_consumer jsonb, -- Localized message for customers
  message_to_staff jsonb, -- Localized message for staff

  -- Timing
  effective_from date, -- Start date (for regular schedules) or calculated start date (for holiday schedules)
  effective_to date,   -- End date (for regular schedules) or calculated end date (for holiday schedules)

  -- Metadata
  is_generated boolean default false, -- True if generated from holiday_rule, false if manually created

  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),

  -- Constraints
  check (effective_to is null or effective_from <= effective_to),
  check (
    (holiday_day_id is null and effective_from is not null) or
    (holiday_day_id is not null and effective_from is not null)
  ), -- Both types need effective dates, but holiday schedules calculate them from holiday_day + rule

  unique (landmark_id, holiday_day_id) where holiday_day_id is not null,
  unique (landmark_id, schedule_kind, effective_from) where holiday_day_id is null
) tablespace pg_default;

-- Time periods within schedules
create table public.schedule_period (
  id uuid primary key default gen_random_uuid(),
  schedule_id uuid not null references public.schedule (id) on delete cascade,
  day_of_week integer references public.day_of_week (id), -- null for holiday schedules
  period_name jsonb, -- Customer-facing name, localized
  start_time time,
  end_time time,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  check (start_time < end_time)
) tablespace pg_default;

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Holiday indexes
create index if not exists idx_holiday_country on public.holiday using btree (country_iso2);
create index if not exists idx_holiday_name on public.holiday using gin (name);

-- Holiday day indexes
create index if not exists idx_holiday_day_holiday_year on public.holiday_day using btree (holiday_id, year);
create index if not exists idx_holiday_day_date on public.holiday_day using btree (actual_date);

-- Business rule indexes
create index if not exists idx_holiday_rule_brand on public.holiday_rule using btree (brand_id);
create index if not exists idx_holiday_rule_holiday on public.holiday_rule using btree (holiday_id);

-- Schedule indexes
create index if not exists idx_schedule_landmark on public.schedule using btree (landmark_id);
create index if not exists idx_schedule_landmark_holiday on public.schedule using btree (landmark_id, holiday_id) where holiday_id is not null;
create index if not exists idx_schedule_landmark_weekly on public.schedule using btree (landmark_id, schedule_kind) where holiday_id is null;

-- Schedule period indexes
create index if not exists idx_schedule_period_schedule on public.schedule_period using btree (schedule_id);
create index if not exists idx_schedule_period_day on public.schedule_period using btree (schedule_id, day_of_week) where day_of_week is not null;

-- =============================================================================
-- RLS POLICIES
-- =============================================================================

-- Enable RLS
alter table public.schedule_kind enable row level security;
alter table public.holiday enable row level security;
alter table public.holiday_day enable row level security;
alter table public.holiday_rule enable row level security;
alter table public.holiday_rule_landmark enable row level security;
alter table public.schedule enable row level security;
alter table public.schedule_period enable row level security;

-- Public tables - readable by all
drop policy if exists "Public read access to schedule_kind" on public.schedule_kind;
create policy "Public read access to schedule_kind" on public.schedule_kind
  for select using (true);

drop policy if exists "Public read access to holiday" on public.holiday;
create policy "Public read access to holiday" on public.holiday
  for select using (true);

drop policy if exists "Public read access to holiday_day" on public.holiday_day;
create policy "Public read access to holiday_day" on public.holiday_day
  for select using (true);

-- Business data - brand owner isolation
drop policy if exists "Brand owner isolation for holiday_rule" on public.holiday_rule;
create policy "Brand owner isolation for holiday_rule" on public.holiday_rule
  using (
    exists (
      select 1 from public.brand b
      where b.id = holiday_rule.brand_id
      and b.owner_profile_id = auth.uid()
    )
  );

drop policy if exists "Brand owner isolation for holiday_rule_landmark" on public.holiday_rule_landmark;
create policy "Brand owner isolation for holiday_rule_landmark" on public.holiday_rule_landmark
  using (
    exists (
      select 1 from public.holiday_rule hr
      join public.brand b on b.id = hr.brand_id
      where hr.id = holiday_rule_landmark.holiday_rule_id
      and b.owner_profile_id = auth.uid()
    )
  );

drop policy if exists "Brand owner isolation for schedule" on public.schedule;
create policy "Brand owner isolation for schedule" on public.schedule
  using (
    exists (
      select 1 from public.landmark l
      join public.brand b on b.id = l.brand_id
      where l.id = schedule.landmark_id
      and b.owner_profile_id = auth.uid()
    )
  );

drop policy if exists "Brand owner isolation for schedule_period" on public.schedule_period;
create policy "Brand owner isolation for schedule_period" on public.schedule_period
  using (
    exists (
      select 1 from public.schedule s
      join public.landmark l on l.id = s.landmark_id
      join public.brand b on b.id = l.brand_id
      where s.id = schedule_period.schedule_id
      and b.owner_profile_id = auth.uid()
    )
  );

-- =============================================================================
-- UTILITY FUNCTIONS
-- =============================================================================

-- Get effective schedule for a landmark on a specific date
create or replace function public.client_get_effective_schedule(
  input_landmark_id uuid,
  input_date date
)
returns table (
  schedule_id uuid,
  schedule_kind text,
  period_name jsonb,
  start_time time,
  end_time time,
  source text
)
language plpgsql
security definer
as $$
declare
  var_holiday_rec record;
  var_dow integer;
begin
  -- Get day of week (0=Sunday, 6=Saturday)
  var_dow := extract(dow from input_date)::integer;

  -- Check if there's a holiday schedule for this landmark and date
  select h.id, h.name into var_holiday_rec
  from public.holiday h
  join public.holiday_day hd on hd.holiday_id = h.id
  join public.schedule s on s.holiday_day_id = hd.id
  where s.landmark_id = input_landmark_id
  and hd.is_observed = true
  and input_date between s.effective_from and s.effective_to
  limit 1;

  -- If holiday found, return holiday schedule
  if found then
    return query
    select
      s.id as schedule_id,
      s.schedule_kind,
      sp.period_name,
      sp.start_time,
      sp.end_time,
      'holiday'::text as source
    from public.schedule s
    join public.schedule_period sp on sp.schedule_id = s.id
    join public.holiday_day hd on hd.id = s.holiday_day_id
    where s.landmark_id = input_landmark_id
    and hd.holiday_id = var_holiday_rec.id
    and input_date between s.effective_from and s.effective_to
    order by sp.start_time, sp.end_time;

    if found then
      return;
    end if;
  end if;

  -- Return regular weekly schedule
  return query
  select
    s.id as schedule_id,
    s.schedule_kind,
    sp.period_name,
    sp.start_time,
    sp.end_time,
    'weekly'::text as source
  from public.schedule s
  join public.schedule_period sp on sp.schedule_id = s.id
  where s.landmark_id = input_landmark_id
  and s.holiday_day_id is null
  and sp.day_of_week = var_dow
  and (s.effective_from is null or s.effective_from <= input_date)
  and (s.effective_to is null or s.effective_to >= input_date)
  order by sp.start_time, sp.end_time;
end;
$$;

-- Check if landmark is open at specific time
create or replace function public.client_is_landmark_open(
  input_landmark_id uuid,
  input_datetime timestamptz
)
returns boolean
language plpgsql
security definer
as $$
declare
  var_check_date date;
  var_check_time time;
  var_period_rec record;
begin
  var_check_date := input_datetime::date;
  var_check_time := input_datetime::time;

  for var_period_rec in
    select * from public.client_get_effective_schedule(input_landmark_id, var_check_date)
  loop
    if var_check_time >= var_period_rec.start_time and var_check_time <= var_period_rec.end_time then
      return true;
    end if;
  end loop;

  return false;
end;
$$;

-- Get weekly hours for a landmark for the week containing the given datetime (performance optimized)
create or replace function public.client_get_weekly_hours(
  input_landmark_id uuid,
  input_datetime timestamptz default now(), -- Week containing this datetime
  input_week_starts_sunday boolean default true -- true=Sunday first (US), false=Monday first (ISO)
)
returns table (
  day_of_week integer,
  day_name_full jsonb,
  day_name_short jsonb,
  date date,
  periods jsonb, -- Array of {period_name, start_time, end_time, schedule_kind, source}
  is_closed boolean,
  holiday_name jsonb
)
language plpgsql
security definer
as $$
declare
  var_week_start date;
  var_current_date date;
  var_dow integer;
  var_brand_id uuid;
  var_day_name_full jsonb;
  var_day_name_short jsonb;
  var_holiday_info record;
  var_periods jsonb;
  var_is_closed boolean;
  var_loop_start integer;
  var_loop_end integer;
  var_day_index integer;
begin
  -- Calculate week start based on preference
  if input_week_starts_sunday then
    -- US style: Sunday = start of week
    var_week_start := date_trunc('week', input_datetime)::date;
    var_loop_start := 0; -- Sunday
    var_loop_end := 6;   -- Saturday
  else
    -- ISO style: Monday = start of week
    var_week_start := date_trunc('week', input_datetime)::date + interval '1 day';
    var_loop_start := 1; -- Monday
    var_loop_end := 7;   -- Sunday (next week's Sunday)
  end if;

  -- Get brand_id once for performance
  select brand_id into var_brand_id
  from public.landmark
  where id = input_landmark_id;

  -- Loop through each day of the week
  for var_day_index in var_loop_start..var_loop_end loop
    -- Handle day of week calculation
    if input_week_starts_sunday then
      var_dow := var_day_index;
      var_current_date := var_week_start + var_day_index;
    else
      -- For Monday-first, map 1-7 to 1-6,0 (Mon-Sat,Sun)
      var_dow := case when var_day_index = 7 then 0 else var_day_index end;
      var_current_date := var_week_start + (var_day_index - 1);
    end if;

    -- Get day names (localized)
    select name_full, name_short into var_day_name_full, var_day_name_short
    from public.day_of_week
    where index_sun_first = var_dow;

    -- Check for holiday schedule on this date
    select
      h.name as holiday_name,
      s.schedule_kind
    into var_holiday_info
    from public.holiday h
    join public.holiday_day hd on hd.holiday_id = h.id
    join public.schedule s on s.holiday_day_id = hd.id
    where s.landmark_id = input_landmark_id
    and hd.is_observed = true
    and var_current_date between s.effective_from and s.effective_to
    limit 1;

    -- Get periods for this day
    if var_holiday_info.holiday_name is not null then
      -- Holiday schedule
      select
        coalesce(
          jsonb_agg(
            jsonb_build_object(
              'period_name', sp.period_name,
              'start_time', sp.start_time::text,
              'end_time', sp.end_time::text,
              'schedule_kind', s.schedule_kind,
              'source', 'holiday'
            ) order by sp.start_time, sp.end_time
          ),
          '[]'::jsonb
        ),
        case when s.schedule_kind = 'closed' then true else false end
      into var_periods, var_is_closed
      from public.schedule s
      left join public.schedule_period sp on sp.schedule_id = s.id
      where s.id = (
        select s2.id from public.schedule s2
        join public.holiday_day hd on hd.id = s2.holiday_day_id
        where s2.landmark_id = input_landmark_id
        and s2.holiday_day_id is not null
        and hd.is_observed = true
        and var_current_date between s2.effective_from and s2.effective_to
        limit 1
      );
    else
      -- Regular weekly schedule
      select
        coalesce(
          jsonb_agg(
            jsonb_build_object(
              'period_name', sp.period_name,
              'start_time', sp.start_time::text,
              'end_time', sp.end_time::text,
              'schedule_kind', s.schedule_kind,
              'source', 'weekly'
            ) order by sp.start_time, sp.end_time
          ),
          '[]'::jsonb
        ),
        case when s.schedule_kind = 'closed' then true else false end
      into var_periods, var_is_closed
      from public.schedule s
      left join public.schedule_period sp on sp.schedule_id = s.id
      where s.landmark_id = input_landmark_id
      and s.holiday_day_id is null
      and sp.day_of_week = var_dow
      and (s.effective_from is null or s.effective_from <= var_current_date)
      and (s.effective_to is null or s.effective_to >= var_current_date);
    end if;

    -- Default to closed if no schedule found
    if var_periods is null then
      var_periods := '[]'::jsonb;
      var_is_closed := true;
    end if;

    -- Return row for this day
    return next (
      var_dow,
      var_day_name_full,
      var_day_name_short,
      var_current_date,
      var_periods,
      var_is_closed,
      var_holiday_info.holiday_name
    );

    -- Reset variables
    var_holiday_info := null;
    var_periods := null;
    var_is_closed := false;
    var_day_name_full := null;
    var_day_name_short := null;
  end loop;
end;
$$;

-- =============================================================================
-- UNIQUE CONSTRAINTS
-- =============================================================================

-- Ensure unique weekly schedule per landmark and kind (already handled by table constraint)
-- unique (landmark_id, schedule_kind) where holiday_id is null

-- Ensure unique holiday schedule per landmark and holiday (already handled by table constraint)
-- unique (landmark_id, holiday_id) where holiday_id is not null

-- Ensure no duplicate time periods within the same schedule and day
create unique index if not exists idx_schedule_period_no_duplicates
on public.schedule_period (schedule_id, day_of_week, start_time, end_time)
where day_of_week is not null;

-- =============================================================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================================================

comment on table public.holiday is 'Master holiday data populated from external APIs';
comment on table public.holiday_day is 'Yearly instances of holidays with calculated dates';
comment on table public.schedule is 'Unified schedules for landmarks (weekly and holiday-specific with relative offsets)';
comment on table public.schedule_period is 'Time periods within schedules for operating hours';
comment on function public.client_get_effective_schedule is 'Returns effective schedule for a landmark on a specific date';
comment on function public.client_is_landmark_open is 'Checks if a landmark is open at a specific time';
comment on function public.client_get_weekly_hours is 'Returns optimized weekly hours display for week containing given datetime, supports Sunday-first or Monday-first weeks';

-- =============================================================================
-- HOLIDAY OBSERVANCE DESIGN EXPLANATION
-- =============================================================================
/*
HOLIDAY OBSERVANCE DESIGN:

GLOBAL LEVEL (holiday_day.is_observed):
- Indicates whether the holiday is officially observed on this date by the government/region
- Handles moved holidays (e.g., Christmas on Sunday observed on Monday)
- Data comes from external APIs (Nager.Date, government sources)
- Shared across ALL businesses - this is factual calendar data

BUSINESS LEVEL (schedule with holiday_id):
- Whether a specific business observes this holiday is determined by having a schedule with holiday_id
- No holiday schedule = business ignores this holiday (stays open normally)
- Has holiday schedule = business follows the schedule (closed, reduced hours, etc.)
- Supports relative offsets (days_before/after_holiday) and full week shutdowns

EXAMPLES:
1. Simple Christmas Closure:
   - holiday_day: is_observed=true (government observes it)
   - Business A: has schedule with holiday_id='christmas', days_before=0, days_after=0
   - Business B: no holiday schedule (international business, stays open)

2. Christmas Long Weekend:
   - holiday_day: Christmas on Wednesday, is_observed=true
   - Business: schedule with holiday_id='christmas', days_before=1, days_after=1
   - Result: Closed Tuesday, Wednesday, Thursday (3-day weekend)

3. Factory Week Shutdown:
   - holiday_day: Christmas on any day, is_observed=true
   - Factory: schedule with holiday_id='christmas', apply_to_full_week=true
   - Result: Closed entire week containing Christmas (Monday-Sunday)

4. Thanksgiving Week (US):
   - holiday_day: Thanksgiving Thursday, is_observed=true
   - Business: schedule with holiday_id='thanksgiving', days_before=0, days_after=1
   - Result: Closed Thursday-Friday (Thanksgiving + Black Friday)

LOGIC:
- Functions check: holiday_day.is_observed=true AND schedule with holiday_id exists
- Relative offsets make rules work every year regardless of what day holiday falls on
- Full week option supports manufacturing/industrial shutdown practices
- Clean separation of concerns: global calendar vs business policy

WEEK START OPTIONS (client_get_weekly_hours):
- input_week_starts_sunday=true: US style (Sun, Mon, Tue, Wed, Thu, Fri, Sat)
- input_week_starts_sunday=false: ISO style (Mon, Tue, Wed, Thu, Fri, Sat, Sun)
- Returns days in the specified order for consistent UI display
*/