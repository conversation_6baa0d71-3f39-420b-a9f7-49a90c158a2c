<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import * as Select from '$lib/components/ui/select';
	import * as Card from '$lib/components/ui/card';
	import * as Alert from '$lib/components/ui/alert';
	import * as Dialog from '$lib/components/ui/dialog';
	import { PageContainer } from '$lib/components/layout';
	import DataTable from '$lib/components/shared/DataTable.svelte';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { importHolidaySchema } from '../schemas';
	import { Label } from '$lib/components/ui/label';
	import type { ColumnDef } from '@tanstack/table-core';
	import {
		createSortableColumn,
		createDateColumn
	} from '$lib/components/shared/table-utils';
	import { getLocalizedText } from '$lib/utils/localization';
	import CalendarDays from '@lucide/svelte/icons/calendar-days';
	import Download from '@lucide/svelte/icons/download';
	import ArrowLeft from '@lucide/svelte/icons/arrow-left';
	import Globe from '@lucide/svelte/icons/globe';
	import type { Holiday } from '../types';

	interface Props {
		data: {
			holidays: Holiday[];
			availableCountries: string[];
			selectedYear: number;
			selectedCountry: string;
			importForm: any;
		};
	}

	let { data }: Props = $props();

	// Form handling
	const form = superForm(data.importForm, {
		validators: zodClient(importHolidaySchema),
		resetForm: false
	});

	const { form: formData, enhance, errors, submitting } = form;

	// State management
	let showImportDialog = $state(false);
	let selectedYear = $state(data.selectedYear);
	let selectedCountry = $state(data.selectedCountry);
	let message = $state<{ type: 'success' | 'error'; text: string } | null>(null);

	// Handle success message from URL parameters
	$effect(() => {
		const successMessage = page.url.searchParams.get('success');
		if (successMessage) {
			message = { type: 'success', text: decodeURIComponent(successMessage) };
			// Remove the success parameter from URL
			const url = new URL(window.location.href);
			url.searchParams.delete('success');
			window.history.replaceState({}, '', url.toString());
		}
	});

	// Year options
	const currentYear = new Date().getFullYear();
	const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i).map((year) => ({
		value: year.toString(),
		label: year.toString()
	}));

	// Country options
	const countryOptions = [
		{ value: '', label: 'All Countries' },
		...data.availableCountries.map((code: string) => ({
			value: code,
			label: code
		}))
	];

	// Navigation functions
	function goBack() {
		goto('/private/holiday');
	}

	function handleYearChange(value: string | undefined) {
		if (value !== undefined) {
			selectedYear = parseInt(value) || currentYear;
			updateFilters();
		}
	}

	function handleCountryChange(value: string | undefined) {
		selectedCountry = value || '';
		updateFilters();
	}

	function updateFilters() {
		const url = new URL(window.location.href);
		url.searchParams.set('year', selectedYear.toString());
		if (selectedCountry) {
			url.searchParams.set('country', selectedCountry);
		} else {
			url.searchParams.delete('country');
		}
		goto(url.toString(), { replaceState: true });
	}

	function openImportDialog() {
		// Pre-fill form with current filters
		$formData.year = selectedYear;
		$formData.country_iso2 = selectedCountry || 'US';
		showImportDialog = true;
	}

	// Table columns
	const columns: ColumnDef<Holiday, any>[] = [
		createSortableColumn('name', 'Holiday Name', (row) => getLocalizedText(row.name)),
		createSortableColumn('typical_date', 'Date', (row) => row.typical_date),
		createSortableColumn('country_iso2', 'Country', (row) => row.country_iso2.toUpperCase()),
		createSortableColumn('is_national', 'Type', (row) => row.is_national ? 'National' : 'Regional'),
		createSortableColumn('source_api', 'Source', (row) => row.source_api || 'Manual'),
		createSortableColumn('recurrence_type', 'Recurrence', (row) => row.recurrence_type || 'Annual')
	];

	// Clear message after 5 seconds
	$effect(() => {
		if (message) {
			const timer = setTimeout(() => {
				message = null;
			}, 5000);
			return () => clearTimeout(timer);
		}
	});
</script>

{#snippet actions()}
	<div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
		<Button variant="outline" onclick={goBack}>
			<ArrowLeft class="mr-2 h-4 w-4" />
			Back to Holidays
		</Button>

		<div class="flex items-center gap-2">
			<Select.Root type="single" value={selectedYear.toString()} onValueChange={handleYearChange}>
				<Select.Trigger class="w-32">
					<Select.Value />
				</Select.Trigger>
				<Select.Content>
					{#each yearOptions as year}
						<Select.Item value={year.value}>{year.label}</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>

			<Select.Root type="single" value={selectedCountry} onValueChange={handleCountryChange}>
				<Select.Trigger class="w-40">
					<Select.Value placeholder="All Countries" />
				</Select.Trigger>
				<Select.Content>
					{#each countryOptions as country}
						<Select.Item value={country.value}>{country.label}</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>

			<Button onclick={openImportDialog}>
				<Download class="mr-2 h-4 w-4" />
				Import Holidays
			</Button>
		</div>
	</div>
{/snippet}

{#snippet content()}
	{#if message}
		<Alert.Root class={`mb-4 ${message.type === 'error' ? 'border-red-500' : 'border-green-500'}`}>
			<Alert.Title>{message.type === 'error' ? 'Error' : 'Success'}</Alert.Title>
			<Alert.Description>{message.text}</Alert.Description>
		</Alert.Root>
	{/if}

	<div class="rounded-md border">
		<DataTable data={data.holidays} {columns} />
	</div>

	{#if data.holidays.length === 0}
		<div class="flex flex-col items-center justify-center py-12 text-center">
			<Globe class="text-muted-foreground mb-4 h-12 w-12" />
			<h3 class="mb-2 text-lg font-semibold">No holiday data found</h3>
			<p class="text-muted-foreground mb-4">
				Import holiday data from external APIs to get started with your holiday management.
			</p>
			<Button onclick={openImportDialog}>
				<Download class="mr-2 h-4 w-4" />
				Import Holidays
			</Button>
		</div>
	{/if}
{/snippet}

<PageContainer
	title="Holiday Reference Data"
	description="Manage master holiday data imported from external APIs for {selectedYear}"
	{actions}
	{content}
/>

<!-- Import Dialog -->
<Dialog.Root open={showImportDialog} onOpenChange={(open) => showImportDialog = open}>
	<Dialog.Content class="max-w-2xl">
		<Dialog.Header>
			<Dialog.Title>Import Holiday Data</Dialog.Title>
			<Dialog.Description>
				Import national holidays from external APIs. This creates the master holiday reference data that you can then create business rules for.
			</Dialog.Description>
		</Dialog.Header>

		<form method="POST" action="?/importHolidays" use:enhance class="space-y-6">
			{#if $errors._errors && $errors._errors.length > 0}
				<Alert.Root class="border-red-500">
					<Alert.Title>Please fix the following errors:</Alert.Title>
					<Alert.Description>
						<ul class="ml-4 list-disc">
							{#each $errors._errors as error}
								<li>{error}</li>
							{/each}
						</ul>
					</Alert.Description>
				</Alert.Root>
			{/if}

			<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
				<div>
					<Label>Year *</Label>
					<Select.Root type="single" bind:value={$formData.year}>
						<Select.Trigger class={$errors.year ? 'border-red-500' : ''}>
							{$formData.year || 'Select year'}
						</Select.Trigger>
						<Select.Content>
							{#each yearOptions as year}
								<Select.Item value={year.value} label={year.label}>
									{year.label}
								</Select.Item>
							{/each}
						</Select.Content>
					</Select.Root>
					<input type="hidden" name="year" bind:value={$formData.year} />
					{#if $errors.year}<p class="text-sm text-red-500">{$errors.year}</p>{/if}
				</div>

				<div>
					<Label>Country *</Label>
					<Select.Root type="single" bind:value={$formData.country_iso2}>
						<Select.Trigger class={$errors.country_iso2 ? 'border-red-500' : ''}>
							{$formData.country_iso2 || 'Select country'}
						</Select.Trigger>
						<Select.Content>
							{#each data.availableCountries as countryCode}
								<Select.Item value={countryCode} label={countryCode}>
									{countryCode}
								</Select.Item>
							{/each}
						</Select.Content>
					</Select.Root>
					<input type="hidden" name="country_iso2" bind:value={$formData.country_iso2} />
					{#if $errors.country_iso2}<p class="text-sm text-red-500">{$errors.country_iso2}</p>{/if}
				</div>
			</div>

			<div class="space-y-4">
				<div class="flex items-center space-x-2">
					<Checkbox bind:checked={$formData.use_local_names} id="use_local_names" />
					<Label for="use_local_names">Use local names when available</Label>
				</div>
				<input type="hidden" name="use_local_names" bind:value={$formData.use_local_names} />

				<div class="flex items-center space-x-2">
					<Checkbox bind:checked={$formData.overwrite_existing} id="overwrite_existing" />
					<Label for="overwrite_existing">Overwrite existing holidays</Label>
				</div>
				<input type="hidden" name="overwrite_existing" bind:value={$formData.overwrite_existing} />
			</div>

			<Dialog.Footer>
				<Button type="button" variant="outline" onclick={() => showImportDialog = false}>
					Cancel
				</Button>
				<Button type="submit" disabled={$submitting}>
					{$submitting ? 'Importing...' : 'Import Holidays'}
				</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>
