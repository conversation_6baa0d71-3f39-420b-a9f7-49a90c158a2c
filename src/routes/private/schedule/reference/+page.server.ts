import type { PageServerLoad, Actions } from './$types';
import { fail, error, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { importHolidaySchema } from '../schemas';
import type { Holiday, NagerDateHoliday, HolidayImportResult } from '../types';

export const load: PageServerLoad = async ({ locals, url }) => {
	try {
		const supabase = locals.supabase;
		const year = parseInt(url.searchParams.get('year') || new Date().getFullYear().toString());
		const country_code = url.searchParams.get('country') || '';

		// Load holiday data
		let query = supabase
			.from('holiday')
			.select('*')
			.order('typical_date', { ascending: true });

		// Filter by country if specified
		if (country_code) {
			query = query.eq('country_iso2', country_code);
		}

		const { data: holidays, error: holidaysError } = await query;

		if (holidaysError) {
			console.error('Error loading holidays:', holidaysError);
			throw error(500, 'Failed to load holiday data');
		}

		// Get available countries from existing data
		const { data: countries, error: countriesError } = await supabase
			.from('holiday')
			.select('country_iso2')
			.order('country_iso2');

		if (countriesError) {
			console.error('Error loading countries:', countriesError);
		}

		const uniqueCountries = Array.from(
			new Set(countries?.map(c => c.country_iso2) || [])
		).sort();

		// Set up the import form
		const importForm = await superValidate(zod(importHolidaySchema));

		return {
			holidays: holidays || [],
			availableCountries: uniqueCountries,
			selectedYear: year,
			selectedCountry: country_code,
			importForm
		};
	} catch (e) {
		console.error('Error in holiday reference load:', e);
		throw error(500, 'Failed to load holiday reference data');
	}
};

export const actions: Actions = {
	importHolidays: async ({ request, locals }) => {
		const supabase = locals.supabase;
		const form = await superValidate(request, zod(importHolidaySchema));

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			// Fetch holidays from Nager.Date API
			const response = await fetch(
				`https://date.nager.at/api/v3/publicholidays/${form.data.year}/${form.data.country_iso2}`
			);

			if (!response.ok) {
				form.errors._errors = ['Failed to fetch holidays from external API'];
				return fail(400, { form });
			}

			const apiHolidays: NagerDateHoliday[] = await response.json();

			// Filter holidays by type if specified
			let filteredHolidays = apiHolidays;
			if (form.data.holiday_types && form.data.holiday_types.length > 0) {
				filteredHolidays = apiHolidays.filter(holiday =>
					holiday.types?.some(type =>
						form.data.holiday_types!.includes(type.toLowerCase())
					)
				);
			}

			// Transform API data to our schema
			const holidaysToImport: Partial<Holiday>[] = filteredHolidays.map(holiday => ({
				id: `${form.data.country_iso2}-${holiday.name.toLowerCase().replace(/\s+/g, '-')}`,
				name: {
					en: holiday.name,
					...(form.data.use_local_names && holiday.localName !== holiday.name ? {
						[getLanguageCodeForCountry(form.data.country_iso2)]: holiday.localName
					} : {})
				},
				description: holiday.localName !== holiday.name ? {
					en: `Local name: ${holiday.localName}`
				} : undefined,
				country_iso2: form.data.country_iso2,
				typical_date: holiday.date.substring(5), // Extract MM-DD from YYYY-MM-DD
				is_national: holiday.global,
				recurrence_type: holiday.fixed ? 'annual' : 'variable',
				source_api: 'nager_date'
			}));

			// Check for existing holidays if not overwriting
			let existingIds: string[] = [];
			if (!form.data.overwrite_existing) {
				const { data: existing } = await supabase
					.from('holiday')
					.select('id')
					.in('id', holidaysToImport.map(h => h.id!));

				existingIds = existing?.map(h => h.id) || [];
			}

			// Filter out existing holidays if not overwriting
			const newHolidays = form.data.overwrite_existing
				? holidaysToImport
				: holidaysToImport.filter(h => !existingIds.includes(h.id!));

			if (newHolidays.length === 0) {
				form.errors._errors = ['All holidays already exist. Enable "Overwrite existing" to update them.'];
				return fail(400, { form });
			}

			// Insert or upsert holidays
			const { data: insertedHolidays, error: insertError } = await supabase
				.from('holiday')
				.upsert(newHolidays, { onConflict: 'id' })
				.select();

			if (insertError) {
				console.error('Error inserting holidays:', insertError);
				form.errors._errors = ['Failed to save holidays: ' + insertError.message];
				return fail(400, { form });
			}

			const result: HolidayImportResult = {
				imported_count: insertedHolidays?.length || 0,
				skipped_count: holidaysToImport.length - newHolidays.length,
				error_count: 0,
				imported_holidays: insertedHolidays || [],
				errors: []
			};

			return redirect(303, `/private/holiday/reference?year=${form.data.year}&country=${form.data.country_iso2}&success=${encodeURIComponent(`Successfully imported ${result.imported_count} holidays`)}`);

		} catch (e) {
			console.error('Error importing holidays:', e);
			return fail(500, {
				form,
				message: 'An unexpected error occurred while importing holidays'
			});
		}
	}
};

// Helper function to get language code for country
function getLanguageCodeForCountry(countryCode: string): string {
	const countryLanguageMap: Record<string, string> = {
		'JP': 'ja',
		'KR': 'ko',
		'CN': 'zh',
		'TW': 'zh',
		'HK': 'zh'
	};
	return countryLanguageMap[countryCode] || 'en';
}
