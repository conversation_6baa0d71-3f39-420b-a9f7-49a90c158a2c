import type { Actions, ServerLoad } from '@sveltejs/kit';
import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { holidaySchema } from '../schemas';

export const load: ServerLoad = async () => {
	const form = await superValidate(zod(holidaySchema));
	return { form };
};

export const actions: Actions = {
	default: async ({ request, locals }) => {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;
		const userId = locals.user?.id;

		if (!brandId) {
			return fail(400, { message: 'Brand ID is required' });
		}

		if (!userId) {
			return fail(400, { message: 'User must be authenticated' });
		}

		const form = await superValidate(request, zod(holidaySchema));

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			const { error: holidayError } = await supabase.from('holiday').insert({
				...form.data,
				brand_id: brandId,
				creator_id: userId
			});

			if (holidayError) {
				console.error('Holiday creation error:', holidayError);
				form.errors._errors = ['Failed to create holiday: ' + holidayError.message];
				return fail(400, { form });
			}

			// Redirect back to holidays list with success message
			redirect(302, '/private/holiday?success=Holiday created successfully');
		} catch (e) {
			console.error('Error creating holiday:', e);
			return fail(500, {
				form,
				message: 'An unexpected error occurred while creating the holiday'
			});
		}
	}
};
