<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import * as Tabs from '$lib/components/ui/tabs';

	import DataTable from '$lib/components/shared/DataTable.svelte';
	import { createSortableColumn } from '$lib/components/shared/table-utils';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { ColumnDef } from '@tanstack/table-core';
	import type { Schedule, HolidayRule } from './types';
	import Calendar from '@lucide/svelte/icons/calendar';
	import Clock from '@lucide/svelte/icons/clock';
	import Plus from '@lucide/svelte/icons/plus';
	import Settings from '@lucide/svelte/icons/settings';

	interface Props {
		data: {
			weeklySchedules: Schedule[];
			holidaySchedules: Schedule[];
			holidayRules: HolidayRule[];
			landmarks: Array<{ id: string; name: string }>;
		};
	}

	const { data }: Props = $props();

	// Weekly schedule columns
	const weeklyColumns: ColumnDef<Schedule, any>[] = [
		createSortableColumn('landmark', 'Location', (row) => {
			const landmark = data.landmarks.find(l => l.id === row.landmark_id);
			return landmark?.name || 'Unknown';
		}),
		createSortableColumn('name', 'Schedule Name', (row) => getLocalizedText(row.name)),
		createSortableColumn('schedule_kind', 'Type', (row) => row.schedule_kind),
		createSortableColumn('periods', 'Periods', (row) => row.periods?.length || 0),
		{
			id: 'actions',
			header: 'Actions',
			cell: ({ row }) => {
				const schedule = row.original;
				return `
					<div class="flex gap-2">
						<button onclick="editSchedule('${schedule.id}')" class="text-blue-600 hover:text-blue-800">
							Edit
						</button>
						<button onclick="deleteSchedule('${schedule.id}')" class="text-red-600 hover:text-red-800">
							Delete
						</button>
					</div>
				`;
			}
		}
	];

	// Holiday schedule columns
	const holidayColumns: ColumnDef<Schedule, any>[] = [
		createSortableColumn('landmark', 'Location', (row) => {
			const landmark = data.landmarks.find(l => l.id === row.landmark_id);
			return landmark?.name || 'Unknown';
		}),
		createSortableColumn('holiday', 'Holiday', (row) => {
			return row.holiday ? getLocalizedText(row.holiday.name) : 'Unknown Holiday';
		}),
		createSortableColumn('schedule_kind', 'Action', (row) => row.schedule_kind),
		createSortableColumn('periods', 'Periods', (row) => row.periods?.length || 0),
		{
			id: 'actions',
			header: 'Actions',
			cell: ({ row }) => {
				const schedule = row.original;
				return `
					<div class="flex gap-2">
						<button onclick="editSchedule('${schedule.id}')" class="text-blue-600 hover:text-blue-800">
							Edit
						</button>
						<button onclick="deleteSchedule('${schedule.id}')" class="text-red-600 hover:text-red-800">
							Delete
						</button>
					</div>
				`;
			}
		}
	];

	// Holiday rule columns
	const ruleColumns: ColumnDef<HolidayRule, any>[] = [
		createSortableColumn('holiday', 'Holiday', (row) => {
			return row.holiday ? getLocalizedText(row.holiday.name) : 'Unknown Holiday';
		}),
		createSortableColumn('schedule_kind', 'Action', (row) => row.schedule_kind),
		createSortableColumn('landmarks', 'Locations', (row) => {
			return row.landmark_ids?.length || 'All locations';
		}),
		{
			id: 'actions',
			header: 'Actions',
			cell: ({ row }) => {
				const rule = row.original;
				return `
					<div class="flex gap-2">
						<button onclick="editRule('${rule.id}')" class="text-blue-600 hover:text-blue-800">
							Edit
						</button>
						<button onclick="deleteRule('${rule.id}')" class="text-red-600 hover:text-red-800">
							Delete
						</button>
					</div>
				`;
			}
		}
	];

	function createWeeklySchedule() {
		goto('/private/schedule/weekly/create');
	}

	function createHolidayRule() {
		goto('/private/schedule/holiday/create');
	}

	function importHolidays() {
		goto('/private/schedule/reference');
	}

	function editSchedule(id: string) {
		goto(`/private/schedule/edit/${id}`);
	}

	function editRule(id: string) {
		goto(`/private/schedule/holiday/edit/${id}`);
	}

	function deleteSchedule(id: string) {
		// TODO: Implement delete functionality
		console.log('Delete schedule:', id);
	}

	function deleteRule(id: string) {
		// TODO: Implement delete functionality
		console.log('Delete rule:', id);
	}
</script>



<!-- Header -->
<div class="flex items-center justify-between">
	<div>
		<h1 class="text-3xl font-bold tracking-tight">Schedule Management</h1>
		<p class="text-muted-foreground">
			Manage weekly schedules and holiday rules for your locations
		</p>
	</div>
	<div class="flex gap-2">
		<Button variant="outline" onclick={importHolidays}>
			<Calendar class="mr-2 h-4 w-4" />
			Import Holidays
		</Button>
	</div>
</div>

<div class="space-y-6">

		<!-- Tabs for different schedule types -->
		<Tabs.Root value="weekly" class="space-y-4">
			<Tabs.List class="grid w-full grid-cols-3">
				<Tabs.Trigger value="weekly">Weekly Schedules</Tabs.Trigger>
				<Tabs.Trigger value="holiday-schedules">Holiday Schedules</Tabs.Trigger>
				<Tabs.Trigger value="holiday-rules">Holiday Rules</Tabs.Trigger>
			</Tabs.List>

			<!-- Weekly Schedules Tab -->
			<Tabs.Content value="weekly" class="space-y-4">
				<div class="flex items-center justify-between">
					<div>
						<h2 class="text-xl font-semibold">Weekly Schedules</h2>
						<p class="text-sm text-muted-foreground">
							Regular operating hours for each day of the week
						</p>
					</div>
					<Button onclick={createWeeklySchedule}>
						<Plus class="mr-2 h-4 w-4" />
						Create Weekly Schedule
					</Button>
				</div>

				<Card.Root>
					<Card.Content class="p-0">
						<DataTable data={data.weeklySchedules} columns={weeklyColumns} />
					</Card.Content>
				</Card.Root>

				{#if data.weeklySchedules.length === 0}
					<div class="flex flex-col items-center justify-center py-12 text-center">
						<Clock class="text-muted-foreground mb-4 h-12 w-12" />
						<h3 class="mb-2 text-lg font-semibold">No weekly schedules found</h3>
						<p class="text-muted-foreground mb-4">
							Create your first weekly schedule to define regular operating hours.
						</p>
						<Button onclick={createWeeklySchedule}>
							<Plus class="mr-2 h-4 w-4" />
							Create Weekly Schedule
						</Button>
					</div>
				{/if}
			</Tabs.Content>

			<!-- Holiday Schedules Tab -->
			<Tabs.Content value="holiday-schedules" class="space-y-4">
				<div class="flex items-center justify-between">
					<div>
						<h2 class="text-xl font-semibold">Holiday Schedules</h2>
						<p class="text-sm text-muted-foreground">
							Specific schedules for individual holidays
						</p>
					</div>
				</div>

				<Card.Root>
					<Card.Content class="p-0">
						<DataTable data={data.holidaySchedules} columns={holidayColumns} />
					</Card.Content>
				</Card.Root>

				{#if data.holidaySchedules.length === 0}
					<div class="flex flex-col items-center justify-center py-12 text-center">
						<Calendar class="text-muted-foreground mb-4 h-12 w-12" />
						<h3 class="mb-2 text-lg font-semibold">No holiday schedules found</h3>
						<p class="text-muted-foreground mb-4">
							Holiday schedules are created automatically when you define holiday rules.
						</p>
					</div>
				{/if}
			</Tabs.Content>

			<!-- Holiday Rules Tab -->
			<Tabs.Content value="holiday-rules" class="space-y-4">
				<div class="flex items-center justify-between">
					<div>
						<h2 class="text-xl font-semibold">Holiday Rules</h2>
						<p class="text-sm text-muted-foreground">
							Define what happens on holidays - rules apply automatically every year
						</p>
					</div>
					<Button onclick={createHolidayRule}>
						<Plus class="mr-2 h-4 w-4" />
						Create Holiday Rule
					</Button>
				</div>

				<Card.Root>
					<Card.Content class="p-0">
						<DataTable data={data.holidayRules} columns={ruleColumns} />
					</Card.Content>
				</Card.Root>

				{#if data.holidayRules.length === 0}
					<div class="flex flex-col items-center justify-center py-12 text-center">
						<Settings class="text-muted-foreground mb-4 h-12 w-12" />
						<h3 class="mb-2 text-lg font-semibold">No holiday rules found</h3>
						<p class="text-muted-foreground mb-4">
							Create holiday rules to define what happens on holidays. Rules apply automatically every year.
						</p>
						<Button onclick={createHolidayRule}>
							<Plus class="mr-2 h-4 w-4" />
							Create Holiday Rule
						</Button>
					</div>
				{/if}
			</Tabs.Content>
		</Tabs.Root>
</div>
