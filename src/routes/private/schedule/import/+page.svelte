<script lang="ts">
	import { goto } from '$app/navigation';
	import { Button } from '$lib/components/ui/button';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import * as Select from '$lib/components/ui/select';
	import * as Card from '$lib/components/ui/card';
	import * as Alert from '$lib/components/ui/alert';
	import { PageContainer } from '$lib/components/layout';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { batchHolidaySchema, availableCountries, holidayTypes } from '../schemas';
	import { Control, Label, Field, FieldErrors } from 'formsnap';
	import Upload from '@lucide/svelte/icons/upload';
	import ArrowLeft from '@lucide/svelte/icons/arrow-left';
	import Globe from '@lucide/svelte/icons/globe';
	import Calendar from '@lucide/svelte/icons/calendar';
	import Filter from '@lucide/svelte/icons/filter';
	import Info from '@lucide/svelte/icons/info';

	interface Props {
		data: {
			form: any;
		};
	}

	let { data }: Props = $props();

	const form = superForm(data.form, {
		validators: zodClient(batchHolidaySchema),
		resetForm: false,
		onResult: ({ result }) => {
			if (result.type === 'redirect') {
				// Will be handled by SvelteKit's navigation
				return;
			}
		}
	});

	const { form: formData, enhance, errors, submitting, message } = form;

	function goBack() {
		goto('/private/holiday');
	}

	// Current year and year options
	const currentYear = new Date().getFullYear();
	const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

	// Initialize form with current year
	$formData.year = currentYear;
	$formData.useLocalNames = false;
	$formData.holidayTypes = [];

	function toggleHolidayType(typeValue: string) {
		if (!$formData.holidayTypes) {
			$formData.holidayTypes = [];
		}

		const index = $formData.holidayTypes.indexOf(typeValue);
		if (index > -1) {
			$formData.holidayTypes.splice(index, 1);
		} else {
			$formData.holidayTypes.push(typeValue);
		}
		// Trigger reactivity
		$formData.holidayTypes = [...$formData.holidayTypes];
	}

	function isHolidayTypeSelected(typeValue: string): boolean {
		return $formData.holidayTypes?.includes(typeValue) || false;
	}
</script>

{#snippet actions()}
	<Button variant="outline" onclick={goBack}>
		<ArrowLeft class="mr-2 h-4 w-4" />
		Back to Holidays
	</Button>
{/snippet}

{#snippet content()}
	<div class="mx-auto max-w-3xl">
		<Card.Root>
			<Card.Header>
				<div class="flex items-center gap-2">
					<Upload class="h-5 w-5" />
					<Card.Title>Import National Holidays</Card.Title>
				</div>
				<Card.Description>
					Import official public holidays for any country. Data is sourced from Nager.Date API which
					covers 100+ countries worldwide.
				</Card.Description>
			</Card.Header>

			<Card.Content>
				{#if $message}
					<Alert.Root
						class={`mb-6 ${$message.type === 'error' ? 'border-red-500' : 'border-green-500'}`}
					>
						<Alert.Title>{$message.type === 'error' ? 'Error' : 'Success'}</Alert.Title>
						<Alert.Description>{$message.text}</Alert.Description>
					</Alert.Root>
				{/if}

				{#if $errors._errors && $errors._errors.length > 0}
					<Alert.Root class="mb-6 border-red-500">
						<Alert.Title>Please fix the following errors:</Alert.Title>
						<Alert.Description>
							<ul class="ml-4 list-disc">
								{#each $errors._errors as error}
									<li>{error}</li>
								{/each}
							</ul>
						</Alert.Description>
					</Alert.Root>
				{/if}

				<form method="POST" use:enhance class="space-y-8">
					<!-- Basic Settings -->
					<div class="space-y-6">
						<div class="flex items-center gap-2 text-lg font-semibold">
							<Calendar class="h-5 w-5" />
							Basic Settings
						</div>

						<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
							<Field form name="year">
								<Control>
									{#snippet children({ props })}
										<Label>Year *</Label>
										<Select.Root type="single" bind:value={$formData.year} name={props.name}>
											<Select.Trigger class={$errors.year ? 'border-red-500' : ''}>
												<Select.Value />
											</Select.Trigger>
											<Select.Content>
												<Select.Group>
													<Select.GroupHeading>Available Years</Select.GroupHeading>
													{#each yearOptions as year}
														<Select.Item value={year} label={year.toString()}>
															{year}
														</Select.Item>
													{/each}
												</Select.Group>
											</Select.Content>
										</Select.Root>
									{/snippet}
								</Control>
								<FieldErrors />
							</Field>

							<Field form name="countryCode">
								<Control>
									{#snippet children({ props })}
										<Label>Country *</Label>
										<Select.Root type="single" bind:value={$formData.countryCode} name={props.name}>
											<Select.Trigger class={$errors.countryCode ? 'border-red-500' : ''}>
												<Select.Value placeholder="Select a country" />
											</Select.Trigger>
											<Select.Content>
												<Select.Group>
													<Select.GroupHeading>Countries</Select.GroupHeading>
													{#each availableCountries as country}
														<Select.Item value={country.code} label={country.name}>
															<Globe class="mr-2 h-4 w-4" />
															{country.name} ({country.code})
														</Select.Item>
													{/each}
												</Select.Group>
											</Select.Content>
										</Select.Root>
									{/snippet}
								</Control>
								<FieldErrors />
							</Field>
						</div>
					</div>

					<!-- Advanced Options -->
					<div class="space-y-6">
						<div class="flex items-center gap-2 text-lg font-semibold">
							<Filter class="h-5 w-5" />
							Advanced Options
						</div>

						<Field form name="useLocalNames">
							<Control>
								{#snippet children({ props })}
									<div class="flex items-center space-x-2">
										<Checkbox
											{...props}
											bind:checked={$formData.useLocalNames}
											id="use_local_names"
										/>
										<Label for="use_local_names">Use Local Names</Label>
									</div>
									<p class="text-muted-foreground text-sm">
										Use the local language names for holidays instead of English names
									</p>
								{/snippet}
							</Control>
							<FieldErrors />
						</Field>

						<div class="space-y-3">
							<Label>Holiday Types (optional)</Label>
							<p class="text-muted-foreground text-sm">
								Select specific types of holidays to import. Leave empty to import all holidays.
							</p>
							<div class="grid grid-cols-2 gap-3 md:grid-cols-3">
								{#each holidayTypes as holidayType}
									<div class="flex items-center space-x-2">
										<Checkbox
											id="type_{holidayType.value}"
											checked={isHolidayTypeSelected(holidayType.value)}
											onCheckedChange={() => toggleHolidayType(holidayType.value)}
										/>
										<Label for="type_{holidayType.value}" class="text-sm">
											{holidayType.label}
										</Label>
									</div>
								{/each}
							</div>
						</div>
					</div>

					<!-- Information Box -->
					<Alert.Root>
						<Info class="h-4 w-4" />
						<Alert.Title>Important Information</Alert.Title>
						<Alert.Description>
							<ul class="ml-4 list-disc space-y-1">
								<li>Holidays will be imported for the selected year only</li>
								<li>Duplicate holidays (same date) will be automatically skipped</li>
								<li>All imported holidays will be marked as "National" holidays</li>
								<li>Data is sourced from the reliable Nager.Date API</li>
							</ul>
						</Alert.Description>
					</Alert.Root>

					<div class="flex items-center justify-end gap-4 pt-4">
						<Button type="button" variant="outline" onclick={goBack}>Cancel</Button>
						<Button type="submit" disabled={$submitting || !$formData.countryCode}>
							{$submitting ? 'Importing...' : 'Import Holidays'}
						</Button>
					</div>
				</form>
			</Card.Content>
		</Card.Root>
	</div>
{/snippet}

<PageContainer
	title="Import Holidays"
	description="Import national holidays in bulk for your brand"
	{actions}
	{content}
/>
