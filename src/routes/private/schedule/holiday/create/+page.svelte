<script lang="ts">
	import { goto } from '$app/navigation';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { holidayRuleSchema } from '../../schemas';
	import ArrowLeft from '@lucide/svelte/icons/arrow-left';

	interface Props {
		data: {
			form: any;
			holidays: Array<{ id: string; name: Record<string, string>; country_iso2: string; typical_date?: string }>;
			scheduleKinds: Array<{ id: string; name: Record<string, string> }>;
			landmarks: Array<{ id: string; name: string }>;
		};
	}

	const { data }: Props = $props();

	const form = superForm(data.form, {
		validators: zodClient(holidayRuleSchema),
		resetForm: false
	});

	const { form: formData, enhance, errors, submitting } = form;

	// Landmark selection state
	let selectedLandmarks = $state<string[]>([]);
	let applyToAllLandmarks = $state(true);

	function goBack() {
		goto('/private/schedule');
	}

	function getLocalizedText(text: Record<string, string>): string {
		return text.en || text.zh || text.ko || text.ja || '';
	}

	function toggleLandmark(landmarkId: string) {
		if (selectedLandmarks.includes(landmarkId)) {
			selectedLandmarks = selectedLandmarks.filter(id => id !== landmarkId);
		} else {
			selectedLandmarks = [...selectedLandmarks, landmarkId];
		}
	}

	function toggleApplyToAll() {
		applyToAllLandmarks = !applyToAllLandmarks;
		if (applyToAllLandmarks) {
			selectedLandmarks = [];
		}
	}
</script>

<!-- Header -->
<div class="flex items-center gap-4">
	<Button variant="ghost" size="sm" onclick={goBack}>
		<ArrowLeft class="mr-2 h-4 w-4" />
		Back to Schedules
	</Button>
	<div>
		<h1 class="text-3xl font-bold tracking-tight">Create Holiday Rule</h1>
		<p class="text-muted-foreground">
			Define what happens on holidays - rules apply automatically every year
		</p>
	</div>
</div>

<div class="space-y-6">

		<form method="POST" use:enhance>
			<div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
				<!-- Rule Details -->
				<div>
					<Card.Root>
						<Card.Header>
							<Card.Title>Holiday Rule Details</Card.Title>
							<Card.Description>
								Define what your business does on this holiday
							</Card.Description>
						</Card.Header>
						<Card.Content class="space-y-4">
							<div class="space-y-2">
								<Label for="holiday_id">Holiday *</Label>
								<Select.Root type="single" bind:value={$formData.holiday_id} name="holiday_id">
									<Select.Trigger>
										{#if $formData.holiday_id}
											{@const selectedHoliday = data.holidays.find(h => h.id === $formData.holiday_id)}
											{selectedHoliday ? getLocalizedText(selectedHoliday.name) : "Select holiday"}
											{#if selectedHoliday?.typical_date}
												<span class="text-muted-foreground ml-2">({selectedHoliday.typical_date})</span>
											{/if}
										{:else}
											Select holiday
										{/if}
									</Select.Trigger>
									<Select.Content>
										{#each data.holidays as holiday}
											<Select.Item value={holiday.id} label={getLocalizedText(holiday.name)}>
												{getLocalizedText(holiday.name)}
												{#if holiday.typical_date}
													<span class="text-muted-foreground ml-2">({holiday.typical_date})</span>
												{/if}
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<div class="space-y-2">
								<Label for="schedule_kind">Business Action *</Label>
								<Select.Root type="single" bind:value={$formData.schedule_kind} name="schedule_kind">
									<Select.Trigger>
										{data.scheduleKinds.find(k => k.id === $formData.schedule_kind) ? getLocalizedText(data.scheduleKinds.find(k => k.id === $formData.schedule_kind)!.name) : "What does your business do?"}
									</Select.Trigger>
									<Select.Content>
										{#each data.scheduleKinds as kind}
											<Select.Item value={kind.id} label={getLocalizedText(kind.name)}>
												{getLocalizedText(kind.name)}
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<div class="space-y-2">
								<Label for="message_to_consumer_en">Message to Customers (English)</Label>
								<Textarea
									id="message_to_consumer_en"
									name="message_to_consumer.en"
									bind:value={$formData.message_to_consumer.en}
									placeholder="e.g., We're closed for Christmas Day. Happy holidays!"
								/>
							</div>

							<div class="space-y-2">
								<Label for="message_to_staff_en">Message to Staff (English)</Label>
								<Textarea
									id="message_to_staff_en"
									name="message_to_staff.en"
									bind:value={$formData.message_to_staff.en}
									placeholder="e.g., Enjoy your holiday! See you tomorrow."
								/>
							</div>
						</Card.Content>
					</Card.Root>
				</div>

				<!-- Location Selection -->
				<div>
					<Card.Root>
						<Card.Header>
							<Card.Title>Apply to Locations</Card.Title>
							<Card.Description>
								Choose which locations this rule applies to
							</Card.Description>
						</Card.Header>
						<Card.Content class="space-y-4">
							<div class="flex items-center space-x-2">
								<Checkbox 
									id="apply-to-all" 
									checked={applyToAllLandmarks}
									onCheckedChange={toggleApplyToAll}
								/>
								<label for="apply-to-all" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
									Apply to all locations
								</label>
							</div>

							{#if !applyToAllLandmarks}
								<div class="space-y-2">
									<p class="text-sm text-muted-foreground">Select specific locations:</p>
									{#each data.landmarks as landmark}
										<div class="flex items-center space-x-2">
											<Checkbox 
												id="landmark-{landmark.id}"
												checked={selectedLandmarks.includes(landmark.id)}
												onCheckedChange={() => toggleLandmark(landmark.id)}
											/>
											<label for="landmark-{landmark.id}" class="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
												{landmark.name}
											</label>
										</div>
									{/each}
								</div>
							{/if}

							{#if !applyToAllLandmarks && selectedLandmarks.length === 0}
								<p class="text-sm text-amber-600">
									⚠️ No locations selected. This rule won't apply anywhere.
								</p>
							{/if}
						</Card.Content>
					</Card.Root>

					<!-- Preview -->
					<Card.Root class="mt-6">
						<Card.Header>
							<Card.Title>Rule Preview</Card.Title>
						</Card.Header>
						<Card.Content>
							{#if $formData.holiday_id && $formData.schedule_kind}
								{@const selectedHoliday = data.holidays.find(h => h.id === $formData.holiday_id)}
								{@const selectedAction = data.scheduleKinds.find(k => k.id === $formData.schedule_kind)}
								<div class="space-y-2">
									<p class="text-sm">
										<strong>Holiday:</strong> {selectedHoliday ? getLocalizedText(selectedHoliday.name) : 'Not selected'}
									</p>
									<p class="text-sm">
										<strong>Action:</strong> {selectedAction ? getLocalizedText(selectedAction.name) : 'Not selected'}
									</p>
									<p class="text-sm">
										<strong>Applies to:</strong> 
										{applyToAllLandmarks ? 'All locations' : `${selectedLandmarks.length} selected location(s)`}
									</p>
								</div>
							{:else}
								<p class="text-sm text-muted-foreground">
									Select a holiday and action to see preview
								</p>
							{/if}
						</Card.Content>
					</Card.Root>
				</div>
			</div>

			<!-- Actions -->
			<div class="flex items-center justify-end gap-2">
				<Button type="button" variant="outline" onclick={goBack}>
					Cancel
				</Button>
				<Button type="submit" disabled={$submitting}>
					{$submitting ? 'Creating...' : 'Create Holiday Rule'}
				</Button>
			</div>

			<!-- Hidden fields for landmark selection -->
			{#if !applyToAllLandmarks}
				{#each selectedLandmarks as landmarkId}
					<input type="hidden" name="landmark_ids" value={landmarkId} />
				{/each}
			{/if}
	</form>
</div>
