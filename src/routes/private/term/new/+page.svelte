<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import TermKindPicker from '$lib/components/term/TermKindPicker.svelte';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import * as Select from '$lib/components/ui/select';
	import { Label } from '$lib/components/ui/label';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { ArrowLeft, Info } from '@lucide/svelte';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';
	import { getLocalizedText } from '$lib/utils/localization';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { z } from 'zod';
	import type { PageData, ActionData } from './$types';

	let { data, form: actionForm }: { data: PageData; form: ActionData } = $props();

	// Create schema
	const schema = z.object({
		brand_id: z.string(),
		term_kind_id: z.string(),
		title: z.object({
			en: z.string().min(1, 'English title is required'),
			zh: z.string().optional(),
			ja: z.string().optional(),
			ko: z.string().optional()
		})
	});

	const form = superForm(
		{
			brand_id: data.defaultBrandId,
			term_kind_id: '',
			title: { en: '', zh: '', ja: '', ko: '' }
		},
		{
			dataType: 'json'
		}
	);

	const { form: formData } = form;

	// Signature options
	let signatureRequirement = $state<'none' | 'full' | 'initials'>('none');

	// Legal jurisdictions
	const jurisdictions = [
		{ value: 'US', label: 'United States' },
		{ value: 'EU', label: 'European Union' },
		{ value: 'CN', label: 'China' },
		{ value: 'JP', label: 'Japan' },
		{ value: 'KR', label: 'South Korea' }
	];
	let selectedJurisdictions = $state<string[]>(['US']);

	// Show error toast
	$effect(() => {
		if (actionForm?.error) {
			toast.error(actionForm.error);
		}
	});
</script>

{#snippet content()}
	<div class="mx-auto max-w-3xl space-y-6">
		<!-- Header -->
		<div class="flex items-center gap-4">
			<Button
				href={data.selectedGroup
					? `/private/term-group/${data.selectedGroup.id}`
					: '/private/term-group'}
				variant="ghost"
				size="icon"
			>
				<ArrowLeft class="h-4 w-4" />
			</Button>
			<div>
				<h1 class="text-2xl font-semibold tracking-tight">Create Legal Document</h1>
				{#if data.selectedGroup}
					<p class="text-sm text-muted-foreground">
						Adding to: {getLocalizedText(data.selectedGroup.title, 'en')}
					</p>
				{/if}
			</div>
		</div>

		<!-- Form -->
		<form method="POST" use:enhance>
			{#if data.selectedGroup}
				<input type="hidden" name="group_id" value={data.selectedGroup.id} />
			{/if}

			<div class="space-y-6">
				<!-- Basic Information -->
				<Card.Root>
					<Card.Header>
						<Card.Title>Document Information</Card.Title>
						<Card.Description>
							Choose the type of legal document and provide basic details
						</Card.Description>
					</Card.Header>
					<Card.Content class="space-y-6">
						<!-- Brand Selection -->
						{#if data.brands.length > 1 && !data.selectedGroup}
							<div class="space-y-2">
								<Label for="brand_id">Brand <span class="text-red-500">*</span></Label>
								<Select.Root type="single" name="brand_id" bind:value={$formData.brand_id}>
									<Select.Trigger id="brand_id">
										{#if $formData.brand_id}
											{@const selectedBrand = data.brands.find((b) => b.id === $formData.brand_id)}
											{#if selectedBrand}
												{getLocalizedText(selectedBrand.name_full, 'en')}
											{:else}
												Select a brand
											{/if}
										{:else}
											Select a brand
										{/if}
									</Select.Trigger>
									<Select.Content>
										{#each data.brands as brand}
											<Select.Item value={brand.id}>
												{getLocalizedText(brand.name_full, 'en')}
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
								<input type="hidden" name="brand_id" value={$formData.brand_id} />
							</div>
						{:else}
							<input type="hidden" name="brand_id" value={$formData.brand_id} />
						{/if}

						<!-- Document Type -->
						<div class="space-y-2">
							<Label for="term_kind_id">Document Type <span class="text-red-500">*</span></Label>
							<TermKindPicker bind:value={$formData.term_kind_id} required />
							<input type="hidden" name="term_kind_id" value={$formData.term_kind_id} />
						</div>

						<!-- Title -->
						<div class="space-y-2">
							<LocalizedTextControl label="Document Title" name="title" {form} required />
							<p class="text-xs text-muted-foreground">
								A clear, descriptive title for this document
							</p>
						</div>
					</Card.Content>
				</Card.Root>

				<!-- Signature Requirements -->
				<Card.Root>
					<Card.Header>
						<Card.Title>Signature Requirements</Card.Title>
						<Card.Description>
							Configure how customers must acknowledge this document
						</Card.Description>
					</Card.Header>
					<Card.Content class="space-y-4">
						<div class="space-y-3">
							<div class="space-y-3">
								<Label>Signature Type</Label>
								<div class="space-y-2">
									<div class="flex items-center gap-3">
										<input
											type="radio"
											id="signature_none"
											name="signature_requirement"
											value="none"
											bind:group={signatureRequirement}
											class="h-4 w-4"
										/>
										<div class="grid gap-0.5">
											<Label for="signature_none" class="cursor-pointer font-normal">
												Checkbox agreement only
											</Label>
											<p class="text-xs text-muted-foreground">
												Customers only need to check a box to agree
											</p>
										</div>
									</div>

									<div class="flex items-center gap-3">
										<input
											type="radio"
											id="signature_full"
											name="signature_requirement"
											value="full"
											bind:group={signatureRequirement}
											class="h-4 w-4"
										/>
										<div class="grid gap-0.5">
											<Label for="signature_full" class="cursor-pointer font-normal">
												Require full signature
											</Label>
											<p class="text-xs text-muted-foreground">
												Customers must provide their full legal signature
											</p>
										</div>
									</div>

									<div class="flex items-center gap-3">
										<input
											type="radio"
											id="signature_initials"
											name="signature_requirement"
											value="initials"
											bind:group={signatureRequirement}
											class="h-4 w-4"
										/>
										<div class="grid gap-0.5">
											<Label for="signature_initials" class="cursor-pointer font-normal">
												Require initials
											</Label>
											<p class="text-xs text-muted-foreground">
												Customers must sign with their initials
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>
					</Card.Content>
				</Card.Root>

				<!-- Legal Jurisdiction -->
				<Card.Root>
					<Card.Header>
						<Card.Title>Legal Jurisdiction</Card.Title>
						<Card.Description>
							Select the regions where this document will be legally binding
						</Card.Description>
					</Card.Header>
					<Card.Content>
						<div class="grid grid-cols-2 gap-3 sm:grid-cols-3">
							{#each jurisdictions as jurisdiction}
								<div class="flex items-center gap-2">
									<Checkbox
										id={`jurisdiction-${jurisdiction.value}`}
										checked={selectedJurisdictions.includes(jurisdiction.value)}
										onCheckedChange={(checked) => {
											if (checked) {
												selectedJurisdictions = [...selectedJurisdictions, jurisdiction.value];
											} else {
												selectedJurisdictions = selectedJurisdictions.filter(
													(j) => j !== jurisdiction.value
												);
											}
										}}
									/>
									<Label
										for={`jurisdiction-${jurisdiction.value}`}
										class="cursor-pointer font-normal"
									>
										{jurisdiction.label}
									</Label>
									<input
										type="hidden"
										name="legal_jurisdiction"
										value={selectedJurisdictions.includes(jurisdiction.value)
											? jurisdiction.value
											: ''}
									/>
								</div>
							{/each}
						</div>
					</Card.Content>
				</Card.Root>

				<!-- Form Actions -->
				<div class="flex justify-end gap-3">
					<Button
						href={data.selectedGroup
							? `/private/term-group/${data.selectedGroup.id}`
							: '/private/term-group'}
						variant="outline"
					>
						Cancel
					</Button>
					<Button type="submit">Create Document</Button>
				</div>
			</div>
		</form>
	</div>
{/snippet}

<PageContainer
	title="Create Legal Document"
	description="Create a new legal document for your brand"
	{content}
/>
