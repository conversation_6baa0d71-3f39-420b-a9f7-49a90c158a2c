import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ url, locals: { supabase, safeGetSession } }) => {
	const { user } = await safeGetSession();
	if (!user) redirect(303, '/login');

	const groupId = url.searchParams.get('group_id');

	// Get user's brands
	const { data: brands, error: brandsError } = await supabase
		.from('brand')
		.select('id, name_full')
		.eq('owner_profile_id', user.id);

	if (brandsError || !brands || brands.length === 0) {
		redirect(303, '/private/term-group');
	}

	// If groupId is provided, get the group to pre-select the brand
	let selectedGroup = null;
	if (groupId) {
		const { data: group } = await supabase
			.from('term_group')
			.select('id, title, brand_id')
			.eq('id', groupId)
			.single();

		if (group && brands.some((b) => b.id === group.brand_id)) {
			selectedGroup = group;
		}
	}

	return {
		brands,
		selectedGroup,
		defaultBrandId: selectedGroup?.brand_id || brands[0].id
	};
};

export const actions = {
	default: async ({ request, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		const formData = await request.formData();
		const brandId = formData.get('brand_id') as string;
		const groupId = formData.get('group_id') as string | null;
		const termKindId = formData.get('term_kind_id') as string;
		const title = {
			en: formData.get('title[en]') as string,
			zh: formData.get('title[zh]') as string,
			ja: formData.get('title[ja]') as string,
			ko: formData.get('title[ko]') as string
		};
		const signatureRequirement = formData.get('signature_requirement') as string;
		const legalJurisdiction = formData.getAll('legal_jurisdiction') as string[];

		// Validate required fields
		if (!brandId || !termKindId || !title.en) {
			return fail(400, {
				error: 'Brand, document type, and English title are required'
			});
		}

		// Verify user owns the brand
		const { data: brand, error: brandError } = await supabase
			.from('brand')
			.select('id')
			.eq('id', brandId)
			.eq('owner_profile_id', user.id)
			.single();

		if (brandError || !brand) {
			return fail(403, {
				error: 'You do not have permission to create documents for this brand'
			});
		}

		// Create the term
		const { data: term, error: termError } = await supabase
			.from('term')
			.insert({
				brand_id: brandId,
				term_kind_id: termKindId,
				title,
				require_signature_full: signatureRequirement === 'full',
				require_signature_initials: signatureRequirement === 'initials',
				legal_jurisdiction: legalJurisdiction,
				creator_id: user.id
			})
			.select()
			.single();

		if (termError) {
			console.error('Error creating term:', termError);
			return fail(500, {
				error: termError.message || 'Failed to create document'
			});
		}

		// If groupId is provided, add the term to the group
		if (groupId) {
			const { error: groupError } = await supabase.from('term_group_item').insert({
				term_group_id: groupId,
				term_id: term.id,
				is_required: true
			});

			if (groupError) {
				console.error('Error adding term to group:', groupError);
			}
		}

		// Redirect to the new term page
		redirect(303, `/private/term/${term.id}`);
	}
} satisfies Actions;
