import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ params, locals: { supabase, safeGetSession } }) => {
	const { user } = await safeGetSession();
	if (!user) redirect(303, '/login');

	// Get the term with all versions
	const { data: term, error: termError } = await supabase
		.from('term')
		.select(
			`
			*,
			brand!inner(id, name_full, owner_profile_id),
			term_kind!inner(title, subtitle),
			term_version(
				*,
				creator:creator_id(auto_user_email)
			)
		`
		)
		.eq('id', params.id)
		.single();

	if (termError || !term) {
		console.error('Error fetching term:', termError);
		error(404, 'Document not found');
	}

	// Check if user owns this brand
	if (term.brand.owner_profile_id !== user.id) {
		error(403, 'You do not have permission to view this document');
	}

	// Sort versions by version number descending
	term.term_version.sort((a: any, b: any) => b.version_number - a.version_number);

	return {
		term
	};
};

export const actions = {
	updateTerm: async ({ request, params, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		const formData = await request.formData();
		const title = {
			en: formData.get('title[en]') as string,
			zh: formData.get('title[zh]') as string,
			ja: formData.get('title[ja]') as string,
			ko: formData.get('title[ko]') as string
		};
		const signatureRequirement = formData.get('signature_requirement') as string;
		const legalJurisdiction = formData.getAll('legal_jurisdiction') as string[];

		// Update the term
		const { error } = await supabase
			.from('term')
			.update({
				title,
				require_signature_full: signatureRequirement === 'full',
				require_signature_initials: signatureRequirement === 'initials',
				legal_jurisdiction: legalJurisdiction
			})
			.eq('id', params.id);

		if (error) {
			return {
				error: error.message
			};
		}

		return { success: true };
	},

	setCurrentVersion: async ({ request, params, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		const formData = await request.formData();
		const versionId = formData.get('version_id') as string;

		// First, set all versions for this term to not current
		const { error: updateError } = await supabase
			.from('term_version')
			.update({ is_current: false })
			.eq('term_id', params.id);

		if (updateError) {
			return {
				error: updateError.message
			};
		}

		// Then set the selected version as current
		const { error: setCurrentError } = await supabase
			.from('term_version')
			.update({ is_current: true })
			.eq('id', versionId)
			.eq('term_id', params.id);

		if (setCurrentError) {
			return {
				error: setCurrentError.message
			};
		}

		return { success: true };
	},

	deleteTerm: async ({ params, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		// Get the term to find which group to redirect to
		const { data: term } = await supabase
			.from('term')
			.select(
				`
				term_group_item(term_group_id)
			`
			)
			.eq('id', params.id)
			.single();

		const groupId = term?.term_group_item?.[0]?.term_group_id;

		// Delete the term (cascades will handle versions and group items)
		const { error } = await supabase.from('term').delete().eq('id', params.id);

		if (error) {
			return {
				error: error.message
			};
		}

		// Redirect to the group if it exists, otherwise to the term group list
		redirect(303, groupId ? `/private/term-group/${groupId}` : '/private/term-group');
	}
} satisfies Actions;
