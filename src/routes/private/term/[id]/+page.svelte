<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import * as AlertDialog from '$lib/components/ui/alert-dialog';
	import { Badge } from '$lib/components/ui/badge';
	import { Label } from '$lib/components/ui/label';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import {
		ArrowLeft,
		Plus,
		Trash2,
		FileText,
		AlertCircle,
		Edit,
		CheckCircle2,
		Clock,
		FileCheck,
		Info
	} from '@lucide/svelte';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';
	import { getLocalizedText } from '$lib/utils/localization';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { z } from 'zod';
	import { invalidateAll } from '$app/navigation';
	import type { PageData, ActionData } from './$types';
	import { buttonVariants } from '$lib/components/ui/button';

	let { data, form: actionForm }: { data: PageData; form: ActionData } = $props();

	// States
	let editMode = $state(false);
	let showDeleteDialog = $state(false);

	// Create form for editing
	const schema = z.object({
		title: z.object({
			en: z.string().min(1, 'English title is required'),
			zh: z.string().optional(),
			ja: z.string().optional(),
			ko: z.string().optional()
		})
	});

	const form = superForm(
		{
			title: data.term.title
		},
		{
			validators: zodClient(schema),
			dataType: 'json'
		}
	);

	const { form: formData } = form;

	// Signature options
	let signatureRequirement = $state<'none' | 'full' | 'initials'>(
		data.term.require_signature_full
			? 'full'
			: data.term.require_signature_initials
				? 'initials'
				: 'none'
	);

	// Legal jurisdictions
	const jurisdictions = [
		{ value: 'US', label: 'United States' },
		{ value: 'EU', label: 'European Union' },
		{ value: 'CN', label: 'China' },
		{ value: 'JP', label: 'Japan' },
		{ value: 'KR', label: 'South Korea' }
	];
	let selectedJurisdictions = $state<string[]>(data.term.legal_jurisdiction || ['US']);

	// Show toast for action results
	$effect(() => {
		if (actionForm?.error) {
			toast.error(actionForm.error);
		} else if (actionForm?.success) {
			editMode = false;
			toast.success('Changes saved successfully');
			invalidateAll();
		}
	});

	// Get current version
	const currentVersion = $derived(data.term.term_version.find((v: any) => v.is_current));

	// Format date
	function formatDate(date: string) {
		return new Date(date).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}
</script>

{#snippet content()}
	<div class="space-y-6">
		<!-- Header -->
		<div class="flex items-center justify-between">
			<div class="flex items-center gap-4">
				<Button href="/private/term-group" variant="ghost" size="icon">
					<ArrowLeft class="h-4 w-4" />
				</Button>
				<div>
					<h1 class="text-2xl font-semibold tracking-tight">
						{getLocalizedText(data.term.title, 'en')}
					</h1>
					<p class="text-sm text-muted-foreground">
						{getLocalizedText(data.term.term_kind.title, 'en')} •
						{getLocalizedText(data.term.brand.name_full, 'en')}
					</p>
				</div>
			</div>
			<div class="flex gap-2">
				<Button variant="outline" onclick={() => (editMode = !editMode)}>
					<Edit class="mr-2 h-4 w-4" />
					{editMode ? 'Cancel' : 'Edit'}
				</Button>
				<Button href="/private/term-version/new?term_id={data.term.id}">
					<Plus class="mr-2 h-4 w-4" />
					New Version
				</Button>
			</div>
		</div>

		<!-- Current Status -->
		{#if !currentVersion}
			<Card.Root class="border-orange-200 bg-orange-50">
				<Card.Content class="flex items-center gap-3 py-4">
					<AlertCircle class="h-5 w-5 text-orange-600" />
					<div class="flex-1">
						<p class="font-medium text-orange-900">No Active Version</p>
						<p class="text-sm text-orange-700">
							This document needs at least one version to be used. Create a version to activate this
							document.
						</p>
					</div>
					<Button href="/private/term-version/new?term_id={data.term.id}" size="sm">
						Create First Version
					</Button>
				</Card.Content>
			</Card.Root>
		{/if}

		<!-- Edit Mode -->
		{#if editMode}
			<form method="POST" action="?/updateTerm" use:enhance>
				<Card.Root>
					<Card.Header>
						<Card.Title>Edit Document Details</Card.Title>
					</Card.Header>
					<Card.Content class="space-y-6">
						<!-- Title -->
						<LocalizedTextControl label="Document Title" name="title" {form} required />

						<!-- Signature Requirements -->
						<div class="space-y-4">
							<Label>Signature Requirements</Label>
							<div class="space-y-3">
								<div class="space-y-3">
									<Label>Signature Type</Label>
									<div class="space-y-2">
										<div class="flex items-center gap-3">
											<input
												type="radio"
												id="signature_none"
												name="signature_requirement"
												value="none"
												bind:group={signatureRequirement}
												class="h-4 w-4"
											/>
											<div class="grid gap-0.5">
												<Label for="signature_none" class="cursor-pointer font-normal">
													Checkbox agreement only
												</Label>
											</div>
										</div>

										<div class="flex items-center gap-3">
											<input
												type="radio"
												id="signature_full"
												name="signature_requirement"
												value="full"
												bind:group={signatureRequirement}
												class="h-4 w-4"
											/>
											<div class="grid gap-0.5">
												<Label for="signature_full" class="cursor-pointer font-normal">
													Require full signature
												</Label>
											</div>
										</div>

										<div class="flex items-center gap-3">
											<input
												type="radio"
												id="signature_initials"
												name="signature_requirement"
												value="initials"
												bind:group={signatureRequirement}
												class="h-4 w-4"
											/>
											<div class="grid gap-0.5">
												<Label for="signature_initials" class="cursor-pointer font-normal">
													Require initials
												</Label>
												<p class="text-xs text-muted-foreground">
													Customers must sign with their initials
												</p>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Legal Jurisdiction -->
						<div class="space-y-4">
							<Label>Legal Jurisdiction</Label>
							<div class="grid grid-cols-2 gap-3 sm:grid-cols-3">
								{#each jurisdictions as jurisdiction}
									<div class="flex items-center gap-2">
										<Checkbox
											id={`jurisdiction-${jurisdiction.value}`}
											checked={selectedJurisdictions.includes(jurisdiction.value)}
											onCheckedChange={(checked) => {
												if (checked) {
													selectedJurisdictions = [...selectedJurisdictions, jurisdiction.value];
												} else {
													selectedJurisdictions = selectedJurisdictions.filter(
														(j) => j !== jurisdiction.value
													);
												}
											}}
										/>
										<Label
											for={`jurisdiction-${jurisdiction.value}`}
											class="cursor-pointer font-normal"
										>
											{jurisdiction.label}
										</Label>
										<input
											type="hidden"
											name="legal_jurisdiction"
											value={selectedJurisdictions.includes(jurisdiction.value)
												? jurisdiction.value
												: ''}
										/>
									</div>
								{/each}
							</div>
						</div>
					</Card.Content>
					<Card.Footer class="flex justify-between">
						<AlertDialog.Root bind:open={showDeleteDialog}>
							<AlertDialog.Trigger class={buttonVariants({ variant: 'destructive' })}>
								<Trash2 class="mr-2 h-4 w-4" />
								Delete Document
							</AlertDialog.Trigger>
							<AlertDialog.Content>
								<AlertDialog.Header>
									<AlertDialog.Title>Delete Document?</AlertDialog.Title>
									<AlertDialog.Description>
										This will permanently delete this document and all its versions. This action
										cannot be undone.
									</AlertDialog.Description>
								</AlertDialog.Header>
								<AlertDialog.Footer>
									<AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
									<form method="POST" action="?/deleteTerm" use:enhance>
										<Button type="submit" variant="destructive">Delete Document</Button>
									</form>
								</AlertDialog.Footer>
							</AlertDialog.Content>
						</AlertDialog.Root>
						<div class="flex gap-2">
							<Button type="submit">Save Changes</Button>
							<Button variant="outline" onclick={() => (editMode = false)}>Cancel</Button>
						</div>
					</Card.Footer>
				</Card.Root>
			</form>
		{/if}

		<!-- Document Info -->
		{#if !editMode}
			<Card.Root>
				<Card.Header>
					<Card.Title>Document Information</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<div class="grid gap-4 sm:grid-cols-2">
						<div>
							<p class="text-sm font-medium text-muted-foreground">Document Type</p>
							<p class="mt-1">
								{getLocalizedText(data.term.term_kind.title, 'en')}
							</p>
							<p class="text-sm text-muted-foreground">
								{getLocalizedText(data.term.term_kind.subtitle, 'en')}
							</p>
						</div>
						<div>
							<p class="text-sm font-medium text-muted-foreground">Legal Jurisdiction</p>
							<div class="mt-1 flex flex-wrap gap-1">
								{#each data.term.legal_jurisdiction as jurisdiction}
									<Badge variant="secondary" class="text-xs">
										{jurisdictions.find((j) => j.value === jurisdiction)?.label || jurisdiction}
									</Badge>
								{/each}
							</div>
						</div>
						<div>
							<p class="text-sm font-medium text-muted-foreground">Signature Requirements</p>
							<div class="mt-1 space-y-1">
								{#if data.term.require_signature_full}
									<Badge variant="outline" class="gap-1">
										<CheckCircle2 class="h-3 w-3" />
										Full signature required
									</Badge>
								{/if}
								{#if data.term.require_signature_initials}
									<Badge variant="outline" class="gap-1">
										<CheckCircle2 class="h-3 w-3" />
										Initials required
									</Badge>
								{/if}
								{#if !data.term.require_signature_full && !data.term.require_signature_initials}
									<Badge variant="secondary">Checkbox agreement only</Badge>
								{/if}
							</div>
						</div>
					</div>
				</Card.Content>
			</Card.Root>
		{/if}

		<!-- Versions -->
		<div class="space-y-4">
			<h2 class="text-lg font-semibold">Document Versions</h2>

			{#if data.term.term_version.length === 0}
				<Card.Root>
					<Card.Content class="flex flex-col items-center justify-center py-12 text-center">
						<FileText class="mb-4 h-12 w-12 text-muted-foreground" />
						<h3 class="mb-2 text-lg font-semibold">No Versions Yet</h3>
						<p class="mb-4 max-w-md text-sm text-muted-foreground">
							Create your first version to make this document available for customers to sign.
						</p>
						<Button href="/private/term-version/new?term_id={data.term.id}">
							<Plus class="mr-2 h-4 w-4" />
							Create First Version
						</Button>
					</Card.Content>
				</Card.Root>
			{:else}
				<div class="grid gap-4">
					{#each data.term.term_version as version}
						<Card.Root>
							<Card.Header>
								<div class="flex items-start justify-between">
									<div class="space-y-1">
										<div class="flex items-center gap-2">
											<Card.Title class="text-lg">Version {version.version_number}</Card.Title>
											{#if version.is_current}
												<Badge class="gap-1">
													<CheckCircle2 class="h-3 w-3" />
													Current
												</Badge>
											{/if}
											{#if !version.published_at}
												<Badge variant="secondary" class="gap-1">
													<Clock class="h-3 w-3" />
													Draft
												</Badge>
											{/if}
										</div>
										<Card.Description>
											Created {formatDate(version.created_at)} by {version.creator?.auto_user_email}
										</Card.Description>
									</div>
									<div class="flex items-center gap-2">
										{#if !version.is_current && version.published_at}
											<form method="POST" action="?/setCurrentVersion" use:enhance>
												<input type="hidden" name="version_id" value={version.id} />
												<Button type="submit" variant="outline" size="sm">Set as Current</Button>
											</form>
										{/if}
										<Button href="/private/term-version/{version.id}" variant="ghost" size="sm">
											View Details
										</Button>
									</div>
								</div>
								{#if version.published_at}
									<div class="mt-2 flex gap-4 text-sm text-muted-foreground">
										<span>Published: {formatDate(version.published_at)}</span>
										{#if version.content_hash}
											<span>Hash: {version.content_hash.substring(0, 8)}...</span>
										{/if}
									</div>
								{/if}
							</Card.Header>
						</Card.Root>
					{/each}
				</div>
			{/if}
		</div>
	</div>
{/snippet}

<PageContainer
	title={getLocalizedText(data.term.title, 'en')}
	description={getLocalizedText(data.term.term_kind.title, 'en')}
	{content}
/>
