import { z } from 'zod';

const requiredLocalizedTextSchema = z.object({
	en: z.string().min(1, 'English title is required'),
	zh: z.string().optional(),
	ja: z.string().optional(),
	ko: z.string().optional()
});

export const programSchema = z.object({
	title: requiredLocalizedTextSchema,
	product_kind: z.string().min(1, 'Product kind is required'),
	product_kind_locked: z.boolean().default(true),
	event_kind: z.string().optional().nullable(),
	event_kind_locked: z.boolean().default(true),
	event_duration_minute_min: z.number().int().min(0).default(0),
	event_duration_minute_max: z.number().int().min(0).optional().nullable(),
	event_count_min: z.number().int().min(0).default(0),
	event_count_max: z.number().int().min(0).optional().nullable(),
	semantic_order: z.number().int().default(0)
});

export const programPriceSchema = z.object({
	id: z.string().uuid().optional(),
	price_id: z.string().uuid().min(1, 'Price is required'),
	cost_unit_fixed: z.number().min(0).optional().nullable(),
	cost_unit_per_minute: z.number().min(0).optional().nullable(),
	cost_unit_locked: z.boolean().default(false),
	cost_unit_min: z.number().min(0).default(0),
	cost_unit_max: z.number().min(0).optional().nullable(),
	order_requirement_id: z.string().uuid().optional().nullable(),
	order_requirement_passcode: z.string().optional().nullable(),
	order_requirement_passcode_locked: z.boolean().default(false),
	is_required: z.boolean().default(true),
	_action: z.enum(['create', 'update', 'delete']).optional()
});

export const programMetadataWikipageSchema = z.object({
	id: z.string().uuid().optional(),
	metadata_wikipage_relation: z.string().min(1, 'Metadata relation is required'),
	is_required: z.boolean().default(true),
	count_min: z.number().int().min(0).default(1),
	count_max: z.number().int().min(0).optional().nullable(),
	allowed_wikipage_ids: z.array(z.string().uuid()).optional().nullable(),
	_action: z.enum(['create', 'update', 'delete']).optional()
});

export const fullProgramSchema = z.object({
	...programSchema.shape,
	prices: z.array(programPriceSchema).default([]),
	metadata_wikipages: z.array(programMetadataWikipageSchema).default([])
});
