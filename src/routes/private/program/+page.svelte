<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import DataTable from '$lib/components/shared/DataTable.svelte';
	import { getLocalizedText, type LocalizedText, type LocaleKey } from '$lib/utils/localization';
	import { PageContainer } from '$lib/components/layout';
	import {
		createLocalizedColumn,
		createSortableColumn,
		createDateColumn,
		createActionsColumn
	} from '$lib/components/shared/table-utils';
	import type { ColumnDef } from '@tanstack/table-core';
	import type { Database } from '$lib/supabase/database.types';
	import { goto } from '$app/navigation';
	import type { Json } from '$lib/supabase/database.types';

	// Define program type with relations
	type Program = Database['public']['Tables']['program']['Row'] & {
		product_kind: Database['public']['Tables']['product_kind']['Row'] | null;
		event_kind: Database['public']['Tables']['event_kind']['Row'] | null;
		program_price: Array<
			Database['public']['Tables']['program_price']['Row'] & {
				price: Database['public']['Tables']['price']['Row'] | null;
			}
		>;
		program_metadata_wikipage: Array<
			Database['public']['Tables']['program_metadata_wikipage']['Row'] & {
				metadata_wikipage_relation:
					| Database['public']['Tables']['metadata_wikipage_relation']['Row']
					| null;
			}
		>;
	};

	interface Props {
		data: {
			programs: Program[];
			productKinds: Database['public']['Tables']['product_kind']['Row'][];
			eventKinds: Database['public']['Tables']['event_kind']['Row'][];
		};
	}

	let { data }: Props = $props();
	let { programs } = $derived(data);

	const currentLocale: LocaleKey = 'en';

	function navigateToCreate() {
		goto('/private/program/new');
	}

	function navigateToEdit(program: Program) {
		goto(`/private/program/${program.id}`);
	}

	// Format event duration display
	function formatDuration(program: Program): string {
		if (!program.event_kind) return 'N/A';
		const min = program.event_duration_minute_min;
		const max = program.event_duration_minute_max;

		if (max === null) return `${min}+ min`;
		if (min === max) return `${min} min`;
		return `${min}-${max} min`;
	}

	// Format event count display
	function formatEventCount(program: Program): string {
		if (!program.event_kind) return 'N/A';
		const min = program.event_count_min;
		const max = program.event_count_max;

		if (max === null) return `${min}+`;
		if (min === max) return `${min}`;
		return `${min}-${max}`;
	}

	const columns: ColumnDef<Program, any>[] = [
		createLocalizedColumn<Program>('title', 'Title', (row) => row.title as Json, currentLocale),
		createSortableColumn<Program>(
			'product_kind',
			'Product Type',
			(row) => row.product_kind?.id || 'N/A'
		),
		createSortableColumn<Program>('event_kind', 'Event Type', (row) => row.event_kind?.id || 'N/A'),
		{
			accessorKey: 'duration',
			header: 'Duration',
			cell: ({ row }) => formatDuration(row.original)
		},
		{
			accessorKey: 'eventCount',
			header: 'Event Count',
			cell: ({ row }) => formatEventCount(row.original)
		},
		{
			accessorKey: 'prices',
			header: 'Prices',
			cell: ({ row }) => row.original.program_price.length
		},
		{
			accessorKey: 'metadata',
			header: 'Metadata Fields',
			cell: ({ row }) => row.original.program_metadata_wikipage.length
		},
		createSortableColumn<Program>('semantic_order', 'Order', (row) => row.semantic_order),
		createDateColumn<Program>('created_at', 'Created', (row) => row.created_at),
		createActionsColumn<Program>({
			label: 'Edit',
			onClick: (row) => navigateToEdit(row)
		})
	];
</script>

{#snippet actions()}
	<Button onclick={navigateToCreate}>Create Program</Button>
{/snippet}

{#snippet content()}
	<DataTable data={programs} {columns} onRowAction={navigateToEdit} />
{/snippet}

<PageContainer
	title="Programs"
	description="Manage product templates and their pricing configurations"
	{actions}
	{content}
/>
