import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';

export const load = (async ({ depends, locals: { supabase, brand, user } }) => {
	depends('supabase:db:programs');

	// Check if user is brand owner
	if (!user || user.id !== brand.owner_profile_id) {
		throw error(403, 'You must be the brand owner to access this page');
	}

	// Load programs with related data
	const { data: programs, error: programsError } = await supabase
		.from('program')
		.select(
			`
      *,
      product_kind (*),
      event_kind (*),
      program_price (
        *,
        price (
          *,
          title
        )
      ),
      program_metadata_wikipage (
        *,
        metadata_wikipage_relation (
          *,
          title
        )
      )
    `
		)
		.eq('brand_id', brand.id)
		.order('semantic_order', { ascending: true })
		.order('created_at', { ascending: false });

	if (programsError) {
		console.error('Error loading programs:', programsError);
		throw error(500, 'Failed to load programs');
	}

	// Load available product kinds
	const { data: productKinds } = await supabase.from('product_kind').select('*').order('id');

	// Load available event kinds
	const { data: eventKinds } = await supabase.from('event_kind').select('*').order('id');

	return {
		programs: programs ?? [],
		productKinds: productKinds ?? [],
		eventKinds: eventKinds ?? []
	};
}) satisfies PageServerLoad;
