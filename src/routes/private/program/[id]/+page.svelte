<script lang="ts">
	import { PageContainer } from '$lib/components/layout';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import * as Select from '$lib/components/ui/select';
	import { Switch } from '$lib/components/ui/switch';
	import { Input } from '$lib/components/ui/input';
	import { Control, Label, Field, FieldErrors } from 'formsnap';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { fullProgramSchema } from '../schema';
	import { goto } from '$app/navigation';
	import { ensureLocalizedText, getLocalizedText } from '$lib/utils/localization';
	import type { Database } from '$lib/supabase/database.types';
	import type { Json } from '$lib/supabase/database.types';
	import { Trash2, Plus } from '@lucide/svelte';
	import { toast } from 'svelte-sonner';

	interface Props {
		data: {
			form: any;
			program: any;
			productKinds: Database['public']['Tables']['product_kind']['Row'][];
			eventKinds: Database['public']['Tables']['event_kind']['Row'][];
			prices: Database['public']['Tables']['price']['Row'][];
			metadataRelations: Database['public']['Tables']['metadata_wikipage_relation']['Row'][];
			orderRequirements: Database['public']['Tables']['order_requirement']['Row'][];
			wikipages: Database['public']['Tables']['wikipage']['Row'][];
		};
	}

	let { data }: Props = $props();

	const superFormInstance = superForm(data.form, {
		validators: zodClient(fullProgramSchema),
		dataType: 'json',
		taintedMessage: null,
		onResult: ({ result }) => {
			if (result.type === 'success' && result.data?.message) {
				// Show success toast
				toast.success(result.data.message);
				// Navigate to program list after a short delay
				setTimeout(() => {
					goto('/private/program');
				}, 500);
			} else if (result.type === 'redirect') {
				// Handle redirect if still used
				goto(result.location);
			} else if (result.type === 'failure' && result.data?.message) {
				toast.error(result.data.message);
			}
		}
	});

	const { form: formData, errors, enhance, submitting, message } = superFormInstance;

	// Initialize form data if editing
	if (data.program && data.form.data) {
		data.form.data.title = ensureLocalizedText(data.program.title as Json);
	}

	const isNew = !data.program;
	const pageTitle = isNew ? 'Create Program' : 'Edit Program';
	const pageDescription = isNew
		? 'Create a new product template'
		: `Editing "${getLocalizedText(data.program?.title || {})}"`;

	function addPrice() {
		$formData.prices = [
			...$formData.prices,
			{
				price_id: '',
				cost_unit_fixed: null,
				cost_unit_per_minute: null,
				cost_unit_locked: false,
				cost_unit_min: 0,
				cost_unit_max: null,
				order_requirement_id: null,
				order_requirement_passcode: null,
				order_requirement_passcode_locked: false,
				is_required: true,
				_action: 'create'
			}
		];
	}

	function removePrice(index: number) {
		const price = $formData.prices[index];
		if (price.id) {
			// Mark existing price for deletion
			$formData.prices[index]._action = 'delete';
		} else {
			// Remove new price from array
			$formData.prices = $formData.prices.filter((_: any, i: number) => i !== index);
		}
	}

	function addMetadata() {
		$formData.metadata_wikipages = [
			...$formData.metadata_wikipages,
			{
				metadata_wikipage_relation: '',
				is_required: true,
				count_min: 1,
				count_max: null,
				allowed_wikipage_ids: null,
				_action: 'create'
			}
		];
	}

	function removeMetadata(index: number) {
		const metadata = $formData.metadata_wikipages[index];
		if (metadata.id) {
			// Mark existing metadata for deletion
			$formData.metadata_wikipages[index]._action = 'delete';
		} else {
			// Remove new metadata from array
			$formData.metadata_wikipages = $formData.metadata_wikipages.filter(
				(_: any, i: number) => i !== index
			);
		}
	}
</script>

{#snippet actions()}
	<div class="flex gap-2">
		<Button variant="outline" onclick={() => goto('/private/program')}>Cancel</Button>
		<Button type="submit" form="program-form" disabled={$submitting}>
			{$submitting ? 'Saving...' : 'Save'}
		</Button>
	</div>
{/snippet}

{#snippet content()}
	<form id="program-form" method="POST" use:enhance class="space-y-8">
		<!-- Basic Information -->
		<Card.Root>
			<Card.Header>
				<Card.Title>Basic Information</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-6">
				<LocalizedTextControl form={superFormInstance} name="title" label="Title" />

				<Field form={superFormInstance} name="product_kind">
					<Control>
						{#snippet children({ props })}
							<Label>Product Kind</Label>
							<Select.Root type="single" bind:value={$formData.product_kind} name={props.name}>
								<Select.Trigger class={`w-full ${$errors.product_kind ? 'border-red-500' : ''}`}>
									{data.productKinds.find((k) => k.id === $formData.product_kind)?.id ||
										'Select product kind'}
								</Select.Trigger>
								<Select.Content>
									{#each data.productKinds as kind}
										<Select.Item value={kind.id}>{kind.id}</Select.Item>
									{/each}
								</Select.Content>
							</Select.Root>
						{/snippet}
					</Control>
					<FieldErrors class="mt-1 text-xs text-red-500" />
				</Field>

				<Field form={superFormInstance} name="product_kind_locked">
					<Control>
						{#snippet children({ props })}
							<div class="flex items-center space-x-2">
								<Switch bind:checked={$formData.product_kind_locked} {...props} />
								<Label>Lock product kind (prevents changes in products)</Label>
							</div>
						{/snippet}
					</Control>
				</Field>

				<Field form={superFormInstance} name="semantic_order">
					<Control>
						{#snippet children({ props })}
							<Label>Display Order</Label>
							<Input
								type="number"
								bind:value={$formData.semantic_order}
								{...props}
								class={$errors.semantic_order ? 'border-red-500' : ''}
							/>
						{/snippet}
					</Control>
					<FieldErrors class="mt-1 text-xs text-red-500" />
				</Field>
			</Card.Content>
		</Card.Root>

		<!-- Event Configuration -->
		<Card.Root>
			<Card.Header>
				<Card.Title>Event Configuration</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-6">
				<Field form={superFormInstance} name="event_kind">
					<Control>
						{#snippet children({ props })}
							<Label>Event Kind (Optional)</Label>
							<Select.Root type="single" bind:value={$formData.event_kind} name={props.name}>
								<Select.Trigger class="w-full">
									{data.eventKinds.find((k) => k.id === $formData.event_kind)?.id || 'No event'}
								</Select.Trigger>
								<Select.Content>
									<Select.Item value={''}>No event</Select.Item>
									{#each data.eventKinds as kind}
										<Select.Item value={kind.id}>{kind.id}</Select.Item>
									{/each}
								</Select.Content>
							</Select.Root>
						{/snippet}
					</Control>
				</Field>

				<Field form={superFormInstance} name="event_kind_locked">
					<Control>
						{#snippet children({ props })}
							<div class="flex items-center space-x-2">
								<Switch bind:checked={$formData.event_kind_locked} {...props} />
								<Label>Lock event kind</Label>
							</div>
						{/snippet}
					</Control>
				</Field>

				<Field form={superFormInstance} name="event_duration_minute_min">
					<Control>
						{#snippet children({ props })}
							<Label>Minimum Duration (minutes)</Label>
							<Input
								type="number"
								bind:value={$formData.event_duration_minute_min}
								{...props}
								class={$errors.event_duration_minute_min ? 'border-red-500' : ''}
							/>
						{/snippet}
					</Control>
					<FieldErrors class="mt-1 text-xs text-red-500" />
				</Field>

				<Field form={superFormInstance} name="event_duration_minute_max">
					<Control>
						{#snippet children({ props })}
							<Label>Maximum Duration (minutes)</Label>
							<Input
								type="number"
								bind:value={$formData.event_duration_minute_max}
								{...props}
								placeholder="Leave empty for unlimited"
								class={$errors.event_duration_minute_max ? 'border-red-500' : ''}
							/>
						{/snippet}
					</Control>
					<FieldErrors class="mt-1 text-xs text-red-500" />
				</Field>

				<Field form={superFormInstance} name="event_count_min">
					<Control>
						{#snippet children({ props })}
							<Label>Minimum Event Count</Label>
							<Input
								type="number"
								bind:value={$formData.event_count_min}
								{...props}
								class={$errors.event_count_min ? 'border-red-500' : ''}
							/>
						{/snippet}
					</Control>
					<FieldErrors class="mt-1 text-xs text-red-500" />
				</Field>

				<Field form={superFormInstance} name="event_count_max">
					<Control>
						{#snippet children({ props })}
							<Label>Maximum Event Count</Label>
							<Input
								type="number"
								bind:value={$formData.event_count_max}
								{...props}
								placeholder="Leave empty for unlimited"
								class={$errors.event_count_max ? 'border-red-500' : ''}
							/>
						{/snippet}
					</Control>
					<FieldErrors class="mt-1 text-xs text-red-500" />
				</Field>
			</Card.Content>
		</Card.Root>

		<!-- Pricing Configuration -->
		<Card.Root>
			<Card.Header>
				<Card.Title>Pricing Configuration</Card.Title>
				<Card.Description>Define the pricing options available for this program</Card.Description>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div class="mb-4 flex justify-end">
					<Button type="button" size="sm" onclick={addPrice}>
						<Plus class="mr-2 h-4 w-4" />
						Add Price
					</Button>
				</div>
				{#each $formData.prices as price, index}
					{#if price._action !== 'delete'}
						<Card.Root class="p-4">
							<div class="space-y-4">
								<div class="flex items-center justify-between">
									<h4 class="font-medium">Price Option {index + 1}</h4>
									<Button
										type="button"
										variant="ghost"
										size="sm"
										onclick={() => removePrice(index)}
									>
										<Trash2 class="h-4 w-4" />
									</Button>
								</div>

								<Field form={superFormInstance} name={`prices[${index}].price_id`}>
									<Control>
										{#snippet children({ props })}
											<Label>Price Type</Label>
											<Select.Root type="single" bind:value={price.price_id} name={props.name}>
												<Select.Trigger class="w-full">
													{data.prices.find((p) => p.id === price.price_id)
														? getLocalizedText(
																data.prices.find((p) => p.id === price.price_id)?.title || {}
															)
														: 'Select price type'}
												</Select.Trigger>
												<Select.Content>
													{#each data.prices as priceOption}
														<Select.Item value={priceOption.id}>
															{getLocalizedText(priceOption.title as Json)}
														</Select.Item>
													{/each}
												</Select.Content>
											</Select.Root>
										{/snippet}
									</Control>
								</Field>

								<Field form={superFormInstance} name={`prices[${index}].cost_unit_fixed`}>
									<Control>
										{#snippet children({ props })}
											<Label>Fixed Cost</Label>
											<Input
												type="number"
												step="0.01"
												bind:value={price.cost_unit_fixed}
												{...props}
												placeholder="Leave empty to use per-minute pricing"
											/>
										{/snippet}
									</Control>
								</Field>

								<Field form={superFormInstance} name={`prices[${index}].cost_unit_per_minute`}>
									<Control>
										{#snippet children({ props })}
											<Label>Cost Per Minute</Label>
											<Input
												type="number"
												step="0.01"
												bind:value={price.cost_unit_per_minute}
												{...props}
												placeholder="Leave empty to use fixed pricing"
											/>
										{/snippet}
									</Control>
								</Field>

								<Field form={superFormInstance} name={`prices[${index}].is_required`}>
									<Control>
										{#snippet children({ props })}
											<div class="flex items-center space-x-2">
												<Switch bind:checked={price.is_required} {...props} />
												<Label>Required price (cannot be removed from products)</Label>
											</div>
										{/snippet}
									</Control>
								</Field>
							</div>
						</Card.Root>
					{/if}
				{/each}
			</Card.Content>
		</Card.Root>

		<!-- Metadata Configuration -->
		<Card.Root>
			<Card.Header>
				<Card.Title>Metadata Relationships</Card.Title>
				<Card.Description>
					Define which metadata wikipage relationships are required or optional for products created
					with this program. Each relationship represents a connection between the product and
					wikipages (e.g., "instructor", "location", "category").
				</Card.Description>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div class="mb-4 flex justify-end">
					<Button type="button" size="sm" onclick={addMetadata}>
						<Plus class="mr-2 h-4 w-4" />
						Add Metadata Relationship
					</Button>
				</div>
				{#each $formData.metadata_wikipages as metadata, index}
					{#if metadata._action !== 'delete'}
						<Card.Root class="p-4">
							<div class="space-y-4">
								<div class="flex items-center justify-between">
									<h4 class="font-medium">
										{data.metadataRelations.find(
											(r) => r.id === metadata.metadata_wikipage_relation
										)
											? getLocalizedText(
													data.metadataRelations.find(
														(r) => r.id === metadata.metadata_wikipage_relation
													)?.title || {}
												)
											: `Relationship ${index + 1}`}
									</h4>
									<Button
										type="button"
										variant="ghost"
										size="sm"
										onclick={() => removeMetadata(index)}
									>
										<Trash2 class="h-4 w-4" />
									</Button>
								</div>

								<Field
									form={superFormInstance}
									name={`metadata_wikipages[${index}].metadata_wikipage_relation`}
								>
									<Control>
										{#snippet children({ props })}
											<Label>Relationship Type</Label>
											<Select.Root
												type="single"
												bind:value={metadata.metadata_wikipage_relation}
												name={props.name}
											>
												<Select.Trigger class="w-full">
													{data.metadataRelations.find(
														(r) => r.id === metadata.metadata_wikipage_relation
													)
														? getLocalizedText(
																data.metadataRelations.find(
																	(r) => r.id === metadata.metadata_wikipage_relation
																)?.title || {}
															)
														: 'Select relationship type'}
												</Select.Trigger>
												<Select.Content>
													{#each data.metadataRelations as relation}
														<Select.Item value={relation.id}>
															{getLocalizedText(relation.title as Json)}
														</Select.Item>
													{/each}
												</Select.Content>
											</Select.Root>
										{/snippet}
									</Control>
								</Field>

								<Field form={superFormInstance} name={`metadata_wikipages[${index}].is_required`}>
									<Control>
										{#snippet children({ props })}
											<div class="flex items-center space-x-2">
												<Switch bind:checked={metadata.is_required} {...props} />
												<Label>Required relationship (must be filled when creating products)</Label>
											</div>
										{/snippet}
									</Control>
								</Field>

								<Field form={superFormInstance} name={`metadata_wikipages[${index}].count_min`}>
									<Control>
										{#snippet children({ props })}
											<Label>Minimum Count</Label>
											<Input
												type="number"
												bind:value={metadata.count_min}
												{...props}
												placeholder="Minimum number of wikipages for this relationship"
											/>
										{/snippet}
									</Control>
								</Field>

								<Field form={superFormInstance} name={`metadata_wikipages[${index}].count_max`}>
									<Control>
										{#snippet children({ props })}
											<Label>Maximum Count</Label>
											<Input
												type="number"
												bind:value={metadata.count_max}
												{...props}
												placeholder="Leave empty for unlimited"
											/>
										{/snippet}
									</Control>
								</Field>
							</div>
						</Card.Root>
					{/if}
				{/each}
			</Card.Content>
		</Card.Root>
	</form>
{/snippet}

<PageContainer title={pageTitle} description={pageDescription} {actions} {content} />
