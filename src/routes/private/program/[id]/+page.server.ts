import type { Actions, PageServerLoad } from './$types';
import { error, fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { fullProgramSchema } from '../schema';
import type { Database } from '$lib/supabase/database.types';

export const load = (async ({ params, locals: { supabase, brand, user } }) => {
	// Check if user is brand owner
	if (!user || user.id !== brand.owner_profile_id) {
		throw error(403, 'You must be the brand owner to access this page');
	}

	const isNew = params.id === 'new';
	let program = null;

	if (!isNew) {
		// Load existing program with all relations
		const { data, error: programError } = await supabase
			.from('program')
			.select(
				`
        *,
        product_kind (*),
        event_kind (*),
        program_price (
          *,
          price (*)
        ),
        program_metadata_wikipage (
          *,
          metadata_wikipage_relation (*)
        )
      `
			)
			.eq('id', params.id)
			.eq('brand_id', brand.id)
			.single();

		if (programError || !data) {
			throw error(404, 'Program not found');
		}

		program = data;
	}

	// Load reference data
	const [
		{ data: productKinds },
		{ data: eventKinds },
		{ data: prices },
		{ data: metadataRelations },
		{ data: orderRequirements },
		{ data: wikipages }
	] = await Promise.all([
		supabase.from('product_kind').select('*').order('id'),
		supabase.from('event_kind').select('*').order('id'),
		supabase.from('price').select('*').eq('brand_id', brand.id).order('id'),
		supabase.from('metadata_wikipage_relation').select('*').order('id'),
		supabase.from('order_requirement').select('*').eq('brand_id', brand.id).order('id'),
		supabase
			.from('wikipage')
			.select('*')
			.eq('brand_id', brand.id)
			.order('created_at', { ascending: false })
	]);

	// Initialize form with program data or defaults
	const form = await superValidate(
		program
			? {
					title: program.title,
					product_kind: program.product_kind?.id || '',
					product_kind_locked: program.product_kind_locked,
					event_kind: program.event_kind?.id || null,
					event_kind_locked: program.event_kind_locked,
					event_duration_minute_min: program.event_duration_minute_min,
					event_duration_minute_max: program.event_duration_minute_max,
					event_count_min: program.event_count_min,
					event_count_max: program.event_count_max,
					semantic_order: program.semantic_order,
					prices: program.program_price || [],
					metadata_wikipages: program.program_metadata_wikipage || []
				}
			: undefined,
		zod(fullProgramSchema)
	);

	return {
		form,
		program,
		productKinds: productKinds ?? [],
		eventKinds: eventKinds ?? [],
		prices: prices ?? [],
		metadataRelations: metadataRelations ?? [],
		orderRequirements: orderRequirements ?? [],
		wikipages: wikipages ?? []
	};
}) satisfies PageServerLoad;

export const actions = {
	default: async ({ request, params, locals: { supabase, brand, user } }) => {
		// Check if user is brand owner
		if (!user || user.id !== brand.owner_profile_id) {
			throw error(403, 'You must be the brand owner to access this page');
		}

		const form = await superValidate(request, zod(fullProgramSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const isNew = params.id === 'new';
		const now = new Date().toISOString();

		try {
			// Start a transaction
			let programId: string;

			// Prepare program data
			const programData: Database['public']['Tables']['program']['Insert'] = {
				title: form.data.title,
				product_kind: form.data.product_kind,
				product_kind_locked: form.data.product_kind_locked,
				event_kind: form.data.event_kind,
				event_kind_locked: form.data.event_kind_locked,
				event_duration_minute_min: form.data.event_duration_minute_min,
				event_duration_minute_max: form.data.event_duration_minute_max,
				event_count_min: form.data.event_count_min,
				event_count_max: form.data.event_count_max,
				semantic_order: form.data.semantic_order,
				brand_id: brand.id,
				updated_at: now
			};

			if (isNew) {
				programData.created_at = now;
				programData.creator_id = user.id;

				// Insert new program
				const { data: newProgram, error: insertError } = await supabase
					.from('program')
					.insert(programData)
					.select()
					.single();

				if (insertError || !newProgram) {
					console.error('Error creating program:', insertError);
					return fail(500, { form, message: 'Failed to create program' });
				}

				programId = newProgram.id;
			} else {
				programId = params.id;

				// Update existing program
				const { error: updateError } = await supabase
					.from('program')
					.update(programData)
					.eq('id', programId)
					.eq('brand_id', brand.id);

				if (updateError) {
					console.error('Error updating program:', updateError);
					return fail(500, { form, message: 'Failed to update program' });
				}
			}

			// Handle program_price updates
			if (form.data.prices) {
				// Get existing prices
				const { data: existingPrices } = await supabase
					.from('program_price')
					.select('id')
					.eq('program_id', programId);

				const existingIds = new Set((existingPrices || []).map((p) => p.id));
				const updatedIds = new Set<string>();

				for (const price of form.data.prices) {
					if (price._action === 'delete' && price.id) {
						// Delete price
						await supabase.from('program_price').delete().eq('id', price.id);
					} else if (price._action === 'create' || !price.id) {
						// Create new price
						const { _action, id, ...priceData } = price;
						await supabase.from('program_price').insert({
							...priceData,
							program_id: programId
						});
					} else if (price.id) {
						// Update existing price
						const { _action, id, ...priceData } = price;
						await supabase.from('program_price').update(priceData).eq('id', id);
						updatedIds.add(id);
					}
				}

				// Delete prices that weren't in the update
				const toDelete = Array.from(existingIds).filter((id) => !updatedIds.has(id));
				if (toDelete.length > 0) {
					await supabase.from('program_price').delete().in('id', toDelete);
				}
			}

			// Handle program_metadata_wikipage updates (similar pattern)
			if (form.data.metadata_wikipages) {
				// Get existing metadata
				const { data: existingMetadata } = await supabase
					.from('program_metadata_wikipage')
					.select('id')
					.eq('program_id', programId);

				const existingIds = new Set((existingMetadata || []).map((m) => m.id));
				const updatedIds = new Set<string>();

				for (const metadata of form.data.metadata_wikipages) {
					if (metadata._action === 'delete' && metadata.id) {
						// Delete metadata
						await supabase.from('program_metadata_wikipage').delete().eq('id', metadata.id);
					} else if (metadata._action === 'create' || !metadata.id) {
						// Create new metadata
						const { _action, id, ...metadataData } = metadata;
						await supabase.from('program_metadata_wikipage').insert({
							...metadataData,
							program_id: programId
						});
					} else if (metadata.id) {
						// Update existing metadata
						const { _action, id, ...metadataData } = metadata;
						await supabase.from('program_metadata_wikipage').update(metadataData).eq('id', id);
						updatedIds.add(id);
					}
				}

				// Delete metadata that weren't in the update
				const toDelete = Array.from(existingIds).filter((id) => !updatedIds.has(id));
				if (toDelete.length > 0) {
					await supabase.from('program_metadata_wikipage').delete().in('id', toDelete);
				}
			}

			// Redirect to programs list
			return {
				form,
				message: `Program ${isNew ? 'created' : 'updated'} successfully`
			};
		} catch (err) {
			// Re-throw redirects
			if (err instanceof Response) throw err;

			console.error('Unexpected error:', err);
			return fail(500, { form, message: 'An unexpected error occurred' });
		}
	}
} satisfies Actions;
