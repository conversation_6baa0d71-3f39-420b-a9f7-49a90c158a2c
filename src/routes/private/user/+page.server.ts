import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import type { ProfileWithUser } from './types';

export type Profile = ProfileWithUser;

export const load = (async ({ locals: { supabase, brand }, url }) => {
	try {
		// Check if brand ID exists
		if (!brand?.id) {
			throw error(400, 'Brand ID is required');
		}

		// Get search query from URL parameters
		const searchQuery = url.searchParams.get('search')?.trim().toLowerCase();
		console.log(`[User] Searching for: ${searchQuery || 'none'}`);

		// Build query for profiles managed by current brand
		let query = supabase
			.from('profile')
			.select(
				`
				*,
				brandManagedProfile:brand_managed_profile!inner(
					id,
					brand_id,
					managed_profile_id,
					preferred_landmark_ids
				)
			`
			)
			.eq('brand_managed_profile.brand_id', brand.id);

		// Apply search filters if search query exists
		if (searchQuery) {
			// Build Postgres-compatible query for each field we want to search
			const filters = [];

			// Add text field filters
			filters.push(`username.ilike.%${searchQuery}%`);
			filters.push(`auto_user_email.ilike.%${searchQuery}%`);
			filters.push(`auto_user_phone.ilike.%${searchQuery}%`);

			// For JSONB fields, we need to use the correct syntax
			// Use ->> operator to extract values as text from JSONB
			filters.push(`given_name->>en.ilike.%${searchQuery}%`);
			filters.push(`family_name->>en.ilike.%${searchQuery}%`);

			// Apply the filters with OR
			query = query.or(filters.join(','));
		}

		// Execute query with ordering
		const { data: profiles, error: fetchError } = await query.order('created_at', {
			ascending: false
		});

		if (fetchError) {
			console.error('[User] Failed to fetch profiles:', fetchError);
			throw error(500, 'Failed to fetch profiles');
		}

		// Apply additional client-side filtering if needed for complex cases
		let finalProfiles = profiles || [];
		if (searchQuery) {
			finalProfiles = finalProfiles.filter((profile) => {
				const email = profile.auto_user_email?.toLowerCase() || '';
				const phone = profile.auto_user_phone?.toLowerCase() || '';
				const username = profile.username?.toLowerCase() || '';
				const givenName =
					typeof profile.given_name === 'object' && profile.given_name?.en
						? profile.given_name.en.toLowerCase()
						: '';
				const familyName =
					typeof profile.family_name === 'object' && profile.family_name?.en
						? profile.family_name.en.toLowerCase()
						: '';

				return (
					!searchQuery ||
					email.includes(searchQuery) ||
					phone.includes(searchQuery) ||
					username.includes(searchQuery) ||
					givenName.includes(searchQuery) ||
					familyName.includes(searchQuery)
				);
			});
		}

		console.log(`[User] Found ${finalProfiles.length} profiles managed by brand ${brand.id}`);

		return {
			profiles: finalProfiles as Profile[],
			searchQuery: searchQuery || ''
		};
	} catch (e) {
		console.error('[User] Server error:', e);
		throw error(500, 'Internal server error');
	}
}) satisfies PageServerLoad;
