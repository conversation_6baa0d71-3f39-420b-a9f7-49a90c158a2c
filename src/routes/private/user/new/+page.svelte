<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { ArrowLeft } from '@lucide/svelte';
	import { goto } from '$app/navigation';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';

	// New user form state
	let firstName = $state('');
	let lastName = $state('');
	let username = $state('');
	let email = $state('');
	let phone = $state('');
	let submitting = $state(false);
</script>

<div class="container mx-auto p-4">
	<div class="mb-6">
		<Button variant="outline" onclick={() => goto('/private/user')}>
			<ArrowLeft class="mr-2 size-4" />
			Back to Users
		</Button>
	</div>

	<div class="mb-6">
		<h1 class="text-3xl font-bold">Create New User</h1>
		<p class="text-muted-foreground">Add a new user profile to the system</p>
	</div>

	<Card class="max-w-xl">
		<CardHeader>
			<CardTitle>New User Information</CardTitle>
			<CardDescription>Enter the details for the new user profile</CardDescription>
		</CardHeader>
		<CardContent>
			<form class="space-y-4">
				<div class="grid grid-cols-2 gap-4">
					<div class="space-y-2">
						<Label for="firstName">First Name</Label>
						<Input id="firstName" bind:value={firstName} placeholder="First name" required />
					</div>
					<div class="space-y-2">
						<Label for="lastName">Last Name</Label>
						<Input id="lastName" bind:value={lastName} placeholder="Last name" required />
					</div>
				</div>

				<div class="space-y-2">
					<Label for="username">Username</Label>
					<Input id="username" bind:value={username} placeholder="Username" required />
				</div>

				<div class="space-y-2">
					<Label for="email">Email</Label>
					<Input id="email" type="email" bind:value={email} placeholder="Email address" />
				</div>

				<div class="space-y-2">
					<Label for="phone">Phone</Label>
					<Input id="phone" type="tel" bind:value={phone} placeholder="Phone number" />
				</div>
			</form>
		</CardContent>
		<CardFooter>
			<Button
				class="w-full"
				onclick={() => alert('Form submission not yet implemented')}
				disabled={submitting}
			>
				{submitting ? 'Creating...' : 'Create User'}
			</Button>
		</CardFooter>
	</Card>
</div>
