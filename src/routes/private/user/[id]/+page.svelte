<script lang="ts">
	import { <PERSON><PERSON> } from '$lib/components/ui/button';
	import { ArrowLeft } from '@lucide/svelte';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { PageData } from './$types';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { goto } from '$app/navigation';
	import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '$lib/components/ui/tabs';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import OrderPriceCard from '../../my-order/OrderPriceCard.svelte';
	import OrderProductCard from '../../my-order/OrderProductCard.svelte';
	import type { ServerOrderPrice, ServerOrderProduct } from '../../my-order/types';
	import { Separator } from '$lib/components/ui/separator';
	import { page } from '$app/state';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	const profile = $derived(data.profile);
	const firstName = $derived(getLocalizedText(profile.given_name as LocalizedText, 'en'));
	const lastName = $derived(getLocalizedText(profile.family_name as LocalizedText, 'en'));

	let activeTab = $state('info');
	let loading = $state(false);

	function handleTabChange(tab: string) {
		activeTab = tab;
	}

	// Get the search params to preserve when going back
	function getBackLink(): string {
		const searchParams = page.url.searchParams;
		if (searchParams.toString()) {
			return `/private/user?${searchParams.toString()}`;
		}
		return '/private/user';
	}

	const backLink = $derived(getBackLink());
</script>

<div class="container mx-auto p-4">
	<div class="mb-6">
		<Button variant="outline" onclick={() => goto(backLink)}>
			<ArrowLeft class="mr-2 size-4" />
			Back to Users
		</Button>
	</div>

	<div class="mb-6">
		<h1 class="text-3xl font-bold">{firstName} {lastName}</h1>
		{#if profile.username}
			<p class="text-muted-foreground">@{profile.username}</p>
		{/if}
	</div>

	<Tabs value={activeTab} class="w-full">
		<TabsList class="mb-6">
			<TabsTrigger value="info" onclick={() => handleTabChange('info')}>Info</TabsTrigger>
			<TabsTrigger value="registrations" onclick={() => handleTabChange('registrations')}>
				Registrations
			</TabsTrigger>
			<TabsTrigger value="class-passes" onclick={() => handleTabChange('class-passes')}>
				Class Passes
			</TabsTrigger>
		</TabsList>

		<TabsContent value="info" class="space-y-6">
			<div class="grid gap-6 md:grid-cols-2">
				<Card>
					<CardHeader>
						<CardTitle>Contact Information</CardTitle>
						<CardDescription>User's contact details</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-4">
							{#if profile.auto_user_email}
								<div>
									<p class="text-sm font-medium">Email</p>
									<p>{profile.auto_user_email}</p>
								</div>
							{/if}
							{#if profile.auto_user_phone}
								<div>
									<p class="text-sm font-medium">Phone</p>
									<p>{profile.auto_user_phone}</p>
								</div>
							{/if}
							{#if profile.website}
								<div>
									<p class="text-sm font-medium">Website</p>
									<p>{profile.website}</p>
								</div>
							{/if}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Social Media</CardTitle>
						<CardDescription>User's social accounts</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-4">
							{#if profile.instagram_id}
								<div>
									<p class="text-sm font-medium">Instagram</p>
									<p>{profile.instagram_id}</p>
								</div>
							{/if}
							{#if profile.twitter_id}
								<div>
									<p class="text-sm font-medium">Twitter</p>
									<p>{profile.twitter_id}</p>
								</div>
							{/if}
							{#if profile.facebook_id}
								<div>
									<p class="text-sm font-medium">Facebook</p>
									<p>{profile.facebook_id}</p>
								</div>
							{/if}
							{#if profile.wechat_id}
								<div>
									<p class="text-sm font-medium">WeChat</p>
									<p>{profile.wechat_id}</p>
								</div>
							{/if}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Profile Information</CardTitle>
						<CardDescription>Additional profile details</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-4">
							{#if profile.about}
								<div>
									<p class="text-sm font-medium">About</p>
									<p>{profile.about}</p>
								</div>
							{/if}
							{#if profile.primary_lang}
								<div>
									<p class="text-sm font-medium">Primary Language</p>
									<p>{profile.primary_lang}</p>
								</div>
							{/if}
							{#if profile.nickname}
								<div>
									<p class="text-sm font-medium">Nickname</p>
									<p>{profile.nickname}</p>
								</div>
							{/if}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Account Information</CardTitle>
						<CardDescription>User account details</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-4">
							<div>
								<p class="text-sm font-medium">ID</p>
								<p class="font-mono text-xs">{profile.id}</p>
							</div>
							{#if profile.created_at}
								<div>
									<p class="text-sm font-medium">Created</p>
									<p>{new Date(profile.created_at).toLocaleString()}</p>
								</div>
							{/if}
							{#if profile.updated_at}
								<div>
									<p class="text-sm font-medium">Last Updated</p>
									<p>{new Date(profile.updated_at).toLocaleString()}</p>
								</div>
							{/if}
						</div>
					</CardContent>
				</Card>
			</div>
		</TabsContent>

		<TabsContent value="registrations" class="space-y-4">
			{#if loading}
				<div class="space-y-4">
					{#each Array(2) as _}
						<Skeleton class="h-48 w-full rounded-lg" />
					{/each}
				</div>
			{:else if !data.orderProducts || data.orderProducts.length === 0}
				<div class="flex flex-col items-center justify-center py-12 text-center">
					<h3 class="text-xl font-semibold">No registrations found</h3>
					<p class="text-muted-foreground">This user hasn't registered for any events yet.</p>
				</div>
			{:else}
				{#each data.orderProducts as orderProduct}
					<OrderProductCard
						{orderProduct}
						viewAllLink={`/private/my-order/order-product/${orderProduct.id}?context=admin&userId=${profile.id}`}
					/>
				{/each}
			{/if}
		</TabsContent>

		<TabsContent value="class-passes" class="space-y-4">
			{#if loading}
				<div class="space-y-4">
					{#each Array(2) as _}
						<Skeleton class="h-48 w-full rounded-lg" />
					{/each}
				</div>
			{:else if !data.orderPrices || data.orderPrices.length === 0}
				<div class="flex flex-col items-center justify-center py-12 text-center">
					<h3 class="text-xl font-semibold">No class passes found</h3>
					<p class="text-muted-foreground">This user hasn't purchased any class passes yet.</p>
				</div>
			{:else}
				{#each data.orderPrices as orderPrice}
					<OrderPriceCard
						{orderPrice}
						showAllHistory={true}
						detailsLink={`/private/my-order/order-price/${orderPrice.id}?context=admin&userId=${profile.id}`}
					/>
				{/each}
			{/if}
		</TabsContent>
	</Tabs>
</div>
