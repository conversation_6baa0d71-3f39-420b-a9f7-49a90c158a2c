import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import type { Profile } from '../+page.server';
import type { ProfileWithUser } from '../types';
import type { ServerOrderPrice, ServerOrderProduct } from '../../my-order/types';

export const load = (async ({ params, locals: { supabase } }) => {
	try {
		const { id } = params;

		if (!id) {
			throw error(400, 'Profile ID is required');
		}

		// Query the specific profile
		const { data: profile, error: fetchError } = await supabase
			.from('profile')
			.select(`*`)
			.eq('id', id)
			.single();

		if (fetchError) {
			console.error('[UserDetail] Failed to fetch profile:', fetchError);
			throw error(404, 'Profile not found');
		}

		if (!profile) {
			throw error(404, 'Profile not found');
		}

		// Try to get auth user data
		try {
			const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(id);

			if (!authError && authUser?.user) {
				// Merge auth user data with profile
				profile.auto_user_email = authUser.user.email || profile.auto_user_email;
				profile.auto_user_phone = authUser.user.phone || profile.auto_user_phone;
			}
		} catch (authErr) {
			// Just log the error and continue - the user might not exist yet
			console.warn('[UserDetail] Auth user not found:', authErr);
		}

		// Fetch order prices (class passes)
		const { data: orderPricesRaw, error: orderPricesError } = await supabase
			.from('order_price')
			.select(
				`
				id,
				created_at,
				deal_units,
				deal_money_int,
				deal_expire_at,
				price_option_purchase_count,
				payer_id,
				auto_price_option_expire_at,
				auto_units_available,
				auto_money_int_unpaid,
				airtable_id,
				priceOption:price_option_id (
					id,
					money_int,
					title
				),
				autoPriceOptionPrice:auto_price_option_price_id (
					payment_kind,
					title,
					color_primary_hex,
					color_primary_semantic,
					brand (
						name_full
					)
				),
				consumerProfile:consumer_profile_id (
					given_name,
					family_name,
					id
				),
				order:order_id (
					id,
					nid
				),
				orderPriceConsumptions:order_price_consumption (
					id,
					orderProduct:order_product_id (
						id,
						canceled_at,
						productPrice:product_price_id (
							product:product_id (
								id,
								title,
								metadata (
								auto_final_title,
								auto_final_subtitle
								),
								autoFirstEvent:auto_first_event_id (
								start_at,
								auto_end_at,
								duration_minute,
								id,
								space:space_id (
									name_full,
									name_short,
									landmark:landmark_id (
										title_short,
										title_full
									)
									)
								),
								eventProducts:event_product (
								event:event_id (
									id,
									start_at,
									auto_end_at,
									duration_minute
								)
							)
						)
					)
				),
				cost_units,
				returned_units,
				created_at
			)
			`
			)
			.eq('payer_id', id)
			.order('auto_price_option_expire_at', { ascending: false });

		if (orderPricesError) {
			console.error('[UserDetail] Failed to fetch class passes:', orderPricesError);
		}

		// Fetch order products (registrations)
		const { data: orderProductsRaw, error: orderProductsError } = await supabase
			.from('order_product')
			.select(
				`
				id,
				consumer_profile_id,
				profile:consumer_profile_id (
				given_name,
				family_name
				),
				orderPriceConsumptions:order_price_consumption (
				id,
				orderPrice:order_price_id (
					auto_units_available,
					auto_money_int_unpaid,
					price_option_purchase_count,
					priceOption:price_option_id (
					id,
					title
					),
					auto_price_option_expire_at
				),
				cost_units,
				returned_units
				),
				auto_units_owed,
				canceled_at,
				created_at,
				product_purchased_count,
				productPrice:product_price_id (
				priceData:price_id (
					color_primary_semantic,
					color_primary_hex,
					title,
					product_classification
				),
				auto_cancel_at_far,
				auto_cancel_at_near,
				auto_cancel_at_far_return_units,
				auto_cancel_at_near_return_units,
				product:product_id (
					id,
					title,
					metadata (
					id,
					auto_final_title,
					auto_final_subtitle,
					custom_attribute,
					metadata_wikipages:metadata_wikipage (
						relation,
						wikipage:wikipage_id (
						id,
						title
						)
					)
					),
					autoFirstEvent:auto_first_event_id (
					start_at,
					auto_end_at,
					id
					),
					eventProducts:event_product (
					event:event_id (
						id,
						title,
						start_at,
						auto_end_at,
						metadata (
						auto_final_title,
						auto_final_subtitle
						),
						space:space_id (
						name_full,
						name_short,
						landmark:landmark_id (
							title_short,
							title_full
						)
						)
					)
					)
				)
				),
				order (
				nid,
				id
				)
			`
			)
			.eq('consumer_profile_id', id)
			.lte('auto_units_owed', 0)
			.is('canceled_at', null)
			.order('created_at', { ascending: false });

		if (orderProductsError) {
			console.error('[UserDetail] Failed to fetch registrations:', orderProductsError);
		}

		// Filter the order prices to match our criteria
		const orderPrices =
			(orderPricesRaw || [])
				.filter((item: any) => item.auto_money_int_unpaid <= 0)
				.map((item) => item as unknown as ServerOrderPrice) || [];

		// Transform order products to match our expected structure
		const orderProducts =
			(orderProductsRaw || []).map((item: any) => {
				// Rename profile to consumer_profile to match our expected structure
				const { profile, ...rest } = item;

				// Create a properly structured object that matches ServerOrderProduct
				const transformedProduct: ServerOrderProduct = {
					...rest,
					consumer_profile: profile,
					productPrice: rest.productPrice
						? {
								...rest.productPrice,
								price: rest.productPrice.priceData
							}
						: null
				};

				return transformedProduct;
			}) || [];

		return {
			profile: profile as ProfileWithUser,
			orderPrices,
			orderProducts
		};
	} catch (e) {
		console.error('[UserDetail] Server error:', e);
		if (e instanceof Error) {
			throw error(500, e.message);
		}
		throw error(500, 'Internal server error');
	}
}) satisfies PageServerLoad;
