<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { PageData } from './$types';
	import DataTable from '$lib/components/shared/DataTable.svelte';
	import { PageContainer } from '$lib/components/layout';
	import {
		createSelectColumn,
		createSortableColumn,
		createDateColumn,
		createActionsColumn
	} from '$lib/components/shared/table-utils';
	import type { ColumnDef } from '@tanstack/table-core';
	import type { Profile } from './+page.server';
	import { goto } from '$app/navigation';
	import { createRawSnippet } from 'svelte';
	import { renderSnippet } from '$lib/components/ui/data-table';
	import { onMount } from 'svelte';

	const { data } = $props<{
		data: PageData & {
			profiles: Profile[];
			searchQuery: string;
		};
	}>();

	console.log('[user/page.svelte] Data:', {
		profilesCount: data.profiles.length,
		searchQuery: data.searchQuery
	});

	// Search functionality
	let searchInput = $state(data.searchQuery);
	let searchTimer: number | undefined = undefined;
	let inputRef: HTMLElement;
	let shouldFocus = $state(false);

	onMount(() => {
		if (data.searchQuery && inputRef instanceof HTMLElement) {
			inputRef.querySelector('input')?.focus();
		}
	});

	// Debounced search function
	function debouncedSearch() {
		clearTimeout(searchTimer);
		searchTimer = window.setTimeout(() => {
			if (searchInput.trim() !== data.searchQuery.trim()) {
				handleSearch();
			}
		}, 500); // 500ms debounce time
	}

	function handleSearch() {
		const url = new URL(window.location.href);
		if (searchInput.trim()) {
			url.searchParams.set('search', searchInput.trim());
		} else {
			url.searchParams.delete('search');
		}
		goto(url.toString()).then(() => {
			// Focus after navigation completes
			setTimeout(() => {
				inputRef.querySelector('input')?.focus();
			}, 0);
		});
	}

	function clearSearch() {
		searchInput = '';
		setTimeout(() => {
			handleSearch();
		}, 0);
	}

	// Define columns for the data table
	const columns: ColumnDef<Profile>[] = [
		{
			accessorKey: 'name',
			header: 'Name',
			cell: ({ row }) => {
				const profile = row.original;
				const nameSnippet = createRawSnippet<[Profile]>((getProfile) => {
					const p = getProfile();
					const firstName = getLocalizedText(p.given_name as LocalizedText, 'en');
					const lastName = getLocalizedText(p.family_name as LocalizedText, 'en');
					return {
						render: () => `<div>${firstName} ${lastName}</div>`
					};
				});
				return renderSnippet(nameSnippet, profile);
			}
		},
		createSortableColumn<Profile>('auto_user_email', 'Email', (row) => row.auto_user_email),
		createSortableColumn<Profile>('auto_user_phone', 'Phone', (row) => row.auto_user_phone),
		createDateColumn<Profile>('created_at', 'Created At', (row) => row.created_at),
		createActionsColumn<Profile>({
			label: 'View',
			onClick: (row) => {
				goto(`/private/user/${row.id}`);
			}
		})
	];
</script>

{#snippet actions()}
	<Button onclick={() => goto('/private/user/new')}>Create New User</Button>
{/snippet}

{#snippet content()}
	<div class="mb-6">
		<div class="relative" bind:this={inputRef}>
			<div class="pointer-events-none absolute inset-y-0 left-0 z-10 flex items-center pl-3">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					stroke="#6b7280"
					stroke-width="2"
					stroke-linecap="round"
					stroke-linejoin="round"
					class="h-4 w-4"
				>
					<circle cx="11" cy="11" r="8"></circle>
					<path d="m21 21-4.3-4.3"></path>
				</svg>
			</div>
			<Input
				type="search"
				placeholder="Search users by name, email, phone..."
				value={searchInput}
				oninput={(e) => {
					searchInput = e.currentTarget.value;
					debouncedSearch();
				}}
				onkeydown={(e) => e.key === 'Enter' && handleSearch()}
				class="w-full pl-10"
			/>
		</div>
	</div>

	<DataTable data={data.profiles} {columns} />
{/snippet}

<PageContainer title="Users" description="Manage user profiles" {actions} {content} />
