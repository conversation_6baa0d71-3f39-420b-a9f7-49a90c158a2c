import { superValidate, message, setError } from 'sveltekit-superforms';
import { formSchema as profileSchema } from './components/ProfileForm.svelte';
import { avatarFormSchema } from './components/AvatarForm.svelte';
import { formSchema as securitySchema } from './components/SecurityForm.svelte';
import { formSchema as notificationsSchema } from './components/NotificationsForm.svelte';
import type { PageServerLoad, Actions } from './$types';
import { fail } from '@sveltejs/kit';
import { zod } from 'sveltekit-superforms/adapters';
import { ensureLocalizedText } from '$lib/utils/localization';
import type { Json } from '$lib/supabase/database.types';

export const load = (async ({ locals: { supabase, user } }) => {
	// Fetch the user's profile data
	const { data: profileData, error: profileError } = await supabase
		.from('profile')
		.select('given_name, family_name, username, avatar_url')
		.eq('id', user?.id)
		.single();

	if (profileError) {
		console.error('Error fetching profile:', profileError);
	}

	// Initialize form data with profile data or defaults
	const profileFormData = {
		given_name: ensureLocalizedText(profileData?.given_name as Json),
		family_name: ensureLocalizedText(profileData?.family_name as Json),
		username: profileData?.username || ''
	};

	// Initialize avatar form data
	const avatarFormData = {
		avatar: undefined // File will be handled on the client
	};

	// Validate forms
	const profile = await superValidate(profileFormData, zod(profileSchema));
	const avatar = await superValidate(avatarFormData, zod(avatarFormSchema));
	const security = await superValidate(zod(securitySchema));
	const notifications = await superValidate(zod(notificationsSchema));

	return {
		profile,
		avatar,
		security,
		notifications,
		user,
		profileData
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	profile: async ({ request, locals: { supabase, user } }) => {
		const form = await superValidate(request, zod(profileSchema));

		if (!form.valid) {
			return message(form, { type: 'error', text: 'Invalid form data' });
		}

		try {
			// Update profile in Supabase
			const { error } = await supabase
				.from('profile')
				.update({
					given_name: form.data.given_name,
					family_name: form.data.family_name,
					username: form.data.username
				})
				.eq('id', user?.id);

			if (error) throw error;

			return message(form, {
				type: 'success',
				text: 'Profile updated successfully'
			});
		} catch (error) {
			console.error('Error updating profile:', error);
			return message(form, {
				type: 'error',
				text: 'Failed to update profile'
			});
		}
	},

	avatar: async ({ request, locals: { supabase, user } }) => {
		// First, get the form data
		const formData = await request.formData();
		const removeAvatar = formData.get('remove') === 'true';

		// Debug form data
		console.log(
			'Form data entries:',
			[...formData.entries()].map(([key, value]) => {
				if (value instanceof File) {
					return [key, { name: value.name, size: value.size, type: value.type }];
				}
				return [key, value];
			})
		);

		// Create a validated form
		const form = await superValidate(formData, zod(avatarFormSchema), { allowFiles: true });
		console.log('Form data after validation:', form.data);
		console.log('Form valid:', form.valid);

		// Handle avatar removal
		if (removeAvatar) {
			try {
				// Get current profile to find avatar path
				const { data: profileData, error: profileError } = await supabase
					.from('profile')
					.select('avatar_url')
					.eq('id', user?.id)
					.single();

				if (profileError) {
					console.error('Error fetching profile:', profileError);
					return setError(form, '', `Failed to fetch profile: ${profileError.message}`);
				}

				// If there's an avatar URL, extract the path and remove from storage
				if (profileData?.avatar_url) {
					// Extract the file path from the URL
					// URL format: https://tilepcwykspsgbllptbf.supabase.co/storage/v1/render/image/public/profile/avatar/[UUID.ext]?width=300&height=300&resize=cover&quality=90
					const url = profileData.avatar_url;
					console.log('Avatar URL to remove:', url);

					try {
						// Remove query parameters from URL
						const baseUrl = url.split('?')[0];
						console.log('Base URL without query params:', baseUrl);

						// Extract the path after /public/
						const publicIndex = baseUrl.indexOf('/public/');

						if (publicIndex === -1) {
							console.error('Could not find /public/ in URL:', baseUrl);
							return setError(form, '', 'Could not parse avatar URL for deletion');
						}

						// Extract the storage path (everything after /public/)
						const storagePath = baseUrl.substring(publicIndex + 8); // +8 to skip "/public/"
						console.log('Extracted storage path:', storagePath);

						// Remove the 'profile/' prefix since that's already the bucket name
						let finalPath = storagePath;
						if (storagePath.startsWith('profile/')) {
							finalPath = storagePath.substring(8); // Remove 'profile/'
							console.log('Final storage path (removed profile/ prefix):', finalPath);
						}

						// Remove the file from storage
						const { error: storageError } = await supabase.storage
							.from('profile')
							.remove([finalPath]);

						if (storageError) {
							console.error('Error removing avatar from storage:', storageError);
							return setError(
								form,
								'',
								`Failed to remove avatar from storage: ${storageError.message}`
							);
						}

						console.log('Successfully removed file from storage');
					} catch (error) {
						console.error('Exception during storage operations:', error);
						return setError(
							form,
							'',
							`Error during storage operations: ${error instanceof Error ? error.message : String(error)}`
						);
					}
				}

				// Remove avatar from profile
				const { error: updateError } = await supabase
					.from('profile')
					.update({
						avatar_url: null
					})
					.eq('id', user?.id);

				if (updateError) {
					return setError(form, '', `Failed to remove avatar: ${updateError.message}`);
				}

				return message(form, {
					type: 'success',
					text: 'Avatar removed successfully'
				});
			} catch (error) {
				console.error('Error removing avatar:', error);
				return setError(
					form,
					'',
					`An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`
				);
			}
		}

		if (!form.valid) {
			console.log('Form validation errors:', form.errors);
			return message(form, { type: 'error', text: 'Invalid form data' });
		}

		try {
			// Get the file from the validated form data
			const avatarFile = form.data.avatar;

			// Debug the file
			console.log(
				'Avatar file from form.data:',
				avatarFile
					? {
							name: avatarFile.name,
							size: avatarFile.size,
							type: avatarFile.type
						}
					: 'No file'
			);

			if (!avatarFile || avatarFile.size === 0) {
				return message(form, {
					type: 'info',
					text: 'No file selected'
				});
			}

			try {
				// Upload avatar to Supabase Storage
				const fileExt = avatarFile.name.split('.').pop();
				const filePath = `avatar/${user?.id}.${fileExt}`;

				const { error: uploadError } = await supabase.storage
					.from('profile')
					.upload(filePath, avatarFile, {
						upsert: true,
						contentType: avatarFile.type
					});

				if (uploadError) {
					console.error('Upload error:', uploadError);
					return setError(form, '', `Upload failed: ${uploadError.message}`);
				}

				// Get public URL with transformations and WebP format
				const { data: publicURL } = supabase.storage.from('profile').getPublicUrl(filePath, {
					transform: {
						width: 300,
						height: 300,
						resize: 'cover',
						quality: 90
					}
				});

				console.log('Public URL:', publicURL.publicUrl);

				// Update profile with new avatar URL
				const { error: updateError } = await supabase
					.from('profile')
					.update({
						avatar_url: publicURL.publicUrl
					})
					.eq('id', user?.id);

				if (updateError) {
					console.error('Profile update error:', updateError);
					return setError(form, '', `Profile update failed: ${updateError.message}`);
				}
			} catch (error) {
				console.error('Error updating avatar:', error);
				return setError(
					form,
					'',
					`An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`
				);
			}

			return message(form, {
				type: 'success',
				text: 'Avatar updated successfully'
			});
		} catch (error) {
			console.error('Error updating avatar:', error);
			return message(form, {
				type: 'error',
				text: 'Failed to update avatar'
			});
		}
	},

	security: async ({ request, locals: { supabase, user } }) => {
		const form = await superValidate(request, zod(securitySchema));

		if (!form.valid) {
			return message(form, { type: 'error', text: 'Invalid form data' });
		}

		try {
			// First verify the current password
			const { error: signInError } = await supabase.auth.signInWithPassword({
				email: user?.email || '',
				password: form.data.current_password
			});

			if (signInError) {
				return setError(form, 'current_password', 'Current password is incorrect');
			}

			// Update the password
			const { error: updateError } = await supabase.auth.updateUser({
				password: form.data.new_password
			});

			if (updateError) {
				throw updateError;
			}

			return message(form, {
				type: 'success',
				text: 'Password updated successfully'
			});
		} catch (error: any) {
			console.error('Error updating password:', error);
			return message(form, {
				type: 'error',
				text: error.message || 'Failed to update password'
			});
		}
	},

	notifications: async ({ request }) => {
		const form = await superValidate(request, zod(notificationsSchema));

		if (!form.valid) {
			return message(form, { type: 'error', text: 'Invalid form data' });
		}

		// Process notifications form data
		return message(form, {
			type: 'success',
			text: 'Notification preferences updated successfully'
		});
	}
};
