<script lang="ts">
	import { Bell, UserIcon, Mail, Shield } from '@lucide/svelte';
	import * as Card from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { cn } from '$lib/utils';
	import { cubicInOut } from 'svelte/easing';
	import { crossfade } from 'svelte/transition';
	import type { User } from '@supabase/supabase-js';
	import * as Avatar from '$lib/components/ui/avatar/index.js';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { LocalizedText } from '$lib/utils/localization';

	interface Props {
		user: User | null;
		currentPath: string;
		onPathChange: (path: string) => void;
		profileData?: {
			username?: string;
			given_name?: Record<string, string>;
			family_name?: Record<string, string>;
			avatar_url?: string;
		};
	}

	let { user, currentPath, onPathChange, profileData }: Props = $props();

	const [send, receive] = crossfade({
		duration: 250,
		easing: cubicInOut
	});

	// Get user's first name in current language
	let firstName = $derived(
		profileData?.given_name
			? getLocalizedText(profileData.given_name as LocalizedText, getLocale())
			: ''
	);

	// Get user's initials for avatar fallback
	let initials = $derived(
		firstName ? firstName.charAt(0).toUpperCase() : user?.email?.charAt(0).toUpperCase() || 'U'
	);

	const navItems = [
		{
			href: '/settings/profile',
			title: 'Profile',
			icon: UserIcon,
			description: 'Manage your account settings'
		},
		// {
		// 	href: '/settings/notifications',
		// 	title: 'Notifications',
		// 	icon: Bell,
		// 	description: 'Configure notification preferences'
		// },
		{
			href: '/settings/security',
			title: 'Security',
			icon: Shield,
			description: 'Update your security settings'
		}
	];
</script>

<div class="space-y-6">
	{#if user}
		<Card.Root class="overflow-hidden">
			<div class="flex items-center gap-3 p-3">
				<Avatar.Root class="h-12 w-12">
					{#if profileData?.avatar_url}
						<Avatar.Image src={profileData.avatar_url} alt="User avatar" />
					{/if}
					<Avatar.Fallback class="bg-primary text-lg uppercase text-primary-foreground">
						{initials}
					</Avatar.Fallback>
				</Avatar.Root>
				<div class="min-w-0 flex-1">
					<p class="truncate text-sm font-medium">
						{firstName || user.email}
					</p>
					<p class="text-xs text-muted-foreground">
						{firstName ? user.email : 'Signed in'}
					</p>
				</div>
			</div>
		</Card.Root>
	{/if}

	<nav class="flex flex-col space-y-1">
		{#each navItems as item}
			{@const isActive = currentPath === item.href}
			{@const Icon = item.icon}
			<Button
				variant="ghost"
				onclick={() => onPathChange(item.href)}
				class={cn(
					'relative flex items-center justify-start gap-2 hover:bg-transparent',
					!isActive && 'hover:underline'
				)}
			>
				{#if isActive}
					<div
						class="absolute inset-0 rounded-md bg-muted"
						in:send={{ key: 'active-sidebar-tab' }}
						out:receive={{ key: 'active-sidebar-tab' }}
					></div>
				{/if}
				<div class="relative z-10 flex items-center gap-2">
					<Icon class="h-4 w-4" />
					<span>{item.title}</span>
				</div>
			</Button>
		{/each}
	</nav>
</div>
