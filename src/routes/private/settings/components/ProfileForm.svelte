<script lang="ts" module>
	import { z } from 'zod';

	// Schema for localized text fields
	const localizedTextSchema = z
		.record(z.string(), z.string().optional())
		.refine((val) => Object.keys(val).length > 0, {
			message: 'At least one language must be provided'
		});

	export const formSchema = z.object({
		given_name: localizedTextSchema,
		family_name: localizedTextSchema,
		username: z
			.string()
			.min(8, 'Username must be at least 8 characters long')
			.regex(
				/^[a-z0-9._]+$/,
				'Username can only contain lowercase letters, numbers, dots and underscores'
			)
			.refine(
				(val) => !val.startsWith('.') && !val.endsWith('.'),
				'Username cannot start or end with a dot'
			)
	});

	export type FormSchema = typeof formSchema;
</script>

<script lang="ts">
	import type { SuperValidated, Infer } from 'sveltekit-superforms';
	import { superForm } from 'sveltekit-superforms/client';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Field, Control, Label, FieldErrors, Description } from 'formsnap';
	import { toast } from 'svelte-sonner';
	import * as Card from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Button } from '$lib/components/ui/button';
	import type { User } from '@supabase/supabase-js';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import type { LocalizedText } from '$lib/utils/localization';

	interface Props {
		form: SuperValidated<Infer<FormSchema>>;
		user: User | null;
		profileData?: {
			username?: string;
			given_name?: Record<string, string>;
			family_name?: Record<string, string>;
		};
	}

	let { form: data, user, profileData }: Props = $props();

	const form = superForm(data, {
		validators: zodClient(formSchema),
		dataType: 'json',
		resetForm: false,
		onUpdated: ({ form }) => {
			if (form.valid) {
				toast.success('Profile updated successfully');
			}
		},
		onError: ({ result }) => {
			toast.error(result.error?.message || 'Failed to update profile');
		},
		// Ensure validation runs before submission
		validationMethod: 'auto'
	});

	const { form: formData, enhance, errors, submitting } = form;
</script>

<form method="POST" action="?/profile" class="space-y-6" use:enhance>
	<Card.Root>
		<Card.Header>
			<Card.Title>Profile Information</Card.Title>
			<Card.Description class="text-muted-foreground"
				>Update your profile information.</Card.Description
			>
		</Card.Header>
		<Card.Content class="space-y-6">
			<div class="flex flex-col md:flex-row md:gap-4">
				<div class="mb-4 flex-1 md:mb-0">
					<LocalizedTextControl label="Given Name" name="given_name" {form} />
				</div>
				<div class="flex-1">
					<LocalizedTextControl label="Family Name" name="family_name" {form} />
				</div>
			</div>

			<Field {form} name="username">
				<Control>
					{#snippet children({ props })}
						<div class="space-y-2">
							<Label>Username</Label>
							<Input {...props} type="text" bind:value={$formData.username} />
						</div>
					{/snippet}
				</Control>
				<Description class="text-sm text-muted-foreground"
					>Your unique username, like an Instagram handle. Must be at least 8 characters and can
					only contain lowercase letters, numbers, dots and underscores.</Description
				>
				<FieldErrors class="mt-1 text-sm text-destructive" />
			</Field>
		</Card.Content>
		<Card.Footer class="flex justify-end">
			<Button type="submit" disabled={$submitting}>
				{#if $submitting}
					Saving...
				{:else}
					Save Profile
				{/if}
			</Button>
		</Card.Footer>
	</Card.Root>
</form>
