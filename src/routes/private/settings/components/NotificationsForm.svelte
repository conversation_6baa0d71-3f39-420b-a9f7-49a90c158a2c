<script lang="ts" module>
	import { z } from 'zod';

	export const formSchema = z.object({
		marketing_emails: z.boolean().default(false),
		social_emails: z.boolean().default(true),
		security_emails: z.boolean().default(true),
		news_emails: z.boolean().default(false),
		push_new_message: z.boolean().default(true),
		push_mentions: z.boolean().default(true),
		email_digest: z.enum(['daily', 'weekly', 'monthly', 'never']).default('daily')
	});

	export type FormSchema = typeof formSchema;
</script>

<script lang="ts">
	import type { SuperValidated, Infer } from 'sveltekit-superforms';
	import { superForm } from 'sveltekit-superforms/client';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Field, Control, Label, FieldErrors, Description } from 'formsnap';
	import { toast } from 'svelte-sonner';
	import * as Card from '$lib/components/ui/card';
	import { Switch } from '$lib/components/ui/switch';
	import * as Select from '$lib/components/ui/select';
	import { Button } from '$lib/components/ui/button';
	import type { User } from '@supabase/supabase-js';

	interface Props {
		form: SuperValidated<Infer<FormSchema>>;
	}

	let { form: data }: Props = $props();

	const form = superForm(data, {
		validators: zodClient(formSchema),
		onUpdated: ({ form }) => {
			if (form.valid) {
				toast.success('Notification preferences updated successfully');
			}
		},
		onError: ({ result }) => {
			toast.error(result.error.message);
		}
	});
	const { form: formData, enhance } = form;
</script>

<form method="POST" action="?/notifications" class="space-y-6" use:enhance>
	<Card.Root>
		<Card.Header>
			<Card.Title>Email Notifications</Card.Title>
			<Card.Description class="text-muted-foreground"
				>Choose what emails you want to receive.</Card.Description
			>
		</Card.Header>
		<Card.Content class="space-y-4">
			<div class="space-y-4">
				<Field {form} name="marketing_emails">
					<Control>
						{#snippet children({ props })}
							<div class="flex items-center justify-between space-x-2">
								<div class="space-y-0.5">
									<Label>Marketing Emails</Label>
									<Description class="text-sm text-muted-foreground"
										>Receive emails about new products, features, and more.</Description
									>
								</div>
								<Switch {...props} bind:checked={$formData.marketing_emails} />
							</div>
						{/snippet}
					</Control>
					<FieldErrors />
				</Field>

				<Field {form} name="social_emails">
					<Control>
						{#snippet children({ props })}
							<div class="flex items-center justify-between space-x-2">
								<div class="space-y-0.5">
									<Label>Social Notifications</Label>
									<Description class="text-sm text-muted-foreground"
										>Receive emails for follows, mentions, and replies.</Description
									>
								</div>
								<Switch {...props} bind:checked={$formData.social_emails} />
							</div>
						{/snippet}
					</Control>
					<FieldErrors />
				</Field>

				<Field {form} name="security_emails">
					<Control>
						{#snippet children({ props })}
							<div class="flex items-center justify-between space-x-2">
								<div class="space-y-0.5">
									<Label>Security Emails</Label>
									<Description class="text-sm text-muted-foreground"
										>Receive emails about your account security.</Description
									>
								</div>
								<Switch
									{...props}
									aria-readonly
									disabled
									bind:checked={$formData.security_emails}
								/>
							</div>
						{/snippet}
					</Control>
					<FieldErrors />
				</Field>

				<Field {form} name="news_emails">
					<Control>
						{#snippet children({ props })}
							<div class="flex items-center justify-between space-x-2">
								<div class="space-y-0.5">
									<Label>News Emails</Label>
									<Description class="text-sm text-muted-foreground"
										>Receive emails about company news and updates.</Description
									>
								</div>
								<Switch {...props} bind:checked={$formData.news_emails} />
							</div>
						{/snippet}
					</Control>
					<FieldErrors />
				</Field>
			</div>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header>
			<Card.Title>Push Notifications</Card.Title>
			<Card.Description class="text-muted-foreground"
				>Configure push notification preferences.</Card.Description
			>
		</Card.Header>
		<Card.Content class="space-y-4">
			<div class="space-y-4">
				<Field {form} name="push_new_message">
					<Control>
						{#snippet children({ props })}
							<div class="flex items-center justify-between space-x-2">
								<div class="space-y-0.5">
									<Label>New Messages</Label>
									<Description class="text-sm text-muted-foreground"
										>Get notified when you receive new messages.</Description
									>
								</div>
								<Switch {...props} bind:checked={$formData.push_new_message} />
							</div>
						{/snippet}
					</Control>
					<FieldErrors />
				</Field>

				<Field {form} name="push_mentions">
					<Control>
						{#snippet children({ props })}
							<div class="flex items-center justify-between space-x-2">
								<div class="space-y-0.5">
									<Label>Mentions</Label>
									<Description class="text-sm text-muted-foreground"
										>Get notified when you are mentioned.</Description
									>
								</div>
								<Switch {...props} bind:checked={$formData.push_mentions} />
							</div>
						{/snippet}
					</Control>
					<FieldErrors />
				</Field>
			</div>
		</Card.Content>
	</Card.Root>

	<Card.Root>
		<Card.Header>
			<Card.Title>Email Digest</Card.Title>
			<Card.Description class="text-muted-foreground"
				>Configure how often you receive email digests.</Card.Description
			>
		</Card.Header>
		<Card.Content>
			<Field {form} name="email_digest">
				<Control>
					{#snippet children({ props })}
						<Label>Email Digest Frequency</Label>
						<Select.Root type="single" bind:value={$formData.email_digest} name={props.name}>
							<Select.Trigger {...props}>
								{$formData.email_digest
									? $formData.email_digest.charAt(0).toUpperCase() + $formData.email_digest.slice(1)
									: 'Select frequency'}
							</Select.Trigger>
							<Select.Content>
								<Select.Item value="daily">Daily</Select.Item>
								<Select.Item value="weekly">Weekly</Select.Item>
								<Select.Item value="monthly">Monthly</Select.Item>
								<Select.Item value="never">Never</Select.Item>
							</Select.Content>
						</Select.Root>
					{/snippet}
				</Control>
				<Description class="text-sm text-muted-foreground"
					>Choose how often you want to receive email digests.</Description
				>
				<FieldErrors />
			</Field>
		</Card.Content>
		<Card.Footer>
			<Button type="submit">Save Preferences</Button>
		</Card.Footer>
	</Card.Root>
</form>
