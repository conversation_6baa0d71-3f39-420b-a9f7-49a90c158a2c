<script lang="ts" module>
	import { z } from 'zod';

	export const avatarFormSchema = z.object({
		avatar: z
			.instanceof(File, { message: 'Please upload an image file' })
			.refine((file) => file.size === 0 || file.size < 2_000_000, 'Avatar must be less than 2MB')
			.refine(
				(file) =>
					file.size === 0 ||
					['image/jpeg', 'image/png', 'image/webp', 'image/gif'].includes(file.type),
				'Only JPEG, PNG, WEBP and GIF images are allowed'
			)
			.optional()
	});

	export type AvatarFormSchema = typeof avatarFormSchema;
</script>

<script lang="ts">
	import type { SuperValidated, Infer } from 'sveltekit-superforms';
	import { superForm, fileProxy } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Field, Control, Label, FieldErrors, Description } from 'formsnap';
	import { toast } from 'svelte-sonner';
	import * as Card from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import type { User } from '@supabase/supabase-js';
	import * as Avatar from '$lib/components/ui/avatar/index.js';
	import { onDestroy } from 'svelte';

	interface Props {
		form: SuperValidated<Infer<AvatarFormSchema>>;
		user: User | null;
		profileData?: {
			avatar_url?: string | null;
			username?: string;
		};
	}

	let { form: data, user, profileData }: Props = $props();

	// Local state to track avatar status
	let avatarPreview = $state<string | null>(null);
	let selectedFile = $state<File | null>(null);

	const form = superForm(data, {
		validators: zodClient(avatarFormSchema),
		dataType: 'form' as const,
		resetForm: false,
		onUpdated: ({ form }) => {
			if (form.valid) {
				// Check for message type to display appropriate toast
				const message = form.message;
				if (message?.type === 'success') {
					toast.success(message.text);
				} else if (message?.type === 'info') {
					toast.info(message.text);
				}
			}
		},
		onError: ({ result }) => {
			// Display the specific error message from the server
			const errorMessage = result.error?.message || 'Failed to update avatar';
			toast.error(errorMessage);
		}
	});

	const { form: formData, enhance, errors, submitting } = form;
	const file = fileProxy(form, 'avatar');

	// Handle file preview
	function handleFileChange(e: Event) {
		const target = e.target as HTMLInputElement;
		if (target.files && target.files.length > 0) {
			// Clean up previous preview if it exists
			if (avatarPreview) {
				URL.revokeObjectURL(avatarPreview);
			}

			// Store the selected file
			selectedFile = target.files[0];

			// Create new preview
			avatarPreview = URL.createObjectURL(selectedFile);

			console.log('File selected:', selectedFile.name, selectedFile.size, selectedFile.type);
		}
	}

	// Clean up on component unmount
	onDestroy(() => {
		if (avatarPreview) {
			URL.revokeObjectURL(avatarPreview);
		}
	});

	// Get initials for avatar fallback
	let initials = $derived(
		profileData?.username ? profileData.username.substring(0, 2).toUpperCase() : 'U'
	);

	// Function to handle avatar removal
	async function handleRemoveAvatar() {
		try {
			const formData = new FormData();
			formData.append('remove', 'true');

			const response = await fetch('?/avatar', {
				method: 'POST',
				body: formData
			});

			if (response.ok) {
				toast.success('Avatar removed successfully');
				// Reload the page to reflect changes
				window.location.reload();
			} else {
				toast.error('Failed to remove avatar');
			}
		} catch (error) {
			toast.error('An error occurred while removing avatar');
			console.error(error);
		}
	}
</script>

<form method="POST" action="?/avatar" class="space-y-6" enctype="multipart/form-data" use:enhance>
	<Card.Root>
		<Card.Header>
			<Card.Title>Profile Picture</Card.Title>
			<Card.Description class="text-muted-foreground">
				Update your profile picture.
			</Card.Description>
		</Card.Header>
		<Card.Content>
			<div
				class="flex flex-col items-center space-y-4 sm:flex-row sm:items-start sm:space-x-4 sm:space-y-0"
			>
				<div class="flex flex-col items-center space-y-2">
					<Avatar.Root class="h-24 w-24">
						{#if avatarPreview}
							<Avatar.Image src={avatarPreview} alt="Avatar preview" />
						{:else if profileData?.avatar_url}
							<Avatar.Image src={profileData.avatar_url} alt="User avatar" fetchpriority="high" />
						{/if}
						<Avatar.Fallback>{initials}</Avatar.Fallback>
					</Avatar.Root>
					<div class="text-center text-sm text-muted-foreground">
						{#if avatarPreview}
							Preview
						{:else if profileData?.avatar_url}
							Current avatar
						{:else}
							No avatar
						{/if}
					</div>
				</div>

				<div class="flex-1 space-y-2">
					<Field {form} name="avatar">
						<Control>
							{#snippet children({ props })}
								<div class="space-y-2">
									<Label>Profile Picture</Label>
									<input
										class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
										id="avatar"
										name="avatar"
										type="file"
										accept="image/png, image/jpeg, image/webp, image/gif"
										bind:files={$file}
										onchange={handleFileChange}
									/>
								</div>
							{/snippet}
						</Control>
						<Description class="text-xs text-muted-foreground">
							Upload a profile picture. Images are automatically optimized to WebP format. Only
							JPEG, PNG, WEBP or GIF, max 2MB.
						</Description>
						<FieldErrors />
					</Field>
				</div>
			</div>
		</Card.Content>
		<div class="flex w-full justify-end gap-2 p-6 pt-0">
			{#if profileData?.avatar_url}
				<Button type="button" variant="outline" onclick={handleRemoveAvatar} disabled={$submitting}>
					{#if $submitting}
						Removing...
					{:else}
						Remove Avatar
					{/if}
				</Button>
			{/if}
			<Button type="submit" disabled={$submitting || (!selectedFile && !profileData?.avatar_url)}>
				{#if $submitting}
					Uploading...
				{:else}
					Update Avatar
				{/if}
			</Button>
		</div>
	</Card.Root>
</form>
