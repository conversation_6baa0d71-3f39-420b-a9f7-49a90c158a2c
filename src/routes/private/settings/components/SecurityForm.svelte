<script lang="ts" module>
	import { z } from 'zod';

	export const formSchema = z
		.object({
			current_password: z.string().min(1, 'Current password is required'),
			new_password: z.string().min(8, 'Password must be at least 8 characters'),
			confirm_password: z.string().min(1, 'Please confirm your password')
		})
		.refine((data) => data.new_password === data.confirm_password, {
			message: "Passwords don't match",
			path: ['confirm_password']
		});

	export type FormSchema = typeof formSchema;
</script>

<script lang="ts">
	import type { SuperValidated, Infer } from 'sveltekit-superforms';
	import { superForm } from 'sveltekit-superforms/client';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Field, Control, Label, FieldErrors, Description } from 'formsnap';
	import { toast } from 'svelte-sonner';
	import * as Card from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Button } from '$lib/components/ui/button';

	interface Props {
		form: SuperValidated<Infer<FormSchema>>;
	}

	let { form: data }: Props = $props();

	const form = superForm(data, {
		validators: zodClient(formSchema),
		dataType: 'json',
		resetForm: true,
		onUpdated: ({ form }) => {
			if (form.valid) {
				toast.success('Password updated successfully');
			}
		},
		onError: ({ result }) => {
			toast.error(result.error?.message || 'Failed to update password');
		},
		validationMethod: 'auto'
	});
	const { form: formData, enhance, submitting } = form;
</script>

<form method="POST" action="?/security" class="space-y-6" use:enhance>
	<Card.Root>
		<Card.Header>
			<Card.Title>Password Update</Card.Title>
			<Card.Description class="text-muted-foreground"
				>Change your account password.</Card.Description
			>
		</Card.Header>
		<Card.Content class="space-y-4">
			<Field {form} name="current_password">
				<Control>
					{#snippet children({ props })}
						<div class="space-y-2">
							<Label>Current Password</Label>
							<Input
								{...props}
								type="password"
								autocomplete="current-password"
								bind:value={$formData.current_password}
							/>
						</div>
					{/snippet}
				</Control>
				<Description class="text-sm text-muted-foreground"
					>Enter your current password to verify your identity.</Description
				>
				<FieldErrors class="mt-1 text-sm text-destructive" />
			</Field>

			<Field {form} name="new_password">
				<Control>
					{#snippet children({ props })}
						<div class="space-y-2">
							<Label>New Password</Label>
							<Input
								{...props}
								type="password"
								autocomplete="new-password"
								bind:value={$formData.new_password}
							/>
						</div>
					{/snippet}
				</Control>
				<Description class="text-sm text-muted-foreground"
					>Choose a strong password with at least 8 characters.</Description
				>
				<FieldErrors class="mt-1 text-sm text-destructive" />
			</Field>

			<Field {form} name="confirm_password">
				<Control>
					{#snippet children({ props })}
						<div class="space-y-2">
							<Label>Confirm New Password</Label>
							<Input
								{...props}
								type="password"
								autocomplete="new-password"
								bind:value={$formData.confirm_password}
							/>
						</div>
					{/snippet}
				</Control>
				<Description class="text-sm text-muted-foreground"
					>Re-enter your new password to confirm.</Description
				>
				<FieldErrors class="mt-1 text-sm text-destructive" />
			</Field>
		</Card.Content>
		<div class="flex justify-end p-6 pt-0">
			<Button type="submit" disabled={$submitting}>
				{#if $submitting}
					Updating...
				{:else}
					Update Password
				{/if}
			</Button>
		</div>
	</Card.Root>
</form>
