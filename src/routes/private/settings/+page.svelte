<script lang="ts">
	import type { SupabaseClient, User } from '@supabase/supabase-js';
	import type { PageData } from './$types';
	import SettingsSidebar from './components/SettingsSidebar.svelte';
	import ProfileForm from './components/ProfileForm.svelte';
	import AvatarForm from './components/AvatarForm.svelte';
	import SecurityForm from './components/SecurityForm.svelte';
	import NotificationsForm from './components/NotificationsForm.svelte';

	let { data } = $props<{ data: PageData }>();
	let { user, profile, security, notifications, avatar } = $derived(data);

	let currentPath = $state('/settings/profile');
</script>

<div class="mx-auto max-w-7xl px-4 py-8 md:px-8">
	<h1 class="mb-8 text-2xl font-semibold tracking-tight">Settings</h1>

	<div class="grid gap-8 md:grid-cols-[240px_1fr]">
		<SettingsSidebar
			{user}
			{currentPath}
			onPathChange={(path) => (currentPath = path)}
			profileData={data.profileData}
		/>

		<!-- Main Content -->
		<div class="min-h-[400px]">
			{#if currentPath === '/settings/profile'}
				<div class="space-y-8">
					<AvatarForm form={avatar} {user} profileData={data.profileData} />
					<ProfileForm form={profile} {user} profileData={data.profileData} />
				</div>
			{:else if currentPath === '/settings/security'}
				<SecurityForm form={security} />
			{:else if currentPath === '/settings/notifications'}
				<NotificationsForm form={notifications} />
			{/if}
		</div>
	</div>
</div>
