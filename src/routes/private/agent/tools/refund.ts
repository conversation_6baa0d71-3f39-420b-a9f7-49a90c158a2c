import type { Database } from '$lib/supabase/database.types';
import type { SupabaseClient } from '@supabase/supabase-js';

/**
 * Checks if an order exists and belongs to the current user
 */
export async function validateOrderOwnership(
	supabase: SupabaseClient<Database>,
	orderId: string,
	userId: string
): Promise<{ valid: boolean; orderDetails?: any }> {
	try {
		// Query to check if order exists and belongs to user
		const { data, error } = await supabase
			.from('order_price')
			.select(
				`
        id,
        deal_money_int,
        price_option_id,
        priceOption:price_option_id (
          title
        ),
        consumer_profile_id
      `
			)
			.eq('id', orderId)
			.single();

		if (error) {
			console.error('Error validating order:', error);
			return { valid: false };
		}

		// Check if the order belongs to the user
		if (data.consumer_profile_id !== userId) {
			return { valid: false };
		}

		return {
			valid: true,
			orderDetails: data
		};
	} catch (error) {
		console.error('Error in validateOrderOwnership:', error);
		return { valid: false };
	}
}

/**
 * Create a refund request in the database
 */
export async function createRefundRequest(
	supabase: SupabaseClient<Database>,
	orderId: string,
	amount: number,
	reason: string,
	userId: string
): Promise<{ success: boolean; refundId?: string; error?: string }> {
	try {
		// Create refund request record
		const { data, error } = await supabase
			.from('order_price_refund')
			.insert({
				order_price_id: orderId,
				money_refunded_int: amount,
				money_currency_code: 'USD', // Default currency, adjust as needed
				// Include metadata with reason and AI-initiated flag
				stripe_customer_id: userId
				// We'll add a custom field for the reason in metadata if needed
			})
			.select()
			.single();

		if (error) {
			console.error('Error creating refund request:', error);
			return { success: false, error: error.message };
		}

		return {
			success: true,
			refundId: data.id
		};
	} catch (error) {
		console.error('Error in createRefundRequest:', error);
		return { success: false, error: 'Internal server error' };
	}
}

/**
 * Process an approved refund
 */
export async function processRefund(
	supabase: SupabaseClient<Database>,
	refundId: string
): Promise<{ success: boolean; error?: string }> {
	try {
		// In a real implementation, you would:
		// 1. Get the refund record from the database
		// 2. Process the refund through your payment processor (Stripe, etc.)
		// 3. Update the refund record with the result

		// Mock implementation for now
		return { success: true };
	} catch (error) {
		console.error('Error in processRefund:', error);
		return { success: false, error: 'Failed to process refund' };
	}
}
