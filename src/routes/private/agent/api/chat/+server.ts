import { json } from '@sveltejs/kit';
import { streamText, tool } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { validateOrderOwnership, createRefundRequest } from '../../tools/refund';
import { z } from 'zod';
import { OPENAI_API_KEY } from '$env/static/private';

// Define types for our messages and state
type Message = {
	id: string;
	role: 'user' | 'assistant' | 'system' | 'function';
	content: string;
	createdAt?: Date;
};

// Define the refund tool parameters schema
const refundParams = z.object({
	orderId: z.string().describe('The ID of the order to refund'),
	amount: z.number().describe('The amount to refund in cents (e.g., 1000 for $10.00)'),
	reason: z.string().describe('The reason for the refund')
});

export async function POST({ request, locals }) {
	try {
		// Get the supabase client from locals
		const supabase = locals.supabase;

		// Get request data
		const { messages, group_id, brand_id } = await request.json();

		// Get current user from locals
		const userId = locals.user?.id;

		if (!userId) {
			return json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Check if OpenAI API key is available
		if (!OPENAI_API_KEY) {
			console.error('Missing OPENAI_API_KEY environment variable');
			return json(
				{ error: 'OpenAI API key is not configured. Please contact the administrator.' },
				{ status: 500 }
			);
		}

		// Create OpenAI provider with explicit API key from environment
		const openai = createOpenAI({
			apiKey: OPENAI_API_KEY
		});

		// Define refund tool
		const refundTool = tool({
			description:
				'Process a refund for a customer order. This requires human approval before completing.',
			parameters: refundParams,
			execute: async ({ orderId, amount, reason }) => {
				try {
					// Validate the order and ownership
					const { valid, orderDetails } = await validateOrderOwnership(supabase, orderId, userId);

					if (!valid) {
						return "Error: Invalid order ID or you don't have permission to refund this order.";
					}

					// Create the refund request in the database
					const { success, refundId, error } = await createRefundRequest(
						supabase,
						orderId,
						amount,
						reason,
						userId
					);

					if (!success) {
						return `Error creating refund request: ${error || 'Unknown error'}`;
					}

					// Return the refund confirmation message
					return {
						html: `
							<div class="flex flex-col gap-2 bg-yellow-50 p-4 rounded-md border border-yellow-200">
								<div class="font-medium">Refund Confirmation Required</div>
								<div>
									<p>A refund has been requested for order #${orderId} for $${(amount / 100).toFixed(2)}.</p>
									<p>Reason: ${reason}</p>
									<p>This requires human confirmation before processing.</p>
								</div>
								<div class="flex gap-2 mt-2">
									<button 
										class="bg-green-500 text-white px-3 py-1 rounded-md"
										data-refund-id="${refundId}"
										data-action="approve"
									>
										Approve
									</button>
									<button 
										class="bg-red-500 text-white px-3 py-1 rounded-md"
										data-refund-id="${refundId}"
										data-action="deny"
									>
										Deny
									</button>
								</div>
							</div>
						`,
						refundId
					};
				} catch (toolError) {
					console.error('Error executing refund tool:', toolError);
					return `Error processing refund: ${
						toolError instanceof Error ? toolError.message : 'Unknown error'
					}`;
				}
			}
		});

		// System prompt with instructions
		const systemPrompt = {
			role: 'system',
			content: `
				You are a helpful customer service assistant for our e-commerce platform.
				
				Your capabilities:
				- Answer questions about products, orders, and services
				- Help customers track orders
				- Process refund requests (requires human approval)
				- Provide shipping information
				
				When handling refund requests:
				1. Ask for the order ID
				2. Ask for the reason for the refund
				3. Confirm the refund amount
				4. Use the refund_order tool to initiate the request
				5. Explain that the refund requires human approval
				
				Be professional, courteous, and helpful at all times.
			`
		};

		try {
			// Generate streaming text response
			const result = streamText({
				model: openai('gpt-4o'),
				messages: [systemPrompt, ...messages],
				tools: {
					refund_order: refundTool
				},
				maxSteps: 3 // Allow multi-step tool use
			});

			// Return the streamed response
			return result.toDataStreamResponse();
		} catch (modelError) {
			console.error('Error generating response from model:', modelError);
			return json(
				{
					error: `Error generating response: ${
						modelError instanceof Error ? modelError.message : 'Unknown model error'
					}`
				},
				{ status: 500 }
			);
		}
	} catch (error) {
		console.error('Error processing chat request:', error);
		return json(
			{
				error: `Failed to process request: ${
					error instanceof Error ? error.message : 'An unknown error occurred'
				}`
			},
			{ status: 500 }
		);
	}
}
