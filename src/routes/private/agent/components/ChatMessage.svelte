<script lang="ts">
	import { Avatar } from '$lib/components/ui/avatar';
	import { cn } from '$lib/utils';
	import type { Message } from 'ai'; // Use the official Message type
	import { fade } from 'svelte/transition';

	// Define the expected structure for a part that needs HTML rendering
	type HtmlPart = { type: 'ui'; node: string };
	// Define expected structure for tool call/result parts (if needed, based on actual data)
	type ToolPart =
		| { type: 'tool-call'; toolName: string; args: any }
		| { type: 'tool-result'; toolName: string; result: any };

	interface Props {
		message: Message;
	}

	let { message }: Props = $props();

	const isUser = $derived(message.role === 'user');
	const isSystem = $derived(message.role === 'system');
	const isData = $derived(message.role === 'data');
	const isAssistant = $derived(message.role === 'assistant');
	// Determine if it's a role that might have special rendering (data, tool results)
	const isSpecialRole = $derived(isData); // Add other roles like 'tool' if they become part of the Message type

	// Helper to check if content needs HTML rendering (tool calls, results, or specific UI nodes)
	const needsHtmlRendering = $derived(
		Array.isArray(message.content) &&
			message.content.some((part: any) => part.type !== 'text' || typeof part.node === 'string')
	);

	// Render message content, handling potential parts array
	function renderContent(content: Message['content']): string {
		if (typeof content === 'string') {
			return content; // Simple string content
		}

		// Handle array of parts if message.content is an array
		let rendered = '';
		if (Array.isArray(content)) {
			for (const part of content as any[]) {
				// Use any[] to handle potential variations
				if (part.type === 'text') {
					rendered += part.value || ''; // Assuming part.value exists for text
				} else if (part.type === 'tool-call') {
					// Assert specific type for tool-call
					const toolCallPart = part as { type: 'tool-call'; toolName: string; args: any };
					rendered += `<div class="mt-2 p-2 bg-muted/50 dark:bg-muted/20 rounded text-xs">
						<p class="font-medium mb-1 text-muted-foreground">Tool call: ${toolCallPart.toolName}</p>
						<pre class="overflow-auto whitespace-pre-wrap break-all">${JSON.stringify(toolCallPart.args, null, 2)}</pre>
					</div>`;
				} else if (part.type === 'tool-result') {
					// Assert specific type for tool-result
					const toolResultPart = part as { type: 'tool-result'; toolName: string; result: any };
					rendered += `<div class="mt-2 p-2 bg-muted/50 dark:bg-muted/20 rounded text-xs">
						<p class="font-medium mb-1 text-muted-foreground">Tool result: ${toolResultPart.toolName}</p>
						<pre class="overflow-auto whitespace-pre-wrap break-all">${JSON.stringify(toolResultPart.result, null, 2)}</pre>
					</div>`;
				} else if (part.type === 'ui' && typeof (part as HtmlPart).node === 'string') {
					// Handle UI parts assuming node is an HTML string
					rendered += (part as HtmlPart).node;
				}
				// Add more part types if necessary
			}
		}
		return rendered;
	}

	function getRoleLabel(role: Message['role']): string {
		switch (role) {
			case 'user':
				return 'You';
			case 'assistant':
				return 'Assistant';
			case 'system':
				return 'System';
			case 'data':
				return 'Data';
			// tool and function roles are not in the base Message type from 'ai'
			default:
				// Attempt to capitalize the role if it's unknown but valid string
				const unknownRole = role as string;
				return unknownRole.charAt(0).toUpperCase() + unknownRole.slice(1);
		}
	}
</script>

<div
	class={cn('flex items-center gap-3 py-3', isUser ? 'flex-row-reverse' : '')}
	in:fade={{ duration: 200, delay: 50 }}
>
	<Avatar
		class={cn(
			'h-8 w-8 shrink-0 border border-border/30 shadow-sm',
			'flex items-center justify-center',
			isUser
				? 'bg-white dark:bg-gray-300'
				: isSystem
					? 'bg-destructive text-destructive-foreground'
					: isSpecialRole
						? 'bg-amber-100 dark:bg-amber-900/50'
						: 'bg-muted dark:bg-gray-600'
		)}
	>
		{#if isUser}
			<svg
				xmlns="http://www.w3.org/2000/svg"
				width="24"
				height="24"
				viewBox="0 0 24 24"
				fill="none"
				stroke="currentColor"
				stroke-width="2"
				stroke-linecap="round"
				stroke-linejoin="round"
				class="h-4 w-4 text-gray-700 dark:text-gray-900"
				><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"
				></circle></svg
			>
		{:else if isSystem}
			<svg
				xmlns="http://www.w3.org/2000/svg"
				width="24"
				height="24"
				viewBox="0 0 24 24"
				fill="none"
				stroke="currentColor"
				stroke-width="2"
				stroke-linecap="round"
				stroke-linejoin="round"
				class="h-4 w-4"
				><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"
				></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"
				></line></svg
			>
		{:else if isSpecialRole}
			<svg
				xmlns="http://www.w3.org/2000/svg"
				width="24"
				height="24"
				viewBox="0 0 24 24"
				fill="none"
				stroke="currentColor"
				stroke-width="2"
				stroke-linecap="round"
				stroke-linejoin="round"
				class="h-4 w-4 text-amber-700 dark:text-amber-300"
			>
				<rect width="18" height="18" x="3" y="3" rx="2" />
				<path d="M7 10h4" />
				<path d="M7 14h4" />
				<path d="m13 7 4 4-4 4" />
			</svg>
		{:else}
			<!-- Assistant -->
			<svg
				xmlns="http://www.w3.org/2000/svg"
				width="24"
				height="24"
				viewBox="0 0 24 24"
				fill="none"
				stroke="currentColor"
				stroke-width="2"
				stroke-linecap="round"
				stroke-linejoin="round"
				class="h-4 w-4 text-gray-300 dark:text-gray-300"
				><circle cx="12" cy="12" r="10"></circle><path d="M8 9.05v-.1"></path><path d="M16 9.05v-.1"
				></path><path d="M12 18c2.5-1 4-2.5 4-6"></path><path d="M8 14c1.5.5 3.5.5 5 0"></path></svg
			>
		{/if}
	</Avatar>
	<div
		class={cn('flex w-full max-w-[85%] flex-col space-y-1', isUser ? 'items-end' : 'items-start')}
	>
		<div class="mb-0.5 text-xs font-medium text-muted-foreground dark:text-gray-400">
			{getRoleLabel(message.role)}
		</div>

		<div
			class={cn(
				'rounded-lg px-4 py-2.5 text-sm shadow-sm',
				isUser
					? 'bg-white text-gray-900 dark:bg-gray-200 dark:text-gray-900'
					: isSystem
						? 'border border-destructive/30 bg-destructive/10 text-destructive'
						: isSpecialRole // Use distinct style for tool/function/data results
							? 'border border-amber-200 bg-amber-50 text-amber-900 dark:border-amber-800 dark:bg-amber-900/30 dark:text-amber-200'
							: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' // Default for assistant
			)}
		>
			{#if needsHtmlRendering}
				<!-- Handle AI SDK UI components with HTML or tool calls -->
				{@html renderContent(message.content)}
			{:else if typeof message.content === 'string'}
				<!-- Regular text content with better text wrapping -->
				<div class="whitespace-pre-wrap break-words">
					{message.content}
				</div>
			{/if}
		</div>
	</div>
</div>
