<script lang="ts">
	import { onMount } from 'svelte';
	import { fade, fly } from 'svelte/transition';
	import { Button } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { ScrollArea } from '$lib/components/ui/scroll-area';
	import { Avatar } from '$lib/components/ui/avatar';
	import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
	import { Chat } from '@ai-sdk/svelte';
	import { v4 as uuidv4 } from 'uuid';
	import { env } from '$env/dynamic/public';
	import type { PageProps } from './$types';
	import ChatMessage from './components/ChatMessage.svelte';
	import { PageContainer } from '$lib/components/layout';

	let { data }: PageProps = $props();

	// Generate UUIDs for conversation tracking
	let groupId = uuidv4();
	let brandId = data.brand.id;

	// Ensure we have needed authentication data
	let authToken = data.session?.access_token;
	let userId = data.user?.id || '';

	let chatError: Error | null = null;

	// Create the chat instance with AI SDK 4.2
	const chat = new Chat({
		api: `/private/agent/api/chat`,
		id: groupId,
		body: {
			group_id: groupId,
			brand_id: brandId
		},
		headers: {
			Authorization: authToken ? `Bearer ${authToken}` : '',
			'x-user-id': userId,
			'x-brand-id': brandId,
			'x-group-id': groupId
		},
		credentials: 'include',
		maxSteps: 3, // Allow multi-step tool usage
		onError: (error) => {
			console.error('Chat error:', error);
			chatError = error;
		}
	});

	// References to DOM elements (will be bound inside the snippet)
	let messageContainer: HTMLDivElement;
	let inputRef: HTMLInputElement;

	// Helper function to format API error messages
	function getErrorMessage(error: any): string {
		if (typeof error === 'string') return error;
		if (error?.message) return error.message;
		if (error?.error)
			return typeof error.error === 'string' ? error.error : JSON.stringify(error.error);
		return 'An unknown error occurred';
	}
</script>

{#snippet actions()}
	<!-- Actions snippet for PageContainer header, currently empty -->
{/snippet}

{#snippet content()}
	{@const _ = (() => {
		onMount(async () => {
			// Focus the input field if available
			if (inputRef) {
				inputRef.focus();
			}
		});

		// Scroll to bottom when messages change
		$effect(() => {
			if (messageContainer && chat.messages.length > 0) {
				setTimeout(() => {
					messageContainer.scrollTo({
						top: messageContainer.scrollHeight,
						behavior: 'smooth'
					});
				}, 100);
			}
		});
		return null; // Need to return something from the IIFE
	})()}

	<Card class="overflow-hidden bg-card shadow-lg">
		<CardHeader class="border-b bg-muted/30 p-4 dark:bg-muted/10">
			<div class="flex items-center justify-between">
				<div>
					<CardTitle class="flex items-center gap-2 text-base font-semibold">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="18"
							height="18"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							stroke-width="2"
							stroke-linecap="round"
							stroke-linejoin="round"
							class="text-primary"
						>
							<path d="M12 5v14" />
							<path d="m19 12-7 7-7-7" />
						</svg>
						Support Assistant
					</CardTitle>
					<CardDescription class="mt-0.5 text-xs text-muted-foreground">
						Ask about orders, products, or request a refund
					</CardDescription>
				</div>
				{#if chat.status === 'streaming'}
					<div
						class="flex animate-pulse items-center rounded-full border border-green-200 bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:border-green-800 dark:bg-green-900/50 dark:text-green-300"
					>
						<span class="mr-1.5 inline-block h-2 w-2 rounded-full bg-green-500"></span>
						Processing...
					</div>
				{:else}
					<div
						class="flex items-center rounded-full border border-blue-200 bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:border-blue-800 dark:bg-blue-900/50 dark:text-blue-300"
					>
						<span class="mr-1.5 inline-block h-2 w-2 rounded-full bg-blue-500"></span>
						Online
					</div>
				{/if}
			</div>
		</CardHeader>
		<CardContent class="p-0">
			<div class="h-[60vh]">
				<ScrollArea class="h-full">
					<div class="h-full px-6 py-4" bind:this={messageContainer}>
						{#if chat.messages.length === 0}
							<div
								class="flex h-full flex-col items-center justify-center space-y-6 p-8 text-center"
								in:fade={{ duration: 300, delay: 150 }}
							>
								<div
									class="flex h-24 w-24 items-center justify-center rounded-full bg-primary/10 p-6"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="28"
										height="28"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										stroke-width="2"
										stroke-linecap="round"
										stroke-linejoin="round"
										class="h-12 w-12 text-primary"
										><circle cx="12" cy="12" r="10" /><path d="M8 9.05v-.1" /><path
											d="M16 9.05v-.1"
										/><path d="M12 18c2.5-1 4-2.5 4-6" /><path d="M8 14c1.5.5 3.5.5 5 0" /></svg
									>
								</div>
								<div>
									<h3 class="text-xl font-medium">Welcome to Customer Support</h3>
									<p class="mt-2 max-w-md text-muted-foreground">
										How can we help you today? Ask about orders, products, or request a refund.
									</p>
									<div class="mt-6 flex flex-wrap justify-center gap-2">
										<Button
											variant="outline"
											class="text-sm"
											onclick={() => (chat.input = 'How do I track my order?')}
										>
											Track my order
										</Button>
										<Button
											variant="outline"
											class="text-sm"
											onclick={() => (chat.input = 'I need a refund for my recent purchase')}
										>
											Request a refund
										</Button>
										<Button
											variant="outline"
											class="text-sm"
											onclick={() => (chat.input = "What's your return policy?")}
										>
											Return policy
										</Button>
									</div>
								</div>
							</div>
						{:else}
							<div class="flex flex-col space-y-6 pt-4" in:fade={{ duration: 300 }}>
								{#each chat.messages as message (message.id)}
									<div in:fly={{ y: 20, duration: 300, delay: 100 }} out:fade={{ duration: 150 }}>
										<ChatMessage {message} />
									</div>
								{/each}
								{#if chat.status === 'streaming' && chat.messages[chat.messages.length - 1]?.role === 'user'}
									<div class="flex items-start gap-3" transition:fade={{ duration: 200 }}>
										<Avatar
											class="mt-6 h-8 w-8 shrink-0 border border-border/30 bg-muted shadow-sm dark:bg-gray-600"
										>
											<svg
												xmlns="http://www.w3.org/2000/svg"
												width="24"
												height="24"
												viewBox="0 0 24 24"
												fill="none"
												stroke="currentColor"
												stroke-width="2"
												stroke-linecap="round"
												stroke-linejoin="round"
												class="h-4 w-4 text-gray-300 dark:text-gray-300"
												><circle cx="12" cy="12" r="10" /><path d="M8 9.05v-.1" /><path
													d="M16 9.05v-.1"
												/><path d="M12 18c2.5-1 4-2.5 4-6" /><path d="M8 14c1.5.5 3.5.5 5 0" /></svg
											>
										</Avatar>
										<div class="flex w-full max-w-[85%] flex-col items-start space-y-1">
											<div
												class="mb-0.5 text-xs font-medium text-muted-foreground dark:text-gray-400"
											>
												Assistant
											</div>
											<div
												class="flex flex-col gap-2 rounded-lg rounded-tl-none bg-gray-100 px-4 py-3 text-gray-800 shadow-sm dark:bg-gray-700 dark:text-gray-200"
											>
												<div class="flex items-center gap-1">
													<span class="h-1.5 w-1.5 animate-pulse rounded-full bg-current"></span>
													<span class="h-1.5 w-1.5 animate-pulse rounded-full bg-current delay-150"
													></span>
													<span class="h-1.5 w-1.5 animate-pulse rounded-full bg-current delay-300"
													></span>
												</div>
											</div>
										</div>
									</div>
								{/if}
							</div>
						{/if}
					</div>
				</ScrollArea>
			</div>
		</CardContent>
		<CardFooter class="flex flex-col gap-4 border-t bg-muted/30 p-4 dark:bg-muted/10">
			{#if chat.error || chatError}
				<div class="w-full" transition:fade={{ duration: 200 }}>
					<Alert variant="destructive" class="mb-4 mt-0 shadow-sm">
						<AlertTitle>Error</AlertTitle>
						<AlertDescription>
							{getErrorMessage(chat.error || chatError)}
							{#if getErrorMessage(chat.error || chatError).includes('API key')}
								<div class="mt-2 text-xs">
									The OpenAI API key is missing or invalid. Please set the OPENAI_API_KEY
									environment variable.
								</div>
							{/if}
						</AlertDescription>
					</Alert>
				</div>
			{/if}
			<form class="flex w-full space-x-2" onsubmit={chat.handleSubmit}>
				<div class="relative flex-1">
					<input
						class="flex h-11 w-full rounded-md border border-input bg-card py-2 pl-4 pr-12 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
						placeholder="Type your message..."
						bind:value={chat.input}
						disabled={chat.status === 'streaming'}
						bind:this={inputRef}
					/>
					{#if chat.status === 'streaming'}
						<div
							class="absolute right-3 top-1/2 -translate-y-1/2"
							transition:fade={{ duration: 150 }}
						>
							<svg
								class="h-5 w-5 animate-spin text-muted-foreground"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
							>
								<circle
									class="opacity-25"
									cx="12"
									cy="12"
									r="10"
									stroke="currentColor"
									stroke-width="4"
								></circle>
								<path
									class="opacity-75"
									fill="currentColor"
									d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
								></path>
							</svg>
						</div>
					{/if}
				</div>
				<Button
					type="submit"
					class="h-11 shrink-0 px-4 transition-opacity duration-200 disabled:opacity-50"
					disabled={chat.status === 'streaming' || !chat.input}
				>
					Send
				</Button>
			</form>
			<div class="mt-4 text-center text-xs text-muted-foreground">
				Your conversation is private and secure
			</div>
		</CardFooter>
	</Card>
{/snippet}

<PageContainer
	title="AI Customer Support"
	description="Get instant help with your inquiries. Our AI assistant is available 24/7."
	{actions}
	{content}
/>
