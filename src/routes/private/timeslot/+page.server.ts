import type { PageServerLoad } from './$types';
import type { Database } from '$lib/supabase/database.types';
import { fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { timeSlotFormSchema } from './schema';
import { createServerClient } from '@supabase/ssr';
import { DateTime } from 'luxon';
import { setError } from 'sveltekit-superforms';

export const load: PageServerLoad = async ({ depends, locals: { supabase, brand } }) => {
	depends('supabase:db:landmarks');

	const landmarksPromise = supabase
		.from('landmark')
		.select(
			`
			id,
			title_short,
			address (
				id,
				time_zone
			)`
		)
		.eq('brand_id', brand.id)
		.order('created_at', { ascending: false })
		.then(({ data: landmarks, error: landmarkError }) => {
			if (landmarkError) {
				console.error('Error loading landmarks:', landmarkError);
				return [];
			}
			return landmarks || [];
		});

	const eventsPromise = landmarksPromise.then((landmarks) =>
		supabase
			.from('event')
			.select(
				`
				id,
				start_at,
				auto_end_at,
				landmark_id,
				metadata (
					auto_final_title,
					auto_final_subtitle
				)
				`
			)
			.in('landmark_id', landmarks?.map((l) => l.id) || [])
			.gte('auto_end_at', new Date().toISOString())
			.order('start_at', { ascending: true })
			.then(({ data: events, error: eventError }) => {
				if (eventError) {
					console.error('Error loading events:', eventError);
					return [];
				}

				const mappedEvents =
					events?.map((event) => ({
						id: event.id,
						title: event.metadata[0]?.auto_final_title,
						subtitle: event.metadata[0]?.auto_final_subtitle,
						start: event.start_at,
						end: event.auto_end_at,
						location: event.landmark_id
					})) || [];

				return mappedEvents;
			})
	);

	return {
		landmarks: landmarksPromise,
		events: eventsPromise,
		form: await superValidate(
			{
				startDate: '',
				startTime: '',
				durationMinutes: 30,
				repeatInterval: null,
				repeatEvery: null,
				repeatOnDays: [],
				repeatUntil: null,
				repeatCount: null,
				space_id: 'f8d884b5-0b20-4548-93ac-60b2a3d913f9',
				excluded_dates: []
			},
			zod(timeSlotFormSchema)
		)
	};
};

export const actions = {
	upsert: async ({ request, locals: { supabase } }) => {
		const form = await superValidate(request, zod(timeSlotFormSchema));
		console.log('Form data:', form.data);
		if (!form.valid) return fail(400, { form });

		const {
			data: { user }
		} = await supabase.auth.getUser();
		if (!user) return fail(401, { form, message: 'You must be logged in to manage time slots' });

		const {
			id,
			startDate,
			startTime,
			durationMinutes,
			repeatInterval,
			repeatEvery,
			repeatOnDays,
			repeatUntil,
			repeatCount,
			space_id
		} = form.data;

		const { error } = await supabase.from('time_slot_rule').upsert({
			id: id || crypto.randomUUID(),
			space_id,
			start_date: startDate,
			start_time: startTime,
			duration_minute: durationMinutes,
			repeat_interval: repeatInterval,
			repeat_every: repeatEvery,
			repeat_on_days: repeatOnDays,
			repeat_until: repeatUntil,
			repeat_count: repeatCount,
			creator_id: user.id
		});

		if (error) {
			console.error('Error upserting time slot rule:', error);
			if (error.code === 'P0001') {
				return setError(form, 'startDate', error.message);
			}
			return setError(form, '', error.message || 'Failed to save time slot rule');
		}

		return { form };
	}
};
