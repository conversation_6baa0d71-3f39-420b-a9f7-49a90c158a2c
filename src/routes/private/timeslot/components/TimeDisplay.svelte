<script lang="ts">
	import { Clock, Globe } from '@lucide/svelte';
	import { DateTime } from 'luxon';
	import { getLocale } from '$lib/paraglide/runtime';

	let { time } = $props<{
		time: string;
	}>();

	let dt = $derived(
		DateTime.fromMillis(Date.parse(time))
			.setLocale(getLocale())
			.setZone(Intl.DateTimeFormat().resolvedOptions().timeZone)
	);
	let formattedTime = $derived.by(() => {
		const hour = dt.hour;
		const minutes = dt.minute;
		const base = dt.toLocaleString(DateTime.DATETIME_MED);
		if (hour === 0 && minutes === 0) return `${base} (Midnight)`;
		if (hour === 12 && minutes === 0) return `${base} (Noon)`;
		if (hour === 0) return `${base} (Night)`;
		if (hour === 12) return `${base} (Noon hour)`;
		return base;
	});
	let timezone = $derived.by(() => `${dt.zoneName} (${dt.toFormat('ZZZZ')})`);

	$effect(() => {
		const interval = setInterval(() => {
			time = Date.now().toString();
		}, 1000);

		return () => clearInterval(interval);
	});
</script>

<div class="time-display">
	<span class="time">
		<Clock class="icon" size={10} />
		{formattedTime}
	</span>
	<span class="timezone">
		<Globe class="icon" size={10} />
		{timezone}
	</span>
</div>

<style>
	.time-display {
		display: flex;
		flex-wrap: wrap;
		align-items: baseline;
		padding: 0.75rem;
		margin-top: 0.5rem;
		border-top: 1px solid var(--ec-border-color);
		gap: 0.5rem;
		font-size: 0.75rem;
		color: hsl(var(--muted-foreground));
	}

	.time,
	.timezone {
		display: inline-flex;
		align-items: center;
		gap: 0.25rem;
	}

	.time {
		font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
			'Courier New', monospace;
	}

	.icon {
		color: hsl(var(--muted-foreground) / 0.8);
		flex-shrink: 0;
	}

	@media (min-width: 768px) {
		.time-display {
			flex-wrap: nowrap;
		}
	}
</style>
