<!-- src/routes/event/+page.svelte -->
<script lang="ts">
	import * as Select from '$lib/components/ui/select';
	import type { PageData } from './$types';
	import EventDialog from './EventDialog.svelte';
	import EventCalendar from './EventCalendar.svelte';
	import TimeDisplay from './components/TimeDisplay.svelte';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { timeSlotFormSchema } from './schema';

	import type { CalendarEventViewModel, LocationViewModel } from './types';
	import type { DatesSetArg } from '@event-calendar/core';
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { Tables } from '$lib/supabase/database.types';
	import type { Database } from '$lib/supabase/database.types';
	let { data }: { data: PageData } = $props();
	let selectedLocation = $state<string | undefined>(undefined);
	let showEventDialog = $state(false);
	let eventInfo = $state<CalendarEventViewModel | { start: string; end: string } | undefined>(
		undefined
	);
	let events = $state<CalendarEventViewModel[]>([]);
	let dateRange = $state<{ start: Date; end: Date } | null>(null);
	let locations = $state<LocationViewModel[]>([]);
	let isLoading = $state(true);
	let currentUserId = $derived(data.user?.id ?? '');
	const form = superForm(data.form, {
		validators: zodClient(timeSlotFormSchema),
		onResult: ({ result }) => {
			if (result.type === 'success') {
				showEventDialog = false;
				fetchEvents();
			}
		}
	});

	let triggerContent = $derived(
		locations.find((loc) => loc.id === selectedLocation)?.name ?? 'All Locations'
	);

	$effect(() => {
		data.events.then((eventData) => {
			events = eventData;
		});
	});

	$effect(() => {
		data.landmarks.then((landmarks) => {
			locations = landmarks.map((landmark) => ({
				id: landmark.id,
				name: getLocalizedText(landmark.title_short, getLocale())
			}));
			isLoading = false;
		});
	});

	$effect(() => {
		if (dateRange && selectedLocation) {
			fetchEvents();
		}
	});

	async function fetchEvents() {
		if (!dateRange || !selectedLocation) {
			return;
		}

		const { data: fetchedEvents, error } = await data.supabase
			.from('event')
			.select(
				`
				id,
				nid,
				start_at,
				auto_end_at,
				landmark_id,
				metadata(
					auto_final_title,
					auto_final_subtitle
				)`
			)
			.eq('landmark_id', selectedLocation)
			.gte('start_at', dateRange.start.toISOString())
			.lte('start_at', dateRange.end.toISOString())
			.order('start_at', { ascending: true });

		if (error) {
			console.error('Error fetching events:', error);
			return;
		}

		events = (fetchedEvents || []).map((event) => ({
			id: event.id,
			nid: event.nid,
			title: event.metadata[0]?.auto_final_title,
			subtitle: event.metadata[0]?.auto_final_subtitle,
			start: event.start_at,
			end: event.auto_end_at,
			location: event.landmark_id
		}));
	}

	function handleDatesSet(info: DatesSetArg): void {
		if (!info?.view?.currentStart || !info?.view?.currentEnd) {
			console.warn('Invalid date range received');
			return;
		}

		dateRange = {
			start: info.view.currentStart,
			end: info.view.currentEnd
		};
	}
</script>

{#snippet actions()}
	<div class="w-[200px]">
		<Select.Root type="single" name="location" bind:value={selectedLocation}>
			<Select.Trigger class="w-full">
				{isLoading ? 'Loading...' : triggerContent}
			</Select.Trigger>
			<Select.Content>
				<Select.Group>
					<Select.GroupHeading>Locations</Select.GroupHeading>
					<Select.Item value="" label="All Locations">All Locations</Select.Item>
					{#each locations as location}
						<Select.Item value={location.id} label={location.name}>
							{location.name}
						</Select.Item>
					{/each}
				</Select.Group>
			</Select.Content>
		</Select.Root>
	</div>
{/snippet}

{#snippet content()}
	<EventCalendar
		{events}
		onEventCreate={(event) => {
			eventInfo = {
				start: event.start,
				end: event.end,
				location: selectedLocation || ''
			};
			showEventDialog = true;
		}}
		onEventUpdate={(event) => {
			eventInfo = event;
			showEventDialog = true;
		}}
		onDatesSet={handleDatesSet}
	/>
	<TimeDisplay time={new Date().toISOString()} />
	<EventDialog bind:open={showEventDialog} {eventInfo} {selectedLocation} {currentUserId} {form} />
{/snippet}

<PageContainer title="Event Calendar" {actions} {content} />
