import type { Event as CalendarEventType } from '@event-calendar/core';
import type { LocalizedText } from '$lib/utils/localization';
import type { Database } from '$lib/supabase/database.types';

export interface LocationViewModel {
	id: string;
	name: string;
}

export interface CalendarEventViewModel {
	id: string;
	nid?: number;
	title: string | LocalizedText;
	subtitle?: string | LocalizedText;
	start: string;
	end: string;
	location: string;
}

export type EventRow = Database['public']['Tables']['event']['Row'];

export type EventMetadata = {
	auto_final_title: LocalizedText;
	auto_final_subtitle?: LocalizedText;
};

export interface DatabaseEventRow {
	id: string;
	start_at: string;
	auto_end_at: string;
	landmark_id: string;
	metadata: EventMetadata[];
}
