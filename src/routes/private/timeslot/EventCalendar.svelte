<!-- src/routes/private/timeslot/EventCalendar.svelte -->
<script lang="ts">
	import { Calendar } from '@event-calendar/core';
	import { TimeGrid } from '@event-calendar/core';
	import { List } from '@event-calendar/core';
	import { Interaction } from '@event-calendar/core';
	import { DateTime } from 'luxon';
	import type { CalendarEventViewModel } from './types';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';

	interface Props {
		events: CalendarEventViewModel[];
		onEventCreate: (event: CalendarEventViewModel) => void;
		onEventUpdate: (event: CalendarEventViewModel) => void;
		onDatesSet?: (info: any) => void;
	}

	let { events, onEventCreate, onEventUpdate, onDatesSet }: Props = $props();

	let plugins = [TimeGrid, Interaction, List];

	function mapToCalendarEvent(event: CalendarEventViewModel) {
		const title =
			typeof event.title === 'string' ? event.title : getLocalizedText(event.title, getLocale());
		const subtitle = event.subtitle
			? typeof event.subtitle === 'string'
				? event.subtitle
				: getLocalizedText(event.subtitle, getLocale())
			: '';
		return {
			id: event.id,
			title: subtitle ? `${title} - ${subtitle}` : title,
			start: event.start,
			end: event.end,
			extendedProps: {
				location: event.location
			}
		};
	}

	let options = $state({
		view: 'timeGridWeek',
		headerToolbar: {
			start: 'prev,next today',
			center: 'title',
			end: 'timeGridWeek,timeGridDay,listWeek'
		},
		scrollTime: '08:00:00',
		slotMinTime: '08:00:00',
		slotMaxTime: '24:00:00',
		views: {
			timeGridWeek: { pointer: true },
			dayGridMonth: { pointer: true },
			timeGridDay: { pointer: true }
		},
		dayMaxEvents: true,
		nowIndicator: true,
		selectable: true,
		editable: false,
		eventStartEditable: false,
		eventDurationEditable: false,
		slotDuration: '00:10:00',
		allDaySlot: false,
		events: [],
		select: (info: SelectInfo) => {
			const start = DateTime.fromJSDate(info.start);
			const end = DateTime.fromJSDate(info.end);

			const newEvent: CalendarEventViewModel = {
				id: crypto.randomUUID(),
				title: '',
				start: start.toISO() || '',
				end: end.toISO() || '',
				location: ''
			};

			onEventCreate(newEvent);
		},
		eventClick: (info: { event: Event }) => {
			const event = info.event;
			// Split combined title back into title and subtitle
			const [title, subtitle] = (event.title || '').split(' - ');
			const calendarEvent: CalendarEventViewModel = {
				id: event.id,
				title: title || '',
				subtitle: subtitle || '',
				start: event.start,
				end: event.end,
				location: event.extendedProps?.location || ''
			};
			onEventUpdate(calendarEvent);
		},
		datesSet: onDatesSet
	});

	$effect(() => {
		options.events = events.map(mapToCalendarEvent);
		if (onDatesSet) {
			options.datesSet = onDatesSet;
		}
	});
</script>

<div class="ec">
	<Calendar {plugins} {options}></Calendar>
</div>

<style>
	:global(.ec) {
		--ec-border-color: hsl(var(--border));
		--ec-today-bg-color: hsl(var(--muted) / 0.3);
		--ec-event-bg-color: hsl(var(--primary));
		--ec-event-border-color: hsl(var(--primary));
		--ec-button-hover-bg-color: hsl(var(--muted));
		--ec-button-active-bg-color: hsl(var(--accent));
		--ec-button-active-border-color: hsl(var(--accent));
		--ec-button-text-color: hsl(var(--foreground));
		--ec-text-color: hsl(var(--foreground));
		--ec-toolbar-bg-color: transparent;
		--ec-bg-color: transparent;
		--ec-button-bg-color: transparent;
		--ec-select-bg-color: hsl(var(--primary) / 0.1);
		font-family: inherit;
	}

	:global(.ec-preview),
	:global(.ec-highlight),
	:global(.ec-event),
	:global(.ec-draggable) {
		z-index: 1 !important;
	}

	:global(.ec-toolbar) {
		padding: 1rem 0rem;
		border-bottom: 1px solid var(--ec-border-color);
	}

	:global(.ec-button) {
		border: 1px solid var(--ec-border-color) !important;
		padding: 0.5rem 1rem !important;
		font-size: 0.875rem !important;
		line-height: 1.25rem !important;
		font-weight: 500 !important;
		text-transform: capitalize !important;
	}

	:global(.ec-button:hover) {
		background-color: var(--ec-button-hover-bg-color) !important;
	}

	:global(.ec-title) {
		font-size: 1.25rem !important;
		line-height: 1.75rem !important;
		font-weight: 600 !important;
	}

	:global(.ec-body) {
		border: none !important;
	}

	:global(.ec-day-grid .ec-bg) {
		border-color: var(--ec-border-color) !important;
	}

	:global(.ec-time-grid .ec-bg) {
		border-color: var(--ec-border-color) !important;
	}

	:global(.dark) :global(.ec-select) {
		background-color: hsl(var(--muted)) !important;
	}

	:global(.dark) :global(.ec-highlight) {
		background-color: hsl(var(--muted)) !important;
	}

	:global(.dark) :global(.ec-preview) {
		background-color: hsl(var(--muted)) !important;
	}

	:global(.dark) :global(.ec-event) {
		background-color: hsl(var(--muted)) !important;
	}
</style>
