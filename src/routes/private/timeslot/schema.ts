import { z } from 'zod';

export const repeatIntervals = ['daily', 'weekly', 'monthly', 'yearly'] as const;
export type RepeatInterval = (typeof repeatIntervals)[number];

export const timeSlotFormSchema = z
	.object({
		id: z.string().optional(),
		startDate: z.string().min(1, 'Start date is required'),
		startTime: z.string().min(1, 'Start time is required'),
		durationMinutes: z.number().min(1, 'Duration must be at least 1 minute'),
		repeatInterval: z.enum(repeatIntervals).nullable(),
		repeatEvery: z.number().min(1, 'Repeat interval must be at least 1').nullable(),
		repeatOnDays: z.array(z.number()),
		repeatUntil: z.string().nullable(),
		repeatCount: z.number().min(1).max(999).nullable(),
		space_id: z.string().min(1, 'Space is required'),
		excluded_dates: z.array(z.string())
	})
	.refine(
		(data) => {
			if (data.repeatInterval === null) return true;
			return data.repeatEvery !== null;
		},
		{
			message: 'Repeat interval requires a value for repeat every',
			path: ['repeatEvery']
		}
	)
	.refine(
		(data) => {
			if (data.repeatInterval !== 'weekly' && data.repeatInterval !== 'monthly') return true;
			return data.repeatOnDays.length > 0;
		},
		{
			message: 'Please select at least one day',
			path: ['repeatOnDays']
		}
	)
	.refine(
		(data) => {
			if (data.repeatInterval === null) return true;
			return data.repeatCount !== null || data.repeatUntil !== null;
		},
		{
			message: 'Please specify when the repeat should end',
			path: ['repeatCount']
		}
	);

export type TimeSlotFormSchema = z.infer<typeof timeSlotFormSchema>;
