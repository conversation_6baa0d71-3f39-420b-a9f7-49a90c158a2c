<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Select from '$lib/components/ui/select';
	import * as RadioGroup from '$lib/components/ui/radio-group';
	import * as ToggleGroup from '$lib/components/ui/toggle-group';
	import { Label } from '$lib/components/ui/label';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { DateTime, Duration } from 'luxon';
	import { timeSlotFormSchema, type TimeSlotFormSchema, type RepeatInterval } from './schema';
	import { superForm } from 'sveltekit-superforms';
	import { Field, Control, Description, FieldErrors } from 'formsnap';
	import type { CalendarEventViewModel } from './types';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { X, CircleAlert } from '@lucide/svelte';
	import * as Alert from '$lib/components/ui/alert';
	import ResponsiveModal from '$lib/components/shared/ResponsiveModal.svelte';
	import type { Tables } from '$lib/supabase/database.types';
	import type { Database } from '$lib/supabase/database.types';
	import SuperDebug from 'sveltekit-superforms';

	interface Props {
		open: boolean;
		eventInfo?: CalendarEventViewModel | { start: string; end: string };
		selectedLocation?: string;
		currentUserId: string;
		form: ReturnType<typeof superForm<TimeSlotFormSchema>>;
	}

	let {
		open = $bindable(false),
		eventInfo,
		selectedLocation,
		currentUserId,
		form
	}: Props = $props();

	const { form: formData, enhance, errors, allErrors, delayed } = form;

	let newExcludedDate = $state('');

	function addExcludedDate() {
		if (newExcludedDate && !$formData.excluded_dates.includes(newExcludedDate)) {
			$formData.excluded_dates = [...$formData.excluded_dates, newExcludedDate].sort();
		}
	}

	function removeExcludedDate(date: string) {
		$formData.excluded_dates = $formData.excluded_dates.filter((d) => d !== date);
	}

	$effect(() => {
		const updates: Partial<typeof $formData> = {};

		// Handle repeat interval changes
		if (!$formData.repeatInterval) {
			if ($formData.repeatEvery !== null) updates.repeatEvery = null;
			if ($formData.repeatOnDays.length > 0) updates.repeatOnDays = [];
			if ($formData.repeatCount !== null) updates.repeatCount = null;
			if ($formData.repeatUntil !== null) updates.repeatUntil = null;
		} else if ($formData.repeatEvery === null) {
			updates.repeatEvery = 1;
		}

		// Handle start date changes for weekly/monthly repeats
		if (
			$formData.startDate &&
			($formData.repeatInterval === 'weekly' || $formData.repeatInterval === 'monthly')
		) {
			const date = DateTime.fromISO($formData.startDate);
			const newDay = $formData.repeatInterval === 'weekly' ? date.weekday % 7 : date.day;

			if (
				$formData.repeatOnDays.length === 0 ||
				($formData.repeatOnDays.length === 1 && $formData.repeatOnDays[0] !== newDay)
			) {
				updates.repeatOnDays = [newDay];
			}
		}

		// Apply updates if any
		if (Object.keys(updates).length > 0) {
			$formData = { ...$formData, ...updates };
		}
	});

	$effect(() => {
		if (!eventInfo || !('start' in eventInfo)) return;

		const start = DateTime.fromISO(eventInfo.start);
		const end = DateTime.fromISO(eventInfo.end);
		const startDate = start.toISODate() || '';
		const startTime = start.toFormat('HH:mm');
		const duration = end.diff(start, 'minutes').minutes;

		if (
			startDate !== $formData.startDate ||
			startTime !== $formData.startTime ||
			duration !== $formData.durationMinutes
		) {
			$formData = {
				...$formData,
				startDate,
				startTime,
				durationMinutes: duration
			};
		}
	});

	const endTime = $derived.by(() => {
		if (!$formData.startTime || !$formData.durationMinutes) return '';
		const [hours, minutes] = $formData.startTime.split(':').map(Number);
		return DateTime.now()
			.set({ hour: hours, minute: minutes })
			.plus({ minutes: $formData.durationMinutes })
			.toFormat('HH:mm');
	});

	const repeatIntervalLabels = {
		daily: 'day(s)',
		weekly: 'week(s)',
		monthly: 'month(s)',
		yearly: 'year(s)'
	} as const;

	const repeatOptions = [
		{ value: null, label: 'One time' },
		{ value: 'daily', label: 'Daily' },
		{ value: 'weekly', label: 'Weekly' },
		{ value: 'monthly', label: 'Monthly' },
		{ value: 'yearly', label: 'Yearly' }
	] as const;

	const repeatTriggerContent = $derived(
		repeatOptions.find(
			(opt) =>
				(opt.value === null && $formData.repeatInterval === null) ||
				opt.value === $formData.repeatInterval
		)?.label ?? 'Select repeat type'
	);

	$effect(() => {
		if (!$formData.space_id) {
			$formData.space_id = 'f8d884b5-0b20-4548-93ac-60b2a3d913f9';
		}
	});
</script>

<ResponsiveModal
	bind:open
	title={eventInfo && 'id' in eventInfo ? `Edit Time Slot #${eventInfo.nid}` : 'Create Time Slot'}
	onOpenChange={(isOpen) => (open = isOpen)}
>
	<form method="POST" action="?/upsert" use:enhance>
		{#if $allErrors.length}
			<Alert.Root variant="destructive" class="mb-4">
				<CircleAlert class="size-4" />
				<Alert.Title>Error</Alert.Title>
				<Alert.Description>
					<ul class="mt-2 space-y-2">
						{#each $allErrors as error}
							<li>{error.messages[0]}</li>
						{/each}
					</ul>
				</Alert.Description>
			</Alert.Root>
		{/if}
		<div class="flex gap-4">
			<div class="flex-1">
				<Field {form} name="startDate">
					<Control>
						{#snippet children({ props })}
							<Label>Start Date</Label>
							<Input type="date" {...props} bind:value={$formData.startDate} />
						{/snippet}
					</Control>
					<FieldErrors />
				</Field>
			</div>

			<div class="flex-1">
				<Field {form} name="startTime">
					<Control>
						{#snippet children({ props })}
							<Label>Start Time</Label>
							<Input type="time" {...props} step="600" bind:value={$formData.startTime} />
						{/snippet}
					</Control>
					<FieldErrors />
				</Field>
			</div>
		</div>

		<Field {form} name="space_id">
			<Control>
				{#snippet children({ props })}
					<Label>Space</Label>
					<Select.Root type="single" {...props} bind:value={$formData.space_id}>
						<Select.Trigger class="w-full">
							{$formData.space_id ? 'Room C' : 'Select a space'}
						</Select.Trigger>
						<Select.Content>
							<Select.Group>
								<Select.Item value="f8d884b5-0b20-4548-93ac-60b2a3d913f9">Room C</Select.Item>
							</Select.Group>
						</Select.Content>
					</Select.Root>
				{/snippet}
			</Control>
			<Description>Select which space this time slot is for</Description>
			<FieldErrors />
		</Field>

		<Field {form} name="durationMinutes">
			<Control>
				{#snippet children({ props })}
					<Label>Duration (minutes)</Label>
					<Input
						type="number"
						{...props}
						min="10"
						step="10"
						bind:value={$formData.durationMinutes}
					/>
				{/snippet}
			</Control>
			<Description>
				How long the time slot lasts {#if endTime}(ends at {endTime}){/if}
			</Description>
			<FieldErrors />
		</Field>

		<Field {form} name="repeatInterval">
			<Control>
				{#snippet children({ props })}
					<Label>Repeat</Label>
					<Select.Root type="single" {...props} bind:value={$formData.repeatInterval}>
						<Select.Trigger class="w-full">
							{repeatTriggerContent}
						</Select.Trigger>
						<Select.Content>
							<Select.Group>
								{#each repeatOptions as option}
									<Select.Item value={option.value} label={option.label}>{option.label}</Select.Item
									>
								{/each}
							</Select.Group>
						</Select.Content>
					</Select.Root>
				{/snippet}
			</Control>
			<Description>How often this time slot should repeat</Description>
			<FieldErrors />
		</Field>

		{#if $formData.repeatInterval !== null}
			<Field {form} name="repeatEvery">
				<Control>
					{#snippet children({ props })}
						<div class="flex items-center gap-2">
							<Label>Repeat Every</Label>
							<Input
								type="number"
								{...props}
								min="1"
								class="w-20"
								bind:value={$formData.repeatEvery}
							/>
							<span
								>{$formData.repeatInterval
									? repeatIntervalLabels[$formData.repeatInterval]
									: ''}</span
							>
						</div>
					{/snippet}
				</Control>
				<Description>How often the time slot should repeat</Description>
				<FieldErrors />
			</Field>

			{#if $formData.repeatInterval === 'weekly'}
				<Field {form} name="repeatOnDays">
					<Control>
						{#snippet children({ props })}
							<Label>Repeat on days</Label>
							<ToggleGroup.Root
								type="multiple"
								{...props}
								value={$formData.repeatOnDays.map(String)}
								onValueChange={(val) => {
									$formData.repeatOnDays = val.map(Number).sort((a, b) => a - b);
								}}
								class="grid grid-cols-7 gap-1"
							>
								{#each ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'] as day, i}
									<ToggleGroup.Item
										value={i.toString()}
										aria-label={`Toggle ${day}`}
										class="w-full border border-input hover:bg-accent hover:text-accent-foreground"
									>
										{day}
									</ToggleGroup.Item>
								{/each}
							</ToggleGroup.Root>
						{/snippet}
					</Control>
					<Description>Select which days of the week this time slot should occur on</Description>
					<FieldErrors />
				</Field>
			{:else if $formData.repeatInterval === 'monthly'}
				<Field {form} name="repeatOnDays">
					<Control>
						{#snippet children({ props })}
							<Label>Repeat on days</Label>
							<ToggleGroup.Root
								type="multiple"
								{...props}
								value={$formData.repeatOnDays.map(String)}
								onValueChange={(val) => {
									$formData.repeatOnDays = val.map(Number).sort((a, b) => a - b);
								}}
								class="grid grid-cols-7 gap-1"
							>
								{#each Array(31) as _, i}
									<ToggleGroup.Item
										value={(i + 1).toString()}
										aria-label={`Toggle day ${i + 1}`}
										class="w-full border border-input hover:bg-accent hover:text-accent-foreground"
									>
										{i + 1}
									</ToggleGroup.Item>
								{/each}
							</ToggleGroup.Root>
						{/snippet}
					</Control>
					<Description>Select which days of the month this time slot should occur on</Description>
					<FieldErrors />
				</Field>
			{/if}

			<Field {form} name="repeatCount">
				<Control>
					{#snippet children({ props })}
						<RadioGroup.Root
							{...props}
							value={$formData.repeatCount ? 'count' : 'until'}
							onValueChange={(val) => {
								if (val === 'count') {
									$formData.repeatUntil = '';
									$formData.repeatCount = 2;
								} else {
									$formData.repeatCount = null;
									$formData.repeatUntil =
										$formData.repeatUntil || DateTime.now().plus({ days: 30 }).toISODate() || '';
								}
							}}
						>
							<div class="flex h-10 items-center space-x-2">
								<RadioGroup.Item value="count" id="count" />
								<Label for="count">End after</Label>
								{#if $formData.repeatCount !== null}
									<Input
										type="number"
										bind:value={$formData.repeatCount}
										min="1"
										max="999"
										class="w-20"
									/>
									<span>occurrences</span>
								{:else}
									<div class="w-20"></div>
									<div class="w-20"></div>
								{/if}
							</div>
							<div class="flex h-10 items-center space-x-2">
								<RadioGroup.Item value="until" id="until" />
								<Label for="until">End on</Label>
								{#if $formData.repeatUntil}
									<Input type="date" bind:value={$formData.repeatUntil} class="w-40" />
								{:else}
									<div class="w-40"></div>
								{/if}
							</div>
						</RadioGroup.Root>
					{/snippet}
				</Control>
				<Description>Choose when this recurring time slot should end</Description>
				<FieldErrors />
			</Field>

			<Field {form} name="excluded_dates">
				<Control>
					{#snippet children({ props })}
						<Label>Excluded Dates</Label>
						<div class="flex gap-2">
							<Input
								type="date"
								value={newExcludedDate}
								onchange={(e) => {
									const date = e.currentTarget.value;
									if (date && !$formData.excluded_dates.includes(date)) {
										$formData.excluded_dates = [...$formData.excluded_dates, date].sort();
									}
									e.currentTarget.value = '';
								}}
								class="flex-1"
								placeholder="Select dates to exclude"
							/>
						</div>
						{#if $formData.excluded_dates.length > 0}
							<div class="mt-2 flex flex-wrap gap-2">
								{#each $formData.excluded_dates as date}
									<div class="flex items-center gap-1 rounded bg-muted px-2 py-1 text-sm">
										{date}
										<button
											type="button"
											class="ml-1 rounded-full p-1 hover:bg-muted-foreground/20"
											onclick={() => removeExcludedDate(date)}
										>
											<X class="h-3 w-3" />
										</button>
									</div>
								{/each}
							</div>
						{/if}
					{/snippet}
				</Control>
				<Description>Select multiple dates to exclude from the schedule</Description>
				<FieldErrors />
			</Field>
		{/if}
		<div class="flex justify-end gap-4">
			<Button
				type="button"
				variant="outline"
				onclick={() => (open = false)}
				class="min-w-[100px] px-6">Cancel</Button
			>
			<Button type="submit" disabled={$delayed} class="min-w-[100px] px-6">
				{eventInfo && 'id' in eventInfo ? 'Update' : 'Create New'}
			</Button>
		</div>
	</form>
	<SuperDebug data={$formData} />
</ResponsiveModal>
