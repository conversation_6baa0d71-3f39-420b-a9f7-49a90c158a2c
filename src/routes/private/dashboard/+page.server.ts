import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { supabase, user, brand, session } }) => {
	// Check if user is authenticated (already handled by hooks.server.ts authGuard but double-checking)
	if (!user) {
		throw redirect(303, '/auth/sign-in');
	}

	// Check if user has a brand context
	if (!brand?.id) {
		console.error('No brand context available');
		throw redirect(303, '/auth/sign-in');
	}

	// Check if the user has the brand_order_read feature
	const { data: features, error } = await supabase
		.from('profile_feature_access')
		.select('feature_access_id')
		.eq('profile_id', user.id)
		.eq('auto_feature_access_brand_id', brand.id);

	if (error) {
		console.error('Error checking feature access:', error);
		throw redirect(303, '/event');
	}

	// Check if user has the required feature
	const hasRequiredFeature = features?.some(
		(feature) => feature.feature_access_id === 'brand_order_read'
	);

	if (!hasRequiredFeature) {
		console.error('User does not have required feature access: brand_order_read');
		throw redirect(303, '/event');
	}

	// Get the date for 30 days ago
	const thirtyDaysAgo = new Date();
	thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
	const thirtyDaysAgoStr = thirtyDaysAgo.toISOString();

	// Get current date for upper bound (exclude future events)
	const today = new Date();
	const todayStr = today.toISOString();

	// Get all publishing states
	const { data: publishingStates } = await supabase.from('publishing_state').select('id');

	// Fetch events from the last 30 days up to current date (no future events)
	const { data: events, error: eventError } = await supabase
		.from('event')
		.select(
			`
			id,
			title,
			start_at,
			auto_end_at,
			publishing_state,
			brand_id,
			landmark_id,
			kind,
			canceled_at,
			metadata:metadata_id (
				id,
				auto_final_title,
				auto_final_subtitle
			),
			event_members:event_member(
				id,
				member_profile_id,
				member:member_profile_id (
					id,
					func_given_name_en_first,
					func_family_name_en_first,
					auto_user_email
				),
				role,
				checked_in_at,
				checked_out_at
			),
			space:space_id (
				id,
				name_short,
                landmark:landmark_id (
                    id,
                    title_short,
                    address:address_id (
                        city,
                        auto_normalized_address_local,
                        timeZone:time_zone (
                            name
                        )
                    )
                )
			),
			landmark:landmark_id (
			id,
			title_short,
			address:address_id (
				city,
				auto_normalized_address_local,
				timeZone:time_zone (
					name
				)
			)
			)
			`
		)
		.eq('brand_id', brand.id)
		.gte('start_at', thirtyDaysAgoStr)
		.lte('start_at', todayStr) // Only include events up to today (exclude future events)
		.order('start_at', { ascending: false });

	if (eventError) {
		console.error('Error fetching events:', eventError);
	}

	// Calculate summary statistics
	const eventStats = {
		total: events?.length || 0,
		published: events?.filter((e) => e.publishing_state === 'published').length || 0,
		draft: events?.filter((e) => e.publishing_state === 'draft').length || 0,
		readyForReview: events?.filter((e) => e.publishing_state === 'ready_for_review').length || 0,
		hidden: events?.filter((e) => e.publishing_state === 'hidden').length || 0,
		canceled: events?.filter((e) => e.canceled_at !== null).length || 0
	};

	const memberStats = {
		total: events?.reduce((acc, event) => acc + (event.event_members?.length || 0), 0) || 0,
		checkedIn:
			events?.reduce(
				(acc, event) =>
					acc + (event.event_members?.filter((m) => m.checked_in_at !== null).length || 0),
				0
			) || 0
	};

	// If the user has the required feature, return data for the page
	return {
		user,
		brand,
		events: events || [],
		eventStats,
		memberStats,
		publishingStates: publishingStates || []
	};
};
