<script lang="ts">
	import { <PERSON>Container } from '$lib/components/layout';
	import type { PageData } from './$types';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '$lib/components/ui/tabs';
	import { getLocalizedText } from '$lib/utils/localization';
	import { format, parseISO } from 'date-fns';
	import { Badge } from '$lib/components/ui/badge';
	import { CalendarDays, Users, CheckCircle, XCircle, FileSearch, EyeOff } from '@lucide/svelte';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();
	let activeTab = $state('overview');

	// Helper function to format dates
	function formatDate(dateString: string | null): string {
		if (!dateString) return 'N/A';
		try {
			return format(parseISO(dateString), 'MMM d, yyyy h:mm a');
		} catch (e) {
			return 'Invalid date';
		}
	}

	// Helper function to get event title
	function getEventTitle(event: any): string {
		// Use metadata.auto_final_title if available
		if (event.metadata?.auto_final_title) {
			return getLocalizedText(event.metadata.auto_final_title || {}, 'en');
		}
		// Fallback to event.title
		return getLocalizedText(event.title || {}, 'en') || 'Untitled Event';
	}

	// Helper function to get event subtitle
	function getEventSubtitle(event: any): string | null {
		if (event.metadata?.auto_final_subtitle) {
			return getLocalizedText(event.metadata.auto_final_subtitle || {}, 'en');
		}
		return null;
	}

	// Helper function to get location display name
	function getLocationName(event: any): string {
		// Try space → landmark path first
		if (event.space?.landmark && typeof event.space.landmark !== 'undefined') {
			return getLocalizedText(event.space.landmark.title_short || {}, 'en');
		}
		// Fall back to direct landmark
		else if (event.landmark && typeof event.landmark !== 'undefined') {
			return getLocalizedText(event.landmark.title_short || {}, 'en');
		}
		return 'Unknown location';
	}

	// Helper function to get city display name
	function getLocationCity(event: any): string | null {
		// Try space → landmark → address path first
		if (event.space?.landmark?.address?.city) {
			return event.space.landmark.address.city;
		}
		// Fall back to direct landmark → address
		else if (event.landmark?.address?.city) {
			return event.landmark.address.city;
		}
		return null;
	}

	// Helper function to get attendee name
	function getAttendeeName(member: any): string {
		if (member.member) {
			const firstName = member.member.func_given_name_en_first || '';
			const lastName = member.member.func_family_name_en_first || '';
			if (firstName || lastName) {
				return `${firstName} ${lastName}`.trim();
			}
			return member.member.auto_user_email || 'Unknown attendee';
		}
		return 'Unknown attendee';
	}

	// Get publishing state badge variant
	function getPublishingStateBadge(
		state: string | null,
		canceled: boolean
	): { variant: 'destructive' | 'default' | 'outline' | 'secondary' | undefined; label: string } {
		if (canceled) {
			return { variant: 'destructive', label: 'Canceled' };
		}

		switch (state) {
			case 'published':
				return { variant: 'default', label: 'Published' };
			case 'draft':
				return { variant: 'outline', label: 'Draft' };
			case 'ready_for_review':
				return { variant: 'secondary', label: 'Ready for Review' };
			case 'hidden':
				return { variant: 'outline', label: 'Hidden' };
			default:
				return { variant: 'outline', label: state || 'Unknown' };
		}
	}

	// Helper function to safely check if event has landmark data
	function hasLandmark(event: any): boolean {
		return (
			(event.space &&
				event.space.landmark &&
				typeof event.space.landmark !== 'undefined' &&
				!Array.isArray(event.space.landmark)) ||
			(event.landmark && typeof event.landmark !== 'undefined' && !Array.isArray(event.landmark))
		);
	}
</script>

{#snippet actions()}
	<div class="flex items-center gap-2">
		<!-- Action buttons can be added here later -->
	</div>
{/snippet}

{#snippet content()}
	<div class="space-y-6">
		<!-- Summary Stats Cards - now with 2 per row on mobile -->
		<div class="grid grid-cols-2 gap-4 lg:grid-cols-4">
			<Card>
				<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle class="text-sm font-medium">Total Events</CardTitle>
					<CalendarDays class="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold">{data.eventStats.total}</div>
					<p class="text-xs text-muted-foreground">Last 30 days (past events only)</p>
				</CardContent>
			</Card>

			<Card>
				<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle class="text-sm font-medium">Total Attendees</CardTitle>
					<Users class="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold">{data.memberStats.total}</div>
					<p class="text-xs text-muted-foreground">
						{Math.round((data.memberStats.checkedIn / data.memberStats.total) * 100) || 0}% checked
						in
					</p>
				</CardContent>
			</Card>

			<Card>
				<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle class="text-sm font-medium">Published Events</CardTitle>
					<CheckCircle class="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold">{data.eventStats.published}</div>
					<p class="text-xs text-muted-foreground">
						{Math.round((data.eventStats.published / data.eventStats.total) * 100) || 0}% of all
						events
					</p>
				</CardContent>
			</Card>

			<Card>
				<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle class="text-sm font-medium">Review / Draft</CardTitle>
					<FileSearch class="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold">
						{data.eventStats.readyForReview + data.eventStats.draft}
					</div>
					<p class="text-xs text-muted-foreground">
						{data.eventStats.draft} drafts, {data.eventStats.readyForReview} in review
					</p>
				</CardContent>
			</Card>
		</div>

		<!-- Tabs for different views -->
		<Tabs value={activeTab} onValueChange={(value) => (activeTab = value)}>
			<TabsList>
				<TabsTrigger value="overview">Overview</TabsTrigger>
				<TabsTrigger value="events">Events</TabsTrigger>
				<TabsTrigger value="attendance">Attendance</TabsTrigger>
			</TabsList>

			<TabsContent value="overview" class="space-y-4">
				<Card>
					<CardHeader>
						<CardTitle>Check-in</CardTitle>
						<CardDescription>Attendee check-in statistics for the last 30 days</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-4">
							<div class="space-y-2">
								<div class="flex items-center justify-between text-sm">
									<div>Checked in</div>
									<div class="font-medium">
										{data.memberStats.checkedIn} / {data.memberStats.total} attendees ({Math.round(
											(data.memberStats.checkedIn / data.memberStats.total) * 100
										) || 0}%)
									</div>
								</div>
								<div class="h-2 w-full overflow-hidden rounded-full bg-muted">
									<div
										class="h-full bg-primary"
										style="width: {(data.memberStats.checkedIn / data.memberStats.total) * 100 ||
											0}%"
									></div>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Event Status</CardTitle>
						<CardDescription>Publishing state of events in the last 30 days</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-4">
							<div class="space-y-2">
								<div class="flex items-center justify-between text-sm">
									<div>Published</div>
									<div class="font-medium">{data.eventStats.published} events</div>
								</div>
								<div class="h-2 w-full overflow-hidden rounded-full bg-muted">
									<div
										class="h-full bg-primary"
										style="width: {(data.eventStats.published / data.eventStats.total) * 100 || 0}%"
									></div>
								</div>
							</div>
							<div class="space-y-2">
								<div class="flex items-center justify-between text-sm">
									<div>Draft</div>
									<div class="font-medium">{data.eventStats.draft} events</div>
								</div>
								<div class="h-2 w-full overflow-hidden rounded-full bg-muted">
									<div
										class="h-full bg-primary"
										style="width: {(data.eventStats.draft / data.eventStats.total) * 100 || 0}%"
									></div>
								</div>
							</div>
							<div class="space-y-2">
								<div class="flex items-center justify-between text-sm">
									<div>Ready for Review</div>
									<div class="font-medium">{data.eventStats.readyForReview} events</div>
								</div>
								<div class="h-2 w-full overflow-hidden rounded-full bg-muted">
									<div
										class="h-full bg-primary"
										style="width: {(data.eventStats.readyForReview / data.eventStats.total) * 100 ||
											0}%"
									></div>
								</div>
							</div>
							<div class="space-y-2">
								<div class="flex items-center justify-between text-sm">
									<div>Hidden</div>
									<div class="font-medium">{data.eventStats.hidden} events</div>
								</div>
								<div class="h-2 w-full overflow-hidden rounded-full bg-muted">
									<div
										class="h-full bg-primary"
										style="width: {(data.eventStats.hidden / data.eventStats.total) * 100 || 0}%"
									></div>
								</div>
							</div>
							<div class="space-y-2">
								<div class="flex items-center justify-between text-sm">
									<div>Canceled</div>
									<div class="font-medium">{data.eventStats.canceled} events</div>
								</div>
								<div class="h-2 w-full overflow-hidden rounded-full bg-muted">
									<div
										class="h-full bg-primary"
										style="width: {(data.eventStats.canceled / data.eventStats.total) * 100 || 0}%"
									></div>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</TabsContent>

			<TabsContent value="events" class="space-y-4">
				{#if data.events.length === 0}
					<Card>
						<CardContent class="p-6 text-center">
							<p class="text-muted-foreground">No events found for the last 30 days.</p>
						</CardContent>
					</Card>
				{:else}
					{#each data.events as event}
						{@const badgeInfo = getPublishingStateBadge(
							event.publishing_state,
							!!event.canceled_at
						)}
						<Card>
							<CardHeader>
								<div class="flex items-center justify-between">
									<CardTitle>{getEventTitle(event)}</CardTitle>
									<Badge variant={badgeInfo.variant}>
										{badgeInfo.label}
									</Badge>
								</div>
								<CardDescription>
									{#if getEventSubtitle(event)}
										{getEventSubtitle(event)}
										<br />
									{/if}
									{formatDate(event.start_at)}
									{#if hasLandmark(event)}
										at {getLocationName(event)}
										{#if getLocationCity(event)}
											, {getLocationCity(event)}
										{/if}
									{/if}
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div class="flex items-center justify-between">
									<div>
										<p class="text-sm text-muted-foreground">
											Attendees: {event.event_members?.length || 0}
										</p>
										<p class="text-sm text-muted-foreground">
											Checked in: {event.event_members?.filter((m) => m.checked_in_at !== null)
												.length || 0}
										</p>
									</div>
									<div>
										<p class="text-sm font-medium">
											{Math.round(
												((event.event_members?.filter((m) => m.checked_in_at !== null).length ||
													0) /
													(event.event_members?.length || 1)) *
													100
											)}% check-in rate
										</p>
									</div>
								</div>
								{#if event.event_members && event.event_members.length > 0}
									<div class="mt-4">
										<h4 class="mb-2 text-sm font-medium">Attendees:</h4>
										<div class="max-h-32 overflow-y-auto">
											<ul class="space-y-1 text-xs">
												{#each event.event_members.slice(0, 5) as member}
													<li class="flex items-center justify-between">
														<span>{getAttendeeName(member)}</span>
														{#if member.checked_in_at}
															<Badge variant="outline" class="px-1 py-0 text-xs">Checked in</Badge>
														{/if}
													</li>
												{/each}
												{#if event.event_members.length > 5}
													<li class="text-muted-foreground">
														+ {event.event_members.length - 5} more attendees
													</li>
												{/if}
											</ul>
										</div>
									</div>
								{/if}
							</CardContent>
						</Card>
					{/each}
				{/if}
			</TabsContent>

			<TabsContent value="attendance" class="space-y-4">
				<Card>
					<CardHeader>
						<CardTitle>Attendance by Event</CardTitle>
						<CardDescription>Check-in rates for each event in the last 30 days</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-6">
							{#each data.events as event}
								{#if event.event_members && event.event_members.length > 0}
									<div class="space-y-2">
										<div class="flex items-center justify-between text-sm">
											<div>{getEventTitle(event)}</div>
											<div class="font-medium">
												{event.event_members.filter((m) => m.checked_in_at !== null).length} / {event
													.event_members.length}
											</div>
										</div>
										<div class="h-2 w-full overflow-hidden rounded-full bg-muted">
											<div
												class="h-full bg-primary"
												style="width: {(event.event_members.filter((m) => m.checked_in_at !== null)
													.length /
													event.event_members.length) *
													100}%"
											></div>
										</div>
										<p class="text-xs text-muted-foreground">
											{formatDate(event.start_at)}
											{#if hasLandmark(event)}
												at {getLocationName(event)}
											{/if}
										</p>
									</div>
								{/if}
							{/each}
						</div>
					</CardContent>
				</Card>
			</TabsContent>
		</Tabs>
	</div>
{/snippet}

<PageContainer
	title="Dashboard"
	description="Overview of events and attendance in the last 30 days"
	{actions}
	{content}
/>
