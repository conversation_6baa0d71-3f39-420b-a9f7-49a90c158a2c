<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Badge } from '$lib/components/ui/badge';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { UserCheck, CalendarClock } from '@lucide/svelte';
	import { format, isToday } from 'date-fns';
	import { toast } from 'svelte-sonner';
	import type { Database } from '$lib/supabase/database.types';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { SubmitFunction } from '@sveltejs/kit';
	import { flip } from 'svelte/animate';
	import { slide } from 'svelte/transition';
	import AttendeeRow from './AttendeeRow.svelte';
	import EventRegistrationModal from '../../event/components/EventRegistrationModal.svelte';
	import { invalidateAll } from '$app/navigation';

	// Types declaration for import.meta.env in Vite
	/// <reference types="vite/client" />

	// Types
	type EventRow = Database['public']['Tables']['event']['Row'];
	type ProfileRow = Database['public']['Tables']['profile']['Row'];
	type EventMemberRow = Database['public']['Tables']['event_member']['Row'];
	type LocalizedText = {
		[key: string]: string;
	};

	// User profile interface for non-registered profiles
	interface UserProfile {
		id: string;
		given_name: LocalizedText;
		family_name: LocalizedText;
		username?: string;
		avatar_url?: string | null;
		auto_user_email?: string;
		auto_user_phone?: string;
	}

	interface EventWithMetadata extends EventRow {
		metadata: {
			id: string;
			title: LocalizedText;
			auto_final_title: LocalizedText;
			auto_final_subtitle: LocalizedText;
		};
		space?: {
			id: string;
			title_short: LocalizedText;
			landmark?: {
				id: string;
				title_short: LocalizedText;
				address?: {
					city: string;
					auto_normalized_address_local: string | null;
					timeZone?: {
						name: string;
					};
				};
			};
		};
		landmark?: {
			id: string;
			title_short: LocalizedText;
			address?: {
				city: string;
				auto_normalized_address_local: string | null;
				timeZone?: {
					name: string;
				};
			};
		};
	}

	interface EventMember extends EventMemberRow {
		memberProfile: ProfileRow & {
			func_family_name_en_first: string;
			func_given_name_en_first: string;
			auto_user_email: string;
			auto_user_phone: string;
			nickname: string;
			avatar: string | null;
			avatar_url: string | null;
		};
		event: {
			id: string;
		};
	}

	interface Props {
		event: EventWithMetadata;
		supabase: SupabaseClient<Database>;
	}

	let { event, supabase }: Props = $props();

	// State variables
	let loading = $state(false);
	let searchLoading = $state(false);
	let eventMembers = $state<EventMember[]>([]);
	let searchQuery = $state('');
	let processingMemberIds = $state<Set<string>>(new Set());
	let searchResults = $state<UserProfile[]>([]);
	let searchError = $state<string | null>(null);
	let processingProfileId = $state<string | null>(null);

	// Registration modal state
	let selectedWalkinProfile = $state<UserProfile | null>(null);
	let selectedProductId = $state('');
	let optionsData = $state<any>(null);
	let eventDetails = $state<any>(null);

	// Pure functions - these don't update state
	// Get non-registered profiles
	function getNonRegisteredProfiles(): UserProfile[] {
		// Get set of already registered profile IDs
		const registeredProfileIds = new Set(eventMembers.map((m) => m.memberProfile.id));

		// Filter out profiles that are already registered
		return searchResults.filter((profile) => !registeredProfileIds.has(profile.id));
	}

	// Utility functions
	function getLocationName(event: EventWithMetadata): string {
		const locationParts: string[] = [];

		// Get space name if available
		if (event.space) {
			const spaceName = String(getLocalizedText(event.space.title_short as LocalizedText));
			if (spaceName) locationParts.push(spaceName);

			// Get landmark name from space if available
			if (event.space.landmark) {
				const landmarkName = String(
					getLocalizedText(event.space.landmark.title_short as LocalizedText)
				);
				if (landmarkName && landmarkName !== spaceName) locationParts.push(landmarkName);
			}
		}
		// If no space, try to get landmark directly
		else if (event.landmark) {
			const landmarkName = String(getLocalizedText(event.landmark.title_short as LocalizedText));
			if (landmarkName) locationParts.push(landmarkName);
		}

		return locationParts.join(', ') || 'No location';
	}

	// Get event title and subtitle
	function getEventTitle(): string {
		return String(
			String(getLocalizedText(event.metadata?.auto_final_title as LocalizedText)) ||
				event.title ||
				'Untitled Event'
		);
	}

	function getEventSubtitle(): string {
		return String(getLocalizedText(event.metadata?.auto_final_subtitle as LocalizedText)) || '';
	}

	// Derived values
	let filteredEventMembers = $derived(
		eventMembers
			.filter((member) => {
				if (!searchQuery) return true;

				const query = searchQuery.toLowerCase();
				const name =
					`${member.memberProfile.func_given_name_en_first} ${member.memberProfile.func_family_name_en_first}`.toLowerCase();
				const email = member.memberProfile.auto_user_email?.toLowerCase() || '';
				const phone = member.memberProfile.auto_user_phone?.toLowerCase() || '';
				const nickname = member.memberProfile.nickname?.toLowerCase() || '';

				return (
					name.includes(query) ||
					email.includes(query) ||
					phone.includes(query) ||
					nickname.includes(query)
				);
			})
			.sort((a, b) => {
				// Primary sort: checked-in status (non-checked-in first)
				if (a.checked_in_at && !b.checked_in_at) return 1;
				if (!a.checked_in_at && b.checked_in_at) return -1;

				// Secondary sort: alphabetical by name
				const nameA =
					`${a.memberProfile.func_given_name_en_first} ${a.memberProfile.func_family_name_en_first}`.toLowerCase();
				const nameB =
					`${b.memberProfile.func_given_name_en_first} ${b.memberProfile.func_family_name_en_first}`.toLowerCase();

				return nameA.localeCompare(nameB);
			})
	);

	let attendeeEmails = $derived(
		eventMembers
			.map((member) => member.memberProfile.auto_user_email)
			.filter((email) => email) // Filter out undefined or empty emails
			.join(', ')
	);

	// Effects
	// Load event members when event changes
	$effect(() => {
		if (event?.id) {
			loadEventMembers();
		}
	});

	// Update search results when search query changes
	$effect(() => {
		if (searchQuery) {
			searchProfiles(searchQuery);
		} else {
			searchResults = [];
		}
	});

	// Component functions
	async function loadEventMembers(): Promise<void> {
		if (!event?.id) return;

		loading = true;

		try {
			const { data: membersData, error } = await supabase
				.from('event_member')
				.select(
					`
					id,
					role,
					checked_in_at,
					checked_out_at,
					created_at,
					memberProfile:member_profile_id (
						id,
						func_family_name_en_first,
						func_given_name_en_first,
						auto_user_email,
						auto_user_phone,
						nickname,
						avatar,
						avatar_url
					),
					event:event_id (
						id
					)
				`
				)
				.eq('event_id', event.id)
				.eq('role', 'consumer')
				.order('checked_in_at', { ascending: true, nullsFirst: true });

			if (error) {
				throw error;
			}

			eventMembers = membersData as unknown as EventMember[];
		} catch (error) {
			console.error('Error loading event members:', error);
			toast.error('Failed to load attendees');
		} finally {
			loading = false;
		}
	}

	// Handle form submission result
	function handleFormSubmit(memberId: string): SubmitFunction {
		return ({ formElement, formData, action, cancel, submitter }) => {
			// Add member ID to processing set when form is submitted
			processingMemberIds.add(memberId);

			return async ({ result, update }) => {
				// Remove from processing set regardless of result
				processingMemberIds.delete(memberId);

				if (result.type === 'success') {
					// Update local state based on the result
					const updatedMember = result.data?.data?.[0];
					if (updatedMember) {
						eventMembers = eventMembers.map((m) => {
							if (m.id === updatedMember.id) {
								return { ...m, checked_in_at: updatedMember.checked_in_at };
							}
							return m;
						});
					}

					toast.success(result.data?.message || 'Check-in status updated');
				} else if (result.type === 'failure') {
					toast.error(result.data?.message || 'Failed to update check-in status');
				} else if (result.type === 'error') {
					toast.error('An error occurred');
					console.error(result.error);
				}

				// Let SvelteKit update the form
				update();
			};
		};
	}

	// Search for profiles using API
	let searchTimer: number | undefined;

	async function searchProfiles(query: string): Promise<void> {
		if (!query.trim()) {
			searchResults = [];
			return;
		}

		clearTimeout(searchTimer);

		searchTimer = window.setTimeout(async () => {
			if (!query.trim()) return;

			searchLoading = true;
			searchError = null;

			try {
				const res = await fetch(`/api/profile/search?q=${encodeURIComponent(query)}`);

				if (!res.ok) {
					const errorData = await res.json();
					console.error('Search error:', errorData);
					searchError = errorData.error || 'Failed to search';
					searchResults = [];
					return;
				}

				const data = await res.json();

				if (Array.isArray(data.profiles)) {
					searchResults = data.profiles;
				} else {
					console.error('Invalid search results format:', data);
					searchError = 'Invalid response format';
					searchResults = [];
				}
			} catch (error) {
				console.error('Search error:', error);
				searchError = 'An error occurred';
				searchResults = [];
			} finally {
				searchLoading = false;
			}
		}, 300);
	}

	// Handle registration of unregistered profile
	async function registerProfile(profileId: string): Promise<void> {
		if (!event?.id || !profileId || processingProfileId === profileId) return;

		// Reset any prior modal state first
		selectedWalkinProfile = null;
		eventDetails = null;
		optionsData = null;
		selectedProductId = '';

		// Set processing state
		processingProfileId = profileId;

		try {
			console.log('Starting walk-in registration for profile:', profileId);

			// Find the profile to register
			const profile = searchResults.find((p) => p.id === profileId);
			if (!profile) {
				throw new Error('Profile not found');
			}

			// First fetch event details including products
			console.log('Fetching event details for ID:', event.id);
			const eventResponse = await fetch(`/api/events/${event.id}`);

			if (!eventResponse.ok) {
				throw new Error(`Failed to fetch event details: ${eventResponse.status}`);
			}

			const eventData = await eventResponse.json();
			console.log('Event data loaded successfully, products:', eventData.products?.length);

			// Check if the event has products in the fetched details
			if (!eventData.products || eventData.products.length === 0) {
				throw new Error('No products available for this event');
			}

			// Select the first product by default
			const firstProductId = eventData.products[0].product.id;
			console.log('Selected first product ID:', firstProductId);

			if (!firstProductId) {
				throw new Error('Invalid product data');
			}

			// Now fetch options with the profile ID
			console.log('Fetching options for product:', firstProductId, 'and profile:', profileId);
			const optionsResponse = await fetch(
				`/event/private/find-options?productId=${firstProductId}&profileId=${profileId}`
			);

			if (!optionsResponse.ok) {
				throw new Error(`Failed to fetch registration options: ${optionsResponse.status}`);
			}

			const optionsResult = await optionsResponse.json();

			if (optionsResult.type === 'error') {
				throw new Error(optionsResult.error.message);
			}

			// Set all the state in the right sequence
			selectedProductId = firstProductId;
			optionsData = optionsResult.data;
			eventDetails = eventData;

			// Store the selected profile LAST after all other data is ready
			// This ensures the #if condition for the modal will only trigger when everything is ready
			selectedWalkinProfile = profile;

			console.log('Walk-in registration data prepared successfully');
		} catch (error) {
			console.error('Error registering profile:', error);
			toast.error(error instanceof Error ? error.message : 'Failed to register profile');

			// Reset state on error
			selectedWalkinProfile = null;
			eventDetails = null;
			optionsData = null;
			selectedProductId = '';
		} finally {
			// Only clear processing state after a small delay to avoid UI flicker
			setTimeout(() => {
				processingProfileId = null;
			}, 500);
		}
	}
</script>

<div class="space-y-4">
	<!-- Event Header -->
	<div class="flex flex-col space-y-1">
		<div class="flex items-center gap-2">
			<h2 class="text-xl font-semibold">
				{getEventTitle()}
				{#if isToday(new Date(event.start_at))}
					<Badge variant="outline" class="ml-2">Today</Badge>
				{/if}
			</h2>
			<Badge variant="outline" class="ml-auto">
				{eventMembers.filter((m) => m.checked_in_at).length} / {eventMembers.length} Checked In
			</Badge>
		</div>
		{#if getEventSubtitle()}
			<p class="text-sm text-muted-foreground">{getEventSubtitle()}</p>
		{/if}
		<p class="flex items-center gap-1 text-sm text-muted-foreground">
			<CalendarClock class="h-3.5 w-3.5" />
			<span>
				{format(new Date(event.start_at), 'EEEE, MMMM d, yyyy')} at
				{format(new Date(event.start_at), 'h:mm a')} - {getLocationName(event)}
			</span>
		</p>
	</div>

	<!-- Search -->
	<div class="relative w-full">
		<Input
			type="search"
			placeholder="Search attendees or find walk-in profiles..."
			class="w-full"
			value={searchQuery}
			oninput={(e) => (searchQuery = e.currentTarget.value)}
		/>
	</div>

	<!-- Status info -->
	{#if searchQuery && !searchLoading}
		<div class="text-xs text-muted-foreground">
			{#if searchResults.length > 0}
				<p>
					Found {searchResults.length} profiles, {getNonRegisteredProfiles().length} available for walk-in
				</p>
			{:else if searchQuery.length > 2}
				<p>No profiles found matching "{searchQuery}"</p>
			{/if}
		</div>
	{:else if searchLoading}
		<div class="text-xs text-muted-foreground">
			<p>Searching profiles...</p>
		</div>
	{/if}

	<!-- Loading State -->
	{#if (loading && eventMembers.length === 0) || searchLoading}
		<div class="space-y-2">
			{#each Array(5) as _}
				<Skeleton class="h-16 w-full" />
			{/each}
		</div>
		<!-- Empty State -->
	{:else if filteredEventMembers.length === 0 && getNonRegisteredProfiles().length === 0}
		<div class="flex flex-col items-center justify-center py-8 text-center">
			<UserCheck class="mb-2 h-12 w-12 text-muted-foreground" />
			{#if searchQuery}
				<h3 class="text-lg font-medium">No matching profiles found</h3>
				<p class="mt-1 text-sm text-muted-foreground">Try a different search term</p>
			{:else}
				<h3 class="text-lg font-medium">No attendees found</h3>
				<p class="mt-1 text-sm text-muted-foreground">
					There are no registered attendees for this event
				</p>
			{/if}
		</div>
	{:else}
		<!-- Registered Attendees Section -->
		{#if filteredEventMembers.length > 0}
			<div class="space-y-2">
				<div class="flex items-center">
					<h3 class="text-md font-medium">Registered Attendees</h3>
					<span class="ml-2 text-sm text-muted-foreground">({filteredEventMembers.length})</span>
				</div>

				<div class="space-y-2">
					{#each filteredEventMembers as member (member.id)}
						<div animate:flip={{ duration: 200 }}>
							<AttendeeRow
								type="registered"
								{member}
								{handleFormSubmit}
								isProcessing={processingMemberIds.has(member.id)}
								disabled={loading}
							/>
						</div>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Non-Registered Profiles Section -->
		{#if getNonRegisteredProfiles().length > 0}
			<div class="mt-6 space-y-2">
				<div class="flex items-center">
					<h3 class="text-md font-medium">Walk-in Profiles</h3>
					<span class="ml-2 text-sm text-muted-foreground"
						>({getNonRegisteredProfiles().length})</span
					>
				</div>

				<div class="space-y-2">
					{#each getNonRegisteredProfiles() as profile (profile.id)}
						<div animate:flip={{ duration: 200 }}>
							<AttendeeRow
								type="walkin"
								{profile}
								onRegister={registerProfile}
								isProcessing={processingProfileId === profile.id}
								disabled={loading}
							/>
						</div>
					{/each}
				</div>
			</div>
		{/if}
	{/if}

	<p class="text-sm text-muted-foreground">
		{#if filteredEventMembers.length > 0 && getNonRegisteredProfiles().length > 0}
			{filteredEventMembers.length} registered, {getNonRegisteredProfiles().length} walk-in available
		{:else if filteredEventMembers.length > 0}
			{filteredEventMembers.length} attendees
		{:else if getNonRegisteredProfiles().length > 0}
			{getNonRegisteredProfiles().length} walk-in profiles found
		{:else}
			No profiles found
		{/if}
	</p>

	<!-- Registration Modal -->
	{#if selectedWalkinProfile && eventDetails}
		<EventRegistrationModal
			bind:selectedEvent={eventDetails}
			{selectedProductId}
			{optionsData}
			walkinProfileId={selectedWalkinProfile.id}
			userId={''}
			brand={{} as any}
			onOpenChange={(isOpen: boolean) => {
				if (!isOpen) {
					// Clear all state when modal is closed
					selectedWalkinProfile = null;
					eventDetails = null;
					optionsData = null;
					selectedProductId = '';
					processingProfileId = null;

					// Force immediate refresh instead of waiting for timeout
					setTimeout(() => {
						// Refresh the list of members after modal is closed
						loadEventMembers();
						invalidateAll();
					}, 100);
				}
			}}
		/>
	{/if}

	<!-- Debug Email List -->
	{#if import.meta.env.DEV}
		<div class="mt-8 rounded-md border border-dashed border-muted bg-muted/10 p-4">
			<div class="mb-2 flex items-center justify-between">
				<h3 class="text-sm font-medium">Attendee Emails (Development Only)</h3>
				<Button
					variant="outline"
					size="sm"
					onclick={() => {
						navigator.clipboard.writeText(attendeeEmails);
						toast.success('Emails copied to clipboard');
					}}
				>
					Copy All
				</Button>
			</div>
			<div class="max-h-24 overflow-y-auto break-all rounded bg-muted/20 p-2 text-xs">
				{attendeeEmails || 'No emails available'}
			</div>
		</div>
	{/if}
</div>
