<!-- src/routes/private/check-in/+page.svelte -->
<script lang="ts">
	import { PageContainer } from '$lib/components/layout';
	import { format, isToday } from 'date-fns';
	import type { PageData } from './$types';
	import EventSelector from './EventSelector.svelte';
	import AttendeeList from './AttendeeList.svelte';
	import type { EventWithMetadata } from './event-utils';
	import { Button } from '$lib/components/ui/button';
	import * as Popover from '$lib/components/ui/popover';
	import { RefreshCw, X, CalendarIcon } from '@lucide/svelte';
	import { cn } from '$lib/utils';
	import { goto } from '$app/navigation';
	import type { CheckInPageData } from './types';
	import type { DateRange } from 'bits-ui';
	import { RangeCalendar } from '$lib/components/ui/range-calendar';
	import {
		CalendarDate,
		DateFormatter,
		getLocalTimeZone,
		type DateValue
	} from '@internationalized/date';

	interface Props {
		data: PageData & CheckInPageData;
	}

	let { data }: Props = $props();

	// State
	let loading = $state(false);
	let selectedEventId = $state('');
	let currentLocale = $state('en');
	let events = $state<EventWithMetadata[]>(data.events as EventWithMetadata[]);
	let popoverOpen = $state(false);

	// Date formatter for compact display (no year)
	const df = new DateFormatter('en-US', {
		month: 'short',
		day: 'numeric'
	});

	// Convert ISO string to CalendarDate
	function isoToCalendarDate(isoString: string | undefined): CalendarDate {
		if (!isoString) {
			const today = new Date();
			return new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate());
		}
		const date = new Date(isoString);
		return new CalendarDate(date.getFullYear(), date.getMonth() + 1, date.getDate());
	}

	// Convert DateValue to ISO string for API
	function dateValueToISO(dateValue: DateValue): string {
		// Create a JavaScript Date object in the local timezone
		const jsDate = dateValue.toDate(getLocalTimeZone());

		// For date ranges, we want to preserve the full day regardless of timezone
		// Set the time to beginning of day (for start date) or end of day (for end date)
		// when this function is used in the UI context
		return jsDate.toISOString();
	}

	// Initialize date range from server data or use today as default start
	const today = new Date();
	const defaultStartDate = new CalendarDate(
		today.getFullYear(),
		today.getMonth() + 1,
		today.getDate()
	);
	const defaultEndDate = new CalendarDate(
		today.getFullYear(),
		today.getMonth() + 1,
		today.getDate()
	);
	defaultEndDate.add({ days: 7 });

	const initialStart = data.startDate ? isoToCalendarDate(data.startDate) : defaultStartDate;
	const initialEnd = data.endDate ? isoToCalendarDate(data.endDate) : defaultEndDate;

	// Date range state
	let dateRange: DateRange = $state({
		start: initialStart,
		end: initialEnd
	});

	let previousDateRange = $state<DateRange | null>(null);

	// Derived values
	let selectedEvent = $derived(
		events.find((event) => event.id === selectedEventId) as EventWithMetadata | undefined
	);

	// Functions
	function handleEventChange(eventId: string): void {
		selectedEventId = eventId;
	}

	// Helper function to compare DateValue objects
	function areDatesEqual(date1: DateValue | undefined, date2: DateValue | undefined): boolean {
		if (!date1 && !date2) return true;
		if (!date1 || !date2) return false;

		return date1.compare(date2) === 0;
	}

	// Apply date range and refresh events
	async function applyDateRange(newRange: DateRange): Promise<void> {
		if (!newRange?.start || !newRange?.end) return;

		// Store current state for comparison
		if (!previousDateRange) {
			previousDateRange = { ...newRange };
			return;
		}

		// Only reload if both dates are selected and different from previous
		const bothDatesSelected = newRange.start && newRange.end;
		const dateChanged =
			!areDatesEqual(previousDateRange.start, newRange.start) ||
			!areDatesEqual(previousDateRange.end, newRange.end);

		if (bothDatesSelected && dateChanged) {
			loading = true;

			// Get local timezone
			const localTimeZone = getLocalTimeZone();

			// Create start date in local timezone at beginning of day
			const startLocalDate = newRange.start.toDate(localTimeZone);
			// Create an ISO string that preserves the local date
			const startISO = startLocalDate.toISOString();

			// Create end date in local timezone at end of day
			const endLocalDate = newRange.end.toDate(localTimeZone);
			// Set to end of day (23:59:59.999)
			endLocalDate.setHours(23, 59, 59, 999);
			// Create an ISO string that preserves the local date
			const endISO = endLocalDate.toISOString();

			console.log(`Range selected in ${localTimeZone}:`);
			console.log('- Start:', startISO);
			console.log('- End:', endISO);
			console.log('- Start local date:', startLocalDate.toDateString());
			console.log('- End local date:', endLocalDate.toDateString());

			// Close popover
			popoverOpen = false;

			// Update previous range before navigation
			previousDateRange = { ...newRange };

			// Clear selected event ID to force reselection after data load
			selectedEventId = '';

			// Force a hard navigation to ensure server reloads data
			const startParam = encodeURIComponent(startISO);
			const endParam = encodeURIComponent(endISO);
			window.location.href = `?start=${startParam}&end=${endParam}`;
		}
	}

	// Reset to default date range
	async function resetDateRange(): Promise<void> {
		if (loading) return;

		loading = true;

		// Clear selected event ID to force reselection after data load
		selectedEventId = '';

		console.log('Resetting to default date range');

		// Navigate to the base URL without date parameters
		window.location.href = window.location.pathname;
	}

	// Watch for date range changes
	$effect(() => {
		if (dateRange) {
			applyDateRange(dateRange);
		}
	});

	// Initialize with first event if available
	$effect(() => {
		if (events.length > 0 && !selectedEventId) {
			// Prioritize today's events
			const todayEvent = events.find((event) => isToday(new Date(event.start_at)));
			if (todayEvent) {
				selectedEventId = todayEvent.id;
			} else {
				selectedEventId = events[0].id;
			}
		}
	});
</script>

{#snippet actions()}
	<div class="flex items-center gap-2">
		<!-- No actions needed in the header -->
	</div>
{/snippet}

{#snippet content()}
	<div class="space-y-6 md:grid md:grid-cols-12 md:gap-6 md:space-y-0">
		<!-- Event Selector (wider but still proportional) -->
		<div class="md:col-span-4 lg:col-span-4 xl:col-span-3">
			<div class="mb-4 space-y-2">
				<h3 class="mb-1 text-lg font-medium">Date Range</h3>
				<div class="relative">
					<Popover.Root bind:open={popoverOpen}>
						<Popover.Trigger class="w-full">
							{#snippet child({ props })}
								<Button
									variant="outline"
									class="relative w-full pl-10 text-left text-sm font-normal"
									{...props}
									disabled={loading}
								>
									<div class="absolute left-3 top-1/2 -translate-y-1/2">
										{#if loading}
											<RefreshCw class="h-4 w-4 animate-spin" />
										{:else}
											<CalendarIcon class="h-4 w-4" />
										{/if}
									</div>
									<span class="truncate">
										{#if dateRange && dateRange.start}
											{#if dateRange.end}
												{df.format(dateRange.start.toDate(getLocalTimeZone()))} - {df.format(
													dateRange.end.toDate(getLocalTimeZone())
												)}
											{:else}
												{df.format(dateRange.start.toDate(getLocalTimeZone()))}
											{/if}
										{:else}
											Select date range
										{/if}
									</span>
								</Button>
							{/snippet}
						</Popover.Trigger>
						<Popover.Content class="w-auto p-0" align="start">
							<RangeCalendar bind:value={dateRange} numberOfMonths={2} />
						</Popover.Content>
					</Popover.Root>

					{#if !loading}
						<Button
							variant="ghost"
							size="icon"
							class="absolute right-2 top-1/2 h-7 w-7 -translate-y-1/2"
							onclick={resetDateRange}
							title="Reset to default date range"
						>
							<X class="h-4 w-4" />
						</Button>
					{/if}
				</div>
			</div>

			<h3 class="mb-2 text-lg font-medium">Events</h3>
			<EventSelector
				{events}
				{selectedEventId}
				onEventChange={handleEventChange}
				{loading}
				showAttendeeCount={true}
			/>
		</div>

		<!-- Attendee List (adjusted to maintain 12-column grid) -->
		<div class="md:col-span-8 lg:col-span-8 xl:col-span-9">
			{#if selectedEvent}
				<AttendeeList event={selectedEvent as any} supabase={data.supabase} />
			{:else if events.length > 0}
				<div class="flex h-40 items-center justify-center">
					<p class="text-muted-foreground">Select an event to view attendees</p>
				</div>
			{:else}
				<div class="flex h-40 items-center justify-center">
					<p class="text-muted-foreground">No events available</p>
				</div>
			{/if}
		</div>
	</div>
{/snippet}

<PageContainer
	title="Check-In"
	description="Manage attendee check-ins for your events"
	{actions}
	{content}
/>
