<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar';
	import { UserCheck, XCircle, PlusCircle } from '@lucide/svelte';
	import { format } from 'date-fns';
	import { enhance } from '$app/forms';
	import type { SubmitFunction } from '@sveltejs/kit';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { Database } from '$lib/supabase/database.types';

	// Types
	type ProfileRow = Database['public']['Tables']['profile']['Row'];
	type EventMemberRow = Database['public']['Tables']['event_member']['Row'];

	// User profile interface for non-registered profiles
	interface UserProfile {
		id: string;
		given_name: LocalizedText;
		family_name: LocalizedText;
		username?: string;
		avatar_url?: string | null;
		auto_user_email?: string;
		auto_user_phone?: string;
	}

	interface EventMember extends EventMemberRow {
		memberProfile: ProfileRow & {
			func_family_name_en_first: string;
			func_given_name_en_first: string;
			auto_user_email: string;
			auto_user_phone: string;
			nickname: string;
			avatar: string | null;
			avatar_url: string | null;
		};
		event: {
			id: string;
		};
	}

	interface Props {
		// Common props
		type: 'registered' | 'walkin';

		// For registered attendees
		member?: EventMember;

		// For walk-in profiles
		profile?: UserProfile;

		// Actions
		onRegister?: (profileId: string) => Promise<void>;
		handleFormSubmit?: (memberId: string) => SubmitFunction;

		// State
		isProcessing?: boolean;

		// Additional flags
		disabled?: boolean;
	}

	let {
		type,
		member,
		profile,
		onRegister,
		handleFormSubmit,
		isProcessing = false,
		disabled = false
	}: Props = $props();

	// Validation
	$effect(() => {
		if (type === 'registered' && !member) {
			console.error('Member prop is required for registered attendee rows');
		}

		if (type === 'walkin' && !profile) {
			console.error('Profile prop is required for walk-in profile rows');
		}
	});

	// Helper functions
	function getFullName(profile: UserProfile): string {
		const firstName = getLocalizedText(profile.given_name, 'en');
		const lastName = getLocalizedText(profile.family_name, 'en');
		return `${firstName} ${lastName}`.trim();
	}

	function maskEmail(email: string): string {
		if (!email || !email.includes('@')) return email;

		const [localPart, domain] = email.split('@');

		// Show first half of characters and last character of local part
		const halfLength = Math.max(1, Math.floor(localPart.length / 3));
		const visiblePrefix = localPart.slice(0, halfLength);
		const visibleSuffix = localPart.slice(-1);
		const maskedLength = localPart.length - halfLength - 1;
		const maskedPart = maskedLength > 0 ? '•'.repeat(Math.min(maskedLength, 2)) : '';

		return `${visiblePrefix}${maskedPart}${visibleSuffix}@${domain}`;
	}
</script>

<div class="flex items-center justify-between gap-3 rounded-lg border p-2">
	<!-- User info with optimized layout -->
	<div class="flex min-w-0 flex-1 items-center gap-3">
		<Avatar class="h-9 w-9 shrink-0">
			{#if type === 'registered' && member}
				<AvatarImage
					src={member.memberProfile.avatar_url || ''}
					alt={member.memberProfile.func_given_name_en_first}
				/>
				<AvatarFallback>
					{member.memberProfile.func_given_name_en_first.charAt(0)}
					{member.memberProfile.func_family_name_en_first.charAt(0)}
				</AvatarFallback>
			{:else if type === 'walkin' && profile}
				<AvatarImage src={profile.avatar_url || ''} alt={getFullName(profile)} />
				<AvatarFallback>
					{getLocalizedText(profile.given_name, 'en').charAt(0)}
					{getLocalizedText(profile.family_name, 'en').charAt(0)}
				</AvatarFallback>
			{/if}
		</Avatar>
		<div class="min-w-0 flex-1 pr-1">
			<!-- Name display -->
			<p class="truncate font-medium">
				{#if type === 'registered' && member}
					{#if member.memberProfile.func_given_name_en_first || member.memberProfile.func_family_name_en_first}
						{member.memberProfile.func_given_name_en_first}
						{member.memberProfile.func_family_name_en_first}
					{:else if member.memberProfile.auto_user_email}
						{member.memberProfile.auto_user_email}
					{:else if member.memberProfile.auto_user_phone}
						{member.memberProfile.auto_user_phone}
					{:else}
						{member.memberProfile.nickname || 'Unknown Attendee'}
					{/if}
				{:else if type === 'walkin' && profile}
					{getFullName(profile) ||
						profile.auto_user_email ||
						profile.auto_user_phone ||
						'Unknown Profile'}
				{/if}
			</p>

			<!-- Secondary info display -->
			<div class="flex items-center">
				<p class="flex-1 truncate text-xs text-muted-foreground">
					{#if type === 'registered' && member}
						{#if member.memberProfile.auto_user_email && (member.memberProfile.func_given_name_en_first || member.memberProfile.func_family_name_en_first)}
							{maskEmail(member.memberProfile.auto_user_email)}
						{:else if !member.memberProfile.func_given_name_en_first && !member.memberProfile.func_family_name_en_first}
							<span class="text-amber-500 dark:text-amber-400"
								>Remind customer to add profile name</span
							>
						{:else if member.memberProfile.auto_user_phone && !member.memberProfile.auto_user_email}
							{member.memberProfile.auto_user_phone}
						{:else}
							{member.memberProfile.nickname || ''}
						{/if}

						{#if member.checked_in_at}
							<span class="ml-1 whitespace-nowrap text-xs text-muted-foreground">
								<UserCheck class="mr-0.5 inline h-3 w-3" />
								{format(new Date(member.checked_in_at), 'h:mm')}
							</span>
						{/if}
					{:else if type === 'walkin' && profile}
						{#if profile.auto_user_email}
							{maskEmail(profile.auto_user_email)}
						{:else if profile.auto_user_phone}
							{profile.auto_user_phone}
						{:else if profile.username}
							@{profile.username}
						{:else}
							<span class="text-amber-500 dark:text-amber-400">Limited profile information</span>
						{/if}
					{/if}
				</p>
			</div>
		</div>
	</div>

	<!-- Action button -->
	{#if type === 'registered' && member && handleFormSubmit}
		<form method="POST" action="?/toggleCheckIn" use:enhance={handleFormSubmit(member.id)}>
			<input type="hidden" name="eventMemberId" value={member.id} />
			<input type="hidden" name="action" value={member.checked_in_at ? 'check-out' : 'check-in'} />
			<Button
				type="submit"
				variant={member.checked_in_at ? 'outline' : 'default'}
				size="sm"
				class="w-[88px] shrink-0"
				{disabled}
			>
				{#if isProcessing}
					<span
						class="inline-block h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"
					></span>
					<span class="ml-1">...</span>
				{:else if member.checked_in_at}
					<XCircle class="mr-1 h-4 w-4" />
					<span>Undo</span>
				{:else}
					<span>Check In</span>
				{/if}
			</Button>
		</form>
	{:else if type === 'walkin' && profile && onRegister}
		<Button
			variant="default"
			size="sm"
			class="w-[100px] shrink-0 bg-green-600 hover:bg-green-700"
			onclick={() => onRegister(profile.id)}
			{disabled}
		>
			{#if isProcessing}
				<span
					class="inline-block h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"
				></span>
			{:else}
				<PlusCircle class="mr-1 h-4 w-4" />
				<span>Register</span>
			{/if}
		</Button>
	{/if}
</div>
