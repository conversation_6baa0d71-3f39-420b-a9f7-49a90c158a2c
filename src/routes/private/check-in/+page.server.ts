import { redirect, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { fetchEvents } from './event-utils';

export const load: PageServerLoad = async ({ locals: { supabase, user, brand }, url }) => {
	// Check if user is authenticated
	if (!user) {
		throw redirect(303, '/auth/signin');
	}

	console.log('Current brand:', brand?.id ? brand.id : 'No brand ID available');

	// Get date range from URL parameters if available, otherwise use defaults
	const today = new Date();

	// Get start date from URL or default to today
	let startDate: Date;
	const startParam = url.searchParams.get('start');
	if (startParam) {
		console.log('Received start date parameter:', startParam);

		// Parse the ISO string directly, preserving timezone information
		startDate = new Date(startParam);

		// Log time information for debugging
		console.log('Start date (parsed):', startDate.toISOString());
		console.log('Start date (local):', startDate.toString());

		// Validate the date - if invalid, fall back to default
		if (isNaN(startDate.getTime())) {
			console.log('Invalid start date, using default');
			// Default to today (start of day in local timezone)
			const now = new Date();
			now.setHours(0, 0, 0, 0);
			startDate = now;
		}
	} else {
		// Default: Today (start of day in local timezone)
		const now = new Date();
		now.setHours(0, 0, 0, 0);
		startDate = now;
	}

	// Get end date from URL or default to one week later
	let endDate: Date;
	const endParam = url.searchParams.get('end');
	if (endParam) {
		console.log('Received end date parameter:', endParam);

		// Parse the ISO string directly, preserving timezone information
		endDate = new Date(endParam);

		// Log time information for debugging
		console.log('End date (parsed):', endDate.toISOString());
		console.log('End date (local):', endDate.toString());

		// Validate the date - if invalid, fall back to default
		if (isNaN(endDate.getTime())) {
			console.log('Invalid end date, using default');
			// Default to one week from today (end of day in local timezone)
			const defaultEndDate = new Date();
			defaultEndDate.setDate(defaultEndDate.getDate() + 7);
			defaultEndDate.setHours(23, 59, 59, 999);
			endDate = defaultEndDate;
		}
	} else {
		// Default: One week from today (end of day in local timezone)
		const defaultEndDate = new Date();
		defaultEndDate.setDate(defaultEndDate.getDate() + 7);
		defaultEndDate.setHours(23, 59, 59, 999);
		endDate = defaultEndDate;
	}

	console.log(
		'Using date range for event filtering:',
		startDate.toISOString(),
		'to',
		endDate.toISOString()
	);

	// First check if there are any events at all without brand filter
	const { data: allEvents, error: allEventsError } = await supabase
		.from('event')
		.select('id, brand_id, start_at')
		.gte('start_at', startDate.toISOString())
		.lte('start_at', endDate.toISOString())
		.order('start_at', { ascending: true });

	console.log('Total events in date range (without brand filter):', allEvents?.length || 0);
	if (allEventsError) {
		console.error('Error fetching all events:', allEventsError);
	}

	// Fetch events with brand filter using shared utility
	const events = await fetchEvents({
		supabase,
		startDate,
		endDate,
		brandId: brand.id
	});

	console.log('Filtered events for brand:', events.length);

	// If no events found with brand filter, log brand IDs from all events
	if (allEvents?.length && events.length === 0) {
		const brandIds = [...new Set(allEvents.map((event) => event.brand_id))];
		console.log('Available brand IDs in events:', brandIds);
		console.log('Current brand ID we are filtering for:', brand?.id);
	}

	return {
		events,
		startDate: startDate.toISOString(),
		endDate: endDate.toISOString()
	};
};

export const actions: Actions = {
	toggleCheckIn: async ({ request, locals: { supabase, user } }) => {
		// Check if user is authenticated
		if (!user) {
			return fail(401, { message: 'Unauthorized' });
		}

		const formData = await request.formData();
		const eventMemberId = formData.get('eventMemberId') as string;
		const action = formData.get('action') as 'check-in' | 'check-out';

		if (!eventMemberId) {
			return fail(400, { message: 'Event member ID is required' });
		}

		try {
			// Set checkedInAt based on the action
			const now = new Date().toISOString();
			const checkedInAt = action === 'check-in' ? now : null;

			const { data, error } = await supabase
				.from('event_member')
				.update({ checked_in_at: checkedInAt })
				.eq('id', eventMemberId)
				.select('id, checked_in_at');

			if (error) {
				console.error('Error updating check-in status:', error);
				return fail(500, { message: 'Failed to update check-in status', error: error.message });
			}

			return {
				success: true,
				message: action === 'check-in' ? 'Checked in successfully' : 'Check-out successful',
				data
			};
		} catch (error) {
			console.error('Error in toggleCheckIn action:', error);
			return fail(500, {
				message: 'An unexpected error occurred',
				error: error instanceof Error ? error.message : String(error)
			});
		}
	}
};
