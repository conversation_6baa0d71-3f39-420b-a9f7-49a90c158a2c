<script lang="ts">
	import * as Command from '$lib/components/ui/command';
	import * as Popover from '$lib/components/ui/popover';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { Separator } from '$lib/components/ui/separator';
	import { Input } from '$lib/components/ui/input';
	import {
		Check,
		ChevronsUpDown,
		AlertCircle,
		CalendarClock,
		MapPin,
		Search,
		Users,
		UserCircle2
	} from '@lucide/svelte';
	import {
		format,
		isToday,
		isYesterday,
		isTomorrow,
		isSameDay,
		isAfter,
		isBefore,
		isWithinInterval,
		addMinutes
	} from 'date-fns';
	import { tick } from 'svelte';
	import { cn } from '$lib/utils';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { EventWithMetadata } from './event-utils';

	interface EventGroup {
		label: string;
		events: EventWithMetadata[];
	}

	interface Props {
		events: EventWithMetadata[];
		selectedEventId: string;
		onEventChange: (eventId: string) => void;
		loading?: boolean;
		showAttendeeCount?: boolean;
	}

	let {
		events,
		selectedEventId,
		onEventChange,
		loading = false,
		showAttendeeCount = false
	}: Props = $props();

	// State
	let open = $state(false);
	let triggerRef = $state<HTMLButtonElement>(null!);
	let eventSearchQuery = $state('');

	// Filter events by date range
	function filterEventsByDateRange(events: EventWithMetadata[]): EventWithMetadata[] {
		// Date filtering now handled on the server side
		return events;
	}

	// Group events by day
	function getEventGroups(events: EventWithMetadata[]): EventGroup[] {
		const filteredEvents = filterEventsByDateRange(events);

		// Sort all events chronologically by start_at
		const sortedEvents = [...filteredEvents].sort(
			(a, b) => new Date(a.start_at).getTime() - new Date(b.start_at).getTime()
		);

		// Group events by day
		const eventsByDay = new Map<string, EventWithMetadata[]>();

		sortedEvents.forEach((event) => {
			// Important: Use the event's timestamp to get the local date
			// This ensures timezone is properly respected
			const eventDate = new Date(event.start_at);

			// Format date as YYYY-MM-DD for grouping (respecting local timezone)
			const dateKey = format(eventDate, 'yyyy-MM-dd');

			if (!eventsByDay.has(dateKey)) {
				eventsByDay.set(dateKey, []);
			}

			eventsByDay.get(dateKey)?.push(event);
		});

		// Create groups with appropriate labels
		const groups: EventGroup[] = [];
		const today = new Date();

		// Convert map to array and keep chronological order
		const sortedDays = Array.from(eventsByDay.entries()).sort(
			(a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime()
		);

		sortedDays.forEach(([dateKey, dayEvents]) => {
			// Use the first event's actual date for the label to ensure consistency
			const firstEventDate = new Date(dayEvents[0].start_at);
			let label: string;

			if (isToday(firstEventDate)) {
				label = 'Today';
			} else if (isYesterday(firstEventDate)) {
				label = 'Yesterday';
			} else if (isTomorrow(firstEventDate)) {
				label = 'Tomorrow';
			} else {
				// Format the date based on the event's timestamp, not the date key
				label = format(firstEventDate, 'EEEE, MMMM d');
			}

			groups.push({ label, events: dayEvents });
		});

		return groups;
	}

	// Filter events by search query
	function filterEventsBySearch(events: EventWithMetadata[], query: string): EventWithMetadata[] {
		if (!query) return events;

		const lowerQuery = query.toLowerCase();
		return events.filter((event) => {
			const title = getLocalizedText(
				event.metadata?.auto_final_title as LocalizedText
			).toLowerCase();
			const subtitle = getLocalizedText(
				event.metadata?.auto_final_subtitle as LocalizedText
			).toLowerCase();
			const location = getLocationName(event).toLowerCase();
			const instructor = getInstructorName(event).toLowerCase();

			return (
				title.includes(lowerQuery) ||
				subtitle.includes(lowerQuery) ||
				location.includes(lowerQuery) ||
				instructor.includes(lowerQuery) ||
				format(new Date(event.start_at), 'EEEE, MMMM d, h:mm a').toLowerCase().includes(lowerQuery)
			);
		});
	}

	// Get event title for display
	function getEventDisplayTitle(event: EventWithMetadata): string {
		const title = String(getLocalizedText(event.metadata?.auto_final_title as LocalizedText));
		const subtitle = String(getLocalizedText(event.metadata?.auto_final_subtitle as LocalizedText));

		return String(title || event.title || 'Untitled Event');
	}

	// Get location name for display
	function getLocationName(event: EventWithMetadata): string {
		const locationParts: string[] = [];

		// Get space name if available
		if (event.space) {
			const spaceName = getLocalizedText(event.space.name_short as LocalizedText);
			if (spaceName) locationParts.push(spaceName);

			// Get landmark name from space if available
			if (event.space.landmark) {
				const landmarkName = getLocalizedText(event.space.landmark.title_short as LocalizedText);
				if (landmarkName && landmarkName !== spaceName) locationParts.push(landmarkName);
			}
		}
		// If no space, try to get landmark directly
		else if (event.landmark) {
			const landmarkName = getLocalizedText(event.landmark.title_short as LocalizedText);
			if (landmarkName) locationParts.push(landmarkName);
		}

		return locationParts.length > 0 ? locationParts.join(', ') : 'Online';
	}

	// Get instructor name for display
	function getInstructorName(event: EventWithMetadata): string {
		if (!event.metadata?.metadata_wikipage) return '';

		const instructors = event.metadata.metadata_wikipage
			.filter((relation) => relation.relation === 'instructor')
			.map((relation) => {
				return getLocalizedText(relation.wikipage.title as LocalizedText);
			})
			.filter(Boolean);

		return instructors.length > 0 ? instructors.join(', ') : '';
	}

	function closeAndFocusTrigger(): void {
		open = false;
		tick().then(() => {
			triggerRef?.focus();
		});
	}

	// Derived values
	let selectedEvent = $derived(
		events.find((event) => event.id === selectedEventId) as EventWithMetadata | undefined
	);

	let selectedEventTitle = $derived(
		selectedEvent ? getEventDisplayTitle(selectedEvent) : 'Select an event'
	);

	let searchFilteredEvents = $derived(filterEventsBySearch(events, eventSearchQuery));

	let searchFilteredGroups = $derived(getEventGroups(searchFilteredEvents));

	// Check if an event is currently ongoing
	function isEventOngoing(event: EventWithMetadata): boolean {
		const now = new Date();
		const eventStart = new Date(event.start_at);
		const eventEnd = addMinutes(eventStart, event.duration_minute || 60); // Default to 1 hour if duration not specified

		return isWithinInterval(now, { start: eventStart, end: eventEnd });
	}

	// Check if an event is upcoming today (today but hasn't started yet)
	function isEventUpcomingToday(event: EventWithMetadata): boolean {
		const now = new Date();
		const eventStart = new Date(event.start_at);

		return isToday(eventStart) && isAfter(eventStart, now);
	}
</script>

<div class="w-full">
	{#if loading && events.length === 0}
		<div class="h-10 w-full animate-pulse rounded-md bg-muted"></div>
	{:else if events.length === 0}
		<div class="flex flex-col items-center justify-center py-4 text-center">
			<AlertCircle class="mb-2 h-8 w-8 text-muted-foreground" />
			<h3 class="text-base font-medium">No events found</h3>
			<p class="mt-1 text-sm text-muted-foreground">
				There are no events scheduled for this brand in the selected date range
			</p>
		</div>
	{:else}
		<!-- Mobile: Dropdown -->
		<div class="md:hidden">
			<Popover.Root bind:open>
				<Popover.Trigger bind:ref={triggerRef}>
					{#snippet child({ props })}
						<Button
							variant="outline"
							class="w-full justify-between"
							{...props}
							role="combobox"
							aria-expanded={open}
						>
							{selectedEventTitle}
							<ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
						</Button>
					{/snippet}
				</Popover.Trigger>
				<Popover.Content class="w-full p-0">
					<Command.Root>
						<Command.Input placeholder="Search events..." bind:value={eventSearchQuery} />
						<Command.List class="max-h-[300px]">
							<Command.Empty>No events found.</Command.Empty>

							{#each searchFilteredGroups as group}
								<Command.Group heading={group.label}>
									{#each group.events as event}
										<Command.Item
											value={event.id}
											onSelect={() => {
												onEventChange(event.id);
												closeAndFocusTrigger();
											}}
										>
											<div class="flex w-full items-start justify-between">
												<div class="flex w-full flex-col gap-1">
													<div class="flex items-center gap-2">
														{#if isEventOngoing(event)}
															<div
																class="animate-pulse-slow h-2.5 w-2.5 rounded-full bg-green-500"
															></div>
														{:else if isEventUpcomingToday(event)}
															<div class="h-2.5 w-2.5 rounded-full bg-blue-500"></div>
														{/if}
														<span class="text-sm">
															{getEventDisplayTitle(event)}
														</span>
													</div>
													{#if event.metadata?.auto_final_subtitle}
														<span class="text-xs text-muted-foreground">
															{getLocalizedText(
																event.metadata?.auto_final_subtitle as LocalizedText
															)}
														</span>
													{/if}
													<div class="flex flex-col gap-1 text-xs text-muted-foreground">
														<div class="flex items-center gap-1">
															<CalendarClock class="h-3.5 w-3.5" />
															{format(new Date(event.start_at), 'h:mm a')}
														</div>

														<div class="flex items-center gap-1">
															<MapPin class="h-3.5 w-3.5" />
															<span class="truncate">{getLocationName(event)}</span>
														</div>
														{#if getInstructorName(event)}
															<div class="flex items-center gap-1">
																<UserCircle2 class="h-3.5 w-3.5" />
																<span class="truncate">{getInstructorName(event)}</span>
															</div>
														{/if}
														{#if showAttendeeCount && event.attendee_count !== undefined}
															<div class="flex items-center gap-1">
																<Users class="h-3.5 w-3.5" />
																{event.attendee_count[0]?.count || 0}
															</div>
														{/if}
													</div>
												</div>
												<Check
													class={cn(
														'ml-auto h-4 w-4',
														selectedEventId === event.id ? 'opacity-100' : 'opacity-0'
													)}
												/>
											</div>
										</Command.Item>
									{/each}
								</Command.Group>
								<Separator />
							{/each}
						</Command.List>
					</Command.Root>
				</Popover.Content>
			</Popover.Root>
		</div>

		<!-- Desktop: List View -->
		<div class="hidden md:block">
			<!-- Search input -->
			<div class="relative mb-3 w-full">
				<Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
				<Input
					type="search"
					placeholder="Search events..."
					class="w-full pl-8"
					bind:value={eventSearchQuery}
				/>
			</div>

			<!-- Event list -->
			<div class="max-h-[calc(100vh-200px)] overflow-y-auto rounded-md border">
				{#each searchFilteredGroups as group}
					<div
						class="sticky top-0 z-10 border-b bg-muted/80 px-3 py-2 text-sm font-medium backdrop-blur-sm"
					>
						{group.label}
					</div>
					{#each group.events as event}
						<button
							class={cn(
								'flex w-full flex-col gap-2 border-b px-4 py-3 text-left transition-colors hover:bg-muted/50',
								selectedEventId === event.id ? 'bg-muted' : ''
							)}
							onclick={() => onEventChange(event.id)}
						>
							<div class="flex items-center justify-between gap-2">
								<div class="flex items-center gap-2">
									{#if isEventOngoing(event)}
										<div class="animate-pulse-slow h-2.5 w-2.5 rounded-full bg-green-500"></div>
									{:else if isEventUpcomingToday(event)}
										<div class="h-2.5 w-2.5 rounded-full bg-blue-500"></div>
									{/if}
									<span class="line-clamp-1 text-sm">
										{getEventDisplayTitle(event)}
									</span>
								</div>
								{#if selectedEventId === event.id}
									<Check class="h-4 w-4 shrink-0 text-primary" />
								{/if}
							</div>

							{#if event.metadata?.auto_final_subtitle}
								<span class="line-clamp-1 text-xs text-muted-foreground">
									{getLocalizedText(event.metadata?.auto_final_subtitle as LocalizedText)}
								</span>
							{/if}

							<div class="flex flex-col gap-1 text-xs text-muted-foreground">
								<div class="flex items-center gap-1.5">
									<CalendarClock class="h-3.5 w-3.5" />
									{format(new Date(event.start_at), 'h:mm a')}
								</div>
								<div class="flex items-center gap-1.5">
									<MapPin class="h-3.5 w-3.5" />
									<span class="truncate">{getLocationName(event)}</span>
								</div>
								{#if getInstructorName(event)}
									<div class="flex items-center gap-1.5">
										<UserCircle2 class="h-3.5 w-3.5" />
										<span class="truncate">{getInstructorName(event)}</span>
									</div>
								{/if}
								{#if showAttendeeCount && event.attendee_count !== undefined}
									<div class="flex items-center gap-1.5">
										<Users class="h-3.5 w-3.5" />
										{event.attendee_count[0]?.count || 0}
									</div>
								{/if}
							</div>
						</button>
					{/each}
					{#if searchFilteredGroups.indexOf(group) < searchFilteredGroups.length - 1}
						<Separator />
					{/if}
				{/each}
			</div>
		</div>
	{/if}
</div>

<style>
	@keyframes pulse {
		0% {
			opacity: 0.5;
			transform: scale(0.95);
		}
		50% {
			opacity: 1;
			transform: scale(1.05);
		}
		100% {
			opacity: 0.5;
			transform: scale(0.95);
		}
	}

	.animate-pulse-slow {
		animation: pulse 2s infinite ease-in-out;
	}
</style>
