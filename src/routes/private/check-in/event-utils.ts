import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '$lib/supabase/database.types';
import type { LocalizedText } from '$lib/utils/localization';

type SupabaseClientType = SupabaseClient<Database>;

/**
 * Fetch events for a specific date range with complete metadata
 */
export async function fetchEvents({
	supabase,
	startDate,
	endDate,
	brandId
}: {
	supabase: SupabaseClientType;
	startDate: Date;
	endDate: Date;
	brandId: string;
}) {
	const query = supabase
		.from('event')
		.select(
			`
			id,
			brand_id,
			title,
			start_at,
			duration_minute,
			auto_start_at_local_dow,
			auto_start_at_local_time,
			attendee_count:event_member!event_id(count),
			event_product (
				id,
				product:product_id (
					id,
					title,
					metadata:metadata_id (
						id,
						title,
						auto_final_title,
						auto_final_subtitle
					),
					product_price (
						id,
						cost_units,
						price:price_id (
							id,
							title,
							unit_kind,
							price_unit_kind:unit_kind (
								id,
								quantity_forms,
								quantity_forms_short
							),
							price_option (
								id,
								title,
								units,
								money_int
							)
						)
					)
				)
			),
			space:space_id (
				id,
				name_short,
                landmark:landmark_id (
                    id,
                    title_short,
                    address:address_id (
                        city,
                        auto_normalized_address_local,
                        timeZone:time_zone (
                            name
                        )
                    )
                )
			),
			landmark:landmark_id (
			id,
			title_short,
			address:address_id (
				city,
				auto_normalized_address_local,
				timeZone:time_zone (
					name
				)
			)
			),
			metadata:metadata_id (
				id,
				title,
				auto_final_title,
				auto_final_subtitle,
				metadata_wikipage (
					wikipage:wikipage_id (
						id,
						title
					),
					relation
				),
				metadata_track (
					track:track_id (
						id,
						genre,
						title
					)
				)
			)
		`
		)
		.gte('start_at', startDate.toISOString())
		.lte('start_at', endDate.toISOString())
		.eq('brand_id', brandId)
		.order('start_at', { ascending: true });

	const { data, error } = await query;

	if (error) {
		console.error('[fetchEvents] Error fetching events:', error);
		return [];
	}

	console.log(`[fetchEvents] Query returned ${data?.length || 0} events`);
	if (data?.length === 0) {
		console.log('[fetchEvents] No events found for the specified date range and brand');
	} else if (data?.length) {
		console.log('[fetchEvents] First event sample:', data[0]);
	}

	return data || [];
}

/**
 * Transform event data to ensure it's compatible with EventWithMetadata
 */
export function transformEventData(events: any[]): EventWithMetadata[] {
	return events.map((event) => {
		// If the event already has the required structure, return as is
		if (event.metadata?.auto_final_title) {
			return event as EventWithMetadata;
		}

		// Otherwise, transform the data to match EventWithMetadata structure
		return {
			...event,
			metadata: {
				id: event.metadata_id || '',
				title: {},
				auto_final_title: {},
				auto_final_subtitle: {}
			}
		} as EventWithMetadata;
	});
}

// Base event row from the database
type EventRow = Database['public']['Tables']['event']['Row'];

// Extended event type with all metadata for use in components
export interface EventWithMetadata extends EventRow {
	metadata: {
		id: string;
		title: LocalizedText;
		auto_final_title: LocalizedText;
		auto_final_subtitle: LocalizedText;
		metadata_wikipage?: {
			wikipage: {
				id: string;
				title: LocalizedText;
				auto_final_title: LocalizedText;
			};
			relation: string;
		}[];
		metadata_track?: {
			track: {
				id: string;
				genre: string;
				auto_final_title: LocalizedText;
				title: LocalizedText;
			};
		}[];
	};
	event_product?: {
		id: string;
		product: {
			id: string;
			title: LocalizedText;
			metadata?: {
				id: string;
				title: LocalizedText;
				auto_final_title: LocalizedText;
				auto_final_subtitle: LocalizedText;
			};
			product_price?: {
				id: string;
				cost_units: number;
				price: {
					id: string;
					title: LocalizedText;
					unit_kind: string;
					price_unit_kind?: {
						id: string;
						quantity_forms: LocalizedText;
						quantity_forms_short: LocalizedText;
					};
					price_option?: {
						id: string;
						title: LocalizedText;
						units: number;
						money_int: number;
					}[];
				};
			}[];
		};
	}[];
	space?: {
		id: string;
		name_short: LocalizedText;
		landmark?: {
			id: string;
			title_short: LocalizedText;
			address?: {
				city: string;
				auto_normalized_address_local: string | null;
				timeZone?: {
					name: string;
				};
			};
		};
	};
	landmark?: {
		id: string;
		title_short: LocalizedText;
		address?: {
			city: string;
			auto_normalized_address_local: string | null;
			timeZone?: {
				name: string;
			};
		};
	};
	attendee_count?: { count: number }[];
}
