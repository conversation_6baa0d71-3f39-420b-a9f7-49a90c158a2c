import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals: { supabase } }) => {
	const { orderProductId } = params;

	if (!orderProductId) {
		throw redirect(303, '/private/my-order');
	}

	try {
		// Fetch the order product data
		const { data, error: orderProductError } = await supabase
			.from('order_product')
			.select(
				`
				id,
				consumer_profile_id,
				profile:consumer_profile_id (
					given_name,
					family_name
				),
				orderPriceConsumptions:order_price_consumption (
					id,
					orderPrice:order_price_id (
						id,
						price_option_purchase_count,
						auto_price_option_expire_at,
						priceOption:price_option_id (
							id,
							title
						),
						autoPriceOptionPrice:auto_price_option_price_id (
							id,
							color_primary_hex,
							payment_kind,
							brand (
								id,
								name_full
							)
						)
					),
					cost_units,
					returned_units,
					created_at
				),
				auto_units_owed,
				canceled_at,
				created_at,
				product_purchased_count,
				productPrice:product_price_id (
					id,
					priceData:price_id (
						id,
						color_primary_semantic,
						color_primary_hex,
						title,
						product_classification
					),
					auto_cancel_at_far,
					auto_cancel_at_near,
					auto_cancel_at_far_return_units,
					auto_cancel_at_near_return_units,
					product:product_id (
						id,
						title,
						metadata (
							id,
							auto_final_title,
							auto_final_subtitle,
							custom_attribute
						),
						autoFirstEvent:auto_first_event_id (
							id,
							start_at,
							auto_end_at,
							duration_minute,
							space (
								id,
								name_full,
								name_short,
								landmark (
									id,
									title_full,
									title_short
								)
							)
						),
						auto_last_event_end_at
					)
				),
				order (
					nid,
					id
				)
				`
			)
			.eq('id', orderProductId)
			.single();

		if (orderProductError) {
			console.error('Error fetching order product:', orderProductError);
			throw error(404, 'Order product not found');
		}

		// Just return the raw data
		return {
			orderProduct: data
		};
	} catch (err) {
		console.error('Error in order product load function:', err);
		throw error(404, 'Error fetching order product');
	}
};
