<script lang="ts">
	import { format, parseISO, isValid } from 'date-fns';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { PageData } from './$types';
	import type { LocalizedText } from '$lib/utils/localization';
	import { Badge } from '$lib/components/ui/badge';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { Separator } from '$lib/components/ui/separator';
	import { CreditCard, Clock, MapPin, Calendar, ArrowLeft } from '@lucide/svelte';
	import OrderProductConsumptionCard from '../../OrderProductConsumptionCard.svelte';
	import { PageContainer } from '$lib/components/layout';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	const locale = getLocale();

	// Helper function to safely extract text from localized objects
	function safeGetLocalizedText(value: any, locale: string): string {
		if (!value) return '';
		if (typeof value === 'string') return value;

		try {
			// For proper LocalizedText objects
			if (typeof value === 'object' && value !== null) {
				// If it's a raw object with locale keys
				if (value[locale]) return String(value[locale]);

				// Try to get the first non-empty value
				for (const key in value) {
					if (value[key] && typeof value[key] === 'string' && value[key].trim() !== '') {
						return value[key];
					}
				}

				// If we have any value, use the first one
				const firstValue = Object.values(value)[0];
				if (firstValue) return String(firstValue);
			}

			return typeof value === 'object'
				? getLocalizedText(value as unknown as LocalizedText, locale)
				: String(value);
		} catch (err) {
			console.error('Failed to extract localized text:', err);
			return typeof value === 'object' ? JSON.stringify(value) : String(value);
		}
	}

	function formatDateTime(dateString: string | null): string {
		if (!dateString) return '--';
		try {
			const date = parseISO(dateString);
			if (!isValid(date)) return '--';
			return format(date, 'MMM d, yyyy h:mm a');
		} catch (error) {
			console.error('Error formatting date:', dateString, error);
			return '--';
		}
	}

	function formatDate(dateString: string | null): string {
		if (!dateString) return '--';
		try {
			const date = parseISO(dateString);
			if (!isValid(date)) return '--';
			return format(date, 'EEE, MMM d, yyyy');
		} catch (error) {
			console.error('Error formatting date:', dateString, error);
			return '--';
		}
	}

	function formatTime(dateString: string | null): string {
		if (!dateString) return '--';
		try {
			const date = parseISO(dateString);
			if (!isValid(date)) return '--';
			return format(date, 'h:mm a');
		} catch (error) {
			console.error('Error formatting time:', dateString, error);
			return '--';
		}
	}

	// Format duration in minutes to "X min" format
	function formatDuration(minutes: number | null | undefined): string {
		if (!minutes) return '';
		return `${minutes} min`;
	}

	const rawOrderProduct = data.orderProduct;

	// Transform the raw data into the expected structure
	const orderProduct = rawOrderProduct
		? {
				...rawOrderProduct,
				consumer_profile: rawOrderProduct.profile,
				productPrice: rawOrderProduct.productPrice
					? {
							...rawOrderProduct.productPrice,
							price: rawOrderProduct.productPrice.priceData
						}
					: null
			}
		: null;

	// We need to safely access nested properties
	const product = orderProduct?.productPrice?.product;
	const metadata = product?.metadata;
	const firstEvent = product?.autoFirstEvent;
	const space = firstEvent?.space;
	const landmark = space?.landmark;
	const priceInfo = orderProduct?.productPrice?.price;

	function getOrderTitle(): string {
		if (!product) return 'Registration Details';

		if (metadata?.auto_final_title) {
			return safeGetLocalizedText(metadata.auto_final_title, locale);
		}

		return safeGetLocalizedText(product.title, locale);
	}

	function getOrderDescription(): string {
		if (!product) return '';

		if (metadata?.auto_final_subtitle) {
			return safeGetLocalizedText(metadata.auto_final_subtitle, locale);
		}

		return '';
	}

	function getOrderStatus(): {
		text: string;
		variant: 'default' | 'secondary' | 'destructive' | 'outline';
	} {
		if (!orderProduct) return { text: 'Unknown', variant: 'outline' };

		if (orderProduct.canceled_at) {
			return { text: 'Canceled', variant: 'destructive' };
		}

		if (orderProduct.auto_units_owed > 0) {
			return { text: 'Payment Required', variant: 'destructive' };
		}

		const today = new Date();
		const lastEventDate = product?.auto_last_event_end_at
			? new Date(product.auto_last_event_end_at)
			: null;

		if (lastEventDate && lastEventDate < today) {
			return { text: 'Completed', variant: 'secondary' };
		}

		return { text: 'Active', variant: 'default' };
	}

	// Get color for card accent
	function getCardAccentColor(): string {
		return priceInfo?.color_primary_hex || '#6366f1';
	}

	// Get valid consumptions - ensure they have orderPrice and transform to match component props
	const consumptions = (orderProduct?.orderPriceConsumptions || [])
		.filter((consumption) => consumption?.orderPrice)
		.map((consumption) => ({
			id: consumption.id,
			cost_units: consumption.cost_units,
			returned_units: consumption.returned_units,
			created_at: consumption.created_at,
			orderPrice: consumption.orderPrice
				? {
						id: consumption.orderPrice.id,
						price_option_purchase_count: consumption.orderPrice.price_option_purchase_count,
						auto_price_option_expire_at: consumption.orderPrice.auto_price_option_expire_at,
						priceOption: {
							id: consumption.orderPrice.priceOption.id,
							title: consumption.orderPrice.priceOption.title
						},
						autoPriceOptionPrice: consumption.orderPrice.autoPriceOptionPrice
							? {
									color_primary_hex: consumption.orderPrice.autoPriceOptionPrice.color_primary_hex,
									payment_kind: consumption.orderPrice.autoPriceOptionPrice.payment_kind,
									brand: consumption.orderPrice.autoPriceOptionPrice.brand
										? {
												name_full: consumption.orderPrice.autoPriceOptionPrice.brand.name_full
											}
										: null
								}
							: null
					}
				: null
		}));

	// Sort consumptions by date (newest first)
	const sortedConsumptions = [...consumptions].sort((a, b) => {
		return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
	});

	// Get location info
	function getLocation(): string {
		if (!space) return '';

		let location = '';

		if (space.name_full || space.name_short) {
			location = safeGetLocalizedText(space.name_full || space.name_short, locale);
		}

		if (landmark?.title_full || landmark?.title_short) {
			const landmarkText = safeGetLocalizedText(
				landmark.title_full || landmark.title_short,
				locale
			);
			if (landmarkText && location) {
				location += ` • ${landmarkText}`;
			} else if (landmarkText) {
				location = landmarkText;
			}
		}

		return location;
	}

	const status = getOrderStatus();
	const pageTitle = product ? getOrderTitle() : 'Registration Details';
	const eventDate = firstEvent?.start_at ? formatDate(firstEvent.start_at) : null;
	const eventTime = firstEvent?.start_at ? formatTime(firstEvent.start_at) : null;
	const duration = formatDuration(firstEvent?.duration_minute);
	const location = getLocation();
</script>

{#snippet content()}
	<div class="mb-6">
		<Button variant="outline" href="/private/my-order" class="gap-2">
			<ArrowLeft class="h-4 w-4" />
			Back to Orders
		</Button>
	</div>

	{#if orderProduct}
		<Card class="mb-8 overflow-hidden">
			<div
				class="h-2"
				style="background-color: {getCardAccentColor()}; background-image: linear-gradient(to right, {getCardAccentColor()}, {getCardAccentColor()}88);"
			></div>
			<CardHeader>
				<div class="flex items-start justify-between">
					<div>
						<CardTitle>{getOrderTitle()}</CardTitle>
						{#if getOrderDescription()}
							<CardDescription>{getOrderDescription()}</CardDescription>
						{/if}
					</div>
					<Badge variant={status.variant}>{status.text}</Badge>
				</div>
			</CardHeader>
			<CardContent>
				<div class="grid gap-4">
					<div class="grid grid-cols-2 gap-4">
						<div class="flex items-center gap-2">
							<CreditCard class="h-4 w-4 text-muted-foreground" />
							<span class="text-sm text-muted-foreground">
								{#if orderProduct.order && orderProduct.order.nid}
									Order #{orderProduct.order.nid}
								{:else}
									Order
								{/if}
							</span>
						</div>
						<div class="flex items-center gap-2">
							<Clock class="h-4 w-4 text-muted-foreground" />
							<span class="text-sm text-muted-foreground">
								{orderProduct.created_at && isValid(parseISO(orderProduct.created_at))
									? formatDateTime(orderProduct.created_at)
									: '--'}
							</span>
						</div>
					</div>

					{#if eventDate || location}
						<div class="grid gap-2 rounded-md bg-muted/50 p-3">
							{#if eventDate && eventTime}
								<div class="flex items-center gap-2">
									<Calendar class="h-4 w-4 text-muted-foreground" />
									<span>
										{eventDate} • {eventTime}
										{#if duration}
											• {duration}
										{/if}
									</span>
								</div>
							{/if}

							{#if location}
								<div class="flex items-center gap-2">
									<MapPin class="h-4 w-4 text-muted-foreground" />
									<span>{location}</span>
								</div>
							{/if}
						</div>
					{/if}

					{#if orderProduct.canceled_at}
						<div class="rounded-md bg-destructive/10 p-3 text-destructive dark:bg-destructive/20">
							<p class="text-sm">
								This registration was canceled on {formatDateTime(orderProduct.canceled_at)}.
							</p>
						</div>
					{/if}
				</div>
			</CardContent>
		</Card>

		{#if sortedConsumptions.length > 0}
			<div class="mb-4">
				<h2 class="text-xl font-semibold">Payment History</h2>
				<p class="text-sm text-muted-foreground">
					{sortedConsumptions.length}
					{sortedConsumptions.length === 1 ? 'payment' : 'payments'} for this registration
				</p>
			</div>

			<div class="space-y-3">
				{#each sortedConsumptions as consumption}
					<OrderProductConsumptionCard {consumption} />
				{/each}
			</div>
		{:else}
			<Card>
				<CardContent class="p-6 text-center">
					<p class="text-muted-foreground">No payment history found for this registration.</p>
				</CardContent>
			</Card>
		{/if}
	{:else}
		<Card>
			<CardContent class="p-6 text-center">
				<p class="text-muted-foreground">Registration not found.</p>
			</CardContent>
		</Card>
	{/if}
{/snippet}

<PageContainer
	title={pageTitle}
	description={orderProduct ? getOrderDescription() : 'View details of your registration'}
	{content}
/>
