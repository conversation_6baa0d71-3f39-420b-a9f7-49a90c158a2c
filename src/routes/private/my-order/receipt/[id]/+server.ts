import { PDFDocument, rgb, StandardFonts } from '@pdfme/pdf-lib';
import { error } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';
import { format } from 'date-fns';
import { getLocalizedText } from '$lib/utils/localization';
import type { LocalizedText } from '$lib/utils/localization';

export const GET: RequestHandler = async ({ params, locals, fetch }) => {
	const { id } = params;

	if (!id) {
		throw error(400, 'Missing order price ID');
	}

	const { supabase } = locals;

	if (!supabase) {
		throw error(500, 'Supabase client not available');
	}

	// Get the order price with related data - we can't directly join to auth.users table
	const { data: orderPrice, error: orderPriceError } = await supabase
		.from('order_price')
		.select(
			`
			*,
			order:order_id (*),
			priceOption:price_option_id (*),
			autoPriceOptionPrice:auto_price_option_price_id (
				*,
				brand:brand_id (
					*,
					support_email,
					support_phone,
					support_web_url,
					slug
				)
			),
			consumer:consumer_profile_id (*),
			orderPriceConsumptions:order_price_consumption (
				*
			)
		`
		)
		.eq('id', id)
		.single();

	if (orderPriceError) {
		console.error('Error fetching order price:', orderPriceError);
		throw error(500, 'Error fetching order price data');
	}

	if (!orderPrice) {
		throw error(404, 'Order price not found');
	}

	// Get the consumer profile to use for the receipt
	const consumer = orderPrice.consumer;
	const brand = orderPrice.autoPriceOptionPrice?.brand;

	if (!consumer) {
		throw error(404, 'Consumer profile data not found');
	}

	// Create PDF document
	const pdfDoc = await PDFDocument.create();

	// Add a page - using standard letter size
	const page = pdfDoc.addPage([612, 792]);
	const { width, height } = page.getSize();

	// Load fonts - no need to register fontkit since we're using standard fonts
	const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
	const helveticaBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

	// Colors
	const primaryColor = rgb(0.14, 0.22, 0.84); // Blue like in the sample
	const textColor = rgb(0.1, 0.1, 0.1);
	const secondaryTextColor = rgb(0.4, 0.4, 0.4);
	const borderColor = rgb(0.9, 0.9, 0.9);

	// Margins and layout
	const margin = 110;
	const rightMargin = margin;
	const rightColumnX = width - rightMargin;

	// Brand information at top
	const brandName = brand
		? getLocalizedText(brand.name_full as LocalizedText, 'en')
		: 'Dance Studio';
	const brandHomepage = brand?.portal_url || '';

	page.drawText(brandName, {
		x: margin,
		y: height - 140,
		size: 18,
		font: helveticaBold,
		color: primaryColor
	});

	page.drawText(brandHomepage, {
		x: margin,
		y: height - 165,
		size: 12,
		font: helveticaFont,
		color: secondaryTextColor
	});

	// Receipt title and info
	const receiptText = 'RECEIPT';
	const receiptTextWidth = helveticaBold.widthOfTextAtSize(receiptText, 18);

	page.drawText(receiptText, {
		x: rightColumnX - receiptTextWidth,
		y: height - 140,
		size: 18,
		font: helveticaBold,
		color: primaryColor
	});

	const receiptNumberText = `#${orderPrice.order.nid}`;
	const receiptNumberWidth = helveticaFont.widthOfTextAtSize(receiptNumberText, 12);

	page.drawText(receiptNumberText, {
		x: rightColumnX - receiptNumberWidth,
		y: height - 165,
		size: 12,
		font: helveticaFont,
		color: textColor
	});

	// Receipt date
	const receiptDate = orderPrice.created_at
		? format(new Date(orderPrice.created_at), 'MMMM d, yyyy')
		: format(new Date(), 'MMMM d, yyyy');

	const dateLabel = 'Date:';
	const dateLabelWidth = helveticaBold.widthOfTextAtSize(dateLabel, 12);

	page.drawText(dateLabel, {
		x: rightColumnX - dateLabelWidth - 110,
		y: height - 190,
		size: 12,
		font: helveticaBold,
		color: secondaryTextColor
	});

	const dateWidth = helveticaFont.widthOfTextAtSize(receiptDate, 12);

	page.drawText(receiptDate, {
		x: rightColumnX - dateWidth,
		y: height - 190,
		size: 12,
		font: helveticaFont,
		color: textColor
	});

	// Draw top divider line
	page.drawLine({
		start: { x: margin, y: height - 210 },
		end: { x: width - margin, y: height - 210 },
		thickness: 1,
		color: borderColor
	});

	// Bill to section
	page.drawText('BILL TO:', {
		x: margin,
		y: height - 235,
		size: 12,
		font: helveticaBold,
		color: secondaryTextColor
	});

	// Get first and last names from profile
	const firstName =
		consumer.func_given_name_en_first ||
		getLocalizedText(consumer.given_name as LocalizedText, 'en') ||
		'';
	const lastName =
		consumer.func_family_name_en_first ||
		getLocalizedText(consumer.family_name as LocalizedText, 'en') ||
		'';

	page.drawText(`${firstName} ${lastName}`, {
		x: margin,
		y: height - 260,
		size: 12,
		font: helveticaFont,
		color: textColor
	});

	// Second divider line
	page.drawLine({
		start: { x: margin, y: height - 290 },
		end: { x: width - margin, y: height - 290 },
		thickness: 1,
		color: borderColor
	});

	// Description and Amount headers
	page.drawText('DESCRIPTION', {
		x: margin,
		y: height - 320,
		size: 12,
		font: helveticaBold,
		color: textColor
	});

	const amountHeaderText = 'AMOUNT';
	const amountHeaderWidth = helveticaBold.widthOfTextAtSize(amountHeaderText, 12);

	page.drawText(amountHeaderText, {
		x: rightColumnX - amountHeaderWidth,
		y: height - 320,
		size: 12,
		font: helveticaBold,
		color: textColor
	});

	// Header divider line
	page.drawLine({
		start: { x: margin, y: height - 330 },
		end: { x: width - margin, y: height - 330 },
		thickness: 1,
		color: borderColor
	});

	// Item description - conditional based on brand.domain
	const itemTitle = brand?.domain === 'dance_studio' ? 'Fitness Dance Class Pass' : 'Class Pass';

	page.drawText(itemTitle, {
		x: margin,
		y: height - 360,
		size: 12,
		font: helveticaFont,
		color: textColor
	});

	page.drawText(brandName, {
		x: margin,
		y: height - 385,
		size: 12,
		font: helveticaFont,
		color: secondaryTextColor
	});

	// Currency formatting
	const currency = orderPrice.auto_price_option_currency_code || 'USD';
	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency,
			minimumFractionDigits: 2
		}).format(amount / 100);
	};

	// Amount
	const amount = formatCurrency(orderPrice.deal_money_int);
	const amountWidth = helveticaFont.widthOfTextAtSize(amount, 12);

	page.drawText(amount, {
		x: rightColumnX - amountWidth,
		y: height - 360,
		size: 12,
		font: helveticaFont,
		color: textColor
	});

	// Bottom divider line
	page.drawLine({
		start: { x: margin, y: height - 415 },
		end: { x: width - margin, y: height - 415 },
		thickness: 1,
		color: borderColor
	});

	// Total section
	const totalLabelText = 'TOTAL';

	page.drawText(totalLabelText, {
		x: rightColumnX - amountWidth - 70,
		y: height - 445,
		size: 12,
		font: helveticaBold,
		color: textColor
	});

	// Draw total amount in primary color
	const totalAmount = amount;
	const totalAmountWidth = helveticaBold.widthOfTextAtSize(totalAmount, 12);

	page.drawText(totalAmount, {
		x: rightColumnX - totalAmountWidth,
		y: height - 445,
		size: 12,
		font: helveticaBold,
		color: primaryColor
	});

	// Bottom divider line before footer
	page.drawLine({
		start: { x: margin, y: margin + 100 },
		end: { x: width - margin, y: margin + 100 },
		thickness: 1,
		color: borderColor
	});

	// Thank you message - centered
	const thankYouMessage = 'Thank you for your business!';
	const thankYouWidth = helveticaFont.widthOfTextAtSize(thankYouMessage, 12);

	page.drawText(thankYouMessage, {
		x: (width - thankYouWidth) / 2,
		y: margin + 70,
		size: 12,
		font: helveticaFont,
		color: secondaryTextColor
	});

	// Support information - also centered
	const contactInfoItems = [];
	const hasContactInfo = !!(
		(brand?.support_email && brand.support_email.trim() !== '') ||
		(brand?.support_phone && brand.support_phone.trim() !== '') ||
		(brand?.support_web_url && brand.support_web_url.trim() !== '')
	);

	if (brand?.support_email && brand.support_email.trim() !== '') {
		contactInfoItems.push(`Email: ${brand.support_email}`);
	}

	if (brand?.support_phone && brand.support_phone.trim() !== '') {
		contactInfoItems.push(`Phone: ${brand.support_phone}`);
	}

	if (brand?.support_web_url && brand.support_web_url.trim() !== '') {
		contactInfoItems.push(`Web: ${brand.support_web_url}`);
	}

	// If we have contact info, display it centered with a message
	if (hasContactInfo) {
		const contactMessage = 'If you have any questions regarding this order, please contact:';
		const contactMessageWidth = helveticaFont.widthOfTextAtSize(contactMessage, 10);

		page.drawText(contactMessage, {
			x: (width - contactMessageWidth) / 2,
			y: margin + 50,
			size: 10,
			font: helveticaFont,
			color: secondaryTextColor
		});

		const contactInfo = contactInfoItems.join('  •  ');
		const contactInfoWidth = helveticaFont.widthOfTextAtSize(contactInfo, 10);

		page.drawText(contactInfo, {
			x: (width - contactInfoWidth) / 2,
			y: margin + 35,
			size: 10,
			font: helveticaFont,
			color: secondaryTextColor
		});
	}

	// Generate PDF bytes
	const pdfBytes = await pdfDoc.save();

	// Get brand slug for filename
	const brandSlug = brand?.slug || 'receipt';

	// Return the PDF as a response
	return new Response(pdfBytes, {
		status: 200,
		headers: {
			'Content-Type': 'application/pdf',
			'Content-Disposition': `attachment; filename="${brandSlug}-receipt-${orderPrice.order.nid}.pdf"`,
			'Content-Length': pdfBytes.byteLength.toString()
		}
	});
};
