<script lang="ts">
	import { format, parseISO, isValid } from 'date-fns';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { ServerOrderPrice } from './types';
	import type { LocalizedText } from '$lib/utils/localization';
	import { Badge } from '$lib/components/ui/badge';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Separator } from '$lib/components/ui/separator';
	import { CreditCard, Clock, Tag, Download, MoreVertical, RefreshCw } from '@lucide/svelte';
	import OrderPriceConsumptionCard from './OrderPriceConsumptionCard.svelte';
	import { Button } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import * as AlertDialog from '$lib/components/ui/alert-dialog';
	import { toast } from 'svelte-sonner';

	// Helper function to safely extract text from localized objects
	function safeGetLocalizedText(value: any, locale: string): string {
		if (!value) return '';
		if (typeof value === 'string') return value;

		try {
			// For proper LocalizedText objects
			if (typeof value === 'object' && value !== null) {
				// If it's a raw object with locale keys
				if (value[locale]) return String(value[locale]);

				// Try to get the first non-empty value
				for (const key in value) {
					if (value[key] && typeof value[key] === 'string' && value[key].trim() !== '') {
						return value[key];
					}
				}

				// If we have any value, use the first one
				const firstValue = Object.values(value)[0];
				if (firstValue) return String(firstValue);

				// Last resort - just return a basic string representation
				const keys = Object.keys(value).join(', ');
				if (keys) return `[${keys}]`;
			}

			// Attempt to use getLocalizedText as a fallback
			// @ts-ignore - Ignoring type issue here since we're handling all cases
			return getLocalizedText(value, locale);
		} catch (err) {
			console.error('Failed to extract localized text:', err);
			return typeof value === 'object' ? JSON.stringify(value) : String(value);
		}
	}

	interface Props {
		orderPrice: ServerOrderPrice;
		showAllHistory?: boolean; // Show all history items without limit
		detailsLink?: string; // Custom link for details button
	}

	let { orderPrice, showAllHistory = false, detailsLink }: Props = $props();

	const locale = getLocale();

	// Refund state
	let showRefundDialog = $state(false);
	let isRefunding = $state(false);

	function formatDate(dateString: string | null): string {
		if (!dateString) return '--';
		try {
			const date = parseISO(dateString);
			if (!isValid(date)) return '--';
			const day = date.getDate();
			const ordinalSuffix = getOrdinalSuffix(day);
			return format(date, `MMMM d'${ordinalSuffix}', yyyy`);
		} catch (error) {
			console.error('Error formatting date:', dateString, error);
			return '--';
		}
	}

	function formatDateTime(dateString: string | null): string {
		if (!dateString) return '--';
		try {
			const date = parseISO(dateString);
			if (!isValid(date)) return '--';
			return format(date, 'MMM d, yyyy h:mm a');
		} catch (error) {
			console.error('Error formatting date:', dateString, error);
			return '--';
		}
	}

	function getOrdinalSuffix(day: number): string {
		if (day > 3 && day < 21) return 'th';
		switch (day % 10) {
			case 1:
				return 'st';
			case 2:
				return 'nd';
			case 3:
				return 'rd';
			default:
				return 'th';
		}
	}

	function formatCurrency(amount: number): string {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD',
			minimumFractionDigits: 2
		}).format(amount / 100);
	}

	function getOrderTitle(): string {
		return `${orderPrice.price_option_purchase_count}× ${safeGetLocalizedText(orderPrice.priceOption.title, locale)}`;
	}

	function getOrderDescription(): string {
		const brandName = orderPrice.autoPriceOptionPrice?.brand
			? safeGetLocalizedText(orderPrice.autoPriceOptionPrice.brand.name_full, locale)
			: '';
		return brandName || 'Class Pass';
	}

	function getOrderStatus(): {
		text: string;
		variant: 'default' | 'secondary' | 'destructive' | 'outline';
	} {
		if (orderPrice.auto_money_int_unpaid > 0) {
			return { text: 'Unpaid', variant: 'destructive' };
		}
		if (orderPrice.auto_units_available <= 0) {
			return { text: 'Used', variant: 'secondary' };
		}
		if (
			orderPrice.auto_price_option_expire_at &&
			new Date(orderPrice.auto_price_option_expire_at) < new Date()
		) {
			return { text: 'Expired', variant: 'secondary' };
		}
		return { text: 'Active', variant: 'default' };
	}

	// Get color for card accent
	function getCardAccentColor(): string {
		return orderPrice.autoPriceOptionPrice?.color_primary_hex || '#6366f1';
	}

	// Get valid consumptions
	const consumptions = $derived(orderPrice.orderPriceConsumptions || []);

	// Default details URL
	const defaultDetailsUrl = $derived(`/private/my-order/order-price/${orderPrice.id}`);

	const status = getOrderStatus();

	// Check if refund is available
	function isRefundAvailable(): boolean {
		// Must be paid
		if (orderPrice.auto_money_int_unpaid > 0) return false;

		// Must not be expired
		if (orderPrice.deal_expire_at && new Date(orderPrice.deal_expire_at) < new Date()) return false;

		// Must be within refund window (check if > 0, since 0 means no refund allowed)
		const moneyBackWindow = orderPrice.priceOption?.price?.money_back_primary_window_minute;
		if (moneyBackWindow && moneyBackWindow > 0) {
			const createdAt = new Date(orderPrice.created_at);
			const refundDeadline = new Date(createdAt.getTime() + moneyBackWindow * 60 * 1000);
			if (new Date() > refundDeadline) return false;
		} else if (!moneyBackWindow || moneyBackWindow === 0) {
			// No refund window or 0 minutes means no refund allowed
			return false;
		}

		return true;
	}

	// Calculate refund details
	function getRefundDetails() {
		// Calculate processing fee (2.9% + $0.30)
		const baseAmount = orderPrice.deal_money_int;
		const processingFee = Math.round(baseAmount * 0.029 + 30);
		const refundAmount = baseAmount;

		return {
			baseAmount,
			processingFee,
			refundAmount
		};
	}

	async function handleRefundRequest() {
		if (!orderPrice.id) return;

		isRefunding = true;

		try {
			const response = await fetch(`/private/my-order/order-price/${orderPrice.id}/refund`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ confirmed: true })
			});

			const result = await response.json();

			if (result.type === 'error') {
				throw new Error(result.error.message);
			}

			if (result.type === 'success') {
				toast.success('Refund processed successfully');
				showRefundDialog = false;
				// Optionally refresh the page or invalidate data
				window.location.reload();
			} else {
				throw new Error('Failed to process refund');
			}
		} catch (err) {
			console.error('Error processing refund:', err);
			toast.error(err instanceof Error ? err.message : 'Failed to process refund');
		} finally {
			isRefunding = false;
		}
	}

	// Check if any menu actions are available
	const hasMenuActions = $derived(isRefundAvailable());

	const refundDetails = $derived(getRefundDetails());
</script>

<Card class="overflow-hidden">
	<div
		class="h-2"
		style="background-color: {getCardAccentColor()}; background-image: linear-gradient(to right, {getCardAccentColor()}, {getCardAccentColor()}88);"
	></div>
	<CardHeader>
		<div class="flex items-start justify-between">
			<div>
				<CardTitle>{getOrderTitle()}</CardTitle>
				<CardDescription>{getOrderDescription()}</CardDescription>
			</div>
			<Badge variant={status.variant}>{status.text}</Badge>
		</div>
	</CardHeader>
	<CardContent>
		<div class="grid gap-4">
			<div class="grid grid-cols-2 gap-4">
				<div class="flex items-center gap-2">
					<CreditCard class="h-4 w-4 text-muted-foreground" />
					<span class="text-sm text-muted-foreground">Order #{orderPrice.order.nid}</span>
				</div>
				<div class="flex items-center gap-2">
					<Clock class="h-4 w-4 text-muted-foreground" />
					<span class="text-sm text-muted-foreground">
						{orderPrice.created_at && isValid(parseISO(orderPrice.created_at))
							? formatDateTime(orderPrice.created_at)
							: '--'}
					</span>
				</div>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div>
					<div class="text-sm font-medium">Price</div>
					<div>{formatCurrency(orderPrice.deal_money_int)}</div>
				</div>
				<div>
					<div class="text-sm font-medium">Points</div>
					<div>
						{orderPrice.auto_units_available} of {orderPrice.deal_units} available
					</div>
				</div>
			</div>

			{#if orderPrice.auto_price_option_expire_at}
				<div>
					<div class="text-sm font-medium">Expires</div>
					<div>{formatDate(orderPrice.auto_price_option_expire_at)}</div>
				</div>
			{/if}

			{#if consumptions.length > 0}
				<div>
					<Separator class="my-2" />
					<div class="mb-2 text-sm font-medium">Usage History</div>
					<div class="space-y-3">
						{#each consumptions as consumption, i}
							{#if showAllHistory || i < 3}
								<OrderPriceConsumptionCard {consumption} />
							{/if}
						{/each}

						{#if !showAllHistory && consumptions.length > 3}
							<div class="flex items-center justify-between">
								<div class="text-sm text-muted-foreground">
									+{consumptions.length - 3} more {consumptions.length - 3 === 1
										? 'usage'
										: 'usages'}
								</div>
								<Button variant="link" href={detailsLink || defaultDetailsUrl} class="h-auto p-0">
									View All
								</Button>
							</div>
						{:else if !showAllHistory}
							<div class="flex justify-end">
								<Button variant="link" href={detailsLink || defaultDetailsUrl} class="h-auto p-0">
									View Details
								</Button>
							</div>
						{/if}
					</div>
				</div>
			{/if}
		</div>
	</CardContent>
	<CardFooter class="pt-0">
		<div class="flex w-full justify-between">
			<Button
				variant="outline"
				size="sm"
				href={`/private/my-order/receipt/${orderPrice.id}`}
				class="flex gap-1"
			>
				<Download class="h-4 w-4" />
				Receipt
			</Button>
			{#if hasMenuActions}
				<DropdownMenu.Root>
					<DropdownMenu.Trigger>
						<Button variant="ghost" size="sm" class="h-8 w-8 p-0">
							<MoreVertical class="h-4 w-4" />
							<span class="sr-only">Open menu</span>
						</Button>
					</DropdownMenu.Trigger>
					<DropdownMenu.Content align="end">
						{#if isRefundAvailable()}
							<DropdownMenu.Item onclick={() => (showRefundDialog = true)}>
								<RefreshCw class="mr-2 h-4 w-4" />
								Request Refund
							</DropdownMenu.Item>
						{/if}
					</DropdownMenu.Content>
				</DropdownMenu.Root>
			{/if}
		</div>
	</CardFooter>
</Card>

<!-- Refund Confirmation Dialog -->
<AlertDialog.Root bind:open={showRefundDialog}>
	<AlertDialog.Content>
		<AlertDialog.Header>
			<AlertDialog.Title>Request Refund</AlertDialog.Title>
			<AlertDialog.Description>
				Are you sure you want to request a refund for this class pass? This action cannot be undone.
			</AlertDialog.Description>
		</AlertDialog.Header>

		<div class="my-4 space-y-3">
			<div class="rounded-md border border-amber-200 bg-amber-50 p-4">
				<div class="flex">
					<div class="shrink-0">
						<svg class="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
							<path
								fill-rule="evenodd"
								d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
								clip-rule="evenodd"
							></path>
						</svg>
					</div>
					<div class="ml-3">
						<h3 class="text-sm font-medium text-amber-800">Processing fees are non-refundable</h3>
						<div class="mt-2 text-sm text-amber-700">
							<p>
								A processing fee of {formatCurrency(refundDetails.processingFee)} (2.9% + $0.30) charged
								by our payment processor is non-refundable. You will receive a refund of {formatCurrency(
									refundDetails.refundAmount
								)}.
							</p>
						</div>
					</div>
				</div>
			</div>

			{#if orderPrice.auto_units_available < orderPrice.deal_units}
				<div class="rounded-md border border-blue-200 bg-blue-50 p-4">
					<div class="flex">
						<div class="shrink-0">
							<svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
								<path
									fill-rule="evenodd"
									d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z"
									clip-rule="evenodd"
								></path>
							</svg>
						</div>
						<div class="ml-3">
							<h3 class="text-sm font-medium text-blue-800">
								Active registrations will be cancelled
							</h3>
							<div class="mt-2 text-sm text-blue-700">
								<p>
									This class pass has been used for {orderPrice.deal_units -
										orderPrice.auto_units_available} registration(s). We will automatically cancel any
									active registrations as part of the refund process.
								</p>
							</div>
						</div>
					</div>
				</div>
			{/if}
		</div>

		<AlertDialog.Footer>
			<AlertDialog.Cancel disabled={isRefunding}>Cancel</AlertDialog.Cancel>
			<AlertDialog.Action onclick={handleRefundRequest} disabled={isRefunding}>
				{#if isRefunding}
					<RefreshCw class="mr-2 h-4 w-4 animate-spin" />
					Processing...
				{:else}
					Request Refund
				{/if}
			</AlertDialog.Action>
		</AlertDialog.Footer>
	</AlertDialog.Content>
</AlertDialog.Root>
