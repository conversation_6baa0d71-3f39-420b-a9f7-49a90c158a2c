<script lang="ts">
	import { format, parseISO } from 'date-fns';
	import type { PageData } from './$types';
	import type { ServerOrderPrice, ServerOrderProduct } from './types';
	import OrderPriceCard from './OrderPriceCard.svelte';
	import OrderProductCard from './OrderProductCard.svelte';
	import { Tabs, TabsContent, TabsList, TabsTrigger } from '$lib/components/ui/tabs';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { PageContainer } from '$lib/components/layout';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	let loading = $state(true);
	let activeTab = $state('registrations');

	$effect(() => {
		if (data) {
			loading = false;
		}
	});

	function handleTabChange(tab: string) {
		activeTab = tab;
	}
</script>

{#snippet content()}
	<Tabs value={activeTab} class="w-full">
		<TabsList class="mb-6">
			<TabsTrigger value="registrations" onclick={() => handleTabChange('registrations')}
				>Registrations</TabsTrigger
			>
			<TabsTrigger value="class-passes" onclick={() => handleTabChange('class-passes')}
				>Class Passes</TabsTrigger
			>
		</TabsList>

		<TabsContent value="registrations" class="space-y-4">
			{#if loading}
				<div class="space-y-4">
					{#each Array(2) as _}
						<Skeleton class="h-48 w-full rounded-lg" />
					{/each}
				</div>
			{:else if !data.orderProducts || data.orderProducts.length === 0}
				<div class="flex flex-col items-center justify-center py-12 text-center">
					<h3 class="text-xl font-semibold">No registrations found</h3>
					<p class="text-muted-foreground">You haven't registered for any events yet.</p>
				</div>
			{:else}
				{#each data.orderProducts as orderProduct}
					<OrderProductCard {orderProduct} />
				{/each}
			{/if}
		</TabsContent>

		<TabsContent value="class-passes" class="space-y-4">
			{#if loading}
				<div class="space-y-4">
					{#each Array(2) as _}
						<Skeleton class="h-48 w-full rounded-lg" />
					{/each}
				</div>
			{:else if !data.orderPrices || data.orderPrices.length === 0}
				<div class="flex flex-col items-center justify-center py-12 text-center">
					<h3 class="text-xl font-semibold">No class passes found</h3>
					<p class="text-muted-foreground">You haven't purchased any class passes yet.</p>
				</div>
			{:else}
				{#each data.orderPrices as orderPrice}
					<OrderPriceCard {orderPrice} />
				{/each}
			{/if}
		</TabsContent>
	</Tabs>
{/snippet}

<PageContainer title="My Orders" description="View your class passes and registrations" {content} />
