import type { LocalizedText } from '$lib/utils/localization';
import type { Database } from '$lib/supabase/database.types';

// Base database types
type DbOrderPrice = Database['public']['Tables']['order_price']['Row'];
type DbOrderProduct = Database['public']['Tables']['order_product']['Row'];

// Server response types - these match the actual shape of data returned from the API
export interface ServerOrderPrice {
	id: string;
	created_at: string;
	deal_units: number;
	deal_money_int: number;
	deal_expire_at: string | null;
	price_option_purchase_count: number;
	payer_id: string;
	auto_price_option_expire_at: string | null;
	auto_units_available: number;
	auto_money_int_unpaid: number;
	airtable_id: string | null;
	priceOption: {
		id: string;
		money_int: number;
		title: LocalizedText;
		price?: {
			money_back_primary_window_minute: number;
		} | null;
	};
	autoPriceOptionPrice: {
		payment_kind: string;
		title: LocalizedText;
		color_primary_hex: string | null;
		color_primary_semantic: string | null;
		brand: {
			name_full: LocalizedText;
		} | null;
	} | null;
	consumerProfile: {
		given_name: string;
		family_name: string;
		id: string;
	} | null;
	order: {
		id: string;
		nid: number;
	};
	orderPriceConsumptions: Array<{
		id: string;
		orderProduct: {
			id: string;
			canceled_at: string | null;
			productPrice?: {
				product?: {
					id: string;
					title: LocalizedText;
					metadata?: {
						auto_final_title?: string | null;
						auto_final_subtitle?: string | null;
					} | null;
					autoFirstEvent?: {
						start_at: string;
						auto_end_at: string | null;
						id: string;
					} | null;
					space?: {
						name_full: LocalizedText;
						name_short: LocalizedText;
						landmark?: {
							title_short: LocalizedText;
							title_full: LocalizedText;
						} | null;
					} | null;
					product_events?: Array<{
						event: {
							id: string;
							title: LocalizedText;
							start_at: string;
							auto_end_at: string | null;
							space_id?: string;
							metadata?: {
								auto_final_title?: string | null;
								auto_final_subtitle?: string | null;
							} | null;
							space?: {
								name_full: LocalizedText;
								name_short: LocalizedText;
								landmark?: {
									title_short: LocalizedText;
									title_full: LocalizedText;
								} | null;
							} | null;
						};
					}> | null;
				};
			} | null;
		} | null;
		cost_units: number;
		returned_units: number | null;
		created_at: string;
	}>;
	autoFirstEvent?: {
		start_at: string;
		auto_end_at: string | null;
		id: string;
	} | null;
}

export interface ServerOrderProduct {
	id: string;
	consumer_profile_id: string;
	consumer_profile: {
		given_name: string;
		family_name: string;
	} | null;
	orderPriceConsumptions: Array<{
		id: string;
		orderPrice: {
			price_option_purchase_count: number;
			priceOption: {
				id: string;
				title: LocalizedText;
			};
			auto_price_option_expire_at: string | null;
		} | null;
		cost_units: number;
		returned_units: number | null;
	}>;
	auto_units_owed: number;
	canceled_at: string | null;
	created_at: string;
	product_purchased_count: number;
	productPrice: {
		price: {
			color_primary_semantic: string | null;
			color_primary_hex: string | null;
			title: LocalizedText;
			product_classification: string | null;
		};
		auto_cancel_at_far: string | null;
		auto_cancel_at_near: string | null;
		auto_cancel_at_far_return_units: number | null;
		auto_cancel_at_near_return_units: number | null;
		product: {
			id: string;
			title: LocalizedText;
			metadata: {
				id: string;
				auto_final_title: string | null;
				auto_final_subtitle: string | null;
				custom_attribute: Record<string, any> | null;
				metadata_wikipages?: Array<{
					relation: string;
					wikipage: {
						id: string;
						title: LocalizedText;
					};
				}> | null;
			} | null;
			autoFirstEvent: {
				start_at: string;
				auto_end_at: string | null;
				id: string;
			} | null;
			auto_last_event_end_at: string | null;
			product_events?: Array<{
				event: {
					id: string;
					title: LocalizedText;
					start_at: string;
					auto_end_at: string | null;
					space_id?: string;
					metadata?: {
						auto_final_title?: string | null;
						auto_final_subtitle?: string | null;
					} | null;
					space?: {
						name_full: LocalizedText;
						name_short: LocalizedText;
						landmark?: {
							title_short: LocalizedText;
							title_full: LocalizedText;
						} | null;
					} | null;
				};
			}> | null;
		};
	} | null;
	order: {
		nid: number;
		id: string;
	};
}

// For our component usage - these are the full DB types with joined data
export interface OrderPrice extends Omit<DbOrderPrice, 'price_option_id'> {
	priceOption: {
		id: string;
		money_int: number;
		title: LocalizedText;
	};
	autoPriceOptionPrice: {
		payment_kind: string;
		title: LocalizedText;
		color_primary_hex: string | null;
		color_primary_semantic: string | null;
		brand: {
			name_full: LocalizedText;
		} | null;
	} | null;
	consumerProfile: {
		given_name: string;
		family_name: string;
		id: string;
	} | null;
	order: {
		id: string;
		nid: number;
	};
	orderPriceConsumptions: Array<{
		id: string;
		orderProduct: {
			id: string;
			canceled_at: string | null;
		} | null;
		cost_units: number;
		returned_units: number | null;
		created_at: string;
	}>;
	autoFirstEvent?: {
		start_at: string;
		auto_end_at: string | null;
		id: string;
	} | null;
}

export interface OrderProduct extends Omit<DbOrderProduct, 'product_price_id'> {
	consumer_profile: {
		given_name: string;
		family_name: string;
	} | null;
	orderPriceConsumptions: Array<{
		id: string;
		orderPrice: {
			price_option_purchase_count: number;
			priceOption: {
				id: string;
				title: LocalizedText;
			};
			auto_price_option_expire_at: string | null;
		} | null;
		cost_units: number;
		returned_units: number | null;
	}>;
	productPrice: {
		price: {
			color_primary_semantic: string | null;
			color_primary_hex: string | null;
			title: LocalizedText;
			product_classification: string | null;
		};
		auto_cancel_at_far: string | null;
		auto_cancel_at_near: string | null;
		auto_cancel_at_far_return_units: number | null;
		auto_cancel_at_near_return_units: number | null;
		product: {
			id: string;
			title: LocalizedText;
			metadata: {
				id: string;
				auto_final_title: string | null;
				auto_final_subtitle: string | null;
				custom_attribute: Record<string, any> | null;
				metadata_wikipages?: Array<{
					relation: string;
					wikipage: {
						id: string;
						title: LocalizedText;
					};
				}> | null;
			} | null;
			autoFirstEvent: {
				start_at: string;
				auto_end_at: string | null;
				id: string;
			} | null;
			auto_last_event_end_at: string | null;
		};
	} | null;
	order: {
		nid: number;
		id: string;
	};
	autoFirstEvent?: {
		start_at: string;
		auto_end_at: string | null;
		id: string;
	} | null;
}

export interface OrderItem {
	type: 'class-pass' | 'registration';
	data: ServerOrderPrice | ServerOrderProduct;
}
