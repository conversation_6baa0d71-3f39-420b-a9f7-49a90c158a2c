<script lang="ts">
	import { format, parseISO, isValid } from 'date-fns';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { LocalizedText } from '$lib/utils/localization';
	import { Card, CardContent } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { MapPin, Calendar, Clock } from '@lucide/svelte';

	interface Props {
		consumption: {
			id: string;
			cost_units: number;
			returned_units: number | null;
			created_at: string;
			orderProduct: {
				id?: string;
				canceled_at?: string | null;
				productPrice?: {
					product?: {
						id: string;
						title: LocalizedText;
						metadata?: {
							auto_final_title?: string | null;
							auto_final_subtitle?: string | null;
						} | null;
						autoFirstEvent?: {
							id: string;
							start_at: string;
							auto_end_at: string | null;
							duration_minutes?: number;
							space?: {
								name_full: LocalizedText;
								name_short: LocalizedText;
								landmark?: {
									title_full: LocalizedText;
									title_short: LocalizedText;
								} | null;
							} | null;
						} | null;
					} | null;
				} | null;
			} | null;
		};
	}

	let { consumption }: Props = $props();

	const locale = getLocale();

	function formatDateTime(dateString: string | null): string {
		if (!dateString) return '--';
		try {
			const date = parseISO(dateString);
			if (!isValid(date)) return '--';
			return format(date, 'MMM d, yyyy h:mm a');
		} catch (error) {
			console.error('Error formatting date:', dateString, error);
			return '--';
		}
	}

	function formatDate(dateString: string | null): string {
		if (!dateString) return '--';
		try {
			const date = parseISO(dateString);
			if (!isValid(date)) return '--';
			return format(date, 'EEE, MMM d');
		} catch (error) {
			console.error('Error formatting date:', dateString, error);
			return '--';
		}
	}

	function formatTime(dateString: string | null): string {
		if (!dateString) return '--';
		try {
			const date = parseISO(dateString);
			if (!isValid(date)) return '--';
			return format(date, 'h:mm a');
		} catch (error) {
			console.error('Error formatting time:', dateString, error);
			return '--';
		}
	}

	// Format duration in minutes to "X min" format
	function formatDuration(minutes: number | null | undefined): string {
		if (!minutes) return '';
		return `${minutes} min`;
	}

	// Get consumption details
	const product = $derived(consumption.orderProduct?.productPrice?.product);
	const event = $derived(product?.autoFirstEvent);

	// Title and subtitle
	const title = $derived(
		product?.metadata?.auto_final_title
			? getLocalizedText(product.metadata.auto_final_title)
			: 'Untitled Product'
	);

	const subtitle = $derived(
		product?.metadata?.auto_final_subtitle
			? getLocalizedText(product.metadata.auto_final_subtitle)
			: ''
	);

	// Event details
	const eventDate = $derived(event?.start_at ? formatDate(event.start_at) : null);
	const eventTime = $derived(event?.start_at ? formatTime(event.start_at) : null);
	const duration = $derived(formatDuration(event?.duration_minutes));

	// Location
	function getLocation(): string | null {
		if (!event?.space) return null;

		const spaceName =
			getLocalizedText(event.space.name_full) || getLocalizedText(event.space.name_short);

		if (!spaceName) return null;

		let result = spaceName;

		if (event.space.landmark) {
			const landmarkName =
				getLocalizedText(event.space.landmark.title_full) ||
				getLocalizedText(event.space.landmark.title_short);

			if (landmarkName) {
				result = `${result} · ${landmarkName}`;
			}
		}

		return result;
	}

	const location = $derived(getLocation());

	// Registration date
	const registrationDate = $derived(formatDateTime(consumption.created_at));

	// Units
	const netUnits = $derived(consumption.cost_units - (consumption.returned_units || 0));
</script>

<Card
	class="overflow-hidden border-0 bg-neutral-50 shadow-none transition-colors hover:bg-neutral-100 dark:bg-neutral-800/20 dark:hover:bg-neutral-800/40"
>
	<CardContent class="p-3">
		<div class="grid gap-2.5">
			<!-- Title and units section -->
			<div class="flex items-start justify-between gap-3">
				<div class="space-y-0.5">
					<h3 class="font-medium leading-tight">{title}</h3>
					{#if subtitle}
						<p class="text-xs leading-tight text-muted-foreground">{subtitle}</p>
					{/if}
				</div>
				<div class="flex shrink-0 flex-col items-end gap-0.5">
					<Badge variant="secondary" class="whitespace-nowrap">
						{netUnits}
						{netUnits === 1 ? 'point' : 'points'}
					</Badge>
					{#if consumption.returned_units}
						<div class="text-xs text-muted-foreground">
							{consumption.cost_units} - {consumption.returned_units} returned
						</div>
					{/if}
				</div>
			</div>

			<!-- Event details section -->
			{#if eventDate || location}
				<div class="grid gap-1.5 text-xs">
					<div class="flex flex-wrap items-center gap-x-3 gap-y-1.5">
						{#if eventDate && eventTime}
							<div class="flex items-center gap-1">
								<Calendar class="h-3 w-3 text-muted-foreground" />
								<span class="text-muted-foreground"
									>{eventDate} • {eventTime} {duration ? `• ${duration}` : ''}</span
								>
							</div>
						{:else if eventDate}
							<div class="flex items-center gap-1">
								<Calendar class="h-3 w-3 text-muted-foreground" />
								<span class="text-muted-foreground">{eventDate}</span>
							</div>
						{/if}
					</div>

					{#if location}
						<div class="flex items-center gap-1">
							<MapPin class="h-3 w-3 text-muted-foreground" />
							<span class="truncate text-muted-foreground">{location}</span>
						</div>
					{/if}
				</div>
			{/if}

			<!-- Registration date -->
			<div class="text-right text-xs text-muted-foreground/75">
				Registered: {registrationDate}
			</div>
		</div>
	</CardContent>
</Card>
