<script lang="ts">
	import { format, parseISO } from 'date-fns';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { ServerOrderProduct } from './types';
	import type { LocalizedText } from '$lib/utils/localization';
	import { Badge } from '$lib/components/ui/badge';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Separator } from '$lib/components/ui/separator';
	import { CalendarDays, CreditCard, Clock, Users, MapPin } from '@lucide/svelte';
	import { Button } from '$lib/components/ui/button';

	// Helper function to safely extract text from localized objects
	function safeGetLocalizedText(value: any, locale: string): string {
		if (!value) return '';
		if (typeof value === 'string') return value;

		try {
			// For proper LocalizedText objects
			if (typeof value === 'object' && value !== null) {
				// If it's a raw object with locale keys
				if (value[locale]) return String(value[locale]);

				// Try to get the first non-empty value
				for (const key in value) {
					if (value[key] && typeof value[key] === 'string' && value[key].trim() !== '') {
						return value[key];
					}
				}

				// If we have any value, use the first one
				const firstValue = Object.values(value)[0];
				if (firstValue) return String(firstValue);

				// Last resort - just return a basic string representation
				const keys = Object.keys(value).join(', ');
				if (keys) return `[${keys}]`;
			}

			// Attempt to use getLocalizedText as a fallback
			// @ts-ignore - Ignoring type issue here since we're handling all cases
			return getLocalizedText(value, locale);
		} catch (err) {
			console.error('Failed to extract localized text:', err);
			return typeof value === 'object' ? JSON.stringify(value) : String(value);
		}
	}

	interface Props {
		orderProduct: ServerOrderProduct;
		viewAllLink?: string;
	}

	let { orderProduct, viewAllLink }: Props = $props();

	const locale = getLocale();

	// Default link if not provided
	const detailsLink = $derived(viewAllLink || `/private/my-order/order-product/${orderProduct.id}`);

	function formatDate(dateString: string | null): string {
		if (!dateString) return '--';
		const date = parseISO(dateString);
		const day = date.getDate();
		const ordinalSuffix = getOrdinalSuffix(day);
		return format(date, `MMMM d'${ordinalSuffix}', yyyy`);
	}

	function formatDateTime(dateString: string | null): string {
		if (!dateString) return '--';
		return format(parseISO(dateString), 'MMM d, yyyy h:mm a');
	}

	function formatEventDateTime(dateString: string | null): string {
		if (!dateString) return '--';
		const date = parseISO(dateString);
		const day = date.getDate();
		const ordinalSuffix = getOrdinalSuffix(day);
		return format(date, `EEEE, MMMM d'${ordinalSuffix}'`);
	}

	function formatEventTime(dateString: string | null): string {
		if (!dateString) return '--';
		return format(parseISO(dateString), 'h:mm a');
	}

	function formatEventDate(dateString: string | null): string {
		if (!dateString) return '--';
		const date = parseISO(dateString);
		const day = date.getDate();
		const ordinalSuffix = getOrdinalSuffix(day);
		return format(date, `EEEE, MMMM d'${ordinalSuffix}'`);
	}

	function getOrdinalSuffix(day: number): string {
		if (day > 3 && day < 21) return 'th';
		switch (day % 10) {
			case 1:
				return 'st';
			case 2:
				return 'nd';
			case 3:
				return 'rd';
			default:
				return 'th';
		}
	}

	function getOrderTitle(): string {
		if (orderProduct.productPrice?.product) {
			// First check if there's an auto_final_title in metadata
			if (orderProduct.productPrice.product.metadata?.auto_final_title) {
				const title = orderProduct.productPrice.product.metadata.auto_final_title;
				return `${orderProduct.product_purchased_count}× ${typeof title === 'string' ? title : safeGetLocalizedText(title as unknown as LocalizedText, locale)}`;
			}
			// Then fall back to the product title
			if (orderProduct.productPrice.product.title) {
				return `${orderProduct.product_purchased_count}× ${safeGetLocalizedText(orderProduct.productPrice.product.title, locale)}`;
			}
		}
		return 'Registration';
	}

	function getOrderDescription(): string {
		if (orderProduct.productPrice?.product?.metadata?.auto_final_subtitle) {
			const subtitle = orderProduct.productPrice.product.metadata.auto_final_subtitle;
			return typeof subtitle === 'string'
				? subtitle
				: safeGetLocalizedText(subtitle as unknown as LocalizedText, locale);
		}
		return 'Event Registration';
	}

	function getOrderStatus(): {
		text: string;
		variant: 'default' | 'secondary' | 'destructive' | 'outline';
	} {
		if (orderProduct.canceled_at) {
			return { text: 'Canceled', variant: 'destructive' };
		}
		if (orderProduct.productPrice?.product?.autoFirstEvent) {
			const eventDate = new Date(orderProduct.productPrice.product.autoFirstEvent.start_at);
			if (eventDate < new Date()) {
				return { text: 'Completed', variant: 'secondary' };
			}
		}
		return { text: 'Upcoming', variant: 'default' };
	}

	// Get color for card accent
	function getCardAccentColor(): string {
		return orderProduct.productPrice?.price.color_primary_hex || '#6366f1';
	}

	// Get event details for registrations
	function getEventDetails(): {
		date: string;
		time: string;
		duration: string | null;
		subtitle: string | null;
		location: string | null;
		landmark: string | null;
	} | null {
		if (!orderProduct.productPrice?.product?.autoFirstEvent) return null;

		const eventDate = orderProduct.productPrice.product.autoFirstEvent.start_at;
		let subtitle = null;
		let duration = null;
		let location = null;
		let landmark = null;

		if (orderProduct.productPrice.product.metadata?.auto_final_subtitle) {
			const subtitleValue = orderProduct.productPrice.product.metadata.auto_final_subtitle;
			subtitle =
				typeof subtitleValue === 'string'
					? subtitleValue
					: safeGetLocalizedText(subtitleValue as unknown as LocalizedText, locale);
		}

		// Calculate duration if end time is available
		if (orderProduct.productPrice.product.autoFirstEvent.auto_end_at) {
			const startAt = parseISO(eventDate);
			const endAt = parseISO(orderProduct.productPrice.product.autoFirstEvent.auto_end_at);
			const durationMs = endAt.getTime() - startAt.getTime();
			const durationMinutes = Math.round(durationMs / (1000 * 60));
			duration = `${durationMinutes} min`;
		}

		// Get location from product_events if available
		const firstEvent = orderProduct.productPrice.product.autoFirstEvent;

		// Check if we have product_events with space information
		if (
			orderProduct.productPrice.product.product_events &&
			orderProduct.productPrice.product.product_events.length > 0
		) {
			// Find the event that matches the autoFirstEvent
			const matchingEvent = orderProduct.productPrice.product.product_events.find(
				(pe) => pe.event.id === firstEvent.id
			);

			if (matchingEvent?.event.space) {
				const spaceName = safeGetLocalizedText(matchingEvent.event.space.name_full, locale);
				const spaceShortName = safeGetLocalizedText(matchingEvent.event.space.name_short, locale);
				location = spaceName || spaceShortName;

				// Get landmark information if available
				if (matchingEvent.event.space.landmark) {
					landmark = safeGetLocalizedText(matchingEvent.event.space.landmark.title_short, locale);
				}
			} else if (matchingEvent?.event.space_id) {
				// We have a space_id but no space details
				location = 'Event location available';
			}
		} else if (firstEvent && firstEvent.space_id) {
			// Fallback: We have a space_id in the autoFirstEvent but no space details
			location = 'Event location available';
		}

		return {
			date: formatEventDate(eventDate),
			time: formatEventTime(eventDate),
			duration,
			subtitle,
			location,
			landmark
		};
	}

	// Get instructor name from metadata_wikipages
	function getInstructorName(): string | null {
		if (!orderProduct.productPrice?.product?.metadata?.metadata_wikipages) return null;

		const instructorRelation = orderProduct.productPrice.product.metadata.metadata_wikipages.find(
			(mw) => mw.relation === 'dance_instructor'
		);

		if (instructorRelation?.wikipage?.title) {
			return safeGetLocalizedText(instructorRelation.wikipage.title, locale);
		}

		return null;
	}

	const status = getOrderStatus();
	const eventDetails = getEventDetails();
	const instructorName = getInstructorName();
</script>

<Card class="overflow-hidden">
	<div
		class="h-2"
		style="background-color: {getCardAccentColor()}; background-image: linear-gradient(to right, {getCardAccentColor()}, {getCardAccentColor()}88);"
	></div>
	<CardHeader>
		<div class="flex items-start justify-between">
			<div>
				<CardTitle>{getOrderTitle()}</CardTitle>
				<CardDescription>{getOrderDescription()}</CardDescription>
			</div>
			<Badge variant={status.variant}>{status.text}</Badge>
		</div>
	</CardHeader>
	<CardContent>
		<div class="grid gap-4">
			<div class="grid grid-cols-2 gap-4">
				<div class="flex items-center gap-2">
					<CreditCard class="h-4 w-4 text-muted-foreground" />
					<span class="text-sm text-muted-foreground">Order #{orderProduct.order.nid}</span>
				</div>
				<div class="flex items-center gap-2">
					<Clock class="h-4 w-4 text-muted-foreground" />
					<span class="text-sm text-muted-foreground">
						{formatDateTime(orderProduct.created_at)}
					</span>
				</div>
			</div>

			{#if eventDetails}
				<div class="rounded-md bg-muted/50 p-3">
					<div class="mb-2 text-sm font-medium">Event Details</div>
					<div class="grid gap-2">
						<div class="flex items-start gap-2">
							<CalendarDays class="mt-0.5 h-4 w-4 text-muted-foreground" />
							<div>
								<div class="text-sm">{eventDetails.date}</div>
								<div class="text-xs text-muted-foreground">
									{eventDetails.time}
									{#if eventDetails.duration}
										• {eventDetails.duration}
									{/if}
								</div>
							</div>
						</div>

						{#if eventDetails.location}
							<div class="flex items-start gap-2">
								<MapPin class="mt-0.5 h-4 w-4 text-muted-foreground" />
								<div>
									<div class="text-sm">{eventDetails.location}</div>
									{#if eventDetails.landmark}
										<div class="text-xs text-muted-foreground">{eventDetails.landmark}</div>
									{/if}
								</div>
							</div>
						{/if}

						{#if instructorName}
							<div class="flex items-start gap-2">
								<Users class="mt-0.5 h-4 w-4 text-muted-foreground" />
								<div class="text-sm">
									Instructor: {instructorName}
								</div>
							</div>
						{/if}
					</div>
				</div>
			{/if}

			{#if orderProduct.orderPriceConsumptions && orderProduct.orderPriceConsumptions.length > 0}
				<div>
					<Separator class="my-2" />
					<div class="mb-2 text-sm font-medium">Payment Method</div>
					<div class="space-y-2">
						{#each orderProduct.orderPriceConsumptions as consumption}
							{#if consumption.orderPrice}
								<div class="text-sm">
									{consumption.cost_units}
									{consumption.cost_units === 1 ? 'point' : 'points'} from
									{safeGetLocalizedText(consumption.orderPrice.priceOption.title, locale)}
								</div>
							{/if}
						{/each}
					</div>
				</div>
			{/if}

			<div class="flex justify-end">
				<Button variant="link" href={detailsLink} class="h-auto p-0">View Details</Button>
			</div>
		</div>
	</CardContent>
</Card>
