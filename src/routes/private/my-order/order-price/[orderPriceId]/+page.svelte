<script lang="ts">
	import { page } from '$app/stores';
	import { format, parseISO, isValid } from 'date-fns';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { PageData } from './$types';
	import type { LocalizedText } from '$lib/utils/localization';
	import { Badge } from '$lib/components/ui/badge';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { Separator } from '$lib/components/ui/separator';
	import { CreditCard, Clock, ArrowLeft, MoreVertical, RefreshCw } from '@lucide/svelte';
	import OrderPriceConsumptionCard from '../../OrderPriceConsumptionCard.svelte';
	import { PageContainer } from '$lib/components/layout';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import * as AlertDialog from '$lib/components/ui/alert-dialog';
	import { toast } from 'svelte-sonner';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	const locale = getLocale();

	// Refund state
	let showRefundDialog = $state(false);
	let isRefunding = $state(false);

	// Helper function to safely extract text from localized objects
	function safeGetLocalizedText(value: any, locale: string): string {
		if (!value) return '';
		if (typeof value === 'string') return value;

		try {
			// For proper LocalizedText objects
			if (typeof value === 'object' && value !== null) {
				// If it's a raw object with locale keys
				if (value[locale]) return String(value[locale]);

				// Try to get the first non-empty value
				for (const key in value) {
					if (value[key] && typeof value[key] === 'string' && value[key].trim() !== '') {
						return value[key];
					}
				}

				// If we have any value, use the first one
				const firstValue = Object.values(value)[0];
				if (firstValue) return String(firstValue);
			}

			return getLocalizedText(value as LocalizedText, locale);
		} catch (err) {
			console.error('Failed to extract localized text:', err);
			return typeof value === 'object' ? JSON.stringify(value) : String(value);
		}
	}

	function formatDateTime(dateString: string | null): string {
		if (!dateString) return '--';
		try {
			const date = parseISO(dateString);
			if (!isValid(date)) return '--';
			return format(date, 'MMM d, yyyy h:mm a');
		} catch (error) {
			console.error('Error formatting date:', dateString, error);
			return '--';
		}
	}

	function formatCurrency(amount: number): string {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD',
			minimumFractionDigits: 2
		}).format(amount / 100);
	}

	const orderPrice = $derived(data.orderPrice);

	function getOrderTitle(): string {
		if (!orderPrice) return 'Order Details';
		return `${orderPrice.price_option_purchase_count}× ${safeGetLocalizedText(orderPrice.price_option.title, locale)}`;
	}

	function getOrderDescription(): string {
		if (!orderPrice) return '';
		const brandName = orderPrice.auto_price_option_price?.brand
			? safeGetLocalizedText(orderPrice.auto_price_option_price.brand.name_full, locale)
			: '';
		return brandName || 'Class Pass';
	}

	function getOrderStatus(): {
		text: string;
		variant: 'default' | 'secondary' | 'destructive' | 'outline';
	} {
		if (!orderPrice) return { text: 'Unknown', variant: 'outline' };

		if (orderPrice.auto_money_int_unpaid > 0) {
			return { text: 'Unpaid', variant: 'destructive' };
		}
		if (orderPrice.auto_units_available <= 0) {
			return { text: 'Used', variant: 'secondary' };
		}
		if (
			orderPrice.auto_price_option_expire_at &&
			new Date(orderPrice.auto_price_option_expire_at) < new Date()
		) {
			return { text: 'Expired', variant: 'secondary' };
		}
		return { text: 'Active', variant: 'default' };
	}

	// Get color for card accent
	function getCardAccentColor(): string {
		return orderPrice?.auto_price_option_price?.color_primary_hex || '#6366f1';
	}

	// Get valid consumptions
	const consumptions = $derived(
		orderPrice?.order_price_consumption?.filter(
			(consumption: any) => !!consumption.order_product
		) || []
	);

	// Sort consumptions by event date (closest first) or registration date (newest first)
	const sortedConsumptions = $derived(
		[...consumptions].sort((a, b) => {
			const productA = a.order_product?.product_price?.product;
			const productB = b.order_product?.product_price?.product;

			const eventDateA = productA?.auto_first_event?.start_at;
			const eventDateB = productB?.auto_first_event?.start_at;

			// Sort by event date if both have it
			if (eventDateA && eventDateB) {
				return new Date(eventDateA).getTime() - new Date(eventDateB).getTime();
			}

			// Otherwise sort by registration date (newest first)
			return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
		})
	);

	const status = getOrderStatus();
	const pageTitle = $derived(orderPrice ? getOrderTitle() : 'Class Pass Details');

	// Check if refund is available
	function isRefundAvailable(): boolean {
		if (!orderPrice) return false;

		// Must be paid
		if (orderPrice.auto_money_int_unpaid > 0) return false;

		// Must not be expired
		if (orderPrice.deal_expire_at && new Date(orderPrice.deal_expire_at) < new Date()) return false;

		// Must be within refund window (check if > 0, since 0 means no refund allowed)
		const moneyBackWindow = orderPrice.price_option?.price?.money_back_primary_window_minute;
		if (moneyBackWindow && moneyBackWindow > 0) {
			const createdAt = new Date(orderPrice.created_at);
			const refundDeadline = new Date(createdAt.getTime() + moneyBackWindow * 60 * 1000);
			if (new Date() > refundDeadline) return false;
		} else if (!moneyBackWindow || moneyBackWindow === 0) {
			// No refund window or 0 minutes means no refund allowed
			return false;
		}

		return true;
	}

	// Calculate refund details
	function getRefundDetails() {
		if (!orderPrice) return { baseAmount: 0, processingFee: 0, refundAmount: 0 };

		// Calculate processing fee (2.9% + $0.30)
		const baseAmount = orderPrice.deal_money_int;
		const processingFee = Math.round(baseAmount * 0.029 + 30);
		const refundAmount = baseAmount;

		return {
			baseAmount,
			processingFee,
			refundAmount
		};
	}

	async function handleRefundRequest() {
		if (!orderPrice?.id) return;

		isRefunding = true;

		try {
			const response = await fetch(`/private/my-order/order-price/${orderPrice.id}/refund`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ confirmed: true })
			});

			const result = await response.json();

			if (result.type === 'error') {
				throw new Error(result.error.message);
			}

			if (result.type === 'success') {
				toast.success('Refund processed successfully');
				showRefundDialog = false;
				// Optionally refresh the page or invalidate data
				window.location.reload();
			} else {
				throw new Error('Failed to process refund');
			}
		} catch (err) {
			console.error('Error processing refund:', err);
			toast.error(err instanceof Error ? err.message : 'Failed to process refund');
		} finally {
			isRefunding = false;
		}
	}

	// Check if any menu actions are available
	const hasMenuActions = $derived(isRefundAvailable());
	const refundDetails = $derived(getRefundDetails());
</script>

{#snippet content()}
	<div class="mb-6">
		<Button variant="outline" href="/private/my-order" class="gap-2">
			<ArrowLeft class="h-4 w-4" />
			Back to Orders
		</Button>
	</div>

	{#if orderPrice}
		<Card class="mb-8 overflow-hidden">
			<div
				class="h-2"
				style="background-color: {getCardAccentColor()}; background-image: linear-gradient(to right, {getCardAccentColor()}, {getCardAccentColor()}88);"
			></div>
			<CardHeader>
				<div class="flex items-start justify-between">
					<div>
						<CardTitle>{getOrderTitle()}</CardTitle>
						<CardDescription>{getOrderDescription()}</CardDescription>
					</div>
					<div class="flex items-center gap-2">
						<Badge variant={status.variant}>{status.text}</Badge>
						{#if hasMenuActions}
							<DropdownMenu.Root>
								<DropdownMenu.Trigger>
									<Button variant="ghost" size="sm" class="h-8 w-8 p-0">
										<MoreVertical class="h-4 w-4" />
										<span class="sr-only">Open menu</span>
									</Button>
								</DropdownMenu.Trigger>
								<DropdownMenu.Content align="end">
									{#if isRefundAvailable()}
										<DropdownMenu.Item onclick={() => (showRefundDialog = true)}>
											<RefreshCw class="mr-2 h-4 w-4" />
											Request Refund
										</DropdownMenu.Item>
									{/if}
								</DropdownMenu.Content>
							</DropdownMenu.Root>
						{/if}
					</div>
				</div>
			</CardHeader>
			<CardContent>
				<div class="grid gap-4">
					<div class="grid grid-cols-2 gap-4">
						<div class="flex items-center gap-2">
							<CreditCard class="h-4 w-4 text-muted-foreground" />
							<span class="text-sm text-muted-foreground">Order #{orderPrice.order.nid}</span>
						</div>
						<div class="flex items-center gap-2">
							<Clock class="h-4 w-4 text-muted-foreground" />
							<span class="text-sm text-muted-foreground">
								{orderPrice.created_at && isValid(parseISO(orderPrice.created_at))
									? formatDateTime(orderPrice.created_at)
									: '--'}
							</span>
						</div>
					</div>

					<div class="grid grid-cols-2 gap-4">
						<div>
							<div class="text-sm font-medium">Price</div>
							<div>{formatCurrency(orderPrice.deal_money_int)}</div>
						</div>
						<div>
							<div class="text-sm font-medium">Points</div>
							<div>
								{orderPrice.auto_units_available} of {orderPrice.deal_units} available
							</div>
						</div>
					</div>
				</div>
			</CardContent>
		</Card>

		<div class="mb-4">
			<h2 class="text-xl font-semibold">Usage History</h2>
			<p class="text-sm text-muted-foreground">
				{consumptions.length}
				{consumptions.length === 1 ? 'usage' : 'usages'} for this pass
			</p>
		</div>

		{#if consumptions.length > 0}
			<div class="space-y-3">
				{#each sortedConsumptions as consumption}
					<OrderPriceConsumptionCard {consumption} />
				{/each}
			</div>
		{:else}
			<Card>
				<CardContent class="p-6 text-center">
					<p class="text-muted-foreground">No usage history found for this pass.</p>
				</CardContent>
			</Card>
		{/if}
	{:else}
		<Card>
			<CardContent class="p-6 text-center">
				<p class="text-muted-foreground">Order price not found.</p>
			</CardContent>
		</Card>
	{/if}
{/snippet}

<!-- Refund Confirmation Dialog -->
<AlertDialog.Root bind:open={showRefundDialog}>
	<AlertDialog.Content>
		<AlertDialog.Header>
			<AlertDialog.Title>Request Refund</AlertDialog.Title>
			<AlertDialog.Description>
				Are you sure you want to request a refund for this class pass? This action cannot be undone.
			</AlertDialog.Description>
		</AlertDialog.Header>

		<div class="my-4 space-y-3">
			<div class="rounded-md border border-amber-200 bg-amber-50 p-4">
				<div class="flex">
					<div class="shrink-0">
						<svg class="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
							<path
								fill-rule="evenodd"
								d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
								clip-rule="evenodd"
							></path>
						</svg>
					</div>
					<div class="ml-3">
						<h3 class="text-sm font-medium text-amber-800">Processing fees are non-refundable</h3>
						<div class="mt-2 text-sm text-amber-700">
							<p>
								A processing fee of {formatCurrency(refundDetails.processingFee)} (2.9% + $0.30) charged
								by our payment processor is non-refundable. You will receive a refund of {formatCurrency(
									refundDetails.refundAmount
								)}.
							</p>
						</div>
					</div>
				</div>
			</div>

			{#if orderPrice && orderPrice.auto_units_available < orderPrice.deal_units}
				<div class="rounded-md border border-blue-200 bg-blue-50 p-4">
					<div class="flex">
						<div class="shrink-0">
							<svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
								<path
									fill-rule="evenodd"
									d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z"
									clip-rule="evenodd"
								></path>
							</svg>
						</div>
						<div class="ml-3">
							<h3 class="text-sm font-medium text-blue-800">
								Active registrations will be cancelled
							</h3>
							<div class="mt-2 text-sm text-blue-700">
								<p>
									This class pass has been used for {orderPrice.deal_units -
										orderPrice.auto_units_available} registration(s). We will automatically cancel any
									active registrations as part of the refund process.
								</p>
							</div>
						</div>
					</div>
				</div>
			{/if}
		</div>

		<AlertDialog.Footer>
			<AlertDialog.Cancel disabled={isRefunding}>Cancel</AlertDialog.Cancel>
			<AlertDialog.Action onclick={handleRefundRequest} disabled={isRefunding}>
				{#if isRefunding}
					<RefreshCw class="mr-2 h-4 w-4 animate-spin" />
					Processing...
				{:else}
					Request Refund
				{/if}
			</AlertDialog.Action>
		</AlertDialog.Footer>
	</AlertDialog.Content>
</AlertDialog.Root>

<PageContainer
	title={pageTitle}
	description={orderPrice ? getOrderDescription() : 'View details of your class pass'}
	{content}
/>
