import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { createServerClient } from '@supabase/ssr';
import type { ServerOrderPrice } from '../../types';

export const load: PageServerLoad = async ({ params, cookies, locals: { supabase, session } }) => {
	const { orderPriceId } = params;

	if (!orderPriceId) {
		throw redirect(303, '/private/my-order');
	}

	// Fetch the order price data
	const { data: orderPrice, error: orderPriceError } = await supabase
		.from('order_price')
		.select(
			`
			*,
			order:order_id!inner (
				id,
				nid
			),
			price_option:price_option_id!inner (
				id,
				title,
				price:price_id (
					money_back_primary_window_minute
				)
			),
			auto_price_option_price:auto_price_option_price_id (
				id,
				color_primary_hex,
				brand:brand_id (
					id,
					name_full
				)
			),
			order_price_consumption (
				id,
				cost_units,
				returned_units,
				created_at,
				order_product:order_product_id (
					id,
					canceled_at,
					product_price:product_price_id (
						id,
						product:product_id (
							id,
							title,
							metadata (
								auto_final_title,
								auto_final_subtitle
							),
							auto_first_event:auto_first_event_id (
								id,
								start_at,
								auto_end_at,
								duration_minute,
								space:space_id (
									id,
									name_full,
									name_short,
									landmark:landmark_id (
										id,
										title_full,
										title_short
									)
								)
							)
						)
					)
				)
			),
			order_price_payment (
				id,
				stripe_payment_intent_id,
				money_received_int,
				succeed_at
			)
		`
		)
		.eq('id', orderPriceId)
		.single();

	if (orderPriceError) {
		console.error('Error fetching order price:', orderPriceError);
		throw error(404, 'Order price not found');
	}

	return {
		orderPrice: orderPrice as any
	};
};
