import { error, json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import {
	STRIPE_SECRET_KEY,
	STRIPE_SECRET_KEY_TEST,
	EDS_STRIPE_SECRET_KEY,
	EDS_STRIPE_SECRET_KEY_TEST
} from '$env/static/private';
import Strip<PERSON> from 'stripe';

export const POST: RequestHandler = async ({
	params,
	request,
	locals: { supabase, user, brand }
}) => {
	if (!user) {
		throw error(401, 'Unauthorized');
	}

	const { orderPriceId } = params;

	if (!orderPriceId) {
		throw error(400, 'Order price ID is required');
	}

	try {
		const body = await request.json();
		const { confirmed } = body;

		if (!confirmed) {
			throw error(400, 'Refund confirmation required');
		}

		// Fetch the order price with payment and refund window information
		const { data: orderPrice, error: orderPriceError } = await supabase
			.from('order_price')
			.select(
				`
				*,
				price_option:price_option_id (
					id,
					title,
					price:price_id (
						money_back_primary_window_minute
					)
				),
				order_price_payment (*),
				order_price_consumption (
					id,
					order_product:order_product_id (
						id,
						canceled_at
					)
				)
			`
			)
			.eq('id', orderPriceId)
			.eq('payer_id', user.id)
			.single();

		if (orderPriceError) {
			console.error('Error fetching order price:', orderPriceError);
			throw error(404, 'Order price not found');
		}

		const orderPriceData = orderPrice as any;

		// Validate refund eligibility
		// 1. Must be paid (auto_money_int_unpaid <= 0)
		if (orderPriceData.auto_money_int_unpaid > 0) {
			throw error(400, 'Cannot refund unpaid order');
		}

		// 2. Must not be expired
		if (orderPriceData.deal_expire_at && new Date(orderPriceData.deal_expire_at) < new Date()) {
			throw error(400, 'Cannot refund expired order');
		}

		// 3. Must be within refund window
		const moneyBackWindow = orderPriceData.price_option?.price?.money_back_primary_window_minute;
		if (moneyBackWindow && moneyBackWindow > 0) {
			const createdAt = new Date(orderPriceData.created_at);
			const refundDeadline = new Date(createdAt.getTime() + moneyBackWindow * 60 * 1000);
			if (new Date() > refundDeadline) {
				throw error(400, 'Refund window has expired');
			}
		} else if (!moneyBackWindow || moneyBackWindow === 0) {
			// No refund window or 0 minutes means no refund allowed
			throw error(400, 'This order is not eligible for refund');
		}

		// 4. Get successful payment
		const successfulPayment = orderPriceData.order_price_payment?.find(
			(payment: any) => payment.succeed_at && payment.stripe_payment_intent_id
		);

		if (!successfulPayment) {
			throw error(400, 'No successful payment found for this order');
		}

		// 5. Check for consumed units and cancel registrations if needed
		const unconsumedUnits = orderPriceData.auto_units_available;
		const totalUnits = orderPriceData.deal_units;
		const consumedUnits = totalUnits - unconsumedUnits;

		if (consumedUnits > 0) {
			// Try to cancel all active registrations first
			const activeConsumptions = orderPriceData.order_price_consumption?.filter(
				(consumption: any) => consumption.order_product && !consumption.order_product.canceled_at
			);

			for (const consumption of activeConsumptions || []) {
				if (consumption.order_product?.id) {
					// Check if cancellation is possible (dry run)
					const { data: cancelData, error: cancelError } = await supabase.rpc(
						'cancel_order_product',
						{
							input_order_product_id: consumption.order_product.id,
							input_perform_update: false
						}
					);

					if (cancelError || !cancelData?.is_cancel_allowed) {
						throw error(
							400,
							'Cannot refund: Some registrations cannot be cancelled. Please cancel your active registrations first.'
						);
					}
				}
			}

			// If all cancellations are possible, perform them
			for (const consumption of activeConsumptions || []) {
				if (consumption.order_product?.id) {
					const { error: cancelError } = await supabase.rpc('cancel_order_product', {
						input_order_product_id: consumption.order_product.id,
						input_perform_update: true
					});

					if (cancelError) {
						console.error('Error cancelling registration:', cancelError);
						throw error(500, 'Failed to cancel registration');
					}
				}
			}
		}

		// Initialize Stripe
		const isDev = process.env.NODE_ENV !== 'production';
		const EDS_BRAND_ID = '073b8001-d1ff-19b2-9fbf-cfd5e80f2c0a';
		const isEdsBrand = brand?.id === EDS_BRAND_ID;

		let stripeKey: string;
		if (isDev) {
			stripeKey = isEdsBrand ? EDS_STRIPE_SECRET_KEY_TEST : STRIPE_SECRET_KEY_TEST;
		} else {
			stripeKey = isEdsBrand ? EDS_STRIPE_SECRET_KEY : STRIPE_SECRET_KEY;
		}

		if (!stripeKey) {
			throw error(500, 'Stripe Secret Key is not configured');
		}

		const stripe = new Stripe(stripeKey);

		// Calculate refund amount (exclude processing fees as per requirements)
		const originalAmount = successfulPayment.money_received_int;
		const baseAmount = orderPriceData.deal_money_int;
		const processingFee = originalAmount - baseAmount;
		const refundAmount = baseAmount; // Refund only the base amount, not processing fees

		// Create the Stripe refund
		const refund = await stripe.refunds.create({
			payment_intent: successfulPayment.stripe_payment_intent_id,
			amount: refundAmount,
			metadata: {
				order_price_id: orderPriceId,
				user_id: user.id,
				refund_type: 'customer_request'
			}
		});

		// Record the refund in the database
		const { error: refundRecordError } = await supabase.from('order_price_refund').insert({
			order_price_id: orderPriceId,
			money_refunded_int: refundAmount,
			money_currency_code: 'USD',
			stripe_payment_intent_id: successfulPayment.stripe_payment_intent_id
		});

		if (refundRecordError) {
			console.error('Error recording refund:', refundRecordError);
			// Continue anyway as the Stripe refund was successful
		}

		return json({
			type: 'success',
			data: {
				refund_id: refund.id,
				refund_amount: refundAmount,
				processing_fee_retained: processingFee,
				status: refund.status
			}
		});
	} catch (err) {
		console.error('Error processing refund:', err);
		if (err instanceof Response) throw err;
		throw error(500, err instanceof Error ? err.message : 'Failed to process refund');
	}
};
