<script lang="ts">
	import { format, parseISO, isValid } from 'date-fns';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { LocalizedText } from '$lib/utils/localization';
	import { Card, CardContent } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { CreditCard, Calendar } from '@lucide/svelte';

	interface Props {
		consumption: {
			id: string;
			cost_units: number;
			returned_units: number | null;
			created_at: string;
			orderPrice: {
				id: string;
				price_option_purchase_count: number;
				auto_price_option_expire_at: string | null;
				priceOption: {
					id: string;
					title: LocalizedText;
				};
				autoPriceOptionPrice?: {
					color_primary_hex?: string | null;
					payment_kind?: string;
					brand?: {
						name_full: LocalizedText;
					} | null;
				} | null;
			} | null;
		};
	}

	let { consumption }: Props = $props();

	const locale = getLocale();

	function formatDateTime(dateString: string | null): string {
		if (!dateString) return '--';
		try {
			const date = parseISO(dateString);
			if (!isValid(date)) return '--';
			return format(date, 'MMM d, yyyy h:mm a');
		} catch (error) {
			console.error('Error formatting date:', dateString, error);
			return '--';
		}
	}

	function formatDate(dateString: string | null): string {
		if (!dateString) return '--';
		try {
			const date = parseISO(dateString);
			if (!isValid(date)) return '--';
			return format(date, 'EEE, MMM d');
		} catch (error) {
			console.error('Error formatting date:', dateString, error);
			return '--';
		}
	}

	// Get consumption details
	const orderPrice = $derived(consumption.orderPrice);

	// Title
	const title = $derived(
		orderPrice?.priceOption?.title
			? `${orderPrice.price_option_purchase_count}× ${getLocalizedText(orderPrice.priceOption.title as LocalizedText, locale)}`
			: 'Unknown Pass'
	);

	// Subtitle - brand name if available
	const subtitle = $derived(
		orderPrice?.autoPriceOptionPrice?.brand
			? getLocalizedText(orderPrice.autoPriceOptionPrice.brand.name_full as LocalizedText, locale)
			: orderPrice?.autoPriceOptionPrice?.payment_kind || 'Class Pass'
	);

	// Expiration date
	const expirationDate = $derived(
		orderPrice?.auto_price_option_expire_at
			? formatDate(orderPrice.auto_price_option_expire_at)
			: null
	);

	// Registration date
	const registrationDate = $derived(formatDateTime(consumption.created_at));

	// Units
	const netUnits = $derived(consumption.cost_units - (consumption.returned_units || 0));
</script>

{#if orderPrice}
	<a href={`/private/my-order/order-price/${orderPrice.id}`} class="block no-underline">
		<Card
			class="overflow-hidden border-0 bg-neutral-50 shadow-none transition-colors hover:bg-neutral-100 dark:bg-neutral-800/20 dark:hover:bg-neutral-800/40"
		>
			<CardContent class="p-3">
				<div class="grid gap-2.5">
					<!-- Title and units section -->
					<div class="flex items-start justify-between gap-3">
						<div class="space-y-0.5">
							<h3 class="font-medium leading-tight">{title}</h3>
							{#if subtitle}
								<p class="text-xs leading-tight text-muted-foreground">{subtitle}</p>
							{/if}
						</div>
						<div class="flex shrink-0 flex-col items-end gap-0.5">
							<Badge variant="secondary" class="whitespace-nowrap">
								{netUnits}
								{netUnits === 1 ? 'point' : 'points'}
							</Badge>
							{#if consumption.returned_units}
								<div class="text-xs text-muted-foreground">
									{consumption.cost_units} - {consumption.returned_units} returned
								</div>
							{/if}
						</div>
					</div>

					<!-- Expiration details section -->
					{#if expirationDate}
						<div class="flex items-center gap-1 text-xs">
							<Calendar class="h-3 w-3 text-muted-foreground" />
							<span class="text-muted-foreground">Expires: {expirationDate}</span>
						</div>
					{/if}

					<!-- Registration date -->
					<div class="text-right text-xs text-muted-foreground/75">
						Registered: {registrationDate}
					</div>
				</div>
			</CardContent>
		</Card>
	</a>
{:else}
	<Card class="overflow-hidden border-0 bg-neutral-50 shadow-none">
		<CardContent class="p-3">
			<div class="grid gap-2.5">
				<!-- Title and units section -->
				<div class="flex items-start justify-between gap-3">
					<div class="space-y-0.5">
						<h3 class="font-medium leading-tight">Unknown Pass</h3>
					</div>
					<div class="flex shrink-0 flex-col items-end gap-0.5">
						<Badge variant="secondary" class="whitespace-nowrap">
							{netUnits}
							{netUnits === 1 ? 'point' : 'points'}
						</Badge>
						{#if consumption.returned_units}
							<div class="text-xs text-muted-foreground">
								{consumption.cost_units} - {consumption.returned_units} returned
							</div>
						{/if}
					</div>
				</div>

				<!-- Registration date -->
				<div class="text-right text-xs text-muted-foreground/75">
					Registered: {registrationDate}
				</div>
			</div>
		</CardContent>
	</Card>
{/if}
