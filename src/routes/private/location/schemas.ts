import { z } from 'zod';
import type { LocalizedText } from '$lib/utils/localization';

// Schema for localized text fields
export const localizedTextSchema = z
	.record(z.string(), z.string().optional())
	.refine((val) => Object.keys(val).length > 0, {
		message: 'At least one language must be provided'
	});

// Address schema for form fields
export const addressSchema = z.object({
	id: z.string().uuid().optional(),
	street: z.string().min(1, 'Street is required'),
	sub_locality: z.string().optional().nullable(),
	city: z.string().min(1, 'City is required'),
	sub_admin_area: z.string().optional().nullable(),
	state: z.string().min(1, 'State is required'),
	postal_code: z.string().min(1, 'Postal code is required'),
	country: z.string().min(1, 'Country is required'),
	country_code: z.string().min(1, 'Country code is required'),
	time_zone: z.string().min(1, 'Time zone is required'),
	name: z.string().optional().nullable(),
	phoneic_name: z.string().optional().nullable(),
	email: z.string().email().optional().nullable(),
	phone: z.string().optional().nullable(),
	delivery_options: z.string().optional().nullable(),
	delivery_instruction: z.string().optional().nullable()
});

// Landmark schema
export const landmarkSchema = z.object({
	id: z.string().uuid().optional(),
	title_short: localizedTextSchema,
	title_full: localizedTextSchema,
	kind: z.string().min(1, 'Landmark kind is required'),
	address_id: z.string().uuid().optional(),
	// Address fields for inline creation
	address: addressSchema.optional(),
	visibility_id: z.string().uuid().optional()
});

// Space schema
export const spaceSchema = z.object({
	id: z.string().uuid().optional(),
	landmark_id: z.string().uuid().min(1, 'Landmark is required'),
	name_short: localizedTextSchema.optional(),
	name_full: localizedTextSchema,
	capacity: z.number().int().nonnegative().nullable(),
	concurrent_event_limit: z.number().int().nonnegative().default(1),
	visibility_id: z.string().uuid().optional()
});
