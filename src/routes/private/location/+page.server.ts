import type { Actions, ServerLoad } from '@sveltejs/kit';
import type { Database } from '$lib/supabase/database.types';
import { landmarkSchema, spaceSchema } from './schemas';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { fail, error } from '@sveltejs/kit';
import type { z } from 'zod';

export const load: ServerLoad = async ({ locals, url }) => {
	try {
		const supabase = locals.supabase;
		const lid = url.searchParams.get('lid');
		const sid = url.searchParams.get('sid');
		const activeTab = url.searchParams.get('tab') || 'landmark';
		const brandId = locals.brand?.id;

		if (!brandId) {
			throw error(400, 'Brand ID is required');
		}

		// Load landmark kinds
		const { data: landmarkKinds, error: landmarkKindsError } = await supabase
			.from('landmark_kind')
			.select('*')
			.order('id');

		if (landmarkKindsError) throw landmarkKindsError;

		// Load landmarks filtered by brand
		const { data: landmarks, error: landmarksError } = await supabase
			.from('landmark')
			.select('*, address:address_id(*), landmark_kind:kind(*)')
			.eq('brand_id', brandId)
			.order('created_at', { ascending: false });

		if (landmarksError) throw landmarksError;

		// Load spaces filtered by brand
		const { data: spaces, error: spacesError } = await supabase
			.from('space')
			.select('*, landmark:landmark_id(*)')
			.eq('brand_id', brandId)
			.order('created_at', { ascending: false });

		if (spacesError) throw spacesError;

		// Get selected landmark for editing if lid is provided
		let selectedLandmark = null;
		let landmarkFormData = null;
		if (lid && lid !== 'new') {
			const { data, error: landmarkError } = await supabase
				.from('landmark')
				.select('*, address:address_id(*), landmark_kind:kind(*)')
				.eq('id', lid)
				.eq('brand_id', brandId)
				.single();

			if (landmarkError) throw landmarkError;
			selectedLandmark = data;

			// Prepare form data for editing
			if (selectedLandmark) {
				landmarkFormData = {
					id: selectedLandmark.id,
					title_short: selectedLandmark.title_short || {},
					title_full: selectedLandmark.title_full || {},
					kind: selectedLandmark.kind,
					address_id: selectedLandmark.address_id || undefined,
					// Include address data if exists
					address: selectedLandmark.address
						? {
								id: selectedLandmark.address.id,
								street: selectedLandmark.address.street || '',
								sub_locality: selectedLandmark.address.sub_locality || null,
								city: selectedLandmark.address.city || '',
								sub_admin_area: selectedLandmark.address.sub_admin_area || null,
								state: selectedLandmark.address.state || '',
								postal_code: selectedLandmark.address.postal_code || '',
								country: selectedLandmark.address.country || '',
								country_code: selectedLandmark.address.country_code || '',
								time_zone: selectedLandmark.address.time_zone || '',
								name: selectedLandmark.address.name || null,
								phoneic_name: selectedLandmark.address.phoneic_name || null,
								email: selectedLandmark.address.email || null,
								phone: selectedLandmark.address.phone || null,
								delivery_options: selectedLandmark.address.delivery_options || null,
								delivery_instruction: selectedLandmark.address.delivery_instruction || null
							}
						: undefined,
					visibility_id: selectedLandmark.visibility_id || undefined
				};
			}
		}

		// Get selected space for editing if sid is provided
		let selectedSpace = null;
		let spaceFormData = null;
		if (sid && sid !== 'new') {
			const { data, error: spaceError } = await supabase
				.from('space')
				.select('*, landmark:landmark_id(*)')
				.eq('id', sid)
				.eq('brand_id', brandId)
				.single();

			if (spaceError) throw spaceError;
			selectedSpace = data;

			// Prepare form data for editing
			if (selectedSpace) {
				spaceFormData = {
					id: selectedSpace.id,
					name_full: selectedSpace.name_full || {},
					name_short: selectedSpace.name_short || {},
					capacity: selectedSpace.capacity,
					concurrent_event_limit: selectedSpace.concurrent_event_limit,
					landmark_id: selectedSpace.landmark_id,
					visibility_id: selectedSpace.visibility_id || undefined
				};
			}
		}

		// Set up the forms with initial data if editing
		const landmarkForm = await superValidate(landmarkFormData, zod(landmarkSchema));
		const spaceForm = await superValidate(spaceFormData, zod(spaceSchema));

		return {
			landmarks,
			spaces,
			landmarkKinds,
			landmarkForm,
			spaceForm,
			selectedLandmark,
			selectedSpace,
			activeTab
		};
	} catch (e) {
		console.error('Error loading locations data:', e);
		throw error(500, 'Failed to load locations data');
	}
};

export const actions: Actions = {
	upsertLandmark: async ({ request, locals, url }) => {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;
		const lid = url.searchParams.get('lid');
		const userId = locals.user?.id;

		if (!brandId) {
			return fail(400, { message: 'Brand ID is required' });
		}

		if (!userId) {
			return fail(400, { message: 'User must be authenticated' });
		}

		const form = await superValidate(request, zod(landmarkSchema));

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			// Validate required fields
			if (!form.data.kind) {
				form.errors.kind = ['Landmark kind is required'];
				return fail(400, { form });
			}

			let addressId = form.data.address_id;

			// Handle address creation/update if address data is provided
			if (form.data.address) {
				const addressData = form.data.address;

				// First, check if an identical address already exists
				const { data: existingAddress, error: checkError } = await supabase
					.from('address')
					.select('id')
					.eq('street', addressData.street)
					.eq('city', addressData.city)
					.eq('state', addressData.state)
					.eq('postal_code', addressData.postal_code)
					.eq('country', addressData.country)
					.eq('country_code', addressData.country_code)
					.eq('time_zone', addressData.time_zone)
					.is('sub_locality', addressData.sub_locality || null)
					.is('sub_admin_area', addressData.sub_admin_area || null)
					.maybeSingle();

				if (checkError) {
					console.error('Address check error:', checkError);
					form.errors._errors = ['Failed to check existing address: ' + checkError.message];
					return fail(400, { form });
				}

				if (existingAddress) {
					// Use existing address
					addressId = existingAddress.id;
				} else {
					// Create new address
					const { data: newAddress, error: addressError } = await supabase
						.from('address')
						.insert({
							street: addressData.street,
							sub_locality: addressData.sub_locality || null,
							city: addressData.city,
							sub_admin_area: addressData.sub_admin_area || null,
							state: addressData.state,
							postal_code: addressData.postal_code,
							country: addressData.country,
							country_code: addressData.country_code,
							time_zone: addressData.time_zone,
							name: addressData.name || null,
							phoneic_name: addressData.phoneic_name || null,
							email: addressData.email || null,
							phone: addressData.phone || null,
							delivery_options: addressData.delivery_options || null,
							delivery_instruction: addressData.delivery_instruction || null,
							creator_id: userId
						})
						.select()
						.single();

					if (addressError) {
						console.error('Address creation error:', addressError);
						form.errors._errors = ['Failed to create address: ' + addressError.message];
						return fail(400, { form });
					}

					addressId = newAddress.id;
				}
			}

			// Handle landmark using upsert
			const landmarkData = {
				...form.data,
				id: lid || form.data.id, // Use lid from URL if available
				brand_id: brandId,
				address_id: addressId
			};

			// Remove address field as it's not part of landmark table
			delete landmarkData.address;

			// Check if this is an update or create operation
			const isUpdate = !!lid;

			const { error: landmarkError } = await supabase.from('landmark').upsert(landmarkData, {
				onConflict: 'id'
			});

			if (landmarkError) {
				console.error('Landmark upsert error:', landmarkError);
				form.errors._errors = ['Failed to save landmark: ' + landmarkError.message];
				return fail(400, { form });
			}

			return {
				form,
				success: true,
				message: isUpdate ? 'Landmark updated successfully!' : 'Landmark created successfully!'
			};
		} catch (e) {
			console.error('Error upserting landmark:', e);
			return fail(500, {
				form,
				message: 'An unexpected error occurred while saving the landmark'
			});
		}
	},

	upsertSpace: async ({ request, locals, url }) => {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;
		const sid = url.searchParams.get('sid');

		if (!brandId) {
			return fail(400, { message: 'Brand ID is required' });
		}

		const form = await superValidate(request, zod(spaceSchema));

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			// Validate required fields
			if (!form.data.landmark_id) {
				form.errors.landmark_id = ['Landmark is required'];
				return fail(400, { form });
			}

			// Create the space data with brand_id and sid from URL
			const spaceData = {
				...form.data,
				id: sid || form.data.id, // Use sid from URL if available
				brand_id: brandId
			};

			// Check if this is an update or create operation
			const isUpdate = !!sid;

			// Use upsert for space
			const { error: spaceError } = await supabase.from('space').upsert(spaceData, {
				onConflict: 'id'
			});

			if (spaceError) {
				console.error('Space upsert error:', spaceError);
				form.errors._errors = ['Failed to save space: ' + spaceError.message];
				return fail(400, { form });
			}

			return {
				form,
				success: true,
				message: isUpdate ? 'Space updated successfully!' : 'Space created successfully!'
			};
		} catch (e) {
			console.error('Error upserting space:', e);
			return fail(500, {
				form,
				message: 'An unexpected error occurred while saving the space'
			});
		}
	}
};
