<!-- src/routes/private/location/LandmarkModal.svelte -->
<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import type { Database } from '$lib/supabase/database.types';
	import type { SuperValidated } from 'sveltekit-superforms';
	import { landmarkSchema } from './schemas';
	import type { z } from 'zod';
	import {
		ensureLocalizedText,
		getLocalizedText,
		type LocalizedText
	} from '$lib/utils/localization';
	import ResponsiveModal from '$lib/components/shared/ResponsiveModal.svelte';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import AddressAutofillForm from '$lib/components/address/AddressAutofillForm.svelte';
	import { superForm } from 'sveltekit-superforms';
	import { Control, Label, Field, FieldErrors } from 'formsnap';
	import { goto, invalidate } from '$app/navigation';
	import { page } from '$app/state';
	import * as Select from '$lib/components/ui/select';
	import { Input } from '$lib/components/ui/input';
	import type { Json } from '$lib/supabase/database.types';
	import { getLocale } from '$lib/paraglide/runtime';

	// Define types from database
	type LandmarkRow = Database['public']['Tables']['landmark']['Row'] & {
		address: Database['public']['Tables']['address']['Row'];
		landmark_kind: Database['public']['Tables']['landmark_kind']['Row'];
	};

	interface Props {
		landmark: LandmarkRow | null;
		landmarkKinds: Database['public']['Tables']['landmark_kind']['Row'][];
		landmarkForm: SuperValidated<z.infer<typeof landmarkSchema>>;
		open: boolean;
	}

	let { landmark, landmarkKinds, landmarkForm, open }: Props = $props();

	// Configure landmark form
	const landmarkSuperForm = superForm(landmarkForm, {
		applyAction: false,
		taintedMessage: null,
		resetForm: false,
		dataType: 'json',
		onResult: ({ result }) => {
			// Close modal on successful submission
			if (result.type === 'success') {
				// Clear URL parameters and close the modal
				goto(page.url.pathname, {
					replaceState: true,
					keepFocus: true
				});
				// Invalidate the landmarks data to refresh the list
				invalidate('supabase:db:landmarks');
			} else if (result.type === 'failure') {
				// Handle server-side validation errors
				console.error('Form submission failure:', result);
				// Set a message to display the error
				landmarkSuperForm.message.set({
					type: 'error',
					text: 'Failed to save landmark. Please check the form for errors.'
				});
			} else if (result.type === 'error') {
				console.error('Form submission error:', result.error);
				landmarkSuperForm.message.set({
					type: 'error',
					text: result.error?.message || 'An unexpected error occurred'
				});
			}
		},
		onError: ({ result }) => {
			console.error('Form validation error:', result);
			landmarkSuperForm.message.set({
				type: 'error',
				text: 'Please correct the errors in the form'
			});
		}
	});

	// Destructure forms
	const {
		enhance: enhanceLandmark,
		submitting: submittingLandmark,
		form: landmarkFormData,
		errors: landmarkErrors,
		message: landmarkMessage
	} = landmarkSuperForm;

	// Determine modal title based on edit/create mode
	let modalTitle = $derived(() => {
		return landmark ? 'Edit Landmark' : 'Create Landmark';
	});

	// Determine modal description based on edit/create mode
	let modalDescription = $derived(() => {
		if (landmark) {
			return `Editing landmark "${getLocalizedText(landmark.title_short)}" created at ${new Date(
				landmark.created_at
			).toLocaleDateString()}`;
		}
		return 'Create a new landmark';
	});

	const currentLocale = getLocale();

	function handleModalClose(open: boolean) {
		if (!open) {
			goto(page.url.pathname, { replaceState: true, keepFocus: true });
		}
	}
</script>

{#snippet footerButtons()}
	<div class="flex justify-end gap-2">
		<form data-sveltekit-replacestate data-sveltekit-noscroll class="inline-block">
			<Button variant="outline" type="submit">Cancel</Button>
		</form>
		<Button type="submit" form="landmark-form" disabled={$submittingLandmark}>
			{$submittingLandmark ? 'Saving...' : 'Save Landmark'}
		</Button>
	</div>
{/snippet}

<ResponsiveModal
	title={typeof modalTitle === 'function' ? modalTitle() : modalTitle}
	description={typeof modalDescription === 'function' ? modalDescription() : modalDescription}
	{open}
	onOpenChange={handleModalClose}
	footer={footerButtons}
>
	<form
		id="landmark-form"
		method="POST"
		action={landmark ? `?/upsertLandmark&lid=${landmark.id}` : '?/upsertLandmark'}
		use:enhanceLandmark
		class="space-y-4 pt-4"
	>
		{#if $landmarkMessage}
			<div
				class={`mb-4 rounded p-3 ${$landmarkMessage.type === 'error' ? 'border-red-400 bg-red-100 text-red-700' : 'border-green-400 bg-green-100 text-green-700'}`}
			>
				{$landmarkMessage.text}
			</div>
		{/if}

		{#if $landmarkErrors._errors && $landmarkErrors._errors.length > 0}
			<div class="mb-4 rounded border border-red-400 bg-red-100 p-3 text-red-700">
				<p class="font-semibold">Please fix the following errors:</p>
				<ul class="ml-4 list-disc">
					{#each $landmarkErrors._errors as error}
						<li>{error}</li>
					{/each}
				</ul>
			</div>
		{/if}

		<div class="grid gap-4">
			<LocalizedTextControl form={landmarkSuperForm} name="title_short" label="Short Name" />

			<LocalizedTextControl form={landmarkSuperForm} name="title_full" label="Full Name" />

			<Field form={landmarkSuperForm} name="kind">
				<Control>
					{#snippet children({ props })}
						<Label>Kind</Label>
						<Select.Root type="single" bind:value={$landmarkFormData.kind} name={props.name}>
							<Select.Trigger
								class={`w-full ${$landmarkErrors.kind ? 'border-red-500' : ''}`}
								{...props}
							>
								{$landmarkFormData.kind
									? landmarkKinds.find((k) => k.id === $landmarkFormData.kind)?.id
									: 'Select kind'}
							</Select.Trigger>
							<Select.Content>
								<Select.Group>
									<Select.GroupHeading>Kinds</Select.GroupHeading>
									{#each landmarkKinds as k}
										<Select.Item value={k.id} label={k.id}>{k.id}</Select.Item>
									{/each}
								</Select.Group>
							</Select.Content>
						</Select.Root>
					{/snippet}
				</Control>
				<FieldErrors class="mt-1 text-xs text-red-500" />
			</Field>

			<!-- Address Section -->
			<div class="space-y-3">
				<h3 class="text-lg font-semibold">Address</h3>
				<p class="text-sm text-muted-foreground">
					Start typing an address and select from the suggestions
				</p>
				<AddressAutofillForm form={landmarkSuperForm} />
			</div>
		</div>
	</form>
</ResponsiveModal>
