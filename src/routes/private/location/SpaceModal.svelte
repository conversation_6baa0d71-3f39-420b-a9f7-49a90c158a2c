<!-- src/routes/private/location/SpaceModal.svelte -->
<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import type { Database } from '$lib/supabase/database.types';
	import type { SuperValidated } from 'sveltekit-superforms';
	import { spaceSchema } from './schemas';
	import type { z } from 'zod';
	import {
		ensureLocalizedText,
		getLocalizedText,
		type LocalizedText
	} from '$lib/utils/localization';
	import ResponsiveModal from '$lib/components/shared/ResponsiveModal.svelte';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import { superForm } from 'sveltekit-superforms';
	import { Control, Label, Field, FieldErrors } from 'formsnap';
	import { goto, invalidate } from '$app/navigation';
	import { page } from '$app/state';
	import * as Select from '$lib/components/ui/select';
	import { Input } from '$lib/components/ui/input';
	import type { Json } from '$lib/supabase/database.types';
	import { getLocale } from '$lib/paraglide/runtime';

	// Define types from database
	type LandmarkRow = Database['public']['Tables']['landmark']['Row'] & {
		address: Database['public']['Tables']['address']['Row'];
		landmark_kind: Database['public']['Tables']['landmark_kind']['Row'];
	};

	type SpaceRow = Database['public']['Tables']['space']['Row'] & {
		landmark: Database['public']['Tables']['landmark']['Row'];
	};

	interface Props {
		space: SpaceRow | null;
		spaceForm: SuperValidated<z.infer<typeof spaceSchema>>;
		landmarks: LandmarkRow[];
		spaces: SpaceRow[];
		open: boolean;
	}

	let { space, spaceForm, landmarks, spaces, open }: Props = $props();

	// Configure space form
	const spaceSuperForm = superForm(spaceForm, {
		applyAction: false,
		taintedMessage: null,
		resetForm: false,
		dataType: 'json',
		onResult: ({ result }) => {
			// Close modal on successful submission
			if (result.type === 'success') {
				// Clear URL parameters and close the modal
				goto(page.url.pathname, {
					replaceState: true,
					keepFocus: true
				});
				// Invalidate the spaces data to refresh the list
				invalidate('supabase:db:spaces');
			} else if (result.type === 'failure') {
				// Handle server-side validation errors
				console.error('Form submission failure:', result);
				// Set a message to display the error
				spaceSuperForm.message.set({
					type: 'error',
					text: 'Failed to save space. Please check the form for errors.'
				});
			} else if (result.type === 'error') {
				console.error('Form submission error:', result.error);
				spaceSuperForm.message.set({
					type: 'error',
					text: result.error?.message || 'An unexpected error occurred'
				});
			}
		},
		onError: ({ result }) => {
			console.error('Form validation error:', result);
			spaceSuperForm.message.set({
				type: 'error',
				text: 'Please correct the errors in the form'
			});
		}
	});

	// Destructure forms
	const {
		enhance: enhanceSpace,
		submitting: submittingSpace,
		form: spaceFormData,
		errors: spaceErrors,
		message: spaceMessage
	} = spaceSuperForm;

	// Determine modal title based on edit/create mode
	let modalTitle = $derived(() => {
		return space ? 'Edit Space' : 'Create Space';
	});

	// Determine modal description based on edit/create mode
	let modalDescription = $derived(() => {
		if (space) {
			return `Editing space "${getLocalizedText(space.name_full)}" created at ${new Date(
				space.created_at
			).toLocaleDateString()}`;
		}
		return 'Create a new space';
	});

	const currentLocale = getLocale();

	function handleModalClose(open: boolean) {
		if (!open) {
			goto(page.url.pathname, { replaceState: true, keepFocus: true });
		}
	}
</script>

{#snippet footerButtons()}
	<div class="flex justify-end gap-2">
		<form data-sveltekit-replacestate data-sveltekit-noscroll class="inline-block">
			<Button variant="outline" type="submit">Cancel</Button>
		</form>
		<Button type="submit" form="space-form" disabled={$submittingSpace}>
			{$submittingSpace ? 'Saving...' : 'Save Space'}
		</Button>
	</div>
{/snippet}

<ResponsiveModal
	title={typeof modalTitle === 'function' ? modalTitle() : modalTitle}
	description={typeof modalDescription === 'function' ? modalDescription() : modalDescription}
	{open}
	onOpenChange={handleModalClose}
	footer={footerButtons}
>
	<form
		id="space-form"
		method="POST"
		action={space ? `?/upsertSpace&sid=${space.id}` : '?/upsertSpace'}
		use:enhanceSpace
		class="space-y-4 pt-4"
	>
		{#if $spaceMessage}
			<div
				class={`mb-4 rounded p-3 ${$spaceMessage.type === 'error' ? 'border-red-400 bg-red-100 text-red-700' : 'border-green-400 bg-green-100 text-green-700'}`}
			>
				{$spaceMessage.text}
			</div>
		{/if}

		{#if Object.keys($spaceErrors).length > 0}
			<div class="mb-4 rounded border border-red-400 bg-red-100 p-3 text-red-700">
				<p class="font-semibold">Please fix the following errors:</p>
				<ul class="ml-4 list-disc">
					{#if $spaceErrors.name_full}
						<li>Full name is required</li>
					{/if}
					{#if $spaceErrors.landmark_id}
						<li>Landmark is required</li>
					{/if}
					{#if $spaceErrors.capacity && $spaceErrors.capacity.includes('less than')}
						<li>Capacity must be a positive number</li>
					{/if}
					{#if $spaceErrors.concurrent_event_limit && $spaceErrors.concurrent_event_limit.includes('less than')}
						<li>Concurrent event limit must be a positive number</li>
					{/if}
					{#if $spaceErrors._errors}
						{#each $spaceErrors._errors as error}
							<li>{error}</li>
						{/each}
					{/if}
				</ul>
			</div>
		{/if}

		<div class="grid gap-4">
			<LocalizedTextControl form={spaceSuperForm} name="name_full" label="Full Name" />

			<LocalizedTextControl form={spaceSuperForm} name="name_short" label="Short Name" />

			<Field form={spaceSuperForm} name="landmark_id">
				<Control>
					{#snippet children({ props })}
						<Label>Landmark</Label>
						<Select.Root type="single" bind:value={$spaceFormData.landmark_id} name={props.name}>
							<Select.Trigger
								class={`w-full ${$spaceErrors.landmark_id ? 'border-red-500' : ''}`}
								{...props}
							>
								{$spaceFormData.landmark_id
									? getLocalizedText(
											landmarks.find((l) => l.id === $spaceFormData.landmark_id)
												?.title_short as LocalizedText
										)
									: 'Select a landmark'}
							</Select.Trigger>
							<Select.Content>
								<Select.Group>
									<Select.GroupHeading>Landmarks</Select.GroupHeading>
									{#each landmarks as l}
										<Select.Item
											value={l.id}
											label={getLocalizedText(l.title_short as LocalizedText)}
											>{getLocalizedText(l.title_short as LocalizedText)}</Select.Item
										>
									{/each}
								</Select.Group>
							</Select.Content>
						</Select.Root>
					{/snippet}
				</Control>
				<FieldErrors />
			</Field>

			<Field form={spaceSuperForm} name="capacity">
				<Control>
					{#snippet children({ props })}
						<Label>Capacity</Label>
						<Input
							type="number"
							min="0"
							placeholder="Enter capacity"
							class={$spaceErrors.capacity ? 'border-red-500' : ''}
							{...props}
						/>
					{/snippet}
				</Control>
				<FieldErrors />
			</Field>

			<Field form={spaceSuperForm} name="concurrent_event_limit">
				<Control>
					{#snippet children({ props })}
						<Label>Concurrent Event Limit</Label>
						<Input
							type="number"
							min="0"
							placeholder="Enter concurrent event limit"
							class={$spaceErrors.concurrent_event_limit ? 'border-red-500' : ''}
							{...props}
						/>
					{/snippet}
				</Control>
				<FieldErrors />
			</Field>
		</div>
	</form>
</ResponsiveModal>
