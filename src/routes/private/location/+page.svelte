<!-- src/routes/private/location/+page.svelte -->
<script lang="ts">
	import { invalidate } from '$app/navigation';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$lib/components/ui/button';
	import LandmarkModal from './LandmarkModal.svelte';
	import SpaceModal from './SpaceModal.svelte';
	import type { Database } from '$lib/supabase/database.types';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import DataTable from '$lib/components/shared/DataTable.svelte';
	import { PageContainer } from '$lib/components/layout';
	import {
		createSelectColumn,
		createLocalizedColumn,
		createSortableColumn,
		createDateColumn,
		createActionsColumn
	} from '$lib/components/shared/table-utils';
	import type { ColumnDef } from '@tanstack/table-core';
	import { getLocale } from '$lib/paraglide/runtime';
	import * as Tabs from '$lib/components/ui/tabs';
	import type { Json } from '$lib/supabase/database.types';

	// Define types from database
	type LandmarkRow = Database['public']['Tables']['landmark']['Row'] & {
		address: Database['public']['Tables']['address']['Row'];
		landmark_kind: Database['public']['Tables']['landmark_kind']['Row'];
	};

	type SpaceRow = Database['public']['Tables']['space']['Row'] & {
		landmark: Database['public']['Tables']['landmark']['Row'];
	};

	let { data } = $props();
	let {
		landmarks,
		spaces,
		landmarkKinds,
		landmarkForm,
		spaceForm,
		selectedLandmark,
		selectedSpace,
		activeTab
	} = $derived(data);

	let errorMessage = $state<string | null>(null);
	const currentLocale = getLocale();

	// URL-driven modal state
	let showLandmarkModal = $derived.by(() => {
		const lid = page.url.searchParams.get('lid');
		return !!lid;
	});

	let showSpaceModal = $derived.by(() => {
		const sid = page.url.searchParams.get('sid');
		return !!sid;
	});

	// Active view state (landmarks or spaces)
	let activeView = $state('landmarks');

	// Open create modals
	function openCreateLandmarkModal() {
		goto('?lid=new', { keepFocus: true });
	}

	function openCreateSpaceModal() {
		// Get current brand from URL or use default if needed
		const brandId = page.url.searchParams.get('brand') || '';
		goto(`?sid=new${brandId ? `&brand=${brandId}` : ''}`, { keepFocus: true });
	}

	// Open edit modals
	function openEditLandmarkModal(landmark: LandmarkRow) {
		goto(`?lid=${landmark.id}`, { keepFocus: true });
	}

	function openEditSpaceModal(space: SpaceRow) {
		// Get current brand from URL or use default if needed
		const brandId = page.url.searchParams.get('brand') || '';
		goto(`?sid=${space.id}${brandId ? `&brand=${brandId}` : ''}`, { keepFocus: true });
	}

	// Landmark table columns
	const landmarkColumns: ColumnDef<LandmarkRow, any>[] = [
		createLocalizedColumn<LandmarkRow>(
			'title',
			'Title',
			(row) => row.title_short as Json,
			currentLocale
		),
		createSortableColumn<LandmarkRow>(
			'address',
			'Address',
			(row) => row.address?.auto_normalized_address_local || '–'
		),
		createSortableColumn<LandmarkRow>('kind', 'Kind', (row) => row.kind || '–'),
		createDateColumn<LandmarkRow>('created_at', 'Created At', (row) => row.created_at),
		createActionsColumn<LandmarkRow>({
			label: 'Edit',
			onClick: (row) => openEditLandmarkModal(row)
		})
	];

	// Space table columns
	const spaceColumns: ColumnDef<SpaceRow, any>[] = [
		createLocalizedColumn<SpaceRow>('name', 'Name', (row) => row.name_full as Json, currentLocale),
		createLocalizedColumn<SpaceRow>(
			'landmark',
			'Landmark',
			(row) => row.landmark?.title_short as Json,
			currentLocale
		),
		createSortableColumn<SpaceRow>(
			'capacity',
			'Capacity',
			(row) => row.capacity?.toString() || '–'
		),
		createDateColumn<SpaceRow>('created_at', 'Created At', (row) => row.created_at),
		createActionsColumn<SpaceRow>({
			label: 'Edit',
			onClick: (row) => openEditSpaceModal(row)
		})
	];
</script>

{#snippet actions()}
	<div class="flex items-center gap-4">
		<Tabs.Root bind:value={activeView} class="w-[300px]">
			<Tabs.List class="grid w-full grid-cols-2">
				<Tabs.Trigger value="landmarks">Landmarks</Tabs.Trigger>
				<Tabs.Trigger value="spaces">Spaces</Tabs.Trigger>
			</Tabs.List>
		</Tabs.Root>

		<Button onclick={activeView === 'landmarks' ? openCreateLandmarkModal : openCreateSpaceModal}>
			Create New {activeView === 'landmarks' ? 'Landmark' : 'Space'}
		</Button>
	</div>
{/snippet}

{#snippet content()}
	{#if errorMessage}
		<div class="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700" role="alert">
			{errorMessage}
		</div>
	{/if}

	<Tabs.Root bind:value={activeView}>
		<Tabs.Content value="landmarks" class="w-full">
			<DataTable data={landmarks} columns={landmarkColumns} />
		</Tabs.Content>

		<Tabs.Content value="spaces" class="w-full">
			<DataTable data={spaces} columns={spaceColumns} />
		</Tabs.Content>
	</Tabs.Root>

	<LandmarkModal
		landmark={selectedLandmark}
		{landmarkKinds}
		{landmarkForm}
		open={showLandmarkModal}
	/>

	<SpaceModal space={selectedSpace} {spaceForm} {landmarks} {spaces} open={showSpaceModal} />
{/snippet}

<PageContainer title="Locations" description="Manage location data" {actions} {content} />
