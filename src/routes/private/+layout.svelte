<script lang="ts">
	import DotGridPattern from '$lib/components/DotGridPattern.svelte';
	import type { Snippet } from 'svelte';
	import { cn } from '$lib/utils';

	interface Props {
		children: Snippet;
	}

	let { children }: Props = $props();
</script>

<div class="relative min-h-screen w-full">
	<!-- Render children first so the pattern appears on top -->
	{@render children()}

	<!-- Pattern container with fixed height to limit to upper corner -->
	<div
		class="pointer-events-none absolute left-0 top-0 z-0 w-full overflow-hidden"
		style:height="250px"
	>
		<DotGridPattern
			width={20}
			height={20}
			x={-1}
			y={-1}
			className={cn(
				'mask-[linear-gradient(to_bottom_right,white,transparent_30%,transparent)]'
			)}
		/>
	</div>
</div>
