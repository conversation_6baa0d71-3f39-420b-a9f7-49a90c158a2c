import { z } from 'zod';
import type { LocalizedText } from '$lib/utils/localization';
import type { SpotifyArtist, SpotifyTrack, VideoKind } from './types';

// Define Spotify track schema for validation
const spotifyTrackSchema = z.object({
	id: z.string(),
	name: z.string(),
	artists: z.array(
		z.object({
			id: z.string(),
			name: z.string()
		})
	),
	album: z.object({
		id: z.string(),
		name: z.string(),
		images: z.array(
			z.object({
				url: z.string(),
				height: z.number(),
				width: z.number()
			})
		)
	}),
	preview_url: z.string().nullable()
});

// Define Spotify artist schema for validation
const spotifyArtistSchema = z.object({
	id: z.string(),
	name: z.string(),
	images: z
		.array(
			z.object({
				url: z.string(),
				height: z.number(),
				width: z.number()
			})
		)
		.optional()
});

export const formSchema = z.object({
	// Use LocalizedText for title and description to support multiple languages
	title: z.record(z.string().optional()).default({ en: '' }),
	description: z.record(z.string().optional()).default({ en: '' }),
	// Required dance_level with error message
	dance_level: z.string().min(1, {
		message: 'Dance level is required'
	}),
	// Required dance_genre with error message
	dance_genre: z.string().min(1, {
		message: 'Dance genre is required'
	}),
	video_kind: z
		.enum(['content_reference', 'dance_reference', 'music_reference'] as const)
		.nullable()
		.default(null),
	video_url: z.string().optional(),
	video_range: z
		.object({
			start: z.number().optional(),
			end: z.number().optional()
		})
		.optional(),
	cover_url: z.string().optional(),
	events: z
		.array(
			z.object({
				id: z.string(),
				start_time: z.string(),
				end_time: z.string(),
				// Required space_id with validation
				space_id: z.string().min(1, {
					message: 'Space selection is required'
				}),
				// At least one assigned instructor is required
				assigned_instructors: z
					.array(
						z.object({
							id: z.string(),
							name: z.string(),
							image_url: z.string().optional()
						})
					)
					.min(1, {
						message: 'At least one instructor must be assigned'
					})
			})
		)
		.min(1, {
			message: 'At least one time slot must be selected'
		}),
	// Add these fields required by child components
	tracks: z.array(spotifyTrackSchema).default([]),
	artists: z.array(spotifyArtistSchema).default([]),
	// Required product form type
	product_form_type: z.enum(['bundled', 'individual', 'trial'] as const).default('bundled'),

	// Advanced product price configurations with validation
	product_form_factor: z
		.array(
			z.object({
				product_type: z.enum(['bundled', 'individual', 'trial_first', 'trial_rest']),
				// At least one price with non-null cost_units is required
				prices: z
					.array(
						z.object({
							price_id: z.string().min(1, {
								message: 'Price selection is required'
							}),
							cost_units: z.number().min(0.01, {
								message: 'Cost must be greater than 0'
							})
						})
					)
					.min(1, {
						message: 'At least one price is required'
					})
			})
		)
		.min(1, {
			message: 'Product form configuration is required'
		})
});

export type FormSchema = z.infer<typeof formSchema>;
