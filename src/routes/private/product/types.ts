import type { LocalizedText } from '$lib/utils/localization';
import type { Database } from '$lib/supabase/database.types';

export interface TokenSearchItem {
	id: string;
	title: string;
	subtitle?: string;
	imageUrl?: string;
	data?: SpotifyArtist | SpotifyTrack | Database['public']['Tables']['wikipage']['Row'];
}

export interface SpotifyExternalUrls {
	spotify: string;
}

export interface SpotifyImage {
	url: string;
	height: number | null;
	width: number | null;
}

export interface SpotifyFollowers {
	href: string | null;
	total: number;
}

export interface SpotifyRestrictions {
	reason: string;
}

export interface SpotifyArtist {
	external_urls: SpotifyExternalUrls;
	followers?: SpotifyFollowers;
	genres?: string[];
	href: string;
	id: string;
	images?: SpotifyImage[];
	name: string;
	popularity?: number;
	type: 'artist';
	uri: string;
}

export interface SpotifyAlbum {
	album_type: string;
	total_tracks: number;
	available_markets?: string[];
	external_urls: SpotifyExternalUrls;
	href: string;
	id: string;
	images: SpotifyImage[];
	name: string;
	release_date?: string;
	release_date_precision?: string;
	restrictions?: SpotifyRestrictions;
	type: 'album';
	uri: string;
	artists: SpotifyArtist[];
}

export interface SpotifyTrack {
	album: SpotifyAlbum;
	artists: SpotifyArtist[];
	available_markets?: string[];
	disc_number: number;
	duration_ms: number;
	explicit: boolean;
	external_urls: SpotifyExternalUrls;
	href: string;
	id: string;
	is_playable?: boolean;
	restrictions?: SpotifyRestrictions;
	name: string;
	popularity: number;
	preview_url: string | null;
	track_number: number;
	type: 'track';
	uri: string;
	is_local: boolean;
}

export interface SpotifyPaging<T> {
	href: string;
	items: T[];
	limit: number;
	next: string | null;
	offset: number;
	previous: string | null;
	total: number;
}

export interface SpotifySearchResponse {
	tracks?: SpotifyPaging<SpotifyTrack>;
	artists?: SpotifyPaging<SpotifyArtist>;
	albums?: SpotifyPaging<SpotifyAlbum>;
}

// Define WikiPage interface
export interface WikiPage {
	id: string;
	title: string;
	description?: string;
	image_url?: string;
	type: 'choreographer' | 'dance_style' | 'dance_studio';
	created_at?: string;
	updated_at?: string;
}

export type DanceLevel = 'beginner' | 'intermediate' | 'advanced' | 'all-levels' | 'pro';

export type VideoType = 'teaching' | 'style' | 'none';

export type TimeSlot = {
	id: string;
	dayOfWeek: 0 | 1 | 2 | 3 | 4 | 5 | 6; // 0 is Sunday
	startTime: string; // HH:mm format
	endTime: string; // HH:mm format
	location: string;
	studio: string;
	instructorId?: string;
	instructorName?: string;
};

export type VideoKind = 'content_reference' | 'dance_reference' | 'music_reference' | null;

export type VideoRange = {
	start: number; // in seconds
	end: number; // in seconds
};

export type ClassMetadata = {
	id?: string;
	title: string;
	description: string;
	dance_level: DanceLevel;
	video_kind: VideoKind;
	video_url?: string;
	video_file?: File;
	video_range?: VideoRange;
	cover_url?: string;
	cover_file?: File;
	tracks: SpotifyTrack[];
	artists: SpotifyArtist[];
	choreographers: WikiPage[];
	time_slots: TimeSlot[];
	created_at?: string;
	updated_at?: string;
};

export interface AIRequest {
	video_url: string;
}

export interface AIResponse {
	dance_level: string;
	tracks: SpotifyTrack[];
	artists: SpotifyArtist[];
	choreos: WikiPage[];
}

export interface Brand {
	id: string;
	logo_url: string;
	name_full: LocalizedText;
	name_short: LocalizedText;
}
