<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Plus } from '@lucide/svelte';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { Product } from './+page.server';
	import type { PageData } from './$types';
	import type { ClassMetadata } from './types';
	import DataTable from '$lib/components/shared/DataTable.svelte';
	import { PageContainer } from '$lib/components/layout';
	import {
		createSelectColumn,
		createLocalizedColumn,
		createDateColumn,
		createActionsColumn
	} from '$lib/components/shared/table-utils';
	import type { ColumnDef } from '@tanstack/table-core';
	import EditProductModal from './components/EditProductModal.svelte';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import type { Database } from '$lib/supabase/database.types';
	import type { Brand } from './types';
	import { getLocale } from '$lib/paraglide/runtime';
	import { invalidate } from '$app/navigation';

	const { data } = $props<{
		data: PageData & {
			products: Product[];
			form: {
				metadata: ClassMetadata;
			};
			supabase: SupabaseClient<Database>;
			brand: Brand;
		};
	}>();

	console.log('[product/page.svelte] Data:', {
		productsCount: data.products.length,
		brand: data.brand
	});

	let selectedProduct = $state<Product | null>(null);
	let selectedMetadata = $state<ClassMetadata | null>(null);
	let showEditModal = $state(false);

	const columns: ColumnDef<Product>[] = [
		createSelectColumn<Product>(),
		createLocalizedColumn<Product>(
			'title',
			'Title',
			(row) => row.metadata?.auto_final_title as LocalizedText,
			getLocale()
		),
		createLocalizedColumn<Product>(
			'subtitle',
			'Subtitle',
			(row) => row.metadata?.auto_final_subtitle as LocalizedText,
			getLocale()
		),
		createDateColumn<Product>('created_at', 'Created At', (row) => row.created_at),
		createActionsColumn<Product>({
			label: 'Edit',
			onClick: (row) => {
				selectedProduct = row;
				showEditModal = true;
			}
		})
	];
</script>

{#snippet actions()}
	<Button
		onclick={() => {
			selectedProduct = null;
			showEditModal = true;
		}}
	>
		<Plus class="mr-2 size-4"></Plus>
		Create New
	</Button>
{/snippet}

{#snippet content()}
	<DataTable
		data={data.products}
		{columns}
		searchKey="title"
		searchPlaceholder="Search products..."
	/>
{/snippet}

{#if showEditModal}
	<EditProductModal
		product={selectedProduct}
		onClose={() => (showEditModal = false)}
		open={showEditModal}
		supabase={data.supabase}
		brand={data.brand}
	/>
{/if}

<PageContainer title="Products" description="Manage your product catalog" {actions} {content} />
