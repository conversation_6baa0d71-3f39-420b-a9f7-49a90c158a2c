<script lang="ts">
	import * as Popover from '$lib/components/ui/popover/index.js';
	import * as Command from '$lib/components/ui/command';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Check, ChevronsUpDown, MapPin, Building2, Clock, AlertCircle } from '@lucide/svelte';
	import { cn } from '$lib/utils';
	import type { LocalizedText } from '$lib/utils/localization';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { Database } from '$lib/supabase/database.types';
	import { page } from '$app/stores';

	interface Space {
		id: string;
		name: string;
		address?: string;
		timeZone?: string;
		landmarkTitle?: string;
	}

	interface Props {
		value: string;
		onChange: (value: string, spaceInfo?: Space) => void;
		spaces?: Space[];
		isLoading?: boolean;
		error?: string | null;
		className?: string;
		placeholder?: string;
	}

	const {
		value = '',
		onChange,
		spaces = [],
		isLoading = false,
		error = null,
		className = '',
		placeholder = 'Select space...'
	}: Props = $props();

	const getBrandSpaces = async (): Promise<Space[]> => {
		const supabase = $page.data.supabase;
		const brandId = $page.data.brand?.id;
		const currentLocale = 'en';

		if (!brandId || !supabase) {
			return [];
		}

		try {
			const { data, error: fetchError } = await supabase
				.from('space')
				.select(
					`
					id, 
					name_full,
					landmark:landmark_id (
						id,
						title_short,
						address:address_id (
							time_zone,
							auto_normalized_address_local
						)
					)
				`
				)
				.eq('brand_id', brandId);

			if (fetchError) throw fetchError;

			return data.map((space: any) => ({
				id: space.id,
				name: getLocalizedText(space.name_full as LocalizedText, currentLocale) || 'Unknown Space',
				address: space.landmark?.address?.auto_normalized_address_local || '',
				timeZone: space.landmark?.address?.time_zone || 'UTC',
				landmarkTitle:
					getLocalizedText(space.landmark?.title_short as LocalizedText, currentLocale) || ''
			}));
		} catch (err) {
			console.error('Error fetching spaces:', err);
			return [];
		}
	};

	// State
	let open = $state(false);
	let spacesList = $state<Space[]>(spaces);
	let isLoadingSpaces = $state(isLoading);
	let spaceError = $state<string | null>(error);
	let searchQuery = $state('');
	let filteredSpaces = $state<Space[]>([]);

	// Update filtered spaces when search query changes
	$effect(() => {
		if (!searchQuery) {
			filteredSpaces = spacesList;
			return;
		}

		const query = searchQuery.toLowerCase();
		filteredSpaces = spacesList.filter(
			(space) =>
				space.name.toLowerCase().includes(query) ||
				(space.address && space.address.toLowerCase().includes(query)) ||
				(space.landmarkTitle && space.landmarkTitle.toLowerCase().includes(query)) ||
				(space.timeZone && space.timeZone.toLowerCase().includes(query))
		);
	});

	// Load spaces if none provided
	$effect(() => {
		if (spaces.length === 0 && !isLoading) {
			isLoadingSpaces = true;
			getBrandSpaces()
				.then((result) => {
					spacesList = result;
					filteredSpaces = result; // Initialize filtered spaces with all spaces
					isLoadingSpaces = false;
				})
				.catch((err) => {
					spaceError = err instanceof Error ? err.message : 'Failed to load spaces';
					isLoadingSpaces = false;
				});
		} else {
			spacesList = spaces;
			filteredSpaces = spaces; // Initialize filtered spaces with provided spaces
		}
	});

	function handleSelect(spaceId: string) {
		const selectedSpace = spacesList.find((space) => space.id === spaceId);
		onChange(spaceId, selectedSpace);
		open = false;
	}
</script>

<div class="w-full {className || ''}">
	<Popover.Root {open} onOpenChange={(isOpen: boolean) => (open = isOpen)}>
		<Popover.Trigger class="w-full">
			<Button
				variant="outline"
				role="combobox"
				aria-expanded={open}
				class="w-full justify-between transition-all duration-200 
                {value ? 'border-border hover:border-primary/60' : 'text-muted-foreground'}"
				disabled={isLoadingSpaces}
			>
				{#if isLoadingSpaces}
					<div class="flex items-center gap-2">
						<div class="h-3.5 w-3.5 animate-spin rounded-full border-b-2 border-primary"></div>
						<span class="text-muted-foreground">Loading spaces...</span>
					</div>
				{:else if value}
					<div class="flex items-center gap-2 truncate">
						<Building2 class="h-3.5 w-3.5 text-muted-foreground/70" />
						<span class="truncate"
							>{spacesList.find((space) => space.id === value)?.name || placeholder}</span
						>
					</div>
				{:else}
					<div class="flex items-center gap-2">
						<Building2 class="h-3.5 w-3.5 text-muted-foreground/70" />
						<span class="text-muted-foreground">{placeholder}</span>
					</div>
				{/if}
				<ChevronsUpDown
					class="ml-2 h-3.5 w-3.5 shrink-0 opacity-50 transition-transform duration-200 {open
						? 'rotate-180'
						: ''}"
				/>
			</Button>
		</Popover.Trigger>
		<Popover.Content class="space-select-content w-[320px] p-0 backdrop-blur-sm" sideOffset={4}>
			<Command.Root>
				<Command.Input
					placeholder="Search spaces..."
					class="h-9 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
					bind:value={searchQuery}
				/>
				<Command.Empty class="py-4 text-center text-sm">
					<div class="flex flex-col items-center gap-1 text-muted-foreground">
						<MapPin class="h-4 w-4" />
						<p>No space found</p>
					</div>
				</Command.Empty>
				{#if spaceError}
					<div class="flex flex-col items-center gap-2 py-6 text-center text-sm text-destructive">
						<AlertCircle class="h-4 w-4" />
						<p>Error: {spaceError}</p>
					</div>
				{:else if filteredSpaces.length === 0 && searchQuery}
					<div class="py-4 text-center text-sm">
						<div class="flex flex-col items-center gap-1 text-muted-foreground">
							<MapPin class="h-4 w-4" />
							<p>No matches for "{searchQuery}"</p>
						</div>
					</div>
				{:else}
					<Command.List>
						<Command.Group>
							{#each filteredSpaces as space}
								<Command.Item
									value={space.id}
									onSelect={() => handleSelect(space.id)}
									class={cn(
										'space-item-hover flex cursor-pointer flex-col items-start px-3 py-2 transition-colors',
										value === space.id ? 'bg-accent/70 ring-1 ring-primary/5' : 'hover:bg-muted/40'
									)}
								>
									<div class="flex w-full items-center justify-between">
										<span class="font-medium" data-space-id={space.id}>{space.name}</span>
										{#if value === space.id}
											<Check class="h-3.5 w-3.5 text-primary" />
										{/if}
									</div>

									{#if space.landmarkTitle || space.address}
										<div class="mt-0.5 flex items-center gap-1.5 text-xs text-muted-foreground">
											{#if space.landmarkTitle}
												<span>{space.landmarkTitle}</span>
											{/if}
											{#if space.address}
												<span class="flex items-center gap-1">
													<MapPin class="h-3 w-3 opacity-70" />
													{space.address}
												</span>
											{/if}
										</div>
									{/if}
								</Command.Item>
							{/each}
						</Command.Group>
					</Command.List>
				{/if}
			</Command.Root>
		</Popover.Content>
	</Popover.Root>
</div>
