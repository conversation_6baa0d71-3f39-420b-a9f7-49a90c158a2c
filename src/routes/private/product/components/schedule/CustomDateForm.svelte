<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { X, Calendar, Clock, TimerOff, AlertCircle } from '@lucide/svelte';
	import SpaceSelector from './SpaceSelector.svelte';
	import { Label } from '$lib/components/ui/label/index.js';

	interface Props {
		date: string;
		startTime: string;
		duration: string;
		spaceId: string;
		error: string | null;
		weekday: string;
		onDateChange: (value: string) => void;
		onTimeChange: (value: string) => void;
		onDurationChange: (value: string) => void;
		onSpaceChange: (value: string, spaceInfo?: any) => void;
		onSave: () => void;
		onCancel: () => void;
	}

	const {
		date = '',
		startTime = '',
		duration = '',
		spaceId = '',
		error = null,
		weekday = '',
		onDateChange,
		onTimeChange,
		onDurationChange,
		onSpaceChange,
		onSave,
		onCancel
	}: Props = $props();

	let dateValue = $state(date);
	let startTimeValue = $state(startTime);
	let durationValue = $state(duration);

	// Set date to next occurrence of the weekday when component mounts
	$effect(() => {
		// Only update if date appears to be default/empty
		if (!dateValue || new Date(dateValue).toString() === 'Invalid Date') {
			const today = new Date();
			const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
			const targetDay = WEEKDAYS.find((d) => d.id === weekday)?.dayNum || 1;

			// Calculate days to add to get to the next occurrence of this weekday
			let daysToAdd = targetDay - currentDay;
			if (daysToAdd <= 0) daysToAdd += 7; // Ensure we're getting a future date

			const targetDate = new Date(today);
			targetDate.setDate(today.getDate() + daysToAdd);

			// Format date as YYYY-MM-DD for the input
			dateValue = targetDate.toISOString().split('T')[0];
		}
	});

	// Use reactivity to trigger callbacks when values change
	$effect(() => {
		if (dateValue !== date) onDateChange(dateValue);
	});

	$effect(() => {
		if (startTimeValue !== startTime) onTimeChange(startTimeValue);
	});

	$effect(() => {
		if (durationValue !== duration) onDurationChange(durationValue);
	});

	// Array of weekdays for finding the target day number
	const WEEKDAYS = [
		{ id: 'Sunday', dayNum: 0 },
		{ id: 'Monday', dayNum: 1 },
		{ id: 'Tuesday', dayNum: 2 },
		{ id: 'Wednesday', dayNum: 3 },
		{ id: 'Thursday', dayNum: 4 },
		{ id: 'Friday', dayNum: 5 },
		{ id: 'Saturday', dayNum: 6 }
	];
</script>

<div class="space-y-3 rounded-md border p-3 shadow-sm">
	{#if error}
		<div
			class="flex w-full items-center gap-2 rounded bg-destructive/10 p-2 text-sm text-destructive"
		>
			<AlertCircle class="h-4 w-4" />
			<span>{error}</span>
		</div>
	{/if}

	<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
		<div class="space-y-1.5">
			<Label
				for="custom-date"
				class="flex items-center gap-1.5 text-xs font-medium text-muted-foreground"
			>
				<Calendar class="h-3.5 w-3.5" />
				<span>Date ({weekday})</span>
			</Label>
			<Input
				id="custom-date"
				type="date"
				class="h-9"
				bind:value={dateValue}
				placeholder="Select date"
			/>
		</div>

		<div class="space-y-1.5">
			<Label
				for="custom-time"
				class="flex items-center gap-1.5 text-xs font-medium text-muted-foreground"
			>
				<Clock class="h-3.5 w-3.5" />
				<span>Start Time</span>
			</Label>
			<Input
				id="custom-time"
				type="time"
				class="h-9"
				bind:value={startTimeValue}
				placeholder="Start time"
			/>
		</div>

		<div class="space-y-1.5">
			<Label
				for="custom-duration"
				class="flex items-center gap-1.5 text-xs font-medium text-muted-foreground"
			>
				<TimerOff class="h-3.5 w-3.5" />
				<span>Duration (minutes)</span>
			</Label>
			<Input
				id="custom-duration"
				type="number"
				class="h-9"
				bind:value={durationValue}
				min="1"
				placeholder="Minutes"
			/>
		</div>

		<div class="space-y-1.5">
			<Label class="flex items-center gap-1.5 text-xs font-medium text-muted-foreground">
				<span>Space</span>
			</Label>
			<SpaceSelector value={spaceId} onChange={onSpaceChange} className="h-9 w-full" />
		</div>
	</div>

	<div class="flex justify-end gap-2 pt-2">
		<Button size="sm" variant="outline" class="h-8 px-3" onclick={onCancel}>
			<X class="mr-1.5 h-3.5 w-3.5" />
			Cancel
		</Button>
		<Button size="sm" class="h-8 px-3" onclick={onSave}>Add to Schedule</Button>
	</div>
</div>

<style>
	/* Add subtle hover effect to inputs */
	:global(.custom-date-form input:hover) {
		border-color: hsl(var(--primary) / 0.2);
	}

	/* Add subtle transition */
	:global(.custom-date-form input) {
		transition:
			border-color 0.2s ease,
			box-shadow 0.2s ease;
	}
</style>
