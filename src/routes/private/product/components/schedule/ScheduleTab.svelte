<!-- ScheduleTab.svelte -->
<script lang="ts">
	import * as Tabs from '$lib/components/ui/tabs/index.js';
	import * as Card from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import {
		DropdownMenu,
		DropdownMenuContent,
		DropdownMenuItem,
		DropdownMenuTrigger
	} from '$lib/components/ui/dropdown-menu/index.js';
	import { MoreVertical, Plus, X, AlertCircle, User, Users } from '@lucide/svelte';
	import type { SuperForm } from 'sveltekit-superforms';
	import type { FormSchema } from '../../schemas';
	import { format, parseISO, addMinutes, startOfWeek, endOfWeek, subWeeks } from 'date-fns';
	import { Input } from '$lib/components/ui/input';
	import { page } from '$app/stores';
	import SpaceSelector from './SpaceSelector.svelte';
	import CustomDateForm from './CustomDateForm.svelte';
	import ComboboxWikiMulti from '../basic-info/ComboboxWikiMulti.svelte';
	import type { LocalizedText } from '$lib/utils/localization';
	import { getLocalizedText } from '$lib/utils/localization';
	import { Badge } from '$lib/components/ui/badge';
	import {
		Sheet,
		SheetContent,
		SheetDescription,
		SheetHeader,
		SheetTitle,
		SheetTrigger
	} from '$lib/components/ui/sheet';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { Database } from '$lib/supabase/database.types';

	// Type definitions for database and component data structures
	type Instructor = {
		id: string;
		name: string;
		image_url?: string;
	};

	type TimeSlot = {
		id: string;
		start_time: string;
		end_time: string;
		space_id: string;
		space_name: string;
		assigned_instructors: Instructor[];
	};

	// Helper function to generate unique IDs
	function generateUniqueId(): string {
		if (typeof crypto !== 'undefined' && typeof crypto.randomUUID === 'function') {
			return crypto.randomUUID();
		}
		return `id-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
	}

	interface Props {
		form: SuperForm<FormSchema>;
	}

	const { form }: Props = $props();
	const { form: formData } = form;

	// Validation helper functions
	function hasSpaceError(slot: TimeSlot): boolean {
		return !slot?.space_id;
	}

	function hasInstructorError(slot: TimeSlot): boolean {
		return !slot?.assigned_instructors || slot?.assigned_instructors.length === 0;
	}

	function hasSlotError(slot: TimeSlot): boolean {
		return hasSpaceError(slot) || hasInstructorError(slot);
	}

	// Get the supabase client and brand ID
	const supabase = $page.data.supabase;
	const brandId = $page.data.brand.id;
	const currentLocale = getLocale() || 'en';

	const WEEKDAYS = [
		{ id: 'Monday', short: 'Mon', dayNum: 1 },
		{ id: 'Tuesday', short: 'Tue', dayNum: 2 },
		{ id: 'Wednesday', short: 'Wed', dayNum: 3 },
		{ id: 'Thursday', short: 'Thu', dayNum: 4 },
		{ id: 'Friday', short: 'Fri', dayNum: 5 },
		{ id: 'Saturday', short: 'Sat', dayNum: 6 },
		{ id: 'Sunday', short: 'Sun', dayNum: 0 }
	];

	// Type definitions for database responses
	interface SpaceRecord {
		id: string;
		name_full: Record<string, string>;
	}

	interface EventRecord {
		id: string;
		start_at: string;
		duration_minute: number;
		space_id: string | null;
	}

	// State for spaces
	let spaces = $state<Array<{ id: string; name: string }>>([]);
	let isLoadingSpaces = $state(true);
	let spacesError = $state<string | null>(null);

	// State for events
	let events = $state<
		Array<{
			id: string;
			start_at: string;
			end_time: string;
			space_id: string;
			space_name: string;
			duration_minute: number;
		}>
	>([]);
	let isLoadingEvents = $state(true);
	let eventsError = $state<string | null>(null);

	// Define variables for our application state
	let allTimeSlots = $state<TimeSlot[]>([]);
	let selectedSlots = $state<string[]>([]);
	let expandedSlotId = $state<string | null>(null);
	let sharedInstructors = $state<Instructor[]>([]);
	let isUpdatingInstructors = $state(false);

	// Get the Monday of the previous week
	const getPrevWeekMonday = () => {
		const today = new Date();
		const thisWeekMonday = startOfWeek(today, { weekStartsOn: 1 });
		return subWeeks(thisWeekMonday, 1);
	};

	// Get the Sunday of the current week
	const getCurrentWeekSunday = () => {
		const today = new Date();
		return endOfWeek(today, { weekStartsOn: 1 });
	};

	// Fetch spaces from Supabase
	async function fetchSpaces() {
		if (!brandId) {
			spacesError = 'No brand ID available';
			isLoadingSpaces = false;
			return;
		}

		try {
			const { data, error } = await supabase
				.from('space')
				.select('id, name_full')
				.eq('brand_id', brandId);

			if (error) throw error;

			spaces = data.map((space: SpaceRecord) => ({
				id: space.id,
				name: getLocalizedText(space.name_full as LocalizedText, currentLocale) || 'Unknown Space'
			}));
		} catch (error) {
			spacesError = error instanceof Error ? error.message : 'Failed to load spaces';
		} finally {
			isLoadingSpaces = false;
		}
	}

	// Fetch events from Supabase for the past week
	async function fetchEvents() {
		if (!brandId) {
			eventsError = 'No brand ID available';
			isLoadingEvents = false;
			return;
		}

		try {
			const startDate = getPrevWeekMonday().toISOString();
			const endDate = getCurrentWeekSunday().toISOString();

			const { data, error } = await supabase
				.from('event')
				.select(
					`
          id,
          start_at,
          duration_minute,
          space_id
        `
				)
				.eq('brand_id', brandId)
				.gte('start_at', startDate)
				.lte('start_at', endDate);

			if (error) throw error;

			// If we have spaces data, use it to get space names
			if (spaces.length > 0) {
				events = data.map((event: EventRecord) => {
					const space = spaces.find((s) => s.id === event.space_id);
					return {
						id: event.id,
						start_at: event.start_at,
						end_time: new Date(
							new Date(event.start_at).getTime() + event.duration_minute * 60000
						).toISOString(),
						space_id: event.space_id || '',
						space_name: space ? space.name : 'Unknown Space',
						duration_minute: event.duration_minute
					};
				});
			} else {
				// If we don't have spaces yet, set space_name to empty for now
				events = data.map((event: EventRecord) => ({
					id: event.id,
					start_at: event.start_at,
					end_time: new Date(
						new Date(event.start_at).getTime() + event.duration_minute * 60000
					).toISOString(),
					space_id: event.space_id || '',
					space_name: 'Unknown Space',
					duration_minute: event.duration_minute
				}));
			}
		} catch (error) {
			eventsError = error instanceof Error ? error.message : 'Failed to load events';
		} finally {
			isLoadingEvents = false;
		}
	}

	// After fetching events, add this function to project them into future weeks
	function projectEventsToFutureWeeks(
		existingEvents: Array<{
			id: string;
			start_at: string;
			end_time: string;
			space_id: string;
			space_name: string;
			duration_minute: number;
		}>,
		weeksToProject: number = 2
	): TimeSlot[] {
		const projectedTimeSlots: TimeSlot[] = [];

		// Deduplicate events by weekday and time to get just one template per time slot
		const eventsByWeekdayAndTime = new Map<string, any>();

		existingEvents.forEach((event) => {
			const startDate = new Date(event.start_at);
			const weekday = format(startDate, 'EEEE');
			const timeKey = format(startDate, 'HH:mm');
			const key = `${weekday}-${timeKey}`;

			// Only keep one event per weekday-time combination
			if (!eventsByWeekdayAndTime.has(key)) {
				eventsByWeekdayAndTime.set(key, event);
			}
		});

		// Use the deduplicated events as base templates
		const templateEvents = Array.from(eventsByWeekdayAndTime.values());

		// Convert template events to time slots
		const baseTimeSlots = templateEvents.map((event) => ({
			id: event.id,
			start_time: event.start_at,
			end_time: event.end_time,
			space_id: event.space_id,
			space_name: event.space_name,
			assigned_instructors: [] as Instructor[] // Initialize with empty instructors
		}));

		// Don't include past events, only project into future
		// Project into future weeks
		for (let week = 1; week <= weeksToProject; week++) {
			baseTimeSlots.forEach((slot) => {
				const startDate = new Date(slot.start_time);
				const endDate = new Date(slot.end_time);

				// Add 7 * week days to create future events
				startDate.setDate(startDate.getDate() + 7 * week);
				endDate.setDate(endDate.getDate() + 7 * week);

				// Only include future dates, skip past dates
				if (startDate > new Date()) {
					const projectedSlot: TimeSlot = {
						id: generateUniqueId(), // Generate new ID for projected slots
						start_time: startDate.toISOString(),
						end_time: endDate.toISOString(),
						space_id: slot.space_id,
						space_name: slot.space_name,
						assigned_instructors: [] as Instructor[] // Initialize with empty instructors
					};

					projectedTimeSlots.push(projectedSlot);
				}
			});
		}

		return projectedTimeSlots;
	}

	// Load and initialize data
	$effect(() => {
		const loadData = async () => {
			await fetchSpaces();
			await fetchEvents();

			if (events.length > 0) {
				// Project events into future weeks
				allTimeSlots = projectEventsToFutureWeeks(events);

				// Initialize form data and state if we have form data
				if ($formData?.events?.length) {
					// Extract instructor data from form events
					const formInstructors = $formData.events
						.flatMap((event: any) => event.assigned_instructors || [])
						.filter((i: any) => i && i.id && i.name);

					// If all events have the same instructors, set them as shared
					const allSame = $formData.events.every(
						(event: any) =>
							event.assigned_instructors?.length === formInstructors.length &&
							event.assigned_instructors?.every((i: any) =>
								formInstructors.some((fi: any) => fi.id === i.id)
							)
					);

					if (allSame && formInstructors.length > 0) {
						// Set as shared instructors
						sharedInstructors = formInstructors.map((i: any) => ({
							id: i.id,
							name: i.name,
							image_url: i.image_url
						}));
					}

					// Map form data events to allTimeSlots
					const formEventIds = new Set($formData.events.map((e: any) => e.id));

					// Set initial selected slots
					selectedSlots = Array.from(formEventIds);

					// Update allTimeSlots with instructor data from form events
					allTimeSlots = allTimeSlots.map((slot) => {
						// Find matching event in form data
						const matchingEvent = $formData.events.find((e: any) => e.id === slot.id);
						if (matchingEvent) {
							// Apply instructors from form data
							return {
								...slot,
								assigned_instructors:
									matchingEvent.assigned_instructors?.map((i: any) => ({
										id: i.id,
										name: i.name,
										image_url: i.image_url
									})) || []
							};
						}
						return slot;
					});
				} else {
					// Ensure selectedSlots is empty if no form data
					selectedSlots = [];
				}

				// Apply shared instructors to slots without specific assignments
				applySharedInstructorsToAll();
			}
		};

		loadData();
	});

	// Sync the form data when changes are made
	function updateFormData() {
		console.log('===== UPDATE FORM DATA =====');
		console.log('Selected slots:', selectedSlots);

		const selectedTimeSlots = allTimeSlots
			.filter((slot) => selectedSlots.includes(slot.id))
			.map((slot) => {
				console.log(`Processing slot ${slot.id}`);
				console.log(`  Original assigned_instructors:`, JSON.stringify(slot.assigned_instructors));
				console.log(`  Shared instructors:`, JSON.stringify(sharedInstructors));

				// Create a deep copy of the slot's instructors
				// IMPORTANT: We're always using what's in the slot, not conditionally choosing
				const instructors = slot.assigned_instructors?.length
					? slot.assigned_instructors.map((instructor) => ({ ...instructor }))
					: sharedInstructors.map((instructor) => ({ ...instructor }));

				console.log(
					`  Final instructors for slot ${slot.id} (${instructors.length}):`,
					JSON.stringify(instructors)
				);

				// Ensure at least one instructor
				if (instructors.length === 0 && sharedInstructors.length > 0) {
					console.log(`  No instructors found, using shared instructors as fallback`);
					instructors.push(...sharedInstructors.map((instructor) => ({ ...instructor })));
				}

				return {
					id: slot.id,
					start_time: slot.start_time,
					end_time: slot.end_time,
					space_id: slot.space_id,
					assigned_instructors: instructors
				};
			});

		console.log(`Total slots in form data: ${selectedTimeSlots.length}`);
		selectedTimeSlots.forEach((slot, i) => {
			console.log(`Slot ${i}: ${slot.id} has ${slot.assigned_instructors.length} instructors`);
		});

		// Update the form data with a fresh copy to ensure reactivity
		formData.update(($formData) => {
			console.log('Updating form data');
			return {
				...$formData,
				events: selectedTimeSlots
			};
		});
	}

	// Handle slot selection/deselection
	function handleSlotSelect(slotId: string) {
		selectedSlots = selectedSlots.includes(slotId)
			? selectedSlots.filter((id) => id !== slotId)
			: [...selectedSlots, slotId];

		// Update form data after selection changes
		updateFormData();
	}

	// Toggle slot expansion (for instructor assignment)
	function toggleExpandSlot(slotId: string | null, e?: Event) {
		if (e) e.stopPropagation(); // Don't trigger slot selection
		expandedSlotId = expandedSlotId === slotId ? null : slotId;
	}

	// Get slot by ID
	function getSlotById(slotId: string | null): TimeSlot | null {
		if (!slotId) return null;
		return allTimeSlots.find((slot) => slot.id === slotId) || null;
	}

	// Handle instructor selection for a time slot
	function handleInstructorSelect(slotId: string, instructors: any[]) {
		console.log(`===== SLOT INSTRUCTOR SELECT (${slotId}) =====`);
		console.log('Original instructors received:', JSON.stringify(instructors));

		// Transform the instructors
		const transformedInstructors = transformWikiToInstructors(instructors);
		console.log('Transformed instructors:', JSON.stringify(transformedInstructors));

		// Find the slot in allTimeSlots and update its instructors
		allTimeSlots = allTimeSlots.map((slot) => {
			if (slot.id === slotId) {
				console.log(`Updating slot ${slotId} with ${transformedInstructors.length} instructors`);
				return {
					...slot,
					assigned_instructors: [...transformedInstructors] // Use spread to create a new array
				};
			}
			return slot;
		});

		// Update form data if this slot is selected
		if (selectedSlots.includes(slotId)) {
			console.log(`Slot ${slotId} is selected, updating form data`);
			updateFormData();
		}
	}

	// Custom slot creation state
	let showCustomForm = $state<string | null>(null);
	let customDate = $state(new Date().toISOString().split('T')[0]);
	let customStartTime = $state('10:00');
	let customDuration = $state('60');
	let customRoom = $state('');
	let lastSelectedRoom = $state(''); // Remember last selected room for convenience
	let customError = $state<string | null>(null);
	// Store selected space information including timezone
	let selectedSpaceInfo = $state<{
		id: string;
		name: string;
		timeZone: string;
		address?: string;
		landmarkTitle?: string;
	} | null>(null);

	// Helper functions for slot management
	function getDaySlots(weekday: string) {
		return allTimeSlots.filter((slot) => format(parseISO(slot.start_time), 'EEEE') === weekday);
	}

	function hasDaySelectedSlots(weekday: string) {
		return getDaySlots(weekday).some((slot) => selectedSlots.includes(slot.id));
	}

	function formatTimeRange(start: string, end: string) {
		return `${format(parseISO(start), 'HH:mm')} - ${format(parseISO(end), 'HH:mm')}`;
	}

	function formatDate(date: string) {
		return format(parseISO(date), 'MMM d, yyyy');
	}

	// Add a new function to handle shared instructor selection
	function handleSharedInstructorSelect(instructors: any[]) {
		console.log('===== SHARED INSTRUCTOR SELECT =====');
		console.log('Original instructors received:', JSON.stringify(instructors));

		// Transform and store the instructors
		sharedInstructors = transformWikiToInstructors(instructors);
		console.log('Shared instructors after transform:', JSON.stringify(sharedInstructors));

		// Force shared instructors on ALL selected slots, regardless of their current state
		allTimeSlots = allTimeSlots.map((slot) => {
			if (selectedSlots.includes(slot.id)) {
				console.log(`Forcing shared instructors on selected slot ${slot.id}`);
				return {
					...slot,
					assigned_instructors: [...sharedInstructors] // Replace with shared instructors
				};
			}
			return slot;
		});
		console.log('Forced shared instructors on all selected slots');

		// Update form data to reflect changes
		updateFormData();
	}

	// Apply shared instructors to all slots without custom assignments
	function applySharedInstructorsToAll() {
		if (sharedInstructors.length > 0 && !isUpdatingInstructors) {
			isUpdatingInstructors = true;

			// First mark any slots that already had custom assignments
			const customAssignedSlots = new Set();
			allTimeSlots.forEach((slot) => {
				// If slot has different instructors than shared, mark it as custom
				if (
					slot.assigned_instructors?.length > 0 &&
					JSON.stringify(slot.assigned_instructors) !== JSON.stringify(sharedInstructors)
				) {
					customAssignedSlots.add(slot.id);
				}
			});

			console.log('Slots with custom assignments:', Array.from(customAssignedSlots));

			// Update all slots
			allTimeSlots = allTimeSlots.map((slot) => {
				// Skip slots with custom assignments
				if (customAssignedSlots.has(slot.id)) {
					return slot;
				}

				// Apply shared instructors to all other slots
				return {
					...slot,
					assigned_instructors: [...sharedInstructors] // Use spread to create a new array
				};
			});

			setTimeout(() => {
				isUpdatingInstructors = false;
			}, 0);
		}
	}

	// Transform data from ComboboxWikiMulti to our instructor format
	function transformWikiToInstructors(wikiPages: any[]): Instructor[] {
		console.log('Transforming wiki pages to instructors:', wikiPages);

		if (!Array.isArray(wikiPages)) {
			console.error('Expected array of wiki pages but got:', wikiPages);
			return [];
		}

		const result = wikiPages.map((page) => {
			console.log('Processing page:', page);

			if (!page || typeof page !== 'object') {
				console.error('Invalid page object:', page);
				return {
					id: 'unknown',
					name: 'Invalid Object',
					image_url: undefined
				};
			}

			// Handle both raw DBWikiPage objects and simplified {id, name, image_url} objects
			if (page.title) {
				// It's a raw DBWikiPage
				const name = getLocalizedText(page.title as LocalizedText, currentLocale);
				console.log(`Raw DBWikiPage: ${page.id} - ${name}`);
				return {
					id: page.id,
					name,
					image_url: typeof page.avatar === 'string' ? page.avatar : undefined
				};
			} else if (page.name) {
				// It's already a simplified object
				console.log(`Simplified object: ${page.id} - ${page.name}`);
				return {
					id: page.id,
					name: page.name,
					image_url: page.image_url
				};
			} else {
				console.error('Invalid instructor format:', page);
				return {
					id: page.id || 'unknown',
					name: 'Unknown Name',
					image_url: undefined
				};
			}
		});

		console.log('Transformation result:', result);
		return result;
	}

	// Custom slot form management
	function toggleCustomForm(weekday: string) {
		if (showCustomForm === weekday) {
			showCustomForm = null;
		} else {
			showCustomForm = weekday;

			// Get the space timezone if available, otherwise use UTC
			const spaceTimeZone = selectedSpaceInfo?.timeZone || 'UTC';

			// Get the expected day number for this weekday
			const targetDay = WEEKDAYS.find((d) => d.id === weekday)?.dayNum || 1;

			// Start with today's date in the browser's timezone
			const today = new Date();

			// Find the next occurrence of the target day in the space's timezone
			let nextDate = new Date(today);

			// Try up to 10 days in the future to find a date that matches the correct weekday in the space's timezone
			for (let i = 0; i < 10; i++) {
				// Add one day
				nextDate.setDate(nextDate.getDate() + 1);

				// Check if this date is the target weekday in the space's timezone
				const dateInSpaceTimeZone = getDateInTimeZone(nextDate, spaceTimeZone);
				if (dateInSpaceTimeZone.getDay() === targetDay) {
					break;
				}
			}

			// Format the date
			customDate = nextDate.toISOString().split('T')[0];
			customStartTime = '10:00';
			customDuration = '60';
			// Use last selected room if available
			customRoom = lastSelectedRoom || '';
			customError = null;
		}
	}

	// Handle space selection
	function handleSpaceChange(value: string, spaceInfo?: any) {
		customRoom = value;
		lastSelectedRoom = value; // Remember this selection for next time

		// Store space info including timezone
		if (spaceInfo) {
			selectedSpaceInfo = {
				id: spaceInfo.id,
				name: spaceInfo.name,
				timeZone: spaceInfo.timeZone || 'UTC',
				address: spaceInfo.address,
				landmarkTitle: spaceInfo.landmarkTitle
			};
		}
	}

	// Get space name by ID
	async function getSpaceName(spaceId: string): Promise<string> {
		if (!spaceId) return 'Unknown Space';

		const supabase = $page.data.supabase;
		const currentLocale = getLocale() || 'en';

		try {
			const { data, error } = await supabase
				.from('space')
				.select('name_full')
				.eq('id', spaceId)
				.single();

			if (error) throw error;

			return getLocalizedText(data.name_full as LocalizedText, currentLocale) || 'Unknown Space';
		} catch (error) {
			console.error('Error fetching space name:', error);
			return 'Unknown Space';
		}
	}

	// Add function to convert date/time to specific timezone
	function getDateInTimeZone(date: Date, timeZone: string): Date {
		const options: Intl.DateTimeFormatOptions = {
			timeZone,
			year: 'numeric',
			month: 'numeric',
			day: 'numeric',
			hour: 'numeric',
			minute: 'numeric',
			second: 'numeric',
			hour12: false
		};

		const formatter = new Intl.DateTimeFormat('en-US', options);
		const parts = formatter.formatToParts(date);

		const mappedParts: Record<string, string> = {};
		parts.forEach((part) => {
			mappedParts[part.type] = part.value;
		});

		// Create the date in the target timezone
		const year = parseInt(mappedParts.year);
		const month = parseInt(mappedParts.month) - 1; // Months are 0-based in JS
		const day = parseInt(mappedParts.day);
		const hour = parseInt(mappedParts.hour);
		const minute = parseInt(mappedParts.minute);
		const second = parseInt(mappedParts.second);

		return new Date(year, month, day, hour, minute, second);
	}

	// Create custom slot
	async function saveCustomSlot(weekday: string) {
		// Validate inputs
		if (!customDate || !customStartTime || !customDuration || !customRoom) {
			customError = 'All fields are required';
			return;
		}

		if (parseInt(customDuration) <= 0) {
			customError = 'Duration must be greater than 0';
			return;
		}

		// Use the space timezone from saved space info
		const spaceTimeZone = selectedSpaceInfo?.timeZone || 'UTC';

		// Get the expected day number for this weekday
		const expectedDayNum = WEEKDAYS.find((d) => d.id === weekday)?.dayNum;

		// Create the date object with the time
		const startDateTime = `${customDate}T${customStartTime}:00`;
		const startDateObj = new Date(startDateTime);

		// Convert the date to the space's timezone to get the correct day of week
		const localDateInSpaceTimeZone = getDateInTimeZone(startDateObj, spaceTimeZone);
		const selectedDayNum = localDateInSpaceTimeZone.getDay(); // 0 = Sunday, 1 = Monday, etc.

		// Validate that date matches the selected weekday
		if (selectedDayNum !== expectedDayNum) {
			customError = `Selected date must be a ${weekday} in ${spaceTimeZone} timezone`;
			return;
		}

		// Calculate end time
		const endDateObj = addMinutes(startDateObj, parseInt(customDuration));

		// Use the space name from saved space info
		const roomName = selectedSpaceInfo?.name || 'Unknown Space';

		// Create new slot with proper instructor structure
		const newSlot: TimeSlot = {
			id: generateUniqueId(),
			start_time: startDateObj.toISOString(),
			end_time: endDateObj.toISOString(),
			space_id: customRoom,
			space_name: roomName,
			assigned_instructors: sharedInstructors.length
				? sharedInstructors.map((instructor) => ({ ...instructor })) // Create deep copies
				: []
		};

		// Add to allTimeSlots
		allTimeSlots = [...allTimeSlots, newSlot];

		// Auto-select the new slot
		selectedSlots = [...selectedSlots, newSlot.id];

		// Update form data with the new selection
		updateFormData();

		// Hide the form
		showCustomForm = null;
	}
</script>

<div class="h-full overflow-y-auto">
	<div class="space-y-3 p-1 pb-6">
		<!-- Shared instructors section -->
		<div class=" py-4">
			<div class="mb-3">
				<h3 class="text-md font-medium">Shared Instructors</h3>
				<p class="mb-3 text-sm text-muted-foreground">
					These instructors will be assigned to all selected time slots. You can override per slot
					below.
				</p>

				{#if supabase && brandId}
					<ComboboxWikiMulti
						{supabase}
						{brandId}
						kind="person"
						selectedItems={sharedInstructors}
						onSelect={(instructors) => {
							console.log(
								'ComboboxWikiMulti (shared) onSelect called with:',
								JSON.stringify(instructors)
							);
							handleSharedInstructorSelect(instructors);
						}}
					/>
				{/if}
			</div>
		</div>
		<div class="flex-1 overflow-hidden">
			{#if isLoadingSpaces || isLoadingEvents}
				<div class="flex items-center justify-center p-8">
					<div class="text-center">
						<div class="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
						<p class="mt-2 text-sm text-muted-foreground">Loading schedule data...</p>
					</div>
				</div>
			{:else if spacesError}
				<div class="p-4 text-destructive">
					<AlertCircle class="mb-2 h-5 w-5" />
					<p>Error loading spaces: {spacesError}</p>
				</div>
			{:else if eventsError}
				<div class="p-4 text-destructive">
					<AlertCircle class="mb-2 h-5 w-5" />
					<p>Error loading events: {eventsError}</p>
				</div>
			{:else}
				<div class="mb-2 flex items-center justify-between">
					<h3 class="text-md font-medium">Schedule</h3>
					<span class="text-sm tabular-nums text-muted-foreground"
						>{selectedSlots.length} selected</span
					>
				</div>
				<Tabs.Root value="Monday" class="flex h-full flex-col">
					<Tabs.List class="grid w-full grid-cols-7">
						{#each WEEKDAYS as day}
							<Tabs.Trigger
								value={day.id}
								class="flex-1 {hasDaySelectedSlots(day.id)
									? 'bg-primary/10 font-medium text-primary ring-2 ring-inset ring-primary/20'
									: ''}"
							>
								<div class="flex items-center gap-1">
									<span>{day.short}</span>
									<span class="text-xs tabular-nums text-muted-foreground"
										>{getDaySlots(day.id).length}</span
									>
								</div>
							</Tabs.Trigger>
						{/each}
					</Tabs.List>
					<div class="flex-1 overflow-y-auto py-2">
						{#each WEEKDAYS as day}
							<Tabs.Content value={day.id} class="h-full">
								<Card.Root class="border-0 shadow-none">
									<Card.Content class="space-y-1 p-0">
										{#each getDaySlots(day.id) as slot}
											<button
												type="button"
												class="group flex w-full items-center space-x-3 rounded-md p-1 text-left hover:bg-accent"
												onclick={() => handleSlotSelect(slot.id)}
												onkeydown={(e) => e.key === 'Enter' && handleSlotSelect(slot.id)}
											>
												<div class="flex flex-1 items-center space-x-3">
													<Checkbox
														checked={selectedSlots.includes(slot.id)}
														onCheckedChange={() => handleSlotSelect(slot.id)}
													/>
													<div class="flex flex-1 items-center justify-between">
														<div>
															<p class="font-medium">
																{formatTimeRange(slot.start_time, slot.end_time)}
															</p>
															<p class="text-sm text-muted-foreground">
																{formatDate(slot.start_time)}
															</p>
														</div>
														<div class="text-right">
															<p class="text-sm {hasSpaceError(slot) ? 'text-destructive' : ''}">
																{slot.space_name || 'No space selected'}
																{#if hasSpaceError(slot)}
																	<AlertCircle class="ml-1 inline-block h-3 w-3" />
																{/if}
															</p>
															{#if slot.assigned_instructors?.length}
																<div class="flex items-center justify-end gap-1">
																	<p class="text-sm text-muted-foreground">
																		{slot.assigned_instructors.map((i) => i.name).join(', ')}
																	</p>
																	<Button
																		variant="ghost"
																		size="sm"
																		class="h-5 w-5 rounded-full p-0"
																		onclick={(e) => toggleExpandSlot(slot.id, e)}
																	>
																		<Users class="h-3 w-3" />
																	</Button>
																</div>
															{:else if sharedInstructors.length}
																<div class="flex items-center justify-end gap-1">
																	<p class="text-sm text-muted-foreground">
																		{sharedInstructors.map((i) => i.name).join(', ')}
																	</p>
																	<Button
																		variant="ghost"
																		size="sm"
																		class="h-5 w-5 rounded-full p-0"
																		onclick={(e) => toggleExpandSlot(slot.id, e)}
																	>
																		<Users class="h-3 w-3" />
																	</Button>
																</div>
															{:else}
																<div class="flex items-center justify-end gap-1">
																	<p
																		class="text-sm {hasInstructorError(slot)
																			? 'font-medium text-destructive'
																			: 'text-muted-foreground'}"
																	>
																		No instructors
																		{#if hasInstructorError(slot)}
																			<AlertCircle class="ml-1 inline-block h-3 w-3" />
																		{/if}
																	</p>
																	<Button
																		variant="ghost"
																		size="sm"
																		class="h-5 w-5 rounded-full p-0"
																		onclick={(e) => toggleExpandSlot(slot.id, e)}
																	>
																		<Users class="h-3 w-3" />
																	</Button>
																</div>
															{/if}
														</div>
													</div>
												</div>
												<DropdownMenu>
													<DropdownMenuTrigger>
														<Button variant="ghost" size="icon" class="h-7 w-7">
															<MoreVertical class="h-4 w-4" />
														</Button>
													</DropdownMenuTrigger>
													<DropdownMenuContent>
														<DropdownMenuItem onclick={() => toggleExpandSlot(slot.id)}>
															Assign Instructors
														</DropdownMenuItem>
														<DropdownMenuItem>Give Back to Admin</DropdownMenuItem>
													</DropdownMenuContent>
												</DropdownMenu>
											</button>

											<!-- Expandable instructor section -->
											{#if expandedSlotId === slot.id}
												<div
													class="ml-9 mt-2 rounded-md border {hasSlotError(slot)
														? 'border-destructive'
														: ''} bg-muted/20 p-3"
												>
													<!-- Display validation errors if present -->
													{#if hasSlotError(slot)}
														<div
															class="mb-2 rounded-md bg-destructive/10 p-2 text-xs text-destructive"
														>
															{#if hasSpaceError(slot)}<p>• Space selection is required</p>{/if}
															{#if hasInstructorError(slot)}<p>
																	• At least one instructor must be assigned
																</p>{/if}
														</div>
													{/if}
													<div class="mb-2 flex items-center justify-between">
														<h4 class="text-sm font-medium">
															{#if slot.assigned_instructors?.length}
																Edit Instructors
															{:else}
																Assign Instructors
															{/if}
														</h4>
														{#if JSON.stringify(slot.assigned_instructors) === JSON.stringify(sharedInstructors) && sharedInstructors.length > 0}
															<span class="rounded-full bg-muted px-2 py-0.5 text-xs"
																>Using shared</span
															>
														{:else if slot.assigned_instructors?.length}
															<span class="rounded-full bg-muted px-2 py-0.5 text-xs">Custom</span>
														{/if}
													</div>

													{#if supabase && brandId}
														<ComboboxWikiMulti
															{supabase}
															{brandId}
															kind="person"
															selectedItems={slot.assigned_instructors?.length
																? slot.assigned_instructors
																: sharedInstructors}
															onSelect={(instructors) => {
																console.log(
																	`ComboboxWikiMulti (slot ${slot.id}) onSelect called with:`,
																	JSON.stringify(instructors)
																);
																handleInstructorSelect(slot.id, instructors);
															}}
														/>
													{/if}

													<div class="mt-3 flex justify-end gap-2">
														<Button
															variant="outline"
															size="sm"
															class="h-8"
															onclick={() => toggleExpandSlot(null)}
														>
															Close
														</Button>
													</div>
												</div>
											{/if}
										{/each}

										{#if getDaySlots(day.id).length === 0 && showCustomForm !== day.id}
											<div
												class="flex h-12 items-center justify-center text-sm text-muted-foreground"
											>
												No slots available
											</div>
										{/if}

										<!-- Custom Inline Time Slot Form -->
										{#if showCustomForm === day.id}
											<div class="mt-3">
												<CustomDateForm
													date={customDate}
													startTime={customStartTime}
													duration={customDuration}
													spaceId={customRoom}
													error={customError}
													weekday={day.id}
													onDateChange={(value) => (customDate = value)}
													onTimeChange={(value) => (customStartTime = value)}
													onDurationChange={(value) => (customDuration = value)}
													onSpaceChange={handleSpaceChange}
													onSave={() => saveCustomSlot(day.id)}
													onCancel={() => toggleCustomForm(day.id)}
												/>
											</div>
										{:else}
											<!-- Add Custom Button -->
											<Button
												variant="outline"
												class="mt-2 w-full justify-start"
												onclick={() => toggleCustomForm(day.id)}
											>
												<Plus class="mr-2 h-4 w-4" />
												Add Custom Time
											</Button>
										{/if}
									</Card.Content>
								</Card.Root>
							</Tabs.Content>
						{/each}
					</div>
				</Tabs.Root>
			{/if}
		</div>
	</div>
</div>
