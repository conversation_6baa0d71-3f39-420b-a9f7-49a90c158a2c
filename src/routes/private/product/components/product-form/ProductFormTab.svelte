<script lang="ts">
	import { Label } from '$lib/components/ui/label';
	import * as RadioGroup from '$lib/components/ui/radio-group';
	import { Card } from '$lib/components/ui/card';
	import type { SuperForm } from 'sveltekit-superforms/client';
	import type { FormSchema } from '../../schemas';

	interface Props {
		form: SuperForm<FormSchema, any>;
	}

	type ProductType = 'bundled' | 'individual' | 'trial_first' | 'trial_rest';
	type FormType = 'bundled' | 'individual' | 'trial';

	let { form }: Props = $props();
	const { form: formStore } = form;
	const formData = $derived($formStore);

	// Use a simple state variable without trying to sync with form
	let productFormType = $state<'bundled' | 'individual' | 'trial'>('individual');

	// Initialize brandPriceId
	let brandPriceId = $state<string | null>(null);

	// Add guard flags to prevent infinite loops
	let isInitialized = $state(false);
	let isUpdatingFormType = $state(false);

	// Fix mismatches between product_form_type and product_form_factor
	let isFixingTypeMismatch = $state(false);

	// Initialize productFormType from the form only once
	$effect(() => {
		if (!isInitialized && !isUpdatingFormType) {
			isInitialized = true;
			productFormType = $formStore.product_form_type;
		}
	});

	// Handle updates to the form
	function updateFormType(value: 'bundled' | 'individual' | 'trial') {
		productFormType = value;

		// Update the form data
		form.form.update((formData) => ({
			...formData,
			product_form_type: value
		}));
	}

	// Handle form type changes
	function handleFormTypeChange(value: string) {
		if (isUpdatingFormType) return;

		// Skip if the value is already set (prevent unnecessary updates)
		if (value === $formStore.product_form_type) return;

		isUpdatingFormType = true;

		try {
			const newFormType = value as FormSchema['product_form_type'];
			productFormType = newFormType;

			const defaultPriceId = 'missing-price-id';
			const currentFormFactors = formData.product_form_factor || [];

			// Get existing cost units for each type
			const getCostUnits = (type: ProductType) => {
				const factor = currentFormFactors.find((f) => f.product_type === type);
				return factor?.prices?.[0]?.cost_units ?? 2;
			};

			// Check if there's already a matching product_form_factor for the new form type
			const hasMatchingFormFactor = currentFormFactors.some(
				(config) =>
					(newFormType === 'bundled' && config.product_type === 'bundled') ||
					(newFormType === 'individual' && config.product_type === 'individual') ||
					(newFormType === 'trial' &&
						(config.product_type === 'trial_first' || config.product_type === 'trial_rest'))
			);

			// Only update product_form_factor if there's no matching form factor
			if (!hasMatchingFormFactor) {
				let newFormFactor: {
					product_type: ProductType;
					prices: { price_id: string; cost_units: number }[];
				}[];
				if (newFormType === 'bundled') {
					newFormFactor = [
						{
							product_type: 'bundled',
							prices: [{ price_id: defaultPriceId, cost_units: getCostUnits('bundled') }]
						}
					];
				} else if (newFormType === 'individual') {
					newFormFactor = [
						{
							product_type: 'individual',
							prices: [{ price_id: defaultPriceId, cost_units: getCostUnits('individual') }]
						}
					];
				} else if (newFormType === 'trial') {
					newFormFactor = [
						{
							product_type: 'trial_first',
							prices: [{ price_id: defaultPriceId, cost_units: getCostUnits('trial_first') }]
						},
						{
							product_type: 'trial_rest',
							prices: [{ price_id: defaultPriceId, cost_units: getCostUnits('trial_rest') }]
						}
					];
				}

				form.form.update((formData) => ({
					...formData,
					product_form_type: newFormType,
					product_form_factor: newFormFactor
				}));
			} else {
				// Only update the form type, not the product_form_factor
				form.form.update((formData) => ({
					...formData,
					product_form_type: newFormType
				}));
			}
		} finally {
			setTimeout(() => {
				isUpdatingFormType = false;
			}, 50); // Use a longer timeout to ensure all updates complete
		}
	}

	// Enhanced illustration components with shadcn styling
	function BundledProductIllustration() {
		return `
      <svg width="160" height="110" viewBox="0 0 160 110" xmlns="http://www.w3.org/2000/svg">
        <!-- Main product box -->
        <rect x="10" y="10" width="140" height="30" rx="6" fill="hsl(var(--primary) / 0.1)" stroke="hsl(var(--primary))" stroke-width="2"/>
        <text x="80" y="30" text-anchor="middle" fill="hsl(var(--primary))" font-size="13" font-weight="500">Product</text>
        
        <!-- Connection lines -->
        <line x1="40" y1="40" x2="40" y2="55" stroke="hsl(var(--border))" stroke-width="1.5" stroke-dasharray="2,2"/>
        <line x1="80" y1="40" x2="80" y2="55" stroke="hsl(var(--border))" stroke-width="1.5" stroke-dasharray="2,2"/>
        <line x1="120" y1="40" x2="120" y2="55" stroke="hsl(var(--border))" stroke-width="1.5" stroke-dasharray="2,2"/>
        
        <!-- Event boxes -->
        <rect x="10" y="55" width="45" height="30" rx="6" fill="hsl(var(--muted) / 0.7)" stroke="hsl(var(--border))" stroke-width="1.5"/>
        <rect x="58" y="55" width="45" height="30" rx="6" fill="hsl(var(--muted) / 0.7)" stroke="hsl(var(--border))" stroke-width="1.5"/>
        <rect x="106" y="55" width="45" height="30" rx="6" fill="hsl(var(--muted) / 0.7)" stroke="hsl(var(--border))" stroke-width="1.5"/>
        
        <!-- Event labels -->
        <text x="32" y="75" text-anchor="middle" fill="hsl(var(--foreground))" font-size="10" font-weight="500">Event 1</text>
        <text x="80" y="75" text-anchor="middle" fill="hsl(var(--foreground))" font-size="10" font-weight="500">Event 2</text>
        <text x="128" y="75" text-anchor="middle" fill="hsl(var(--foreground))" font-size="10" font-weight="500">Event 3</text>
      </svg>
    `;
	}

	function IndividualProductIllustration() {
		return `
      <svg width="160" height="110" viewBox="0 0 160 110" xmlns="http://www.w3.org/2000/svg">
        <!-- Product boxes -->
        <rect x="10" y="10" width="45" height="25" rx="6" fill="hsl(var(--primary) / 0.1)" stroke="hsl(var(--primary))" stroke-width="1.5"/>
        <rect x="58" y="10" width="45" height="25" rx="6" fill="hsl(var(--primary) / 0.1)" stroke="hsl(var(--primary))" stroke-width="1.5"/>
        <rect x="106" y="10" width="45" height="25" rx="6" fill="hsl(var(--primary) / 0.1)" stroke="hsl(var(--primary))" stroke-width="1.5"/>
        
        <!-- Product labels -->
        <text x="32" y="26" text-anchor="middle" fill="hsl(var(--primary))" font-size="8" font-weight="500">Product 1</text>
        <text x="80" y="26" text-anchor="middle" fill="hsl(var(--primary))" font-size="8" font-weight="500">Product 2</text>
        <text x="128" y="26" text-anchor="middle" fill="hsl(var(--primary))" font-size="8" font-weight="500">Product 3</text>
        
        <!-- Connection lines -->
        <line x1="32" y1="35" x2="32" y2="55" stroke="hsl(var(--border))" stroke-width="1.5" stroke-dasharray="2,2"/>
        <line x1="80" y1="35" x2="80" y2="55" stroke="hsl(var(--border))" stroke-width="1.5" stroke-dasharray="2,2"/>
        <line x1="128" y1="35" x2="128" y2="55" stroke="hsl(var(--border))" stroke-width="1.5" stroke-dasharray="2,2"/>
        
        <!-- Event boxes -->
        <rect x="10" y="55" width="45" height="30" rx="6" fill="hsl(var(--muted) / 0.7)" stroke="hsl(var(--border))" stroke-width="1.5"/>
        <rect x="58" y="55" width="45" height="30" rx="6" fill="hsl(var(--muted) / 0.7)" stroke="hsl(var(--border))" stroke-width="1.5"/>
        <rect x="106" y="55" width="45" height="30" rx="6" fill="hsl(var(--muted) / 0.7)" stroke="hsl(var(--border))" stroke-width="1.5"/>
        
        <!-- Event labels -->
        <text x="32" y="75" text-anchor="middle" fill="hsl(var(--foreground))" font-size="10" font-weight="500">Event 1</text>
        <text x="80" y="75" text-anchor="middle" fill="hsl(var(--foreground))" font-size="10" font-weight="500">Event 2</text>
        <text x="128" y="75" text-anchor="middle" fill="hsl(var(--foreground))" font-size="10" font-weight="500">Event 3</text>
      </svg>
    `;
	}

	function TrialProductIllustration() {
		return `
      <svg width="160" height="110" viewBox="0 0 160 110" xmlns="http://www.w3.org/2000/svg">
        <!-- Product boxes -->
        <rect x="10" y="10" width="45" height="25" rx="6" fill="hsl(var(--secondary) / 0.3)" stroke="hsl(var(--primary))" stroke-width="1.5"/>
        <rect x="58" y="10" width="92" height="25" rx="6" fill="hsl(var(--primary) / 0.1)" stroke="hsl(var(--primary))" stroke-width="1.5"/>
        
        <!-- Product labels -->
        <text x="32" y="26" text-anchor="middle" fill="hsl(var(--primary))" font-size="9" font-weight="500">Trial</text>
        <text x="104" y="26" text-anchor="middle" fill="hsl(var(--primary))" font-size="9" font-weight="500">Main Product</text>
        
        <!-- Connection lines -->
        <line x1="32" y1="35" x2="32" y2="55" stroke="hsl(var(--border))" stroke-width="1.5" stroke-dasharray="2,2"/>
        <line x1="80" y1="35" x2="80" y2="55" stroke="hsl(var(--border))" stroke-width="1.5" stroke-dasharray="2,2"/>
        <line x1="128" y1="35" x2="128" y2="55" stroke="hsl(var(--border))" stroke-width="1.5" stroke-dasharray="2,2"/>
        
        <!-- Event boxes -->
        <rect x="10" y="55" width="45" height="30" rx="6" fill="hsl(var(--muted) / 0.7)" stroke="hsl(var(--border))" stroke-width="1.5"/>
        <rect x="58" y="55" width="45" height="30" rx="6" fill="hsl(var(--muted) / 0.7)" stroke="hsl(var(--border))" stroke-width="1.5"/>
        <rect x="106" y="55" width="45" height="30" rx="6" fill="hsl(var(--muted) / 0.7)" stroke="hsl(var(--border))" stroke-width="1.5"/>
        
        <!-- Event labels -->
        <text x="32" y="75" text-anchor="middle" fill="hsl(var(--foreground))" font-size="10" font-weight="500">Event 1</text>
        <text x="80" y="75" text-anchor="middle" fill="hsl(var(--foreground))" font-size="10" font-weight="500">Event 2</text>
        <text x="128" y="75" text-anchor="middle" fill="hsl(var(--foreground))" font-size="10" font-weight="500">Event 3</text>
      </svg>
    `;
	}

	// Fix mismatches between product_form_type and product_form_factor
	$effect(() => {
		// Skip if already fixing or updating form type
		if (isFixingTypeMismatch || !isInitialized || isUpdatingFormType) return;

		const formValue = formData;
		if (!formValue) return;

		// Get the form type and form factor
		const formType = formValue.product_form_type;
		const formFactor = formValue.product_form_factor || [];

		// Check for mismatches
		const hasMismatch = formFactor.some((config) => {
			if (formType === 'bundled' && config.product_type !== 'bundled') return true;
			if (formType === 'individual' && config.product_type !== 'individual') return true;
			if (formType === 'trial' && !['trial_first', 'trial_rest'].includes(config.product_type))
				return true;
			return false;
		});

		// If there's a mismatch, fix it by calling handleFormTypeChange
		if (hasMismatch) {
			isFixingTypeMismatch = true;
			try {
				handleFormTypeChange(formType);
			} finally {
				// Reset guard flag after a short delay
				setTimeout(() => {
					isFixingTypeMismatch = false;
				}, 0);
			}
		}
	});
</script>

<div class="h-full overflow-y-auto">
	<div class="space-y-3 p-1 pb-6">
		<div class="text-sm text-muted-foreground">
			Select how you want to structure this product bundle. This affects how customers will purchase
			and interact with the events.
		</div>

		<RadioGroup.Root value={$formStore.product_form_type} onValueChange={handleFormTypeChange}>
			<div class="grid gap-3">
				<!-- Individual Products Option (First) -->
				<label for="individual" class="cursor-pointer">
					<Card class="relative p-2 transition-all hover:shadow-md">
						<div class="absolute right-2 top-2">
							<RadioGroup.Item value="individual" id="individual" />
						</div>
						<div class="flex flex-col gap-2 pr-8">
							<Label class="text-base font-semibold" for="individual">Individual Products</Label>
							<div class="flex flex-col items-center gap-3 md:flex-row">
								<div
									class="flex w-full shrink-0 justify-center rounded-lg bg-muted/20 p-1 md:w-[160px]"
								>
									{@html IndividualProductIllustration()}
								</div>
								<div class="text-xs sm:text-sm">
									<p>
										Each event becomes a separate product. Customers can purchase and manage them
										individually.
									</p>
									<ul class="mt-1 list-inside list-disc">
										<li>Flexible purchasing options</li>
										<li>Independent registration for each event</li>
										<li>Individual refund/cancellation for each event</li>
									</ul>
								</div>
							</div>
						</div>
					</Card>
				</label>

				<!-- Trial Mode Option (Second) -->
				<label for="trial" class="cursor-pointer">
					<Card class="relative p-2 transition-all hover:shadow-md">
						<div class="absolute right-2 top-2">
							<RadioGroup.Item value="trial" id="trial" />
						</div>
						<div class="flex flex-col gap-2 pr-8">
							<Label class="text-base font-semibold" for="trial">Trial Mode</Label>
							<div class="flex flex-col items-center gap-3 md:flex-row">
								<div
									class="flex w-full shrink-0 justify-center rounded-lg bg-muted/20 p-1 md:w-[160px]"
								>
									{@html TrialProductIllustration()}
								</div>
								<div class="text-xs sm:text-sm">
									<p>
										First event is a standalone product, remaining events are bundled together as a
										separate product.
									</p>
									<ul class="mt-1 list-inside list-disc">
										<li>Try-before-you-buy approach</li>
										<li>First event can be cancelled separately</li>
										<li>Remaining events are managed as a single unit</li>
									</ul>
								</div>
							</div>
						</div>
					</Card>
				</label>

				<!-- Bundled Product Option (Last) -->
				<label for="bundled" class="cursor-pointer">
					<Card class="relative p-2 transition-all hover:shadow-md">
						<div class="absolute right-2 top-2">
							<RadioGroup.Item value="bundled" id="bundled" />
						</div>
						<div class="flex flex-col gap-2 pr-8">
							<Label class="text-base font-semibold" for="bundled">Bundled Product</Label>
							<div class="flex flex-col items-center gap-3 md:flex-row">
								<div
									class="flex w-full shrink-0 justify-center rounded-lg bg-muted/20 p-1 md:w-[160px]"
								>
									{@html BundledProductIllustration()}
								</div>
								<div class="text-xs sm:text-sm">
									<p>
										One single product containing all events. Customers purchase and manage this as
										a single item.
									</p>
									<ul class="mt-1 list-inside list-disc">
										<li>Simplified checkout process</li>
										<li>Customer must register for all events</li>
										<li>Refunds apply to the entire product</li>
									</ul>
								</div>
							</div>
						</div>
					</Card>
				</label>
			</div>
		</RadioGroup.Root>
	</div>
</div>
