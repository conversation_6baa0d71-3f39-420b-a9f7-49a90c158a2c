<!-- ComboboxWiki.svelte -->
<script lang="ts">
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { Button } from '$lib/components/ui/button';
	import { Check, ChevronsUpDown, Loader2, X } from '@lucide/svelte';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import type { Database } from '$lib/supabase/database.types';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import { cn } from '$lib/utils';
	import { tick, onMount } from 'svelte';

	type DBWikiPage = Database['public']['Tables']['wikipage']['Row'];

	interface Props {
		kind: string;
		onSelect: (wikipage: DBWikiPage) => void;
		supabase: SupabaseClient<Database>;
		brandId: string;
	}

	const { kind, onSelect, supabase, brandId }: Props = $props();

	let open = $state(false);
	let value = $state('');
	let displayValue = $state('');
	let searchQuery = $state('');
	let isLoading = $state(false);
	let pages = $state<DBWikiPage[]>([]);
	let triggerRef = $state<HTMLButtonElement>(null!);
	let hasError = $state(false);
	let lastQuery = $state<string | null>(null);
	let shouldSkipNextSearch = $state(false);

	// Simple search handler
	async function handleSearch(query: string) {
		// Skip if already loading or if this is the same query we just ran
		if (isLoading || query === lastQuery || shouldSkipNextSearch) {
			shouldSkipNextSearch = false;
			return;
		}

		console.log('[ComboboxWiki] Searching for', kind, 'with query:', query);
		lastQuery = query;
		isLoading = true;
		hasError = false;

		try {
			let supabaseQuery = supabase
				.from('wikipage')
				.select('*')
				.eq('kind', kind)
				.eq('brand_id', brandId);

			if (query) {
				supabaseQuery = supabaseQuery.ilike('title->>en', `%${query}%`);
			}

			const { data, error } = await supabaseQuery.limit(10);

			if (error) {
				console.error('[ComboboxWiki] Query error:', error);
				shouldSkipNextSearch = true;
				pages = [];
				hasError = true;
				return;
			}

			console.log(`[ComboboxWiki] Found ${data?.length || 0} wiki pages for ${kind}`);
			shouldSkipNextSearch = true;
			pages = [...(data || [])];
		} catch (error) {
			console.error('[ComboboxWiki] Error:', error);
			shouldSkipNextSearch = true;
			pages = [];
			hasError = true;
		} finally {
			isLoading = false;
			console.log('[ComboboxWiki] Search completed, pages:', pages.length, 'hasError:', hasError);
		}
	}

	function handleSelect(page: DBWikiPage) {
		console.log(
			'[ComboboxWiki] Selected page:',
			getLocalizedText(page.title as LocalizedText, getLocale())
		);
		value = page.id;
		displayValue = getLocalizedText(page.title as LocalizedText, getLocale());
		open = false;
		onSelect(page);
	}

	// Load initial data just once
	onMount(() => {
		handleSearch('');
	});

	// Handle open state changes
	$effect(() => {
		if (open && pages.length === 0 && !isLoading && searchQuery !== lastQuery) {
			handleSearch(searchQuery);
		}
	});

	// Handle search query changes - only when the query actually changes
	let previousSearchQuery = '';
	$effect(() => {
		if (open && searchQuery !== previousSearchQuery && !isLoading) {
			previousSearchQuery = searchQuery;
			handleSearch(searchQuery);
		}
	});

	$effect(() => {
		console.log('[ComboboxWiki] State update:', {
			open,
			pagesCount: pages.length,
			isLoading,
			hasError
		});
	});

	function closeAndFocusTrigger() {
		open = false;
		tick().then(() => {
			triggerRef.focus();
		});
	}
</script>

<Popover.Root bind:open>
	<Popover.Trigger bind:ref={triggerRef} class="w-full">
		<div
			class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background"
		>
			{#if displayValue}
				<p class="truncate font-medium">{displayValue}</p>
				<div>
					<ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50" />
				</div>
			{:else}
				<span class="text-muted-foreground">
					{kind === 'dance_genre' ? 'Search dance genres...' : 'Search dance levels...'}
				</span>
				<ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50" />
			{/if}
		</div>
	</Popover.Trigger>
	<Popover.Content class="p-0" align="start">
		<Command.Root shouldFilter={false}>
			<Command.Input
				placeholder={kind === 'dance_genre' ? 'Search dance genres...' : 'Search dance levels...'}
				bind:value={searchQuery}
			/>
			<Command.List>
				{#if isLoading}
					<Command.Loading>
						<div class="flex items-center justify-center py-6">
							<Loader2 class="h-6 w-6 animate-spin text-muted-foreground" />
						</div>
					</Command.Loading>
				{:else if hasError}
					<Command.Empty>An error occurred while searching.</Command.Empty>
				{:else if pages.length === 0}
					<Command.Empty>
						No {kind === 'dance_genre' ? 'dance genres' : 'dance levels'} found.
					</Command.Empty>
				{:else}
					<Command.Group>
						{#each pages as page (page.id)}
							<Command.Item
								value={getLocalizedText(page.title as LocalizedText, getLocale())}
								onSelect={() => handleSelect(page)}
								class="w-full"
							>
								<div class="flex-1 overflow-hidden">
									<p class="truncate">
										{getLocalizedText(page.title as LocalizedText, getLocale())}
									</p>
									{#if page.brief}
										<p class="truncate text-xs text-muted-foreground">
											{getLocalizedText(page.brief as LocalizedText, getLocale())}
										</p>
									{/if}
								</div>
								<Check class={cn(value === page.id ? '' : 'text-transparent')} />
							</Command.Item>
						{/each}
					</Command.Group>
				{/if}
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>
