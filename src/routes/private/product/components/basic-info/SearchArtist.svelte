<!-- SearchArtist.svelte -->
<script lang="ts">
	import TokenSearch from './TokenSearch.svelte';
	import type { SpotifyArtist } from '../../types';
	import { searchSpotify } from '$lib/utils/spotify';
	import type { TokenSearchItem } from '../../types';

	const { onSelect } = $props<{
		onSelect: (artist: SpotifyArtist) => void;
	}>();

	let selectedArtists = $state<TokenSearchItem[]>([]);
	let searchValue = $state('');

	async function searchArtists(query: string): Promise<TokenSearchItem[]> {
		try {
			const response = await searchSpotify(query, 'artist');

			if (!response.artists?.items?.length) {
				console.log('No artists found in response');
				return [];
			}

			const items = response.artists.items.map((artist: SpotifyArtist) => {
				const item: TokenSearchItem = {
					id: artist.id,
					title: artist.name,
					subtitle: artist.genres?.slice(0, 2).join(', ') || '',
					imageUrl: artist.images?.[0]?.url,
					data: artist
				};
				console.log('Mapped item:', item);
				return item;
			});

			return items;
		} catch (error) {
			console.error('Search error:', error);
			return [];
		}
	}

	function handleChange(items: TokenSearchItem[]) {
		selectedArtists = items;
		if (items.length > 0) {
			const artist = items[0].data as SpotifyArtist;
			onSelect(artist);
		}
	}
</script>

<TokenSearch
	value={searchValue}
	placeholder="Search for an artist..."
	onSearch={searchArtists}
	selectedItems={selectedArtists}
	onChange={handleChange}
/>
