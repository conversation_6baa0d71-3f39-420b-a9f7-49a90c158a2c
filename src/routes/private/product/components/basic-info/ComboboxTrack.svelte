<!-- ComboboxTrack.svelte -->
<script lang="ts">
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { Button } from '$lib/components/ui/button';
	import { Check, ChevronsUpDown, Loader2, X } from '@lucide/svelte';
	import { searchSpotify } from '$lib/utils/spotify';
	import type { SpotifyTrack } from '../../types';
	import { cn } from '$lib/utils';
	import { tick, onMount } from 'svelte';

	interface Props {
		onSelect: (track: SpotifyTrack) => void;
	}

	const { onSelect }: Props = $props();

	let open = $state(false);
	let value = $state('');
	let searchQuery = $state('');
	let isLoading = $state(false);
	let tracks = $state<SpotifyTrack[]>([]);
	let selectedTrack = $state<SpotifyTrack | null>(null);
	let triggerRef = $state<HTMLButtonElement>(null!);
	let defaultSearch = $state('dance');
	let hasError = $state(false);
	let lastQuery = $state<string | null>(null);
	let shouldSkipNextSearch = $state(false);

	// Simple search handler with query tracking to avoid infinite loops
	async function handleSearch(query: string) {
		// Skip if already loading or if this is the same query we just ran
		if (isLoading || query === lastQuery || shouldSkipNextSearch) {
			shouldSkipNextSearch = false;
			return;
		}

		// Ensure query is not empty
		const searchTerm = query || defaultSearch;
		if (!searchTerm.trim()) {
			console.log('[ComboboxTrack] Skipping search with empty query');
			return;
		}

		console.log('[ComboboxTrack] Searching:', searchTerm);
		lastQuery = searchTerm;
		isLoading = true;
		hasError = false;

		try {
			const response = await searchSpotify(searchTerm, 'track');

			if (response?.tracks?.items) {
				// Set flag to skip the next search that might be triggered by state changes
				shouldSkipNextSearch = true;
				tracks = [...response.tracks.items];
				console.log('[ComboboxTrack] Found', tracks.length, 'tracks');
			} else {
				shouldSkipNextSearch = true;
				tracks = [];
				console.log('[ComboboxTrack] No tracks found');
			}
		} catch (error) {
			console.error('[ComboboxTrack] Search error:', error);
			shouldSkipNextSearch = true;
			tracks = [];
			hasError = true;
		} finally {
			isLoading = false;
			console.log(
				'[ComboboxTrack] Search completed, tracks:',
				tracks.length,
				'hasError:',
				hasError
			);
		}
	}

	function handleSelect(track: SpotifyTrack) {
		console.log('[ComboboxTrack] Selected track:', track.name);
		selectedTrack = track;
		value = track.name;
		open = false;
		onSelect(track);
	}

	function handleRemove() {
		selectedTrack = null;
		value = '';
	}

	// Load initial data just once - only if we have a default search term
	onMount(() => {
		if (defaultSearch.trim()) {
			handleSearch(defaultSearch);
		}
	});

	// Handle open state changes
	$effect(() => {
		if (open && tracks.length === 0 && !isLoading && searchQuery !== lastQuery) {
			// Only search if there's something to search for
			if (searchQuery.trim() || defaultSearch.trim()) {
				handleSearch(searchQuery || defaultSearch);
			}
		}
	});

	// Handle search query changes - only when the query actually changes
	let previousSearchQuery = '';
	$effect(() => {
		if (open && searchQuery !== previousSearchQuery && !isLoading && searchQuery.trim()) {
			previousSearchQuery = searchQuery;
			handleSearch(searchQuery);
		}
	});

	$effect(() => {
		console.log('[ComboboxTrack] State update:', {
			open,
			tracksCount: tracks.length,
			isLoading,
			hasError
		});
	});

	function closeAndFocusTrigger() {
		open = false;
		tick().then(() => {
			triggerRef.focus();
		});
	}
</script>

<Popover.Root bind:open>
	<Popover.Trigger bind:ref={triggerRef} class="w-full">
		<div
			class="border-input ring-offset-background flex h-10 w-full items-center justify-between rounded-md border bg-transparent px-3 py-2 text-sm"
		>
			{#if selectedTrack}
				<div class="flex items-center gap-2 overflow-hidden">
					{#if selectedTrack.album?.images?.[0]?.url}
						<img
							src={selectedTrack.album.images[0]?.url}
							alt=""
							class="h-6 w-6 shrink-0 rounded-md object-cover"
						/>
					{/if}
					<p class="truncate font-medium">{selectedTrack.name}</p>
				</div>
				<div class="flex gap-1">
					<button
						class="ring-offset-background h-4 w-4 rounded-sm opacity-70 hover:opacity-100 focus:outline-none"
						onclick={(e) => {
							e.stopPropagation();
							handleRemove();
						}}
					>
						<X class="h-4 w-4" />
					</button>
					<ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50" />
				</div>
			{:else}
				<span class="text-muted-foreground">Search for a track...</span>
				<ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50" />
			{/if}
		</div>
	</Popover.Trigger>
	<Popover.Content class="w-[--radix-popover-trigger-width] p-0" align="start">
		<Command.Root shouldFilter={false} value={searchQuery}>
			<Command.Input placeholder="Search for a track..." bind:value={searchQuery} />
			<Command.List>
				{#if isLoading}
					<Command.Loading>
						<div class="flex items-center justify-center py-6">
							<Loader2 class="text-muted-foreground h-6 w-6 animate-spin" />
						</div>
					</Command.Loading>
				{:else if hasError}
					<Command.Empty>An error occurred while searching.</Command.Empty>
				{:else if tracks.length === 0}
					<Command.Empty>
						{searchQuery ? 'No tracks found.' : 'Type to search for tracks'}
					</Command.Empty>
				{:else}
					<Command.Group>
						{#each tracks as track (track.id)}
							<Command.Item value={track.id} onSelect={() => handleSelect(track)} class="w-full">
								{#if track.album?.images?.[0]?.url}
									<img
										src={track.album.images[0].url}
										alt=""
										class="mr-2 h-8 w-8 rounded-md object-cover"
									/>
								{/if}
								<div class="flex-1 overflow-hidden">
									<p class="truncate">{track.name}</p>
									<p class="text-muted-foreground truncate text-xs">
										{track.artists.map((a) => a.name).join(', ')}
									</p>
								</div>
								<Check class={cn(selectedTrack?.id === track.id ? '' : 'text-transparent')} />
							</Command.Item>
						{/each}
					</Command.Group>
				{/if}
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>
