<!-- SearchWiki.svelte -->
<script lang="ts">
	import TokenSearch from './TokenSearch.svelte';
	import type { TokenSearchItem } from '../../types';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import type { Database } from '$lib/supabase/database.types';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';

	type DBWikiPage = Database['public']['Tables']['wikipage']['Row'];

	interface Props {
		kind: string;
		onSelect: (wikipage: DBWikiPage) => void;
		supabase: SupabaseClient<Database>;
		brandId: string;
	}

	const { kind, onSelect, supabase, brandId }: Props = $props();

	let selectedItems = $state<TokenSearchItem[]>([]);
	let searchValue = $state('');
	let pages: DBWikiPage[] = [];

	async function searchWikiPages(query: string): Promise<TokenSearchItem[]> {
		try {
			let supabaseQuery = supabase
				.from('wikipage')
				.select('*')
				.eq('kind', kind)
				.eq('brand_id', brandId);

			if (query) {
				supabaseQuery = supabaseQuery.ilike('title->>en', `%${query}%`);
			}

			const { data, error } = await supabaseQuery.limit(10);

			if (error) {
				console.error('[SearchWiki] Query error:', error);
				return [];
			}

			if (!data?.length) {
				return [];
			}

			pages = data;

			return data.map((page) => ({
				id: page.id,
				title: getLocalizedText(page.title as LocalizedText, getLocale()),
				subtitle: getLocalizedText(page.brief as LocalizedText, getLocale()) || '',
				imageUrl: undefined,
				data: page
			}));
		} catch (error) {
			console.error('[SearchWiki] Error:', error);
			return [];
		}
	}

	function handleChange(items: TokenSearchItem[]) {
		selectedItems = items;
		if (items.length > 0) {
			const wikipage = items[0].data as DBWikiPage;
			if (wikipage) {
				onSelect(wikipage);
			}
		}
	}
</script>

<div class="relative w-full">
	<TokenSearch
		value={searchValue}
		onSearch={searchWikiPages}
		{selectedItems}
		onChange={handleChange}
		placeholder={kind === 'dance_genre' ? 'Search dance genres...' : 'Search dance levels...'}
		loadImmediately={true}
		maxItems={1}
	/>
</div>
