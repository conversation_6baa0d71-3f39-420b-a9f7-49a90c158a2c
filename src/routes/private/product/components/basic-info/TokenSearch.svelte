<!-- TokenSearch.svelte -->
<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import { Card } from '$lib/components/ui/card';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { X } from '@lucide/svelte';
	import { fade } from 'svelte/transition';
	import { clickOutside } from '$lib/utils/click-outside';
	import type { TokenSearchItem } from '../../types';

	interface Props {
		value: string;
		placeholder?: string;
		loading?: boolean;
		onSearch: (query: string) => Promise<TokenSearchItem[]>;
		selectedItems: TokenSearchItem[];
		onChange: (items: TokenSearchItem[]) => void;
		loadImmediately?: boolean;
		maxItems?: number;
	}

	let {
		value = $bindable(''),
		placeholder = 'Search...',
		loading = false,
		onSearch,
		selectedItems = [],
		onChange,
		loadImmediately = false,
		maxItems = 5
	}: Props = $props();

	let results = $state<TokenSearchItem[]>([]);
	let showDropdown = $state(false);
	let debounceTimer: NodeJS.Timeout;

	let isMaxItemsReached = $derived(selectedItems.length >= maxItems);

	async function loadResults(query: string) {
		const searchResults = await onSearch(query);
		results = searchResults.filter(
			(item) => !selectedItems.some((selected) => selected.id === item.id)
		);
		showDropdown = results.length > 0 && !isMaxItemsReached;
	}

	$effect(() => {
		clearTimeout(debounceTimer);
		if (!value && loadImmediately && !isMaxItemsReached) {
			loadResults('');
		} else if (value && !isMaxItemsReached) {
			debounceTimer = setTimeout(() => loadResults(value), 300);
		} else {
			results = [];
			showDropdown = false;
		}
	});

	function handleFocus() {
		if (loadImmediately && !isMaxItemsReached) {
			loadResults('');
		}
	}

	function handleSelect(item: TokenSearchItem) {
		if (!selectedItems.some((selected) => selected.id === item.id) && !isMaxItemsReached) {
			onChange([...selectedItems, item]);
		}
		value = '';
		showDropdown = false;
	}

	function handleRemove(item: TokenSearchItem) {
		onChange(selectedItems.filter((selected) => selected.id !== item.id));
	}

	function handleClickOutside() {
		showDropdown = false;
	}
</script>

<div class="relative">
	<div class="flex flex-wrap gap-2 rounded-lg border bg-card p-2">
		{#each selectedItems as item}
			<div
				class="flex h-8 items-center gap-2 rounded-md bg-accent py-1.5 pl-2 pr-1 text-sm text-accent-foreground"
			>
				{#if item.imageUrl}
					<img src={item.imageUrl} alt="" class="h-5 w-5 rounded-md object-cover" />
				{/if}
				<span>{item.title}</span>
				<button
					class="rounded-md p-0.5 hover:bg-accent-foreground/10"
					onclick={() => handleRemove(item)}
				>
					<X class="h-4 w-4" />
				</button>
			</div>
		{/each}
		<div class="min-w-[200px] flex-1">
			{#if isMaxItemsReached}
				<div class="flex h-8 items-center justify-between text-sm">
					<span class="text-muted-foreground">Maximum items selected</span>
					<span class="px-1.5 py-0.5 tabular-nums tracking-wider text-muted-foreground/60"
						>{selectedItems.length}/{maxItems}</span
					>
				</div>
			{:else}
				<div class="flex items-center gap-2">
					<Input
						bind:value
						{placeholder}
						onfocus={handleFocus}
						class="h-8 border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
					/>
					<span class="px-1.5 py-0.5 text-sm tabular-nums tracking-wider text-muted-foreground/60"
						>{selectedItems.length}/{maxItems}</span
					>
				</div>
			{/if}
		</div>
	</div>

	{#if showDropdown && (results.length > 0 || loading)}
		<div
			transition:fade={{ duration: 100 }}
			use:clickOutside={handleClickOutside}
			class="absolute z-50 mt-1 w-full"
		>
			<Card class="p-2 shadow-lg">
				{#if loading}
					{#each Array(3) as _}
						<div class="flex items-center gap-2 p-2">
							<Skeleton class="h-8 w-8 rounded-lg" />
							<div class="flex-1 space-y-1">
								<Skeleton class="h-4 w-3/4" />
								<Skeleton class="h-3 w-1/2" />
							</div>
						</div>
					{/each}
				{:else}
					{#each results as item}
						<button
							class="flex w-full items-center gap-2 rounded-lg p-2 hover:bg-muted"
							onclick={() => handleSelect(item)}
						>
							{#if item.imageUrl}
								<img src={item.imageUrl} alt="" class="h-8 w-8 rounded-lg object-cover" />
							{/if}
							<div class="flex-1 text-left">
								<div class="font-medium">{item.title}</div>
								{#if item.subtitle}
									<div class="text-sm text-muted-foreground">{item.subtitle}</div>
								{/if}
							</div>
						</button>
					{/each}
				{/if}
			</Card>
		</div>
	{/if}
</div>
