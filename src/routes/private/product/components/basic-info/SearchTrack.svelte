<!-- SearchTrack.svelte -->
<script lang="ts">
	import TokenSearch from './TokenSearch.svelte';
	import type { SpotifyTrack } from '../../types';
	import { searchSpotify } from '$lib/utils/spotify';
	import type { TokenSearchItem } from '../../types';

	const { onSelect } = $props<{
		onSelect: (track: SpotifyTrack) => void;
	}>();

	let selectedTracks = $state<TokenSearchItem[]>([]);
	let searchValue = $state('');

	async function searchTracks(query: string): Promise<TokenSearchItem[]> {
		const response = await searchSpotify(query, 'track');
		return (response.tracks?.items || []).map((track: SpotifyTrack) => ({
			id: track.id,
			title: track.name,
			subtitle: track.artists.map((artist: { name: string }) => artist.name).join(', '),
			imageUrl: track.album?.images?.[0]?.url,
			data: track
		}));
	}

	function handleChange(items: TokenSearchItem[]) {
		selectedTracks = items;
		if (items.length > 0) {
			const track = items[0].data as SpotifyTrack;
			onSelect(track);
		}
	}
</script>

<TokenSearch
	value={searchValue}
	placeholder="Search for a track..."
	onSearch={searchTracks}
	selectedItems={selectedTracks}
	onChange={handleChange}
/>
