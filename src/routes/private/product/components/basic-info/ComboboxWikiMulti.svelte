<!-- ComboboxWikiMulti.svelte - Multi-select version of ComboboxWiki -->
<script lang="ts">
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { Button } from '$lib/components/ui/button';
	import { Check, ChevronsUpDown, Loader2, X, Plus } from '@lucide/svelte';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import type { Database } from '$lib/supabase/database.types';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import { cn } from '$lib/utils';
	import { tick, onMount } from 'svelte';
	import { Badge } from '$lib/components/ui/badge';

	type DBWikiPage = Database['public']['Tables']['wikipage']['Row'];

	interface Props {
		kind: string;
		onSelect: (selectedItems: { id: string; name: string; image_url?: string }[]) => void;
		supabase: SupabaseClient<Database>;
		brandId: string;
		selectedItems?: { id: string; name: string; image_url?: string }[];
	}

	const { kind, onSelect, supabase, brandId, selectedItems = [] }: Props = $props();

	let open = $state(false);
	let searchQuery = $state('');
	let isLoading = $state(false);
	let pages = $state<DBWikiPage[]>([]);
	let triggerRef = $state<HTMLButtonElement>(null!);
	let hasError = $state(false);
	let lastQuery = $state<string | null>(null);
	let shouldSkipNextSearch = $state(false);

	// Track selected items locally
	let selected = $state<DBWikiPage[]>([]);

	// Initialize selected items from props
	$effect(() => {
		if (selectedItems && selectedItems.length > 0) {
			// We don't have the full wikipage objects, just IDs and names
			// We'll need to fetch the full objects if needed, but for now
			// we can use what we have for display purposes
			const selectedIds = new Set(selectedItems.map((item) => item.id));

			// If we already have pages loaded, match them with selected IDs
			const matchedPages = pages.filter((page) => selectedIds.has(page.id));
			if (matchedPages.length > 0) {
				selected = matchedPages;
			}
		}
	});

	// Simple search handler
	async function handleSearch(query: string) {
		// Skip if already loading or if this is the same query we just ran
		if (isLoading || query === lastQuery || shouldSkipNextSearch) {
			shouldSkipNextSearch = false;
			return;
		}

		console.log('[ComboboxWikiMulti] Searching for', kind, 'with query:', query);
		lastQuery = query;
		isLoading = true;
		hasError = false;

		try {
			let supabaseQuery = supabase
				.from('wikipage')
				.select('*')
				.eq('kind', kind)
				.eq('brand_id', brandId);

			if (query) {
				supabaseQuery = supabaseQuery.ilike('title->>en', `%${query}%`);
			}

			const { data, error } = await supabaseQuery.limit(10);

			if (error) {
				console.error('[ComboboxWikiMulti] Query error:', error);
				shouldSkipNextSearch = true;
				pages = [];
				hasError = true;
				return;
			}

			console.log(`[ComboboxWikiMulti] Found ${data?.length || 0} wiki pages for ${kind}`);
			shouldSkipNextSearch = true;
			pages = [...(data || [])];
		} catch (error) {
			console.error('[ComboboxWikiMulti] Error:', error);
			shouldSkipNextSearch = true;
			pages = [];
			hasError = true;
		} finally {
			isLoading = false;
		}
	}

	function handleSelect(page: DBWikiPage) {
		// Toggle selection - add if not already selected, remove if already selected
		const index = selected.findIndex((p) => p.id === page.id);

		if (index >= 0) {
			// Remove item
			selected = selected.filter((p) => p.id !== page.id);
		} else {
			// Add item
			selected = [...selected, page];
		}

		// Notify parent
		notifyParent();

		// Close the popover after selection
		closeAndFocusTrigger();
	}

	function notifyParent() {
		// Convert selected wikipages to simplified format for form data
		const selectedForForm = selected.map((page) => ({
			id: page.id,
			name: getLocalizedText(page.title as LocalizedText, getLocale()),
			image_url: typeof page.avatar === 'string' ? page.avatar : undefined
		}));

		// Notify parent component with the formatted data, not the raw wikipages
		onSelect(selectedForForm);
	}

	function removeItem(id: string) {
		selected = selected.filter((p) => p.id !== id);
		notifyParent();
	}

	// Load initial data just once
	onMount(() => {
		handleSearch('');
	});

	// Handle open state changes
	$effect(() => {
		if (open && pages.length === 0 && !isLoading && searchQuery !== lastQuery) {
			handleSearch(searchQuery);
		}
	});

	// Handle search query changes - only when the query actually changes
	let previousSearchQuery = '';
	$effect(() => {
		if (open && searchQuery !== previousSearchQuery && !isLoading) {
			previousSearchQuery = searchQuery;
			handleSearch(searchQuery);
		}
	});

	function closeAndFocusTrigger() {
		open = false;
		tick().then(() => {
			triggerRef.focus();
		});
	}

	// Check if a page is currently selected
	function isSelected(page: DBWikiPage): boolean {
		return selected.some((p) => p.id === page.id);
	}
</script>

<div class="flex w-full flex-col gap-2">
	<!-- Display selected items as badges -->
	{#if selected.length > 0}
		<div class="flex flex-wrap gap-2">
			{#each selected as item (item.id)}
				<Badge variant="secondary" class="flex items-center gap-2 px-2 py-1.5 text-sm">
					{getLocalizedText(item.title as LocalizedText, getLocale())}
					<button
						type="button"
						class="inline-flex h-5 w-5 items-center justify-center rounded-full transition-colors hover:bg-muted-foreground/20"
						onclick={() => removeItem(item.id)}
						aria-label="Remove {getLocalizedText(item.title as LocalizedText, getLocale())}"
					>
						<X class="h-3.5 w-3.5" />
					</button>
				</Badge>
			{/each}
		</div>
	{/if}

	<Popover.Root bind:open>
		<Popover.Trigger bind:ref={triggerRef} class="w-full">
			<div
				class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background"
			>
				<span class="flex items-center gap-2 text-muted-foreground">
					<Plus class="h-4 w-4" />
					<span>Add {kind === 'person' ? 'instructor' : kind}...</span>
				</span>
				<ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50" />
			</div>
		</Popover.Trigger>
		<Popover.Content class="p-0" align="start">
			<Command.Root shouldFilter={false}>
				<Command.Input
					placeholder={`Search ${kind === 'person' ? 'instructors' : kind}...`}
					bind:value={searchQuery}
				/>
				<Command.List>
					{#if isLoading}
						<Command.Loading>
							<div class="flex items-center justify-center py-6">
								<Loader2 class="h-6 w-6 animate-spin text-muted-foreground" />
							</div>
						</Command.Loading>
					{:else if hasError}
						<Command.Empty>An error occurred while searching.</Command.Empty>
					{:else if pages.length === 0}
						<Command.Empty>
							No {kind === 'person' ? 'instructors' : kind} found.
						</Command.Empty>
					{:else}
						<Command.Group>
							{#each pages as page (page.id)}
								<Command.Item
									value={getLocalizedText(page.title as LocalizedText, getLocale())}
									onSelect={() => handleSelect(page)}
									class="w-full"
								>
									<div class="flex-1 overflow-hidden">
										<p class="truncate">
											{getLocalizedText(page.title as LocalizedText, getLocale())}
										</p>
										{#if page.brief}
											<p class="truncate text-xs text-muted-foreground">
												{getLocalizedText(page.brief as LocalizedText, getLocale())}
											</p>
										{/if}
									</div>
									<Check class={cn(isSelected(page) ? 'opacity-100' : 'opacity-0')} />
								</Command.Item>
							{/each}
						</Command.Group>
					{/if}
				</Command.List>
			</Command.Root>
		</Popover.Content>
	</Popover.Root>
</div>
