<!-- BasicInfoTab.svelte -->
<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Field, Control, Description, FieldErrors } from 'formsnap';
	import { Music, Search, X } from '@lucide/svelte';
	import type { formSchema, FormSchema } from '../../schemas';
	import type { SuperForm } from 'sveltekit-superforms';
	import type { SpotifyTrack, SpotifyArtist } from '../../types';
	import ComboboxTrack from './ComboboxTrack.svelte';
	import ComboboxWiki from './ComboboxWiki.svelte';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import type { Database } from '$lib/supabase/database.types';
	import type { Product } from '../../+page.server';
	import type { Brand } from '../../types';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';

	interface Props {
		form: SuperForm<FormSchema>;
		supabase: SupabaseClient<Database>;
		brand: Brand;
		product?: Product | null;
		onSave?: (data: FormSchema) => Promise<void>;
		onClose?: () => void;
	}

	const { form, supabase, brand, product, onSave, onClose }: Props = $props();
	const { form: formData, enhance } = form;

	let selectedTrack = $state<SpotifyTrack | null>(null);
	let isLoading = $state(false);

	// Initialize form data with existing values
	$effect(() => {
		if (product?.metadata) {
			const metadata = product.metadata as Record<string, any>;
			selectedTrack = (metadata.tracks?.[0] as SpotifyTrack) ?? null;
		}
	});

	function handleTrackSelect(track: SpotifyTrack) {
		selectedTrack = track;

		// Map the Spotify track and artists to match the schema-expected structure
		const formattedTrack = {
			id: track.id,
			name: track.name,
			artists: track.artists.map((artist) => ({
				id: artist.id,
				name: artist.name
			})),
			album: {
				id: track.album.id,
				name: track.album.name,
				images: track.album.images.map((image) => ({
					url: image.url,
					height: image.height || 0,
					width: image.width || 0
				}))
			},
			preview_url: track.preview_url
		};

		const formattedArtists = track.artists.map((artist) => ({
			id: artist.id,
			name: artist.name,
			images: artist.images?.map((image) => ({
				url: image.url,
				height: image.height || 0,
				width: image.width || 0
			}))
		}));

		// Update the form data with the formatted track and artists
		formData.update(($formData) => ({
			...$formData,
			tracks: [formattedTrack],
			artists: formattedArtists
		}));
	}

	function handleLevelSelect(level: Database['public']['Tables']['wikipage']['Row']) {
		formData.update(($formData) => ({
			...$formData,
			dance_level: level.id as FormSchema['dance_level']
		}));
	}

	function handleGenreSelect(genre: Database['public']['Tables']['wikipage']['Row']) {
		formData.update(($formData) => ({
			...$formData,
			dance_genre: genre.id
		}));
	}

	async function handleSubmit() {
		if (onSave) {
			isLoading = true;
			try {
				await onSave($formData);
				onClose?.();
			} catch (error) {
				console.error('Failed to save:', error);
			} finally {
				isLoading = false;
			}
		}
	}
</script>

<div class="h-full overflow-y-auto">
	<div class="space-y-3 p-1 pb-6">
		<div class="space-y-3">
			<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
				<div class="flex flex-col space-y-1.5">
					<Field {form} name="dance_level">
						<Control>
							{#snippet children({ props })}
								<Label>Dance Level<span class="text-destructive ml-1">*</span></Label>
								<ComboboxWiki
									kind="dance_level"
									onSelect={handleLevelSelect}
									{supabase}
									brandId={brand.id}
								/>
							{/snippet}
						</Control>
						<Description class="mt-2 text-muted-foreground">
							Select the appropriate dance level
						</Description>
						<FieldErrors class="mt-1 text-xs text-destructive" />
					</Field>
				</div>

				<div class="flex flex-col space-y-1.5">
					<Field {form} name="dance_genre">
						<Control>
							{#snippet children({ props })}
								<Label>Dance Genre<span class="text-destructive ml-1">*</span></Label>
								<ComboboxWiki
									kind="dance_genre"
									onSelect={handleGenreSelect}
									{supabase}
									brandId={brand.id}
								/>
							{/snippet}
						</Control>
						<Description class="mt-2 text-muted-foreground">
							Select the dance genre
						</Description>
						<FieldErrors class="mt-1 text-xs text-destructive" />
					</Field>
				</div>
			</div>
			<LocalizedTextControl {form} name="title" label="Title" />

			<LocalizedTextControl {form} name="description" label="Description" multiline={true} />

			<div class="space-y-1">
				<Label>Track</Label>
				<div class="flex flex-col">
					<ComboboxTrack onSelect={handleTrackSelect} />
				</div>
				<p class="text-sm text-muted-foreground">
					The selected track will automatically include its artists
				</p>
			</div>
		</div>
	</div>
</div>
