<!-- src/routes/private/product/components/EditProductModal.svelte -->
<script lang="ts">
	import type { Database } from '$lib/supabase/database.types';
	import type { Brand } from '../types';
	import type { Product as BaseProduct } from '../+page.server';
	import { formSchema, type FormSchema } from '../schemas';
	import ResponsiveModal from '$lib/components/shared/ResponsiveModal.svelte';
	import { Tabs, TabsContent, TabsList, TabsTrigger } from '$lib/components/ui/tabs';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Sparkles, AlertCircle } from '@lucide/svelte';
	import BasicInfoTab from './basic-info/BasicInfoTab.svelte';
	import ScheduleTab from './schedule/ScheduleTab.svelte';
	import ProductFormTab from './product-form/ProductFormTab.svelte';
	import PricingTab from './pricing/PricingTab.svelte';
	import { superForm } from 'sveltekit-superforms/client';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import { zod } from 'sveltekit-superforms/adapters';
	import type { LocalizedText } from '$lib/utils/localization';
	import type { SpotifyArtist, SpotifyTrack, VideoKind } from '../types';
	import SuperDebug from 'sveltekit-superforms';

	type UpsertProductInput = Database['public']['CompositeTypes']['upsert_product_input_product'];
	type UpsertMetadataInput = Database['public']['CompositeTypes']['upsert_product_input_metadata'];
	type UpsertEventProductInput =
		Database['public']['CompositeTypes']['upsert_product_input_event_product'];

	// Add these type definitions for the inputs
	type EventProductInput =
		Database['public']['CompositeTypes']['upsert_offering_input_event_product'];
	type ProductInput = Database['public']['CompositeTypes']['upsert_offering_input_product'];
	type EventInput = Database['public']['CompositeTypes']['upsert_offering_input_event'];
	type MetadataInput = Database['public']['CompositeTypes']['upsert_offering_input_metadata'];

	// Define a more accurate Product type based on the actual query response
	type Product = Omit<
		BaseProduct,
		'event_products' | 'prices' | 'metadata_wikipages' | 'metadata_tracks'
	> & {
		event_product?: Array<{
			id: string;
			kind: string;
			event_id: string;
			event: {
				kind: string;
				start_at: string;
				duration_minute: number;
				space_id: string | null;
			};
		}>;
		product_price?: Array<{
			id: string;
			price_id: string;
			cost_units: number;
			start_at: string;
			end_at: string | null;
			stock: number | null;
			order_requirement_id: string | null;
			reward_price_id: string | null;
			reward_price_units: number | null;
		}>;
		metadata?: {
			id: string | null;
			kind: string;
			title: any;
			subtitle: any;
			desc: any;
			message: any;
			custom_attribute: any;
			promo_message: any;
			promo_video_url: string | null;
			promo_image_url: string | null;
			promo_webpage_url: string | null;
			metadata_wikipage?: Array<{
				id: string;
				relation: string;
				wikipage_id: string;
			}>;
			metadata_track?: Array<{
				id: string;
				track_id: string;
			}>;
		} | null;
	};

	interface Props {
		product?: Product | null;
		onClose: () => void;
		open: boolean;
		supabase: SupabaseClient<Database>;
		brand: Brand;
	}

	let { product, onClose, open, supabase, brand }: Props = $props();

	let currentTab = $state<'basic' | 'schedule' | 'form' | 'pricing'>('basic');
	let videoUrl = $state('');
	let isProcessingVideo = $state(false);
	let processingError = $state<string | null>(null);
	let isLoading = $state(false);
	let isInitializing = $state(false);
	let tabsWithErrors = $state<Record<'basic' | 'schedule' | 'form' | 'pricing', boolean>>({
		basic: false,
		schedule: false,
		form: false,
		pricing: false
	});
	let formSubmitted = $state(false);

	$effect(() => {
		// This effect runs once to ensure initialization is complete before enabling other reactivity
		if (!isInitializing) {
			isInitializing = true;
			// Set any initial state that depends on props here
			// ...
			// Mark initialization as complete after a tick
			setTimeout(() => {
				isInitializing = false;
			}, 0);
		}
	});

	// Effect to update tab error indicators when errors change
	$effect(() => {
		if (isInitializing) return;

		// Check for errors in the errors store
		const formErrors = $errors;
		if (Object.keys(formErrors).length === 0) return;

		updateTabsWithErrors(formErrors);
	});

	// Helper function to update which tabs have errors
	function updateTabsWithErrors(formErrors: Record<string, any>) {
		tabsWithErrors = {
			basic: ['dance_level', 'dance_genre', 'title', 'description'].some(
				(field) => formErrors[field] !== undefined
			),
			schedule: ['events'].some((field) => {
				// Check if events array has errors or any of its children have errors
				if (formErrors.events) {
					return true;
				}
				// Check for deep path errors like events[0].space_id
				return Object.keys(formErrors).some((key) => key.startsWith('events['));
			}),
			form: ['product_form_type'].some((field) => formErrors[field] !== undefined),
			pricing: ['product_form_factor'].some((field) => {
				// Check for product_form_factor errors or any of its children
				if (formErrors.product_form_factor) {
					return true;
				}
				// Check for deep path errors
				return Object.keys(formErrors).some((key) => key.startsWith('product_form_factor['));
			})
		};
	}

	function convertProductToInput(product: Product | null | undefined): UpsertProductInput {
		if (!product) {
			return {
				product_id: null,
				product_brand_id: brand.id,
				product_kind: 'class',
				product_stock: 100,
				product_publishing_state: 'draft',
				product_open_to_buy_at: new Date().toISOString(),
				product_close_to_buy_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
				product_creator_id: null,
				metadata: {
					metadata_id: null,
					metadata_kind: 'class',
					metadata_title: { en: '' } as LocalizedText,
					metadata_subtitle: null,
					metadata_desc: { en: '' } as LocalizedText,
					metadata_message: null,
					metadata_custom_attribute: {
						dance_level: 'all-levels',
						dance_genre: '',
						video_kind: null
					},
					metadata_promo_message: null,
					metadata_promo_video_url: null,
					metadata_promo_image_url: null,
					metadata_promo_webpage_url: null
				},
				event_products: [],
				product_prices: [
					{
						product_price_id: null,
						product_price_price_id: null,
						product_price_cost_units: 1000,
						product_price_start_at: new Date().toISOString(),
						product_price_end_at: null,
						product_price_stock: null,
						product_price_order_requirement_id: null,
						product_price_reward_price_id: null,
						product_price_reward_price_units: null
					}
				],
				metadata_wikipages: [],
				metadata_tracks: []
			};
		}

		// Safely cast to LocalizedText
		const metadataTitle = (product.metadata?.title as LocalizedText) || { en: '' };
		const metadataDesc = (product.metadata?.desc as LocalizedText) || { en: '' };
		const metadataMessage = product.metadata?.message as LocalizedText;
		const metadataCustomAttr = (product.metadata?.custom_attribute as Record<string, any>) || {
			dance_level: 'all-levels',
			video_kind: null
		};
		const metadataPromoMsg = product.metadata?.promo_message as LocalizedText;

		// Handle the nested data structure from the query
		const eventProducts = product.event_product ?? [];
		const productPrices = product.product_price ?? [];
		const metadataWikipages = product.metadata?.metadata_wikipage ?? [];
		const metadataTracks = product.metadata?.metadata_track ?? [];

		return {
			product_id: product.id,
			product_brand_id: brand.id,
			product_kind: product.kind,
			product_stock: product.stock,
			product_publishing_state: product.publishing_state,
			product_open_to_buy_at: product.open_to_buy_at,
			product_close_to_buy_at: product.close_to_buy_at,
			product_creator_id: product.creator_id,
			metadata: {
				metadata_id: product.metadata?.id || null,
				metadata_kind: product.metadata?.kind || 'class',
				metadata_title: metadataTitle,
				metadata_subtitle: product.metadata?.subtitle as LocalizedText,
				metadata_desc: metadataDesc,
				metadata_message: metadataMessage,
				metadata_custom_attribute: metadataCustomAttr,
				metadata_promo_message: metadataPromoMsg,
				metadata_promo_video_url: product.metadata?.promo_video_url ?? null,
				metadata_promo_image_url: product.metadata?.promo_image_url ?? null,
				metadata_promo_webpage_url: product.metadata?.promo_webpage_url ?? null
			},
			event_products:
				eventProducts.map((ep) => ({
					event_product_id: ep.id,
					event_product_relation: ep.kind,
					event_id: ep.event_id,
					event_kind: ep.event.kind,
					event_start_at: ep.event.start_at,
					event_duration_minute: ep.event.duration_minute,
					event_space_id: ep.event.space_id
				})) || [],
			product_prices:
				productPrices.map((price) => ({
					product_price_id: price.id,
					product_price_price_id: price.price_id,
					product_price_cost_units: price.cost_units,
					product_price_start_at: price.start_at,
					product_price_end_at: price.end_at,
					product_price_stock: price.stock,
					product_price_order_requirement_id: price.order_requirement_id,
					product_price_reward_price_id: price.reward_price_id,
					product_price_reward_price_units: price.reward_price_units
				})) || [],
			metadata_wikipages:
				metadataWikipages.map((mw) => ({
					metadata_wikipage_relation: mw.relation,
					metadata_wikipage_id: mw.id,
					wikipage_id: mw.wikipage_id
				})) || [],
			metadata_tracks:
				metadataTracks.map((mt) => ({
					metadata_track_id: mt.id,
					track_id: mt.track_id
				})) || []
		};
	}

	const productInput = convertProductToInput(product);

	// Get metadata from product safely
	const metadataTitle = (product?.metadata?.title as LocalizedText) || { en: '' };
	const metadataDesc = (product?.metadata?.desc as LocalizedText) || { en: '' };
	const customAttr = (product?.metadata?.custom_attribute as Record<string, any>) || {};

	// Initialize form with localized text structure for title and description
	const initialFormData: FormSchema = {
		// Initialize title and description as LocalizedText objects for LocalizedTextControl
		title: metadataTitle,
		description: metadataDesc,
		dance_level: (customAttr.dance_level || 'all-levels') as FormSchema['dance_level'],
		dance_genre: customAttr.dance_genre || '',
		video_kind: (customAttr.video_kind || null) as VideoKind,
		video_url: product?.metadata?.promo_video_url || undefined,
		cover_url: product?.metadata?.promo_image_url || undefined,
		tracks: [],
		artists: [],
		product_form_type: 'bundled',
		product_form_factor: [],
		events:
			product?.event_product?.map((ep) => ({
				id: ep.id,
				start_time: ep.event.start_at,
				end_time: new Date(
					new Date(ep.event.start_at).getTime() + ep.event.duration_minute * 60000
				).toISOString(),
				space_id: ep.event.space_id || '',
				assigned_instructors: []
			})) || []
	};

	// Create the superForm - don't destructure the form object since we need to pass the whole thing
	const superFormObj = superForm(initialFormData, {
		validators: zod(formSchema),
		resetForm: true,
		SPA: true,
		dataType: 'json',
		onUpdated: ({ form }) => {
			// When form is updated and there are errors, check them
			if (formSubmitted && Object.keys(form.errors).length > 0) {
				updateTabsWithErrors(form.errors);
			}
		}
	});

	// Destructure what we need from the form
	const { form, enhance, submitting, errors } = superFormObj;

	// Add an effect to ensure product_form_factor is in sync with product_form_type
	let isSyncingFormFactor = $state(false);

	$effect(() => {
		// Skip if already syncing or during initialization
		if (isSyncingFormFactor || isInitializing) return;

		// Get current form type and form factor
		const formType = $form.product_form_type;
		const formFactor = $form.product_form_factor || [];

		// Check if there's a mismatch
		const hasMismatch = formFactor.some((config) => {
			if (formType === 'bundled' && config.product_type !== 'bundled') return true;
			if (formType === 'individual' && config.product_type !== 'individual') return true;
			if (formType === 'trial' && !['trial_first', 'trial_rest'].includes(config.product_type))
				return true;
			return false;
		});

		// If there's a mismatch or the form factor is empty, update it
		if (hasMismatch || formFactor.length === 0) {
			isSyncingFormFactor = true;

			// Get default price ID
			const defaultPriceId = getDefaultPriceId();

			// Create the new form factor based on form type
			type FormFactorType = {
				product_type: 'bundled' | 'individual' | 'trial_first' | 'trial_rest';
				prices: { price_id: string; cost_units: number }[];
				event_index?: number;
			}[];

			let newFormFactor: FormFactorType;
			if (formType === 'bundled') {
				newFormFactor = [
					{
						product_type: 'bundled',
						prices: [
							{
								price_id: defaultPriceId,
								cost_units: 2 // Default cost of 2 points
							}
						]
					}
				];
			} else if (formType === 'individual') {
				newFormFactor = [
					{
						product_type: 'individual',
						prices: [
							{
								price_id: defaultPriceId,
								cost_units: 2 // Default cost of 2 points
							}
						]
					}
				];
			} else if (formType === 'trial') {
				newFormFactor = [
					{
						product_type: 'trial_first',
						prices: [
							{
								price_id: defaultPriceId,
								cost_units: 1 // Trial class cost of 1 point
							}
						]
					},
					{
						product_type: 'trial_rest',
						prices: [
							{
								price_id: defaultPriceId,
								cost_units: 2 // Main product cost of 2 points
							}
						]
					}
				];
			} else {
				// Default to bundled if no valid type is provided
				newFormFactor = [
					{
						product_type: 'bundled',
						prices: [
							{
								price_id: defaultPriceId,
								cost_units: 2
							}
						]
					}
				];
			}

			// Check if current form factor already matches what we're about to set
			const isAlreadyMatching = JSON.stringify(formFactor) === JSON.stringify(newFormFactor);

			if (!isAlreadyMatching) {
				// Update the form with the new form factor
				form.update(($formData) => ({
					...$formData,
					product_form_factor: newFormFactor
				}));
			}

			// Reset syncing flag after a longer timeout
			setTimeout(() => {
				isSyncingFormFactor = false;
			}, 50);
		}
	});

	async function handleProcessVideo() {
		if (!videoUrl) return;
		isProcessingVideo = true;
		processingError = null;
		try {
			// TODO: Implement video processing
			await new Promise((resolve) => setTimeout(resolve, 1000));
		} catch (error) {
			processingError = error instanceof Error ? error.message : 'Failed to process video';
		} finally {
			isProcessingVideo = false;
		}
	}

	function getDefaultPriceId(): string {
		// If there are prices already in the form, use the first one
		const firstConfig = $form.product_form_factor?.[0];
		const firstPrice = firstConfig?.prices?.[0]?.price_id;
		if (firstPrice) {
			return firstPrice;
		}

		// Default price ID if none is available
		return 'missing-price-id';
	}

	async function handleSubmit() {
		// Set a flag to indicate form has been submitted (for error handling)
		formSubmitted = true;

		// Get the current form data
		const formData = $form;

		// Perform manual validation
		let hasErrors = false;
		const errors: Record<string, string[]> = {};

		// Validate dance_level and dance_genre
		if (!formData.dance_level) {
			errors.dance_level = ['Dance level is required'];
			hasErrors = true;
		}

		if (!formData.dance_genre) {
			errors.dance_genre = ['Dance genre is required'];
			hasErrors = true;
		}

		// Validate time slots
		if (!formData.events || formData.events.length === 0) {
			errors.events = ['At least one time slot must be selected'];
			hasErrors = true;
		} else {
			// Check for space_id and assigned_instructors in each event
			formData.events.forEach((event, index) => {
				if (!event.space_id) {
					errors[`events[${index}].space_id`] = ['Space selection is required'];
					hasErrors = true;
				}

				if (!event.assigned_instructors || event.assigned_instructors.length === 0) {
					errors[`events[${index}].assigned_instructors`] = [
						'At least one instructor must be assigned'
					];
					hasErrors = true;
				}
			});
		}

		// Validate product form
		if (!formData.product_form_type) {
			errors.product_form_type = ['Product form type is required'];
			hasErrors = true;
		}

		// Validate pricing
		if (!formData.product_form_factor || formData.product_form_factor.length === 0) {
			errors.product_form_factor = ['Product pricing is required'];
			hasErrors = true;
		} else {
			// Check that each price has a price_id and non-zero cost_units
			formData.product_form_factor.forEach((factor, index) => {
				if (!factor.prices || factor.prices.length === 0) {
					errors[`product_form_factor[${index}].prices`] = ['At least one price is required'];
					hasErrors = true;
				} else {
					factor.prices.forEach((price, priceIndex) => {
						if (!price.price_id) {
							errors[`product_form_factor[${index}].prices[${priceIndex}].price_id`] = [
								'Price selection is required'
							];
							hasErrors = true;
						}
						if (!price.cost_units || price.cost_units <= 0) {
							errors[`product_form_factor[${index}].prices[${priceIndex}].cost_units`] = [
								'Cost must be greater than 0'
							];
							hasErrors = true;
						}
					});
				}
			});
		}

		// If we have errors, update the error store
		if (hasErrors) {
			// Update the error store
			superFormObj.errors.set(errors);

			// Update tab indicators
			updateTabsWithErrors(errors);

			// Change to the first tab with errors
			const tabsOrder = ['basic', 'schedule', 'form', 'pricing'] as const;
			for (const tab of tabsOrder) {
				if (tabsWithErrors[tab]) {
					currentTab = tab;
					break;
				}
			}

			return; // Don't proceed with submission
		}

		isLoading = true;
		try {
			// Get form data from SuperForm using store value
			const currentFormData = $form;

			// Get default price ID
			const defaultPriceId = getDefaultPriceId();

			// Create a metadata ID for this submission
			const metadataId = crypto.randomUUID();

			// Get auth data (verify the user has permission)
			const { data: authData } = await supabase.auth.getSession();
			const userId = authData?.session?.user?.id;

			if (!userId) {
				throw new Error('User not authenticated');
			}

			// Initialize arrays for the RPC call
			const metadataInputs: any[] = [];
			const productInputs: any[] = [];
			const eventInputs: any[] = [];
			const eventProductInputs: any[] = [];

			// Add metadata record
			metadataInputs.push({
				metadata_id: metadataId,
				metadata_kind: 'class',
				metadata_title: currentFormData.title,
				metadata_subtitle: null,
				metadata_desc: currentFormData.description,
				metadata_message: null,
				metadata_custom_attribute: {
					dance_level: currentFormData.dance_level,
					dance_genre: currentFormData.dance_genre,
					video_kind: currentFormData.video_kind,
					tracks: currentFormData.tracks,
					product_form_type: currentFormData.product_form_type
				},
				metadata_promo_message: null,
				metadata_promo_video_url: currentFormData.video_url || null,
				metadata_promo_image_url: currentFormData.cover_url || null,
				metadata_promo_webpage_url: null,
				// Note: Instructors are handled as metadata_wikipage entries with relation = "instructor"
				// They are not part of the form data but will be managed by the backend
				metadata_wikipages: [],
				metadata_tracks: []
			});

			// Construct the product prices based on the product_form_factor array or fallback to default prices
			type ProductPriceConfig = {
				product_type: 'bundled' | 'individual' | 'trial_first' | 'trial_rest';
				prices: {
					price_id: string;
					cost_units: number;
				}[];
			};

			let productPriceConfigs: ProductPriceConfig[] = [];

			if (currentFormData.product_form_factor && currentFormData.product_form_factor.length > 0) {
				// Only use product_form_factor entries that have valid price IDs
				productPriceConfigs = currentFormData.product_form_factor
					.map((config: any) => ({
						...config,
						prices: config.prices.filter((p: any) => p.price_id) // Only include prices with an ID
					}))
					.filter((config: any) => config.prices.length > 0); // Only include configs with prices
			} else {
				// Fallback to default price configuration based on product form type
				const productType = currentFormData.product_form_type;
				if (productType === 'bundled') {
					// Find the default price ID
					productPriceConfigs = [
						{
							product_type: 'bundled',
							prices: [
								{
									price_id: defaultPriceId,
									cost_units: 2 // Default cost of 2 points
								}
							]
						}
					];
				} else if (productType === 'individual') {
					// Find the default price ID
					productPriceConfigs = [
						{
							product_type: 'individual',
							prices: [
								{
									price_id: defaultPriceId,
									cost_units: 2 // Default cost of 2 points
								}
							]
						}
					];
				} else if (productType === 'trial') {
					// Find the default price ID
					productPriceConfigs = [
						{
							product_type: 'trial_first',
							prices: [
								{
									price_id: defaultPriceId,
									cost_units: 1 // Trial class cost of 1 point
								}
							]
						},
						{
							product_type: 'trial_rest',
							prices: [
								{
									price_id: defaultPriceId,
									cost_units: 2 // Main product cost of 2 points
								}
							]
						}
					];
				}
			}

			// Only continue if we have valid prices
			if (productPriceConfigs.length === 0) {
				throw new Error('At least one price must be defined for the product');
			}

			// Use the first price for each product type, if available
			const getDefaultPrice = (
				productType: 'bundled' | 'individual' | 'trial_first' | 'trial_rest'
			) => {
				const config = productPriceConfigs.find((c) => c.product_type === productType);
				if (!config || config.prices.length === 0) {
					return null; // No price found
				}

				return config.prices[0];
			};

			// Handle logic based on product form type
			switch (currentFormData.product_form_type) {
				case 'bundled': {
					// For bundled products, create a single product for all events
					const productId = crypto.randomUUID();
					const bundledPrice = getDefaultPrice('bundled');

					if (!bundledPrice) {
						throw new Error('No valid price found for bundled product');
					}

					productInputs.push({
						product_id: null,
						product_brand_id: brand.id,
						product_kind: 'class',
						product_stock: 100,
						product_publishing_state: 'draft',
						product_open_to_buy_at: new Date().toISOString(),
						product_close_to_buy_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
						product_creator_id: userId,
						metadata_id: metadataId,
						product_prices: [
							{
								product_price_id: null,
								product_price_price_id: bundledPrice.price_id,
								product_price_cost_units: bundledPrice.cost_units,
								product_price_start_at: new Date().toISOString(),
								product_price_end_at: null,
								product_price_stock: null,
								product_price_order_requirement_id: null,
								product_price_reward_price_id: null,
								product_price_reward_price_units: null
							}
						]
					});

					// Create events for each time slot
					currentFormData.events.forEach(
						(
							slot: {
								id: string;
								start_time: string;
								end_time: string;
								space_id: string;
							},
							index: number
						) => {
							const eventId = crypto.randomUUID();

							// Calculate duration minutes
							const startTime = new Date(slot.start_time);
							const endTime = new Date(slot.end_time);
							const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / 60000);

							eventInputs.push({
								event_id: null,
								event_kind: 'dance_class',
								event_start_at: slot.start_time,
								event_duration_minute: durationMinutes,
								event_space_id: slot.space_id || null,
								event_brand_id: brand.id,
								event_publishing_state: 'draft',
								event_creator_id: userId,
								metadata_id: metadataId
							});

							// Link event to the single product
							eventProductInputs.push({
								event_product_id: null,
								event_product_relation: 'main',
								event_id: eventId,
								product_id: productId
							});
						}
					);
					break;
				}

				case 'individual': {
					// For individual products, create one product per event
					const individualPrice = getDefaultPrice('individual');

					if (!individualPrice) {
						throw new Error('No valid price found for individual product');
					}

					currentFormData.events.forEach(
						(
							slot: {
								id: string;
								start_time: string;
								end_time: string;
								space_id: string;
							},
							index: number
						) => {
							const productId = crypto.randomUUID();
							const eventId = crypto.randomUUID();

							// Create a product for this event
							productInputs.push({
								product_id: null,
								product_brand_id: brand.id,
								product_kind: 'class',
								product_stock: 100,
								product_publishing_state: 'draft',
								product_open_to_buy_at: new Date().toISOString(),
								product_close_to_buy_at: new Date(
									Date.now() + 365 * 24 * 60 * 60 * 1000
								).toISOString(),
								product_creator_id: userId,
								metadata_id: metadataId,
								product_prices: [
									{
										product_price_id: null,
										product_price_price_id: individualPrice.price_id,
										product_price_cost_units: individualPrice.cost_units,
										product_price_start_at: new Date().toISOString(),
										product_price_end_at: null,
										product_price_stock: null,
										product_price_order_requirement_id: null,
										product_price_reward_price_id: null,
										product_price_reward_price_units: null
									}
								]
							});

							// Calculate duration minutes
							const startTime = new Date(slot.start_time);
							const endTime = new Date(slot.end_time);
							const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / 60000);

							// Create an event for this time slot
							eventInputs.push({
								event_id: null,
								event_kind: 'dance_class',
								event_start_at: slot.start_time,
								event_duration_minute: durationMinutes,
								event_space_id: slot.space_id || null,
								event_brand_id: brand.id,
								event_publishing_state: 'draft',
								event_creator_id: userId,
								metadata_id: metadataId
							});

							// Link this event to its product
							eventProductInputs.push({
								event_product_id: null,
								event_product_relation: 'main',
								event_id: eventId,
								product_id: productId
							});
						}
					);
					break;
				}

				case 'trial': {
					// For trial mode, create one product for the first event and one for the rest
					if (currentFormData.events.length > 0) {
						const trialProductId = crypto.randomUUID();
						const mainProductId = crypto.randomUUID();

						const trialPrice = getDefaultPrice('trial_first');
						const mainPrice = getDefaultPrice('trial_rest');

						if (!trialPrice) {
							throw new Error('No valid price found for trial product');
						}

						// Create the trial product (first event)
						productInputs.push({
							product_id: null,
							product_brand_id: brand.id,
							product_kind: 'class',
							product_stock: 100,
							product_publishing_state: 'draft',
							product_open_to_buy_at: new Date().toISOString(),
							product_close_to_buy_at: new Date(
								Date.now() + 365 * 24 * 60 * 60 * 1000
							).toISOString(),
							product_creator_id: userId,
							metadata_id: metadataId,
							product_prices: [
								{
									product_price_id: null,
									product_price_price_id: trialPrice.price_id,
									product_price_cost_units: trialPrice.cost_units,
									product_price_start_at: new Date().toISOString(),
									product_price_end_at: null,
									product_price_stock: null,
									product_price_order_requirement_id: null,
									product_price_reward_price_id: null,
									product_price_reward_price_units: null
								}
							]
						});

						// Create the main product (all other events)
						if (currentFormData.events.length > 1) {
							if (!mainPrice) {
								throw new Error('No valid price found for main product after trial');
							}

							productInputs.push({
								product_id: null,
								product_brand_id: brand.id,
								product_kind: 'class',
								product_stock: 100,
								product_publishing_state: 'draft',
								product_open_to_buy_at: new Date().toISOString(),
								product_close_to_buy_at: new Date(
									Date.now() + 365 * 24 * 60 * 60 * 1000
								).toISOString(),
								product_creator_id: userId,
								metadata_id: metadataId,
								product_prices: [
									{
										product_price_id: null,
										product_price_price_id: mainPrice.price_id,
										product_price_cost_units: mainPrice.cost_units,
										product_price_start_at: new Date().toISOString(),
										product_price_end_at: null,
										product_price_stock: null,
										product_price_order_requirement_id: null,
										product_price_reward_price_id: null,
										product_price_reward_price_units: null
									}
								]
							});
						}

						// Process all time slots
						currentFormData.events.forEach(
							(
								slot: {
									id: string;
									start_time: string;
									end_time: string;
									space_id: string;
								},
								index: number
							) => {
								const eventId = crypto.randomUUID();

								// Calculate duration minutes
								const startTime = new Date(slot.start_time);
								const endTime = new Date(slot.end_time);
								const durationMinutes = Math.round(
									(endTime.getTime() - startTime.getTime()) / 60000
								);

								// Create an event for this time slot
								eventInputs.push({
									event_id: null,
									event_kind: 'class',
									event_start_at: slot.start_time,
									event_duration_minute: durationMinutes,
									event_space_id: slot.space_id || null,
									event_brand_id: brand.id,
									event_publishing_state: 'draft',
									event_creator_id: userId,
									metadata_id: metadataId
								});

								// Link this event to the appropriate product
								if (index === 0) {
									// First event goes to trial product
									eventProductInputs.push({
										event_product_id: null,
										event_product_relation: 'main',
										event_id: eventId,
										product_id: trialProductId
									});
								} else {
									// All other events go to main product
									eventProductInputs.push({
										event_product_id: null,
										event_product_relation: 'main',
										event_id: eventId,
										product_id: mainProductId
									});
								}
							}
						);
					}
					break;
				}
			}

			// Call the RPC function
			const { data, error: rpcError } = await supabase.rpc('client_upsert_offering' as any, {
				upsert_offering_input_all_event_product: eventProductInputs,
				upsert_offering_input_all_product: productInputs,
				upsert_offering_input_all_event: eventInputs,
				upsert_offering_input_all_metadata: metadataInputs,
				upsert_offering_input_request_id: null,
				upsert_offering_input_perform_action: true
			});

			if (rpcError) throw rpcError;

			onClose?.();
		} catch (error) {
			console.error('Failed to save:', error);
		} finally {
			isLoading = false;
		}
	}
</script>

{#snippet header()}
	<!-- Auto-fill section -->
	<div class="px-3">
		<div class="flex gap-2">
			<Input bind:value={videoUrl} placeholder="Enter video URL for AI auto-fill" />
			<Button
				type="button"
				variant="outline"
				onclick={handleProcessVideo}
				disabled={isProcessingVideo || !videoUrl}
			>
				<Sparkles class="mr-2 h-4 w-4" />
				{#if isProcessingVideo}
					Processing...
				{:else}
					Auto-fill
				{/if}
			</Button>
		</div>
		{#if processingError}
			<div class="mt-1 text-sm text-destructive">{processingError}</div>
		{/if}

		<!-- Added margin-top for spacing -->
		<TabsList class="mt-3 grid w-full grid-cols-4 border-b">
			<TabsTrigger value="basic">
				Basic Info
				{#if tabsWithErrors.basic}
					<span class="ml-1 text-destructive">
						<AlertCircle class="inline-block h-3 w-3" />
					</span>
				{/if}
			</TabsTrigger>
			<TabsTrigger value="schedule">
				Schedule
				{#if tabsWithErrors.schedule}
					<span class="ml-1 text-destructive">
						<AlertCircle class="inline-block h-3 w-3" />
					</span>
				{/if}
			</TabsTrigger>
			<TabsTrigger value="form">
				Product Form
				{#if tabsWithErrors.form}
					<span class="ml-1 text-destructive">
						<AlertCircle class="inline-block h-3 w-3" />
					</span>
				{/if}
			</TabsTrigger>
			<TabsTrigger value="pricing">
				Pricing
				{#if tabsWithErrors.pricing}
					<span class="ml-1 text-destructive">
						<AlertCircle class="inline-block h-3 w-3" />
					</span>
				{/if}
			</TabsTrigger>
		</TabsList>
	</div>
{/snippet}

{#snippet footer()}
	<div class="flex justify-between gap-2">
		<div>
			{#if formSubmitted && Object.keys($errors).length > 0}
				<ul class="mt-1 text-xs text-destructive">
					{#if tabsWithErrors.basic}
						<li>• Check Basic Info fields</li>
					{/if}
					{#if tabsWithErrors.schedule}
						<li>• Check Schedule settings (time slots and instructors)</li>
					{/if}
					{#if tabsWithErrors.form}
						<li>• Check Product Form setup</li>
					{/if}
					{#if tabsWithErrors.pricing}
						<li>• Check Pricing configurations</li>
					{/if}
				</ul>
			{/if}
		</div>
		<div class="flex gap-2">
			<Button variant="outline" onclick={onClose}>Cancel</Button>
			<Button type="button" onclick={handleSubmit} disabled={$submitting || isLoading}>
				{$submitting || isLoading ? 'Saving...' : 'Save'}
			</Button>
		</div>
	</div>
{/snippet}

<form id="productForm" method="POST">
	<!-- Tabs component wraps everything to maintain context -->
	<Tabs value={currentTab} onValueChange={(value) => (currentTab = value as typeof currentTab)}>
		<ResponsiveModal
			{open}
			title={`${product ? 'Edit' : 'Create'} Product`}
			onOpenChange={(isOpen) => (isOpen ? undefined : onClose())}
			{header}
			{footer}
		>
			<!-- Scrollable tab content -->
			<div class="flex h-full flex-col">
				<div class="relative min-h-0 flex-1">
					<TabsContent value="basic" class="absolute inset-0 h-full">
						<BasicInfoTab form={superFormObj} {supabase} {brand} product={product as any} />
					</TabsContent>

					<TabsContent value="schedule" class="absolute inset-0 h-full">
						<ScheduleTab form={superFormObj} />
					</TabsContent>

					<TabsContent value="form" class="absolute inset-0 h-full">
						<ProductFormTab form={superFormObj} />
					</TabsContent>

					<TabsContent value="pricing" class="absolute inset-0 h-full">
						<PricingTab form={superFormObj} {supabase} brandId={brand.id} />
					</TabsContent>
				</div>
			</div>
			<SuperDebug data={$form} />
		</ResponsiveModal>
	</Tabs>
</form>
