<!-- PriceSelector.svelte -->
<script lang="ts">
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { Button } from '$lib/components/ui/button';
	import { Check, ChevronsUpDown, Loader2, X } from '@lucide/svelte';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import type { Database } from '$lib/supabase/database.types';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import { cn } from '$lib/utils';
	import { tick, onMount } from 'svelte';

	type Price = {
		id: string;
		title: Record<string, string>;
		product_classification: Record<string, string>;
		unit_kind: string;
	};

	interface Props {
		onSelect: (priceId: string, priceName: string) => void;
		supabase: SupabaseClient<Database>;
		brandId: string;
		selectedPriceId?: string;
		showValidationError?: boolean;
	}

	const {
		onSelect,
		supabase,
		brandId,
		selectedPriceId = '',
		showValidationError = false
	}: Props = $props();

	let open = $state(false);
	let value = $state(selectedPriceId);
	let displayValue = $state('');
	let searchQuery = $state('');
	let isLoading = $state(false);
	let prices = $state<Price[]>([]);
	let triggerRef = $state<HTMLButtonElement>(null!);
	let hasError = $state(false);
	let lastQuery = $state<string | null>(null);
	let shouldSkipNextSearch = $state(false);

	// Search handler for prices
	async function handleSearch(query: string) {
		// Skip if already loading or if this is the same query we just ran
		if (isLoading || query === lastQuery || shouldSkipNextSearch) {
			shouldSkipNextSearch = false;
			return;
		}

		console.log('[PriceSelector] Searching with query:', query);
		lastQuery = query;
		isLoading = true;
		hasError = false;

		try {
			let supabaseQuery = supabase
				.from('price')
				.select('id, title, product_classification, unit_kind')
				.order('created_at', { ascending: false });

			if (brandId) {
				// If brandId is provided, filter prices by brand
				supabaseQuery = supabaseQuery.eq('brand_id', brandId);
			}

			if (query) {
				// Search by title in English locale
				supabaseQuery = supabaseQuery.ilike('title->>en', `%${query}%`);
			}

			const { data, error } = await supabaseQuery.limit(10);

			if (error) {
				console.error('[PriceSelector] Query error:', error);
				shouldSkipNextSearch = true;
				prices = [];
				hasError = true;
				return;
			}

			console.log(`[PriceSelector] Found ${data?.length || 0} prices`);
			shouldSkipNextSearch = true;

			// Handle JSON data from Supabase by ensuring it's the right type
			prices = (data || []).map((item) => ({
				id: item.id,
				title: (item.title as Record<string, string>) || { en: '' },
				product_classification: (item.product_classification as Record<string, string>) || {
					en: ''
				},
				unit_kind: item.unit_kind || ''
			}));

			// If we have a selected price ID, ensure we have the display value set
			if (value && !displayValue) {
				const selectedPrice = prices.find((p) => p.id === value);
				if (selectedPrice) {
					displayValue = getLocalizedText(selectedPrice.title as LocalizedText, getLocale());
				}
			}
		} catch (error) {
			console.error('[PriceSelector] Error:', error);
			shouldSkipNextSearch = true;
			prices = [];
			hasError = true;
		} finally {
			isLoading = false;
			console.log(
				'[PriceSelector] Search completed, prices:',
				prices.length,
				'hasError:',
				hasError
			);
		}
	}

	function handleSelect(price: Price) {
		console.log(
			'[PriceSelector] Selected price:',
			getLocalizedText(price.title as LocalizedText, getLocale())
		);
		value = price.id;
		displayValue = getLocalizedText(price.title as LocalizedText, getLocale());
		open = false;

		// Pass both ID and name
		onSelect(price.id, getLocalizedText(price.title as LocalizedText, getLocale()));
	}

	// Load initial data just once
	onMount(() => {
		handleSearch('');
	});

	// Handle open state changes
	$effect(() => {
		if (open && prices.length === 0 && !isLoading && searchQuery !== lastQuery) {
			handleSearch(searchQuery);
		}
	});

	// Handle search query changes - only when the query actually changes
	let previousSearchQuery = '';
	$effect(() => {
		if (open && searchQuery !== previousSearchQuery && !isLoading) {
			previousSearchQuery = searchQuery;
			handleSearch(searchQuery);
		}
	});

	// Initialize from props if provided
	$effect(() => {
		if (selectedPriceId && selectedPriceId !== value) {
			value = selectedPriceId;
			// If we already have prices loaded, find the display value
			if (prices.length > 0) {
				const selectedPrice = prices.find((p) => p.id === selectedPriceId);
				if (selectedPrice) {
					displayValue = getLocalizedText(selectedPrice.title as LocalizedText, getLocale());
				}
			}
		}
	});
</script>

<Popover.Root bind:open>
	<Popover.Trigger bind:ref={triggerRef} class="w-full">
		<div
			class={cn(
				'ring-offset-background flex h-9 w-full items-center justify-between rounded-md border bg-transparent px-3 py-2 text-sm',
				showValidationError && !displayValue ? 'border-destructive' : 'border-input'
			)}
		>
			{#if displayValue}
				<p class="truncate font-medium">{displayValue}</p>
				<div>
					<ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50" />
				</div>
			{:else}
				<span class="text-muted-foreground"> Select a price... </span>
				<ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50" />
			{/if}
		</div>
	</Popover.Trigger>
	<Popover.Content class="p-0" align="start">
		<Command.Root shouldFilter={false}>
			<Command.Input placeholder="Search prices..." bind:value={searchQuery} />
			<Command.List>
				{#if isLoading}
					<Command.Loading>
						<div class="flex items-center justify-center py-6">
							<Loader2 class="text-muted-foreground h-6 w-6 animate-spin" />
						</div>
					</Command.Loading>
				{:else if hasError}
					<Command.Empty>An error occurred while searching.</Command.Empty>
				{:else if prices.length === 0}
					<Command.Empty>No prices found.</Command.Empty>
				{:else}
					<Command.Group>
						{#each prices as price (price.id)}
							<Command.Item
								value={getLocalizedText(price.title as LocalizedText, getLocale())}
								onSelect={() => handleSelect(price)}
								class="w-full"
							>
								<div class="flex-1 overflow-hidden">
									<p class="truncate">
										{getLocalizedText(price.title as LocalizedText, getLocale())}
									</p>
									<p class="text-muted-foreground truncate text-xs">
										{getLocalizedText(price.product_classification as LocalizedText, getLocale())}
										· {price.unit_kind}
									</p>
								</div>
								<Check class={cn(value === price.id ? '' : 'text-transparent')} />
							</Command.Item>
						{/each}
					</Command.Group>
				{/if}
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>
