<!-- PricingTab.svelte -->
<script lang="ts">
	import { Card } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { Label } from '$lib/components/ui/label';
	import { Input } from '$lib/components/ui/input';
	import { Plus, Trash2, Wallet, Calendar, Clock, MapPin, Users, AlertCircle } from '@lucide/svelte';
	import type { SuperForm } from 'sveltekit-superforms/client';
	import type { FormSchema } from '../../schemas';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import type { Database } from '$lib/supabase/database.types';
	import type { LocalizedText } from '$lib/utils/localization';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import PriceSelector from './PriceSelector.svelte';
	import { Badge } from '$lib/components/ui/badge';
	import { Separator } from '$lib/components/ui/separator';
	import { format, parseISO } from 'date-fns';
	import { page } from '$app/stores';

	interface Props {
		form: SuperForm<FormSchema>;
		supabase: SupabaseClient<Database>;
		brandId: string;
	}

	const { form, supabase, brandId }: Props = $props();

	// Get the form data object
	const formData = form.form;

	// Define product price structure
	interface ProductPrice {
		id: string; // A local ID for UI handling
		price_id: string;
		price_name?: string;
		cost_units: number;
	}

	// Define event/timeslot details structure
	interface EventDetails {
		start_time: string;
		end_time: string;
		space_id: string;
		space_name?: string;
		location_desc?: string;
		assigned_instructors?: Array<{ id: string; name: string }>;
	}

	// Define product structure for UI
	interface ProductConfig {
		id: string; // Local ID to identify the product
		title: string;
		description: string;
		type: 'bundled' | 'individual' | 'trial_first' | 'trial_rest';
		eventIndex?: number; // Track which event this product belongs to
		prices: ProductPrice[];
		eventDetails?: EventDetails; // Add event details for individual products
	}

	// Type for form product prices
	interface FormProductPrice {
		product_type: 'bundled' | 'individual' | 'trial_first' | 'trial_rest';
		prices: {
			price_id: string;
			cost_units: number;
		}[];
	}

	// Define pricing configs state
	let pricingConfigs = $state<FormProductPrice[]>([]);

	// Validation state
	let showValidation = $state(false);

	// Function to check if a price ID is valid
	function hasPriceError(priceId: string): boolean {
		return !priceId || priceId === 'missing-price-id';
	}

	// Function to validate all prices in a product
	function hasProductPriceErrors(product: ProductConfig): boolean {
		return product.prices.some((price) => hasPriceError(price.price_id));
	}

	// Function to check if any product has price errors
	function hasAnyPriceErrors(): boolean {
		return productConfigs.some((product) => hasProductPriceErrors(product));
	}

	// Function to validate the form
	function validateForm(): boolean {
		showValidation = true;
		return !hasAnyPriceErrors();
	}

	// Function to reset pricing configs
	function resetPricingConfigs() {
		const defaultPriceId = pricesList.length > 0 ? pricesList[0].id : 'missing-price-id';
		const formType = $formData?.product_form_type || 'bundled';

		if (formType === 'bundled') {
			pricingConfigs = [
				{
					product_type: 'bundled',
					prices: [
						{
							price_id: defaultPriceId,
							cost_units: 2 // Start with 2 points (assuming 2 hours class)
						}
					]
				}
			];
		} else if (formType === 'individual') {
			pricingConfigs = [
				{
					product_type: 'individual',
					prices: [
						{
							price_id: defaultPriceId,
							cost_units: 2 // Start with 2 points (assuming 2 hours class)
						}
					]
				}
			];
		} else if (formType === 'trial') {
			pricingConfigs = [
				{
					product_type: 'trial_first',
					prices: [
						{
							price_id: defaultPriceId,
							cost_units: 1 // 1 point for trial (assuming 1 hour class)
						}
					]
				},
				{
					product_type: 'trial_rest',
					prices: [
						{
							price_id: defaultPriceId,
							cost_units: 2 // 2 points for main product (assuming 2 hours class)
						}
					]
				}
			];
		}

		// Update form data immediately if we have a real price ID
		if (defaultPriceId !== 'missing-price-id') {
			updateFormData();
		}
	}

	// Generate a unique ID for UI elements
	function generateId() {
		return crypto.randomUUID();
	}

	// State for products and their prices
	let productConfigs = $state<ProductConfig[]>([]);
	let currentFormType = $state<'bundled' | 'individual' | 'trial'>('bundled');
	let pricesList = $state<
		{ id: string; title: Record<string, string>; product_classification: Record<string, string> }[]
	>([]);
	let isLoadingPrices = $state(false);

	// State for space location data
	interface SpaceInfo {
		room_name: string;
		location_desc: string;
	}
	let spaceInfoCache = $state<Record<string, SpaceInfo>>({});
	let isLoadingSpaces = $state(false);

	// Guard flags to prevent infinite loops
	let isInitializing = $state(true);
	let isUpdatingForm = $state(false);
	let hasInitialized = $state(false);
	let isProcessingTimeSlotChanges = $state(false);
	let isPriceSelecting = $state(false);
	let isCostUnitsUpdating = $state(false);

	// Add a debounce helper to prevent rapid updates
	let syncFormDebounceTimer: number | null = null;

	function debouncedSyncForm() {
		// Cancel existing timer
		if (syncFormDebounceTimer !== null) {
			clearTimeout(syncFormDebounceTimer);
		}

		// Set new timer
		syncFormDebounceTimer = window.setTimeout(() => {
			synchronizeFormWithProductConfigs();
			syncFormDebounceTimer = null;
		}, 50);
	}

	// Helper function to calculate cost units based on event duration in minutes
	function calculateCostUnits(durationMinutes: number): number {
		// 0.5 points per 30 minutes: 60 mins = 1pt, 90 mins = 1.5pt, 120 mins = 2pt, etc.
		return Math.round((durationMinutes / 30) * 0.5 * 100) / 100;
	}

	// Helper functions to format dates and times
	function formatDate(dateString: string): string {
		try {
			return format(parseISO(dateString), 'EEE, MMM d, yyyy');
		} catch (e) {
			return 'Invalid Date';
		}
	}

	function formatTime(dateString: string): string {
		try {
			return format(parseISO(dateString), 'h:mm a');
		} catch (e) {
			return 'Invalid Time';
		}
	}

	function formatTimeRange(start: string, end: string): string {
		return `${formatTime(start)} - ${formatTime(end)}`;
	}

	// Fetch space details from Supabase
	async function fetchSpaceDetails(spaceId: string): Promise<SpaceInfo> {
		// If we already have this space's info in cache, return it
		if (spaceInfoCache[spaceId]) {
			return spaceInfoCache[spaceId];
		}

		try {
			isLoadingSpaces = true;

			// Query for space details with corrected field names
			const { data, error } = await supabase
				.from('space')
				.select(
					`
					name_full,
					landmark:landmark_id (
						title_short,
						address:address_id (
							auto_normalized_address_local
						)
					)
				`
				)
				.eq('id', spaceId)
				.single();

			if (error) {
				console.error('[PricingTab] Error fetching space details:', error);
				throw error;
			}

			if (!data) {
				console.warn('[PricingTab] No space data found for id:', spaceId);
				return {
					room_name: 'Unknown Space',
					location_desc: 'Space details not found'
				};
			}

			const currentLang = getLocale();
			const roomName =
				getLocalizedText(data.name_full as LocalizedText, currentLang) || 'Unknown Space';
			let locationDesc = '';

			// Try to get landmark name if available
			if (data.landmark?.title_short) {
				locationDesc = getLocalizedText(data.landmark.title_short as LocalizedText, currentLang);
			}
			// Fall back to address local_desc if available
			else if (data.landmark?.address?.auto_normalized_address_local) {
				locationDesc = data.landmark.address.auto_normalized_address_local;
			}

			const spaceInfo = {
				room_name: roomName,
				location_desc: locationDesc || 'No location details available'
			};

			// Cache this space info
			spaceInfoCache = { ...spaceInfoCache, [spaceId]: spaceInfo };
			return spaceInfo;
		} catch (error) {
			console.error('[PricingTab] Error fetching space details:', error);
			return {
				room_name: 'Unknown Space',
				location_desc: 'Location details unavailable'
			};
		} finally {
			isLoadingSpaces = false;
		}
	}

	// Get selected time slots from the form data
	function getSelectedTimeSlots(): Array<{
		id: string;
		start_time: string;
		end_time: string;
		space_id: string;
		space_name?: string;
		assigned_instructors?: Array<{ id: string; name: string }>;
	}> {
		const formValue = $formData;
		return formValue?.events || [];
	}

	// Get event durations from the form data
	function getEventDurations(): number[] {
		const timeSlots = getSelectedTimeSlots();
		if (!timeSlots || timeSlots.length === 0) {
			return [60]; // Default to 60 minutes if no time slots found
		}

		return timeSlots.map((slot) => {
			// Calculate duration between start and end time in minutes
			if (!slot.start_time || !slot.end_time) return 60;

			try {
				const startTime = new Date(slot.start_time);
				const endTime = new Date(slot.end_time);
				const durationMs = endTime.getTime() - startTime.getTime();
				return durationMs / (1000 * 60); // Convert ms to minutes
			} catch (error) {
				return 60; // Default to 60 minutes if calculation fails
			}
		});
	}

	// Initialize product configs based on form type
	async function initializeProductConfigs(
		formType: 'bundled' | 'individual' | 'trial',
		formValue: FormSchema
	) {
		// Set current form type
		currentFormType = formType;

		// Clear existing configs
		productConfigs = [];

		// Get event durations and time slots
		const eventDurations = getEventDurations();
		const timeSlots = getSelectedTimeSlots();
		const eventCount = timeSlots.length;

		// Get a default price ID to use
		const defaultPriceId = pricesList.length > 0 ? pricesList[0].id : 'missing-price-id';

		// Extract form factor data for trial products if needed
		const trialRestPrices =
			formValue?.product_form_factor?.find((p) => p.product_type === 'trial_rest')?.prices || [];

		// For forms without any events yet, ensure we have at least a default product
		if (eventCount === 0) {
			let defaultProducts: ProductConfig[] = [];

			if (formType === 'bundled') {
				defaultProducts = [
					{
						id: generateId(),
						title: 'Bundled Product',
						description: 'No time slots selected yet',
						type: 'bundled',
						prices: [] // Start with no prices
					}
				];
			} else if (formType === 'individual') {
				defaultProducts = [
					{
						id: generateId(),
						title: 'Individual Product',
						description: 'No time slots selected yet',
						type: 'individual',
						prices: [] // Start with no prices
					}
				];
			} else if (formType === 'trial') {
				defaultProducts = [
					{
						id: generateId(),
						title: 'Trial Product',
						description: 'No time slots selected yet',
						type: 'trial_first',
						prices: []
					},
					{
						id: generateId(),
						title: 'Main Product',
						description: 'No time slots selected yet',
						type: 'trial_rest',
						prices: []
					}
				];
			}

			// Add default price for each product
			defaultProducts.forEach((product) => {
				if (product.type === 'trial_first') {
					addEmptyPriceWithCostUnits(product, 1);
				} else {
					addEmptyPriceWithCostUnits(product, 2);
				}
			});

			productConfigs = defaultProducts;
			return;
		}

		if (formType === 'bundled') {
			// Just one product for all events
			const bundledProduct: ProductConfig = {
				id: generateId(),
				title: 'Bundled Product',
				description: `One product containing all ${eventCount} events`,
				type: 'bundled',
				prices: [] // Start with no prices
			};

			// Calculate total duration for bundled product
			const totalDuration = eventDurations.reduce((sum, duration) => sum + duration, 0);
			const costUnits = calculateCostUnits(totalDuration);

			// Add empty price with calculated cost units
			addEmptyPriceWithCostUnits(bundledProduct, costUnits);

			productConfigs = [bundledProduct];
		} else if (formType === 'individual') {
			// For individual products, create a product for each event
			const individualProducts =
				formValue.product_form_factor?.filter((p) => p.product_type === 'individual') || [];

			if (eventCount > 0) {
				// Create temporary configs
				const tempConfigs: ProductConfig[] = [];

				for (let i = 0; i < eventCount; i++) {
					const timeSlot = timeSlots[i];
					const eventDuration = getEventDurations()[i] || 60;

					const formattedDate = formatDate(timeSlot.start_time);
					const formattedTime = formatTimeRange(timeSlot.start_time, timeSlot.end_time);

					// Find the product that corresponds to this position, if any
					// Without event_index, we just use array position
					const eventProduct = individualProducts[i];

					const product: ProductConfig = {
						id: generateId(),
						title: `Event ${i + 1}`,
						description: `${formattedDate}, ${formattedTime}`,
						type: 'individual',
						eventIndex: i, // Keep this for internal tracking but don't pass to form
						prices: [],
						eventDetails: {
							start_time: timeSlot.start_time,
							end_time: timeSlot.end_time,
							space_id: timeSlot.space_id,
							space_name: timeSlot.space_name || 'Unknown Space',
							assigned_instructors: timeSlot.assigned_instructors || []
						}
					};

					// Add prices from the form or calculate based on duration
					if (eventProduct && eventProduct.prices && eventProduct.prices.length > 0) {
						product.prices = eventProduct.prices.map((price) => ({
							id: generateId(),
							price_id: price.price_id,
							price_name: '',
							cost_units: price.cost_units
						}));
					} else {
						// Calculate cost units based on event duration
						const costUnits = calculateCostUnits(eventDuration);
						addEmptyPriceWithCostUnits(product, costUnits);
					}

					tempConfigs.push(product);
				}

				// Update productConfigs first
				productConfigs = tempConfigs;

				// Then asynchronously load space info
				Promise.all(
					tempConfigs.map(async (product) => {
						if (product.eventDetails?.space_id) {
							try {
								const spaceInfo = await fetchSpaceDetails(product.eventDetails.space_id);
								if (product.eventDetails) {
									product.eventDetails.space_name = spaceInfo.room_name;
									product.eventDetails.location_desc = spaceInfo.location_desc;
								}
							} catch (error) {
								console.error('Error fetching space details:', error);
							}
						}
					})
				);
			}
		} else if (formType === 'trial') {
			// Get time slots
			if (eventCount > 0) {
				// Create trial product (first event)
				const firstSlot = timeSlots[0];
				const firstDuration = eventDurations[0] || 60;

				const firstFormattedDate = formatDate(firstSlot.start_time);
				const firstFormattedTime = formatTimeRange(firstSlot.start_time, firstSlot.end_time);

				const trialProduct: ProductConfig = {
					id: generateId(),
					title: 'Trial Product',
					description: `${firstFormattedDate}, ${firstFormattedTime}`,
					type: 'trial_first',
					prices: [],
					eventDetails: {
						start_time: firstSlot.start_time,
						end_time: firstSlot.end_time,
						space_id: firstSlot.space_id,
						space_name: firstSlot.space_name || 'Unknown Space',
						assigned_instructors: firstSlot.assigned_instructors || []
					}
				};

				// Calculate cost units for trial
				const trialCostUnits = calculateCostUnits(firstDuration);
				addEmptyPriceWithCostUnits(trialProduct, trialCostUnits);

				// Create main product (remaining events)
				const remainingEvents = eventCount - 1;

				// Create a description that lists all remaining time slots
				let mainDescription = 'No remaining events';

				if (remainingEvents > 0) {
					const remainingTimeSlots = timeSlots.slice(1);

					// Format each remaining time slot with detailed information
					const formattedSlots = [];

					// Process each time slot with complete details
					for (const slot of remainingTimeSlots) {
						const date = formatDate(slot.start_time);
						const time = formatTimeRange(slot.start_time, slot.end_time);
						let spaceInfo = slot.space_name || 'Unknown Space';

						// Add space details to each row
						if (slot.space_id) {
							// Try to get space info from cache first
							const cachedInfo = spaceInfoCache[slot.space_id];
							if (cachedInfo) {
								spaceInfo = cachedInfo.room_name;
							}
						}

						formattedSlots.push(`• ${date}, ${time}\n  Location: ${spaceInfo}`);
					}

					// Join with double line breaks for better readability
					mainDescription = formattedSlots.join('\n\n');
				}

				const mainProduct: ProductConfig = {
					id: generateId(),
					title: 'Main Product',
					description: mainDescription,
					type: 'trial_rest',
					prices: []
				};

				// If we have trial rest prices from form data, use them
				if (trialRestPrices.length > 0) {
					mainProduct.prices = trialRestPrices.map(
						(price: { price_id: string; cost_units: number }) => ({
							id: generateId(),
							price_id: price.price_id,
							price_name: '',
							cost_units: price.cost_units
						})
					);
				} else {
					// Otherwise add empty prices with calculated cost units
					if (remainingEvents > 0) {
						// Calculate event durations
						const durations = getEventDurations();
						const remainingDurations: number[] = durations.slice(1);
						const totalRemainingDuration = remainingDurations.reduce(
							(sum: number, duration: number) => sum + duration,
							0
						);
						// Calculate cost units for main product
						const mainCostUnits = calculateCostUnits(totalRemainingDuration);
						addEmptyPriceWithCostUnits(mainProduct, mainCostUnits);
					} else {
						// No remaining events
						addEmptyPriceWithCostUnits(mainProduct, 1);
					}
				}

				productConfigs = [trialProduct, mainProduct];

				// Asynchronously fetch space details for the trial product
				if (trialProduct.eventDetails?.space_id) {
					fetchSpaceDetails(trialProduct.eventDetails.space_id)
						.then((spaceInfo) => {
							if (trialProduct.eventDetails) {
								trialProduct.eventDetails.space_name = spaceInfo.room_name;
								trialProduct.eventDetails.location_desc = spaceInfo.location_desc;
							}
						})
						.catch((error) => {
							console.error('Error fetching space details:', error);
						});
				}
			}
		}

		// Update pricingConfigs to match productConfigs
		updatePricingConfigsFromProductConfigs();
	}

	// New helper to update pricingConfigs from productConfigs
	function updatePricingConfigsFromProductConfigs() {
		if (productConfigs.length === 0) return;

		// Group products by type
		const groupedConfigs: Record<string, ProductConfig[]> = {};
		productConfigs.forEach((product) => {
			if (!groupedConfigs[product.type]) {
				groupedConfigs[product.type] = [];
			}
			groupedConfigs[product.type].push(product);
		});

		// Create pricing configs
		const newPricingConfigs: FormProductPrice[] = [];

		// Handle bundled products
		if (groupedConfigs['bundled']) {
			const bundledProduct = groupedConfigs['bundled'][0];
			newPricingConfigs.push({
				product_type: 'bundled',
				prices: bundledProduct.prices.map((price) => ({
					price_id:
						price.price_id === 'missing-price-id' && pricesList.length > 0
							? pricesList[0].id
							: price.price_id,
					cost_units: price.cost_units
				}))
			});
		}

		// Handle individual products - maintain order by eventIndex
		if (groupedConfigs['individual']) {
			// Sort by eventIndex to ensure consistent order
			const sortedProducts = [...groupedConfigs['individual']].sort(
				(a, b) => (a.eventIndex || 0) - (b.eventIndex || 0)
			);

			// Create one config for each product, preserving order
			sortedProducts.forEach((product) => {
				newPricingConfigs.push({
					product_type: 'individual',
					prices: product.prices.map((price) => ({
						price_id:
							price.price_id === 'missing-price-id' && pricesList.length > 0
								? pricesList[0].id
								: price.price_id,
						cost_units: price.cost_units
					}))
				});
			});
		}

		// Handle trial products
		if (groupedConfigs['trial_first']) {
			const trialProduct = groupedConfigs['trial_first'][0];
			newPricingConfigs.push({
				product_type: 'trial_first',
				prices: trialProduct.prices.map((price) => ({
					price_id:
						price.price_id === 'missing-price-id' && pricesList.length > 0
							? pricesList[0].id
							: price.price_id,
					cost_units: price.cost_units
				}))
			});
		}

		if (groupedConfigs['trial_rest']) {
			const mainProduct = groupedConfigs['trial_rest'][0];
			newPricingConfigs.push({
				product_type: 'trial_rest',
				prices: mainProduct.prices.map((price) => ({
					price_id:
						price.price_id === 'missing-price-id' && pricesList.length > 0
							? pricesList[0].id
							: price.price_id,
					cost_units: price.cost_units
				}))
			});
		}

		// Update pricingConfigs if different - use deep comparison
		const currentJson = JSON.stringify(pricingConfigs);
		const newJson = JSON.stringify(newPricingConfigs);

		if (currentJson !== newJson) {
			pricingConfigs = newPricingConfigs;
		}
	}

	// Add an empty price entry to a product with specified cost units
	function addEmptyPriceWithCostUnits(product: ProductConfig, costUnits: number) {
		const newPrice = {
			id: generateId(),
			price_id: '',
			price_name: '',
			cost_units: costUnits
		};

		// If prices are already loaded, automatically select the first one
		if (pricesList.length > 0) {
			const firstPrice = pricesList[0];
			newPrice.price_id = firstPrice.id;
			newPrice.price_name = getLocalizedText(firstPrice.title as LocalizedText, getLocale());
		}

		product.prices = [...product.prices, newPrice];

		// If we've auto-selected a price, ensure the form is updated
		if (newPrice.price_id && hasInitialized && !isInitializing) {
			setTimeout(() => updateFormData(), 50);
		}
	}

	// Add an empty price entry to a product
	function addEmptyPrice(product: ProductConfig) {
		// Determine the appropriate duration to use for cost units calculation
		let durationToUse = 60; // Default to 60 minutes
		const eventDurations = getEventDurations();

		if (currentFormType === 'individual' && product.eventIndex !== undefined) {
			// For individual products, use the specific event duration
			durationToUse = eventDurations[product.eventIndex] || 60;
		} else if (currentFormType === 'bundled') {
			// For bundled products, calculate total duration of all events
			durationToUse = eventDurations.reduce((sum, duration) => sum + duration, 0);
		} else if (currentFormType === 'trial') {
			// For trial, first product uses first event duration, second uses the rest
			if (product.type === 'trial_first' && eventDurations.length > 0) {
				durationToUse = eventDurations[0];
			} else if (product.type === 'trial_rest' && eventDurations.length > 1) {
				durationToUse = eventDurations.slice(1).reduce((sum, duration) => sum + duration, 0);
			}
		}

		// Calculate cost units based on duration
		const costUnits = calculateCostUnits(durationToUse);

		addEmptyPriceWithCostUnits(product, costUnits);
	}

	// Add a new price to a product
	function addPrice(productIndex: number) {
		const product = productConfigs[productIndex];
		if (product) {
			addEmptyPrice(product);
		}
	}

	// Remove a price from a product
	function removePrice(productIndex: number, priceIndex: number) {
		const product = productConfigs[productIndex];
		if (product && product.prices.length > 1) {
			// Don't remove the last price
			product.prices = product.prices.filter((_, index) => index !== priceIndex);

			// Update form with removed price
			updateFormData();
		}
	}

	// Handle price selection
	function handlePriceSelect(
		productIndex: number,
		priceIndex: number,
		priceId: string,
		priceName: string
	) {
		// Add a guard to prevent multiple rapid updates
		if (isPriceSelecting) return;

		isPriceSelecting = true;

		try {
			const product = productConfigs[productIndex];
			if (product) {
				const updatedPrices = [...product.prices];
				updatedPrices[priceIndex] = {
					...updatedPrices[priceIndex],
					price_id: priceId,
					price_name: priceName
				};
				product.prices = updatedPrices;

				// Explicitly update pricingConfigs to ensure sync
				const updatedPricingConfigs = [...pricingConfigs];
				const configIndex = updatedPricingConfigs.findIndex(
					(config) => config.product_type === product.type
				);

				if (configIndex !== -1) {
					updatedPricingConfigs[configIndex] = {
						...updatedPricingConfigs[configIndex],
						prices: updatedPricingConfigs[configIndex].prices.map((price, idx) =>
							idx === priceIndex
								? { ...price, price_id: priceId, cost_units: price.cost_units }
								: price
						)
					};
					pricingConfigs = updatedPricingConfigs;
				}

				// Force immediate sync to form
				synchronizeFormWithProductConfigs();
			}
		} finally {
			// Release the guard after a short delay to prevent rapid re-renders
			setTimeout(() => {
				isPriceSelecting = false;
			}, 100); // Increased timeout for stability
		}
	}

	// Handle cost units change
	function handleCostUnitsChange(productIndex: number, priceIndex: number, costUnits: number) {
		// Add a guard to prevent multiple rapid updates
		if (isCostUnitsUpdating) return;

		isCostUnitsUpdating = true;

		try {
			const product = productConfigs[productIndex];
			if (product) {
				const updatedPrices = [...product.prices];
				updatedPrices[priceIndex] = {
					...updatedPrices[priceIndex],
					cost_units: costUnits
				};
				product.prices = updatedPrices;

				// Update form data immediately
				updateFormData();

				// Also update pricing configs to ensure sync
				if (pricingConfigs.length > 0) {
					const updatedPricingConfigs = [...pricingConfigs];
					const configIndex = updatedPricingConfigs.findIndex(
						(config) => config.product_type === product.type
					);
					if (configIndex !== -1) {
						updatedPricingConfigs[configIndex] = {
							...updatedPricingConfigs[configIndex],
							prices: updatedPricingConfigs[configIndex].prices.map((price, idx) =>
								idx === priceIndex
									? { ...price, price_id: price.price_id, cost_units: costUnits }
									: price
							)
						};
						pricingConfigs = updatedPricingConfigs;
					}
				}
			}
		} finally {
			// Release the guard after a short delay
			setTimeout(() => {
				isCostUnitsUpdating = false;
			}, 0);
		}
	}

	// Fetch available prices
	async function fetchPrices() {
		if (isLoadingPrices) return;

		isLoadingPrices = true;
		try {
			// Add timeout for fetch operation
			const fetchPromise = supabase
				.from('price')
				.select('id, title, product_classification')
				.order('created_at', { ascending: false });

			// Create a timeout promise
			const timeoutPromise = new Promise((_, reject) => {
				setTimeout(() => reject(new Error('Fetch timeout after 10 seconds')), 10000);
			});

			// Use Promise.race to handle potential timeouts
			const { data: pricesData, error } = (await Promise.race([
				fetchPromise,
				timeoutPromise
			])) as any;

			if (error) {
				console.error('[PricingTab] Error fetching prices:', error);
				throw error;
			}

			if (!pricesData || pricesData.length === 0) {
				console.warn('[PricingTab] No prices found');
				return;
			}

			// Handle JSON data from Supabase by ensuring it's the right type
			pricesList = (pricesData || []).map(
				(item: {
					id: string;
					title: Record<string, string>;
					product_classification: Record<string, string>;
				}) => ({
					id: item.id,
					title: (item.title as Record<string, string>) || { en: '' },
					product_classification: (item.product_classification as Record<string, string>) || {
						en: ''
					}
				})
			);

			// Auto-select prices for any empty price options after loading prices
			if (productConfigs.length > 0 && pricesList.length > 0) {
				let hasUpdates = false;
				const firstPrice = pricesList[0];

				productConfigs.forEach((product) => {
					product.prices.forEach((price) => {
						if (!price.price_id || price.price_id === 'missing-price-id') {
							price.price_id = firstPrice.id;
							price.price_name = getLocalizedText(firstPrice.title as LocalizedText, getLocale());
							hasUpdates = true;
						}
					});
				});

				// Update form immediately if we've made changes
				if (hasUpdates && hasInitialized && !isInitializing) {
					updateFormData();
				}
			}

			// Also update pricingConfigs to ensure sync
			if (pricingConfigs.length > 0 && pricesList.length > 0) {
				const firstPrice = pricesList[0];
				let hasUpdates = false;

				const updatedPricingConfigs = pricingConfigs.map((config) => ({
					...config,
					prices: config.prices.map((price) => {
						if (!price.price_id || price.price_id === 'missing-price-id') {
							hasUpdates = true;
							return {
								...price,
								price_id: firstPrice.id
							};
						}
						return price;
					})
				}));

				if (hasUpdates) {
					pricingConfigs = updatedPricingConfigs;
					// Also update form data
					if (hasInitialized && !isInitializing) {
						updateFormData();
					}
				}
			}
		} catch (error) {
			console.error('[PricingTab] Error fetching prices:', error);
			// Continue without prices data, we'll handle UI accordingly
		} finally {
			isLoadingPrices = false;
		}
	}

	// Enhanced update method that understands tab interdependencies
	function synchronizeFormWithProductConfigs() {
		if (isInitializing || !hasInitialized) return;

		// Get current form data to understand the complete structure
		const currentForm = $formData;
		if (!currentForm) return;

		const formType = currentForm.product_form_type;
		const currentEvents = currentForm.events || [];
		const currentFormFactor = (currentForm.product_form_factor || []) as FormProductPrice[];

		try {
			// Set flag to prevent recursive updates
			isUpdatingForm = true;

			// Update pricingConfigs from productConfigs first
			updatePricingConfigsFromProductConfigs();

			// Handle specific logic based on form type to ensure proper structure
			if (formType === 'individual') {
				// For individual type, ensure we have one product per event
				const shouldHaveConfigs = currentEvents.length;
				const hasConfigs = pricingConfigs.filter((c) => c.product_type === 'individual').length;

				// First, ensure we have the right number of configs - this is critical for stability
				if (hasConfigs !== shouldHaveConfigs && shouldHaveConfigs > 0) {
					console.log(
						`[PricingTab] Config count mismatch: has ${hasConfigs}, should have ${shouldHaveConfigs}`
					);

					// We need to reset the product initialization because the counts don't match
					initializeProductConfigs(formType, currentForm);
					return; // Exit early - this will trigger another sync later
				}
			}

			// Clean up any 'missing-price-id' values before updating form
			const cleanedPricingConfigs = pricingConfigs.map((config) => ({
				...config,
				prices: config.prices.map((price) => {
					// Replace any missing price IDs with an actual price ID if available
					if (price.price_id === 'missing-price-id' && pricesList.length > 0) {
						return {
							...price,
							price_id: pricesList[0].id
						};
					}
					return price;
				})
			}));

			// Carefully update the form data only when needed
			const currentFormFactorStr = JSON.stringify(currentFormFactor);
			const newPricingConfigsStr = JSON.stringify(cleanedPricingConfigs);

			if (currentFormFactorStr !== newPricingConfigsStr) {
				console.log('[PricingTab] Updating form with new pricing configs');
				formData.update((formValue) => ({
					...formValue,
					product_form_factor: JSON.parse(JSON.stringify(cleanedPricingConfigs))
				}));
			}
		} finally {
			setTimeout(() => {
				isUpdatingForm = false;
			}, 100);
		}
	}

	// Replace updateFormData calls with our debounced version
	function updateFormData() {
		if (isInitializing || !hasInitialized || productConfigs.length === 0) return;
		debouncedSyncForm();
	}

	// Initialize from existing form data if available
	async function initializeFromFormData(formValue: FormSchema) {
		// Skip if no form value
		if (!formValue) return false;

		try {
			// Only process product_form_factor if they exist and have data
			if (formValue.product_form_factor && formValue.product_form_factor.length > 0) {
				// Clear existing configs
				productConfigs = [];
				pricingConfigs = formValue.product_form_factor.map((config: FormProductPrice) => ({
					...config,
					prices: config.prices.map((price) => ({
						...price,
						price_id: price.price_id || 'missing-price-id',
						cost_units: price.cost_units || 2
					}))
				}));

				// Get the form type
				const formType = formValue.product_form_type || 'bundled';
				currentFormType = formType;

				// Get time slots
				const timeSlots = getSelectedTimeSlots();
				const eventCount = timeSlots.length;

				if (formType === 'bundled') {
					// Create bundled product config
					const bundledProducts = formValue.product_form_factor.filter(
						(p) => p.product_type === 'bundled'
					);

					if (bundledProducts.length > 0) {
						const bundledProduct: ProductConfig = {
							id: generateId(),
							title: 'Bundled Product',
							description: `One product containing all ${eventCount} events`,
							type: 'bundled',
							prices: bundledProducts[0].prices.map((price) => ({
								id: generateId(),
								price_id: price.price_id || 'missing-price-id',
								price_name: '', // We'll populate this later if possible
								cost_units: price.cost_units || 2
							}))
						};

						productConfigs = [bundledProduct];
					} else {
						// Initialize a default bundled product
						const bundledProduct: ProductConfig = {
							id: generateId(),
							title: 'Bundled Product',
							description: `One product containing all ${eventCount} events`,
							type: 'bundled',
							prices: []
						};

						productConfigs = [bundledProduct];
					}
				} else if (formType === 'individual') {
					// For individual products, create a product for each event
					const individualProducts =
						formValue.product_form_factor.filter((p) => p.product_type === 'individual') || [];

					if (eventCount > 0) {
						// Create temporary configs
						const tempConfigs: ProductConfig[] = [];

						for (let i = 0; i < eventCount; i++) {
							const timeSlot = timeSlots[i];
							const eventDuration = getEventDurations()[i] || 60;

							const formattedDate = formatDate(timeSlot.start_time);
							const formattedTime = formatTimeRange(timeSlot.start_time, timeSlot.end_time);

							// Find the product that corresponds to this position, if any
							// Without event_index, we just use array position
							const eventProduct = individualProducts[i];

							const product: ProductConfig = {
								id: generateId(),
								title: `Event ${i + 1}`,
								description: `${formattedDate}, ${formattedTime}`,
								type: 'individual',
								eventIndex: i, // Keep this for internal tracking but don't pass to form
								prices: [],
								eventDetails: {
									start_time: timeSlot.start_time,
									end_time: timeSlot.end_time,
									space_id: timeSlot.space_id,
									space_name: timeSlot.space_name || 'Unknown Space',
									assigned_instructors: timeSlot.assigned_instructors || []
								}
							};

							// Add prices from the form or calculate based on duration
							if (eventProduct && eventProduct.prices && eventProduct.prices.length > 0) {
								product.prices = eventProduct.prices.map((price) => ({
									id: generateId(),
									price_id: price.price_id,
									price_name: '',
									cost_units: price.cost_units
								}));
							} else {
								// Calculate cost units based on event duration
								const costUnits = calculateCostUnits(eventDuration);
								addEmptyPriceWithCostUnits(product, costUnits);
							}

							tempConfigs.push(product);
						}

						// Update productConfigs first
						productConfigs = tempConfigs;

						// Then asynchronously load space info
						Promise.all(
							tempConfigs.map(async (product) => {
								if (product.eventDetails?.space_id) {
									try {
										const spaceInfo = await fetchSpaceDetails(product.eventDetails.space_id);
										if (product.eventDetails) {
											product.eventDetails.space_name = spaceInfo.room_name;
											product.eventDetails.location_desc = spaceInfo.location_desc;
										}
									} catch (error) {
										console.error('Error fetching space details:', error);
									}
								}
							})
						);
					} else if (individualProducts.length > 0) {
						// Fallback if no events, but we have product data
						productConfigs = [
							{
								id: generateId(),
								title: 'Individual Products',
								description: 'No time slots selected yet',
								type: 'individual',
								prices: individualProducts[0].prices.map((price) => ({
									id: generateId(),
									price_id: price.price_id,
									price_name: '',
									cost_units: price.cost_units
								}))
							}
						];
					} else {
						// Default if no products and no events
						productConfigs = [
							{
								id: generateId(),
								title: 'Individual Products',
								description: 'No time slots selected yet',
								type: 'individual',
								prices: []
							}
						];
					}
				} else if (formType === 'trial') {
					// Create trial products if available
					const trialFirstProducts = formValue.product_form_factor.filter(
						(p) => p.product_type === 'trial_first'
					);
					const trialRestProducts = formValue.product_form_factor.filter(
						(p) => p.product_type === 'trial_rest'
					);

					if (eventCount > 0) {
						// First event for trial product
						const firstSlot = timeSlots[0];

						const firstFormattedDate = formatDate(firstSlot.start_time);
						const firstFormattedTime = formatTimeRange(firstSlot.start_time, firstSlot.end_time);

						// First product (trial)
						const trialProduct: ProductConfig = {
							id: generateId(),
							title: 'Trial Product',
							description: `${firstFormattedDate}, ${firstFormattedTime}`,
							type: 'trial_first',
							prices:
								trialFirstProducts.length > 0
									? trialFirstProducts[0].prices.map((price) => ({
											id: generateId(),
											price_id: price.price_id,
											price_name: '',
											cost_units: price.cost_units
										}))
									: [],
							eventDetails: {
								start_time: firstSlot.start_time,
								end_time: firstSlot.end_time,
								space_id: firstSlot.space_id,
								space_name: firstSlot.space_name || 'Unknown Space',
								assigned_instructors: firstSlot.assigned_instructors || []
							}
						};

						// Second product (main)
						const remainingEvents = eventCount - 1;

						// Create a description that lists all remaining time slots
						let mainDescription = 'No remaining events';

						if (remainingEvents > 0) {
							const remainingTimeSlots = timeSlots.slice(1);

							// Format each remaining time slot with detailed information
							const formattedSlots = [];

							// Process each time slot with complete details
							for (const slot of remainingTimeSlots) {
								const date = formatDate(slot.start_time);
								const time = formatTimeRange(slot.start_time, slot.end_time);
								let spaceInfo = slot.space_name || 'Unknown Space';

								// Add space details to each row
								if (slot.space_id) {
									// Try to get space info from cache first
									const cachedInfo = spaceInfoCache[slot.space_id];
									if (cachedInfo) {
										spaceInfo = cachedInfo.room_name;
									}
								}

								formattedSlots.push(`• ${date}, ${time}\n  Location: ${spaceInfo}`);
							}

							// Join with double line breaks for better readability
							mainDescription = formattedSlots.join('\n\n');
						}

						const mainProduct: ProductConfig = {
							id: generateId(),
							title: 'Main Product',
							description: mainDescription,
							type: 'trial_rest',
							prices: []
						};

						// If we have trial rest prices from form data, use them
						if (trialRestProducts && trialRestProducts.length > 0 && trialRestProducts[0].prices) {
							mainProduct.prices = trialRestProducts[0].prices.map(
								(price: { price_id: string; cost_units: number }) => ({
									id: generateId(),
									price_id: price.price_id,
									price_name: '',
									cost_units: price.cost_units
								})
							);
						} else {
							// Otherwise add empty prices with calculated cost units
							if (remainingEvents > 0) {
								// Calculate event durations
								const durations = getEventDurations();
								const remainingDurations: number[] = durations.slice(1);
								const totalRemainingDuration = remainingDurations.reduce(
									(sum: number, duration: number) => sum + duration,
									0
								);
								// Calculate cost units for main product
								const mainCostUnits = calculateCostUnits(totalRemainingDuration);
								addEmptyPriceWithCostUnits(mainProduct, mainCostUnits);
							} else {
								// No remaining events
								addEmptyPriceWithCostUnits(mainProduct, 1);
							}
						}

						productConfigs = [trialProduct, mainProduct];

						// Asynchronously fetch space details for the trial product
						if (trialProduct.eventDetails?.space_id) {
							fetchSpaceDetails(trialProduct.eventDetails.space_id)
								.then((spaceInfo) => {
									if (trialProduct.eventDetails) {
										trialProduct.eventDetails.space_name = spaceInfo.room_name;
										trialProduct.eventDetails.location_desc = spaceInfo.location_desc;
									}
								})
								.catch((error) => {
									console.error('Error fetching space details:', error);
								});
						}
					} else {
						// No time slots selected yet
						productConfigs = [
							{
								id: generateId(),
								title: 'Trial Product',
								description: 'No time slots selected yet',
								type: 'trial_first',
								prices:
									trialFirstProducts.length > 0
										? trialFirstProducts[0].prices.map((price) => ({
												id: generateId(),
												price_id: price.price_id,
												price_name: '',
												cost_units: price.cost_units
											}))
										: []
							},
							{
								id: generateId(),
								title: 'Main Product',
								description: 'No time slots selected yet',
								type: 'trial_rest',
								prices:
									trialRestProducts.length > 0
										? trialRestProducts[0].prices.map((price) => ({
												id: generateId(),
												price_id: price.price_id,
												price_name: '',
												cost_units: price.cost_units
											}))
										: []
							}
						];
					}
				}

				// Ensure all products have at least one price option for UI
				if (productConfigs.length > 0) {
					productConfigs.forEach((product) => {
						if (product.prices.length === 0) {
							addEmptyPrice(product);
						}
					});
					return true;
				}
			}

			// If we got here, we need to initialize with defaults based on form type
			const formType = formValue.product_form_type || 'bundled';
			currentFormType = formType;
			await initializeProductConfigs(formType, formValue);
			return true;
		} catch (error) {
			console.error('[PricingTab] Error initializing from form data:', error);
			return false;
		}
	}

	// One-time initialization when component mounts
	$effect(() => {
		if (hasInitialized) return;

		const initialize = async () => {
			isInitializing = true;

			try {
				// Start loading prices first and wait for it to complete
				await fetchPrices();

				// Initialize from form data
				const formValue = $formData;
				const formFactors = formValue?.product_form_factor;

				if (formFactors && formFactors.length > 0) {
					// Initialize pricing configs first
					pricingConfigs = formFactors.map((config: FormProductPrice) => ({
						...config,
						prices: config.prices.map((price) => ({
							price_id:
								price.price_id || (pricesList.length > 0 ? pricesList[0].id : 'missing-price-id'),
							cost_units: price.cost_units || 2
						}))
					}));

					// Then initialize product configs
					await initializeFromFormData(formValue);
				} else {
					// Default initialization if no form data
					resetPricingConfigs();
					await initializeProductConfigs('bundled', formValue || ({} as FormSchema));
				}

				// Update form data one final time to ensure sync
				updateFormData();
			} finally {
				isInitializing = false;
				hasInitialized = true;
			}
		};

		initialize();
	});

	// Watch form type changes - use direct comparison to avoid infinite loops
	$effect(() => {
		// Skip if not initialized or if we're already updating
		if (isInitializing || !hasInitialized || isUpdatingForm) return;

		const formValue = $formData;
		if (!formValue?.product_form_type) return;

		// Always trigger if the form type actually changed
		if (formValue.product_form_type !== currentFormType) {
			isInitializing = true;

			const updateFormType = async () => {
				try {
					// Store the new form type to avoid race conditions
					const newFormType = formValue.product_form_type;

					// Update current form type immediately
					currentFormType = newFormType;

					// Preserve existing price IDs and cost units before reinitializing
					const existingPrices = formValue.product_form_factor?.reduce<
						Record<string, Array<{ price_id: string; cost_units: number }>>
					>((acc, config) => {
						acc[config.product_type] = config.prices;
						return acc;
					}, {});

					await initializeProductConfigs(newFormType, formValue);

					// After initialization, restore preserved prices if they exist
					if (existingPrices && Object.keys(existingPrices).length > 0) {
						productConfigs.forEach((product) => {
							const existingProductPrices = existingPrices[product.type];
							if (existingProductPrices?.length > 0) {
								product.prices = product.prices.map((price, idx) => ({
									...price,
									price_id: existingProductPrices[idx]?.price_id || price.price_id,
									cost_units: existingProductPrices[idx]?.cost_units || price.cost_units
								}));
							}
						});

						// Update form data with preserved prices
						debouncedSyncForm();
					}
				} finally {
					// Use a timeout to ensure all async operations complete
					setTimeout(() => {
						isInitializing = false;
					}, 100);
				}
			};

			updateFormType();
		}
	});

	// Watch for changes in time slots to update product configs
	let prevTimeSlotsCount = -1;
	let prevTimeSlotIds = $state<string[]>([]);

	$effect(() => {
		// Skip if not initialized or if we're in the middle of updating the form ourselves
		if (isInitializing || !hasInitialized || isUpdatingForm) return;

		const formValue = $formData;
		if (!formValue) return;

		const timeSlots = getSelectedTimeSlots();
		const eventCount = timeSlots.length;
		const currentSlotIds = timeSlots.map((slot) => slot.id).join(',');

		// Check if slots have changed by count or by IDs
		const slotsChanged =
			eventCount !== prevTimeSlotsCount || currentSlotIds !== prevTimeSlotIds.join(',');

		if (!slotsChanged) return;

		// Update our tracking variables
		prevTimeSlotsCount = eventCount;
		prevTimeSlotIds = timeSlots.map((slot) => slot.id);

		// Force reinitialize based on current form type
		isProcessingTimeSlotChanges = true;

		const updateTimeSlots = async () => {
			try {
				await initializeProductConfigs(formValue.product_form_type, formValue);

				// Use our improved synchronization
				debouncedSyncForm();
			} finally {
				isProcessingTimeSlotChanges = false;
			}
		};

		updateTimeSlots();
	});

	// Update price names when price list is loaded
	$effect(() => {
		if (pricesList.length > 0 && productConfigs.length > 0) {
			// Update price names based on the loaded price list
			productConfigs.forEach((product: ProductConfig) => {
				product.prices.forEach((price: ProductPrice) => {
					if (price.price_id && !price.price_name) {
						const matchingPrice = pricesList.find((p) => p.id === price.price_id);
						if (matchingPrice) {
							price.price_name = getLocalizedText(
								matchingPrice.title as LocalizedText,
								getLocale()
							);
						}
					}
				});
			});
		}
	});

	// Update form when pricing configs change
	let isUpdatingFormFromPricingConfigs = $state(false);

	$effect(() => {
		// Only update form if configs have been initialized and we're not already updating
		if (
			pricingConfigs.length > 0 &&
			!isUpdatingFormFromPricingConfigs &&
			!isInitializing &&
			hasInitialized
		) {
			// Skip update if the current form value already matches pricingConfigs
			const currentFormFactor = $formData?.product_form_factor || [];

			// Ensure we have price IDs set
			if (pricesList.length > 0) {
				const defaultPriceId = pricesList[0].id;

				// Update any missing price IDs in pricingConfigs
				const updatedConfigs = pricingConfigs.map((config) => ({
					...config,
					prices: config.prices.map((price) => {
						if (!price.price_id || price.price_id === 'missing-price-id') {
							return { ...price, price_id: defaultPriceId };
						}
						return price;
					})
				}));

				// Update if there were changes
				if (JSON.stringify(updatedConfigs) !== JSON.stringify(pricingConfigs)) {
					pricingConfigs = updatedConfigs;
				}
			}

			// Use our improved synchronization method instead of direct form update
			const configsMatch = JSON.stringify(currentFormFactor) === JSON.stringify(pricingConfigs);
			if (configsMatch) return;

			isUpdatingFormFromPricingConfigs = true;

			try {
				debouncedSyncForm();
			} finally {
				// Reset the guard flag after the update with a longer timeout
				setTimeout(() => {
					isUpdatingFormFromPricingConfigs = false;
				}, 100); // Longer timeout
			}
		}
	});

	// Initialize data from form
	let isUpdatingPricingConfigsFromForm = $state(false);

	$effect(() => {
		// Only update if we're not in a recursive update and form data exists
		if (
			!isUpdatingPricingConfigsFromForm &&
			$formData &&
			!isUpdatingFormFromPricingConfigs &&
			hasInitialized
		) {
			const formFactor = $formData?.product_form_factor || [];

			// Skip update if pricingConfigs already matches form data
			const configsMatch = JSON.stringify(formFactor) === JSON.stringify(pricingConfigs);
			if (configsMatch) return;

			isUpdatingPricingConfigsFromForm = true;

			try {
				// Initialize pricing configurations based on form data
				if (formFactor.length > 0) {
					// Make sure price IDs are valid
					const defaultPriceId = pricesList.length > 0 ? pricesList[0].id : 'missing-price-id';

					// Use existing product_form_factor data but ensure all price IDs are valid
					pricingConfigs = formFactor.map((config) => ({
						...config,
						prices: config.prices.map((price) => ({
							...price,
							price_id:
								price.price_id && price.price_id !== 'missing-price-id'
									? price.price_id
									: defaultPriceId
						}))
					}));

					// Also update product configs if they've been initialized
					if (productConfigs.length > 0) {
						productConfigs.forEach((product) => {
							// Find matching config
							const matchingConfig = pricingConfigs.find(
								(config) => config.product_type === product.type
							);

							if (matchingConfig) {
								// Update prices based on matching config
								product.prices = product.prices.map((price, idx) => {
									const configPrice = matchingConfig.prices[idx];
									if (configPrice) {
										return {
											...price,
											price_id: configPrice.price_id,
											cost_units: configPrice.cost_units,
											price_name: price.price_name // Preserve existing name if any
										};
									}
									return price;
								});
							}
						});
					}
				} else if (!hasInitialized) {
					// Initialize with defaults only once
					hasInitialized = true;
					resetPricingConfigs();
				}
			} finally {
				// Reset the guard flag after the update with a longer timeout
				setTimeout(() => {
					isUpdatingPricingConfigsFromForm = false;
				}, 100);
			}
		}
	});

	// Fix mismatches between product_form_type and product_form_factor
	let isFixingTypeMismatch = $state(false);

	$effect(() => {
		// Skip if already fixing or initializing
		if (isFixingTypeMismatch || isInitializing || !hasInitialized) return;

		const formValue = $formData;
		if (!formValue) return;

		// Get the form type and form factor
		const formType = formValue.product_form_type;
		const formFactor = formValue.product_form_factor || [];

		// Check for mismatches
		const hasMismatch = formFactor.some((config) => {
			if (formType === 'bundled' && config.product_type !== 'bundled') return true;
			if (formType === 'individual' && config.product_type !== 'individual') return true;
			if (formType === 'trial' && !['trial_first', 'trial_rest'].includes(config.product_type))
				return true;
			return false;
		});

		// If there's a mismatch, fix it
		if (hasMismatch) {
			isFixingTypeMismatch = true;

			// Create correct configurations
			let correctedConfigs: any[] = [];

			if (formType === 'bundled') {
				correctedConfigs = [
					{
						product_type: 'bundled',
						prices:
							formFactor.length > 0
								? formFactor[0].prices
								: [{ price_id: 'missing-price-id', cost_units: 2 }]
					}
				];
			} else if (formType === 'individual') {
				correctedConfigs = [
					{
						product_type: 'individual',
						prices:
							formFactor.length > 0
								? formFactor[0].prices
								: [{ price_id: 'missing-price-id', cost_units: 2 }]
					}
				];
			} else if (formType === 'trial') {
				// Try to preserve existing prices
				const trialFirstConfig = formFactor.find((c) => c.product_type === 'trial_first');
				const trialRestConfig = formFactor.find((c) => c.product_type === 'trial_rest');

				correctedConfigs = [
					{
						product_type: 'trial_first',
						prices: trialFirstConfig?.prices || [{ price_id: 'missing-price-id', cost_units: 1 }]
					},
					{
						product_type: 'trial_rest',
						prices: trialRestConfig?.prices || [{ price_id: 'missing-price-id', cost_units: 2 }]
					}
				];
			}

			// Update the form
			formData.update(($formData) => ({
				...$formData,
				product_form_factor: correctedConfigs
			}));

			// Reset guard flag after a short delay
			setTimeout(() => {
				isFixingTypeMismatch = false;
			}, 0);
		}
	});
</script>

<div class="h-full overflow-y-auto px-1">
	<div class="space-y-4">
		<div class="text-sm text-muted-foreground">
			Set pricing for your product based on your selected product form type.
			{#if currentFormType === 'bundled'}
				You're creating a single bundled product.
			{:else if currentFormType === 'individual'}
				You're creating individual products for each event.
			{:else if currentFormType === 'trial'}
				You're creating a trial product for the first event and a main product for remaining events.
			{/if}
		</div>

		{#if isInitializing || isLoadingSpaces}
			<div class="flex items-center justify-center py-8">
				<div
					class="h-6 w-6 animate-spin rounded-full border-2 border-primary/20 border-t-primary"
				></div>
				<span class="ml-2 text-sm text-muted-foreground">Loading product details...</span>
			</div>
		{:else}
			{#each productConfigs as product, productIndex}
				<Card class="border-muted/70 p-4 shadow-sm">
					<div class="flex items-center justify-between">
						<div>
							<h3 class="text-lg font-semibold">{product.title}</h3>
							<p class="text-sm text-muted-foreground">{product.description}</p>
						</div>
						<Badge variant={product.type.includes('trial') ? 'secondary' : 'default'}>
							{product.type === 'bundled'
								? 'Bundled'
								: product.type === 'individual'
									? 'Individual'
									: product.type === 'trial_first'
										? 'Trial'
										: 'Main'}
						</Badge>
					</div>

					{#if product.eventDetails}
						<div class="mt-3 rounded-md bg-muted/30 p-3">
							<div class="grid gap-2 text-sm">
								<div class="flex items-center gap-2">
									<Calendar class="h-4 w-4 text-muted-foreground" />
									<span>{formatDate(product.eventDetails.start_time)}</span>
								</div>
								<div class="flex items-center gap-2">
									<Clock class="h-4 w-4 text-muted-foreground" />
									<span>
										{formatTimeRange(
											product.eventDetails.start_time,
											product.eventDetails.end_time
										)}
									</span>
								</div>
								<div class="flex items-center gap-2">
									<MapPin class="h-4 w-4 text-muted-foreground" />
									<div>
										<div>{product.eventDetails.space_name || 'Unknown Space'}</div>
										{#if product.eventDetails.location_desc}
											<div class="text-xs text-muted-foreground">
												{product.eventDetails.location_desc}
											</div>
										{/if}
									</div>
								</div>
								{#if product.eventDetails.assigned_instructors && product.eventDetails.assigned_instructors.length > 0}
									<div class="flex items-start gap-2">
										<Users class="mt-1 h-4 w-4 text-muted-foreground" />
										<div>
											{#each product.eventDetails.assigned_instructors as instructor, i}
												<span>
													{instructor.name}
													{i < product.eventDetails.assigned_instructors.length - 1 ? ', ' : ''}
												</span>
											{/each}
										</div>
									</div>
								{/if}
							</div>
						</div>
					{/if}

					<Separator class="my-4 opacity-60" />

					<div class="space-y-4">
						{#each product.prices as price, priceIndex}
							<div class="space-y-2 {priceIndex > 0 ? 'border-t border-muted/60 pt-3' : ''}">
								{#if product.prices.length > 1}
									<div class="flex items-center justify-between">
										<h4 class="text-sm font-medium">
											Price Option {priceIndex + 1}
										</h4>
										<div class="flex items-center gap-2">
											<Button
												type="button"
												variant="ghost"
												size="icon"
												class="h-7 w-7 transition-colors hover:bg-destructive/10 hover:text-destructive"
												onclick={() => removePrice(productIndex, priceIndex)}
												disabled={product.prices.length <= 1}
											>
												<Trash2 class="h-3.5 w-3.5" />
											</Button>
										</div>
									</div>
								{:else}
									<div class="flex items-center justify-between">
										<h4 class="text-sm font-medium">Price Option</h4>
									</div>
								{/if}

								<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
									<div class="space-y-2">
										<Label>Price Type<span class="ml-1 text-destructive">*</span></Label>
										<PriceSelector
											{supabase}
											{brandId}
											selectedPriceId={price.price_id}
											showValidationError={showValidation && hasPriceError(price.price_id)}
											onSelect={(id: string, name: string) =>
												handlePriceSelect(productIndex, priceIndex, id, name)}
										/>
										{#if showValidation && hasPriceError(price.price_id)}
											<p class="mt-1.5 flex items-center gap-1 text-xs text-destructive">
												<AlertCircle class="h-3 w-3 shrink-0" />
												Price is required
											</p>
										{/if}
									</div>

									<div class="space-y-2">
										<Label>Cost Units</Label>
										<div class="flex items-center gap-2">
											<Input
												type="number"
												min="0.1"
												step="0.1"
												value={price.cost_units}
												oninput={(e) =>
													handleCostUnitsChange(
														productIndex,
														priceIndex,
														parseFloat(e.currentTarget.value)
													)}
												class="focus-visible:ring-primary/50"
											/>
										</div>
									</div>
								</div>
							</div>
						{/each}

						<Button
							type="button"
							variant="outline"
							onclick={() => addPrice(productIndex)}
							class="mt-2 w-full text-sm transition-colors hover:bg-primary/5"
						>
							<Plus class="mr-2 h-3.5 w-3.5" />
							Add Price Option
						</Button>
					</div>
				</Card>
			{/each}
		{/if}
	</div>

	{#if showValidation && hasAnyPriceErrors()}
		<div class="mt-6 flex items-start gap-2 rounded-md bg-destructive/10 p-3">
			<AlertCircle class="mt-0.5 h-5 w-5 shrink-0 text-destructive" />
			<div>
				<p class="text-sm font-medium text-destructive">Please fix the errors before submitting</p>
				<ul class="ml-2 mt-1 space-y-1 text-xs text-destructive">
					<li class="flex items-center gap-1">• Check Pricing configurations</li>
				</ul>
			</div>
		</div>
	{/if}
</div>
