import { formSchema } from './schemas';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageServerLoad } from './$types';
import { error, fail } from '@sveltejs/kit';
import type { z } from 'zod';
import type { LocalizedText } from '$lib/utils/localization';
import type { Actions } from './$types';
import type { Database } from '$lib/supabase/database.types';

type Tables = Database['public']['Tables'];

export type Product = Tables['product']['Row'] & {
	metadata: Tables['metadata']['Row'] | null;
	event_products: (Tables['event_product']['Row'] & {
		event: Tables['event']['Row'];
	})[];
	prices: Tables['product_price']['Row'][];
	metadata_wikipages: Tables['metadata_wikipage']['Row'][];
	metadata_tracks: Tables['metadata_track']['Row'][];
};

type FormSchema = z.infer<typeof formSchema>;
type UpsertProductInput = Database['public']['CompositeTypes']['upsert_product_input_product'];

// Add these type definitions for the inputs
type EventProductInput =
	Database['public']['CompositeTypes']['upsert_offering_input_event_product'];
type ProductInput = Database['public']['CompositeTypes']['upsert_offering_input_product'];
type EventInput = Database['public']['CompositeTypes']['upsert_offering_input_event'];
type MetadataInput = Database['public']['CompositeTypes']['upsert_offering_input_metadata'];

// Initialize form with defaults, using LocalizedText for title and description
const defaultFormData = {
	title: { en: '' } as LocalizedText,
	description: { en: '' } as LocalizedText,
	dance_level: 'all-levels' as const,
	dance_genre: '',
	product_form_type: 'bundled' as const,
	video_kind: null,
	video_url: undefined,
	video_range: undefined,
	cover_url: undefined,
	time_slots: [],
	tracks: [],
	artists: [],
	cost_units: 1000,
	price_id: undefined,
	product_prices: []
} satisfies FormSchema;

export const load = (async ({ locals: { supabase, brand } }) => {
	try {
		const form = await superValidate(defaultFormData, zod(formSchema));

		// Check if brand ID exists
		if (!brand?.id) {
			throw error(400, 'Brand ID is required');
		}

		const { data: products, error: fetchError } = await supabase
			.from('product')
			.select(
				`
				*,
				metadata (
				    *,
					metadata_wikipage (*),
					metadata_track (*)
				),
				event_product (
					*,
					event (*)
				),
				product_price (*)
				`
			)
			.eq('brand_id', brand.id)
			.order('created_at', { ascending: false });

		if (fetchError) {
			console.error('[Product] Failed to fetch products:', fetchError);
			throw error(500, 'Failed to fetch products');
		}

		return {
			form,
			products: products as Product[],
			brand
		};
	} catch (e) {
		console.error('[Product] Server error:', e);
		throw error(500, 'Internal server error');
	}
}) satisfies PageServerLoad;

export const actions = {
	upsert: async ({ request, locals: { supabase, brand } }) => {
		const form = await superValidate(request, zod(formSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			const {
				data: { user }
			} = await supabase.auth.getUser();
			if (!user) throw error(401, 'Unauthorized');

			// The title and description should already be LocalizedText objects from the form
			const titleLocalized = form.data.title as LocalizedText;
			const descriptionLocalized = form.data.description as LocalizedText;

			// Create a single metadata record
			const metadataId = crypto.randomUUID();

			// Prepare input arrays based on product form type
			const metadataInputs: MetadataInput[] = [];
			const productInputs: ProductInput[] = [];
			const eventInputs: EventInput[] = [];
			const eventProductInputs: EventProductInput[] = [];

			// Get price info from form data
			type ProductPriceConfig = {
				product_type: 'bundled' | 'individual' | 'trial_first' | 'trial_rest';
				prices: {
					price_id: string;
					cost_units: number;
					is_default: boolean;
				}[];
			};

			let productPriceConfigs: ProductPriceConfig[] = [];

			if (form.data.product_prices && form.data.product_prices.length > 0) {
				// Only use product_prices that have valid price IDs
				productPriceConfigs = form.data.product_prices
					.map((config) => ({
						...config,
						prices: config.prices.filter((p) => p.price_id) // Only include prices with an ID
					}))
					.filter((config) => config.prices.length > 0); // Only include configs with prices
			} else if (form.data.price_id) {
				// Fallback to single price configuration
				const productType = form.data.product_form_type;
				if (productType === 'bundled') {
					productPriceConfigs = [
						{
							product_type: 'bundled',
							prices: [
								{
									price_id: form.data.price_id,
									cost_units: form.data.cost_units,
									is_default: true
								}
							]
						}
					];
				} else if (productType === 'individual') {
					productPriceConfigs = [
						{
							product_type: 'individual',
							prices: [
								{
									price_id: form.data.price_id,
									cost_units: form.data.cost_units,
									is_default: true
								}
							]
						}
					];
				} else if (productType === 'trial') {
					productPriceConfigs = [
						{
							product_type: 'trial_first',
							prices: [
								{
									price_id: form.data.price_id,
									cost_units: form.data.cost_units / 2, // Half price for trial
									is_default: true
								}
							]
						},
						{
							product_type: 'trial_rest',
							prices: [
								{
									price_id: form.data.price_id,
									cost_units: form.data.cost_units,
									is_default: true
								}
							]
						}
					];
				}
			}

			// Helper to get default price for a product type, if available
			const getDefaultPrice = (
				productType: 'bundled' | 'individual' | 'trial_first' | 'trial_rest'
			) => {
				const config = productPriceConfigs.find((c) => c.product_type === productType);
				if (!config || config.prices.length === 0) {
					return null; // No price found
				}

				const defaultPrice = config.prices.find((p) => p.is_default === true) || config.prices[0];
				return defaultPrice;
			};

			// Only create products if we have prices defined
			if (productPriceConfigs.length === 0) {
				// If no prices defined, we can't create products
				return fail(400, {
					form,
					error: 'At least one price must be defined for the product'
				});
			}

			// Prepare the metadata record
			metadataInputs.push({
				metadata_id: metadataId,
				metadata_kind: 'class',
				metadata_title: titleLocalized,
				metadata_subtitle: null,
				metadata_desc: descriptionLocalized,
				metadata_message: null,
				metadata_custom_attribute: {
					dance_level: form.data.dance_level,
					dance_genre: form.data.dance_genre,
					video_kind: form.data.video_kind,
					artists: form.data.artists,
					tracks: form.data.tracks,
					product_form_type: form.data.product_form_type,
					product_prices: productPriceConfigs // Store this in metadata for reference
				},
				metadata_promo_message: null,
				metadata_promo_video_url: form.data.video_url || null,
				metadata_promo_image_url: form.data.cover_url || null,
				metadata_promo_webpage_url: null,
				metadata_wikipages: [],
				metadata_tracks: []
			});

			// Handle logic based on product form type
			switch (form.data.product_form_type) {
				case 'bundled': {
					// For bundled products, create a single product for all events
					const productId = crypto.randomUUID();
					const bundledPrice = getDefaultPrice('bundled');

					productInputs.push({
						product_id: null,
						product_brand_id: brand.id,
						product_kind: 'class',
						product_stock: 100,
						product_publishing_state: 'draft',
						product_open_to_buy_at: new Date().toISOString(),
						product_close_to_buy_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
						product_creator_id: user.id,
						metadata_id: metadataId,
						product_prices: [
							{
								product_price_id: null,
								product_price_price_id:
									bundledPrice?.price_id || 'd3419501-50a9-8d26-7f69-de55865d08ec',
								product_price_cost_units: bundledPrice?.cost_units || 1000,
								product_price_start_at: new Date().toISOString(),
								product_price_end_at: null,
								product_price_stock: null,
								product_price_order_requirement_id: null,
								product_price_reward_price_id: null,
								product_price_reward_price_units: null
							}
						]
					});

					// Create events for each time slot
					form.data.time_slots.forEach((slot, index) => {
						const eventId = crypto.randomUUID();

						// Calculate duration minutes
						const startTime = new Date(slot.start_time);
						const endTime = new Date(slot.end_time);
						const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / 60000);

						eventInputs.push({
							event_id: null,
							event_kind: 'class',
							event_start_at: slot.start_time,
							event_duration_minute: durationMinutes,
							event_space_id: slot.room || null,
							event_brand_id: brand.id,
							event_publishing_state: 'draft',
							event_creator_id: user.id,
							metadata_id: metadataId
						});

						// Link event to the single product
						eventProductInputs.push({
							event_product_id: null,
							event_product_relation: 'main',
							event_id: eventId,
							product_id: productId
						});
					});
					break;
				}

				case 'individual': {
					// For individual products, create one product per event
					const individualPrice = getDefaultPrice('individual');

					form.data.time_slots.forEach((slot, index) => {
						const productId = crypto.randomUUID();
						const eventId = crypto.randomUUID();

						// Create a product for this event
						productInputs.push({
							product_id: null,
							product_brand_id: brand.id,
							product_kind: 'class',
							product_stock: 100,
							product_publishing_state: 'draft',
							product_open_to_buy_at: new Date().toISOString(),
							product_close_to_buy_at: new Date(
								Date.now() + 365 * 24 * 60 * 60 * 1000
							).toISOString(),
							product_creator_id: user.id,
							metadata_id: metadataId,
							product_prices: [
								{
									product_price_id: null,
									product_price_price_id:
										individualPrice?.price_id || 'd3419501-50a9-8d26-7f69-de55865d08ec',
									product_price_cost_units: individualPrice?.cost_units || 1000,
									product_price_start_at: new Date().toISOString(),
									product_price_end_at: null,
									product_price_stock: null,
									product_price_order_requirement_id: null,
									product_price_reward_price_id: null,
									product_price_reward_price_units: null
								}
							]
						});

						// Calculate duration minutes
						const startTime = new Date(slot.start_time);
						const endTime = new Date(slot.end_time);
						const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / 60000);

						// Create an event for this time slot
						eventInputs.push({
							event_id: null,
							event_kind: 'class',
							event_start_at: slot.start_time,
							event_duration_minute: durationMinutes,
							event_space_id: slot.room || null,
							event_brand_id: brand.id,
							event_publishing_state: 'draft',
							event_creator_id: user.id,
							metadata_id: metadataId
						});

						// Link this event to its product
						eventProductInputs.push({
							event_product_id: null,
							event_product_relation: 'main',
							event_id: eventId,
							product_id: productId
						});
					});
					break;
				}

				case 'trial': {
					// For trial mode, create one product for the first event and one for the rest
					if (form.data.time_slots.length > 0) {
						const trialProductId = crypto.randomUUID();
						const mainProductId = crypto.randomUUID();

						const trialPrice = getDefaultPrice('trial_first');
						const mainPrice = getDefaultPrice('trial_rest');

						// Create the trial product (first event)
						productInputs.push({
							product_id: null,
							product_brand_id: brand.id,
							product_kind: 'class',
							product_stock: 100,
							product_publishing_state: 'draft',
							product_open_to_buy_at: new Date().toISOString(),
							product_close_to_buy_at: new Date(
								Date.now() + 365 * 24 * 60 * 60 * 1000
							).toISOString(),
							product_creator_id: user.id,
							metadata_id: metadataId,
							product_prices: [
								{
									product_price_id: null,
									product_price_price_id:
										trialPrice?.price_id || 'd3419501-50a9-8d26-7f69-de55865d08ec',
									product_price_cost_units: trialPrice?.cost_units || 1000,
									product_price_start_at: new Date().toISOString(),
									product_price_end_at: null,
									product_price_stock: null,
									product_price_order_requirement_id: null,
									product_price_reward_price_id: null,
									product_price_reward_price_units: null
								}
							]
						});

						// Create the main product (all other events)
						if (form.data.time_slots.length > 1) {
							productInputs.push({
								product_id: null,
								product_brand_id: brand.id,
								product_kind: 'class',
								product_stock: 100,
								product_publishing_state: 'draft',
								product_open_to_buy_at: new Date().toISOString(),
								product_close_to_buy_at: new Date(
									Date.now() + 365 * 24 * 60 * 60 * 1000
								).toISOString(),
								product_creator_id: user.id,
								metadata_id: metadataId,
								product_prices: [
									{
										product_price_id: null,
										product_price_price_id:
											mainPrice?.price_id || 'd3419501-50a9-8d26-7f69-de55865d08ec',
										product_price_cost_units: mainPrice?.cost_units || 1000,
										product_price_start_at: new Date().toISOString(),
										product_price_end_at: null,
										product_price_stock: null,
										product_price_order_requirement_id: null,
										product_price_reward_price_id: null,
										product_price_reward_price_units: null
									}
								]
							});
						}

						// Process all time slots
						form.data.time_slots.forEach((slot, index) => {
							const eventId = crypto.randomUUID();

							// Calculate duration minutes
							const startTime = new Date(slot.start_time);
							const endTime = new Date(slot.end_time);
							const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / 60000);

							// Create an event for this time slot
							eventInputs.push({
								event_id: null,
								event_kind: 'class',
								event_start_at: slot.start_time,
								event_duration_minute: durationMinutes,
								event_space_id: slot.room || null,
								event_brand_id: brand.id,
								event_publishing_state: 'draft',
								event_creator_id: user.id,
								metadata_id: metadataId
							});

							// Link this event to the appropriate product
							if (index === 0) {
								// First event goes to trial product
								eventProductInputs.push({
									event_product_id: null,
									event_product_relation: 'main',
									event_id: eventId,
									product_id: trialProductId
								});
							} else {
								// All other events go to main product
								eventProductInputs.push({
									event_product_id: null,
									event_product_relation: 'main',
									event_id: eventId,
									product_id: mainProductId
								});
							}
						});
					}
					break;
				}
			}

			// Call the RPC function to create/update the offerings
			const { data, error: rpcError } = await supabase.rpc('client_upsert_offering' as any, {
				upsert_offering_input_all_event_product: eventProductInputs,
				upsert_offering_input_all_product: productInputs,
				upsert_offering_input_all_event: eventInputs,
				upsert_offering_input_all_metadata: metadataInputs,
				upsert_offering_input_request_id: null,
				upsert_offering_input_perform_action: true
			});

			if (rpcError) throw rpcError;

			return { form, success: true };
		} catch (err) {
			console.error('Error upserting product:', err);
			return fail(500, {
				form,
				error: err instanceof Error ? err.message : 'Failed to save product'
			});
		}
	}
} satisfies Actions;
