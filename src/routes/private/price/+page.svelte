<script lang="ts">
	import { invalidate } from '$app/navigation';
	import { goto } from '$app/navigation';
	import { Button } from '$lib/components/ui/button';
	import DataTable from '$lib/components/shared/DataTable.svelte';
	import { getLocalizedText, type LocalizedText, type LocaleKey } from '$lib/utils/localization';
	import { PageContainer } from '$lib/components/layout';
	import {
		createSelectColumn,
		createLocalizedColumn,
		createSortableColumn,
		createDateColumn,
		createActionsColumn
	} from '$lib/components/shared/table-utils';
	import type { ColumnDef } from '@tanstack/table-core';
	import type { Database } from '$lib/supabase/database.types';
	import * as Select from '$lib/components/ui/select';
	import type { Json } from '$lib/supabase/database.types';
	import { onMount } from 'svelte';

	// Use the database row type directly
	type Price = Database['public']['Tables']['price']['Row'];

	interface Props {
		data: {
			prices: Price[];
			successMessage: { type: 'success' | 'error'; text: string } | null;
		};
	}

	let { data }: Props = $props();
	let { prices, successMessage } = $derived(data);

	let isLoading = $state(false);
	let errorMessage = $state<string | null>(null);
	let selectedPaymentKind = $state<string>('');
	const currentLocale: LocaleKey = 'en';
	let filteredPrices = $state<Price[]>(prices);

	// Copy success message to local state
	let message = $state(successMessage);

	// Clear success message from URL after displaying
	onMount(() => {
		if (successMessage && window.history.replaceState) {
			// Remove the query parameter without a page reload
			const url = new URL(window.location.href);
			url.searchParams.delete('success');
			window.history.replaceState({}, '', url);
		}
	});

	function openCreatePage() {
		goto('/private/price/edit/new');
	}

	function openEditPage(price: Price) {
		goto(`/private/price/edit/${price.id}`);
	}

	$effect(() => {
		filteredPrices = selectedPaymentKind
			? prices.filter((p) => p.payment_kind === selectedPaymentKind)
			: prices;
	});

	const paymentKinds = ['one_time', 'subscription', 'membership'];

	const columns: ColumnDef<Price, any>[] = [
		createSelectColumn<Price>(),
		createLocalizedColumn<Price>('title', 'Title', (row) => row.title as Json, currentLocale),
		createLocalizedColumn<Price>(
			'product_classification',
			'Classification',
			(row) => row.product_classification as Json,
			currentLocale
		),
		createSortableColumn<Price>('payment_kind', 'Payment Kind', (row) => row.payment_kind),
		createSortableColumn<Price>('unit_kind', 'Unit Kind', (row) => row.unit_kind),
		createSortableColumn<Price>('unit_step', 'Unit Step', (row) => row.unit_step),
		createSortableColumn<Price>('semantic_order', 'Order', (row) => row.semantic_order),
		createDateColumn<Price>('updated_at', 'Updated At', (row) => row.updated_at),
		createActionsColumn<Price>({
			label: 'Edit',
			onClick: (row) => openEditPage(row)
		})
	];
</script>

{#snippet actions()}
	<div class="flex items-center gap-4">
		<Select.Root type="single" bind:value={selectedPaymentKind}>
			<Select.Trigger class="w-[200px]">
				{selectedPaymentKind || 'All Payment Kinds'}
			</Select.Trigger>
			<Select.Content>
				<Select.Group>
					<Select.GroupHeading>Filter by Payment Kind</Select.GroupHeading>
					<Select.Item value="" label="All">All</Select.Item>
					{#each paymentKinds as kind}
						<Select.Item value={kind} label={kind}>{kind}</Select.Item>
					{/each}
				</Select.Group>
			</Select.Content>
		</Select.Root>

		<Button onclick={openCreatePage}>Create New</Button>
	</div>
{/snippet}

{#snippet content()}
	{#if message}
		<div
			class={`mb-4 rounded p-3 ${message.type === 'error' ? 'border-red-400 bg-red-100 text-red-700' : 'border-green-400 bg-green-100 text-green-700'}`}
		>
			{message.text}
		</div>
	{/if}

	{#if errorMessage}
		<div class="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700" role="alert">
			{errorMessage}
		</div>
	{/if}

	<DataTable data={filteredPrices} {columns} onRowAction={openEditPage} />
{/snippet}

<PageContainer title="Price Table" description="Manage your prices" {actions} {content} />
