import type { Actions, ServerLoad } from '@sveltejs/kit';
import type { Database } from '$lib/supabase/database.types';
import { schema } from '../../schema';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { fail, error, redirect } from '@sveltejs/kit';
import type { z } from 'zod';

// Define type for price input including DB fields
type PriceInput = z.infer<typeof schema> & {
	id?: string;
	brand_id: string;
	created_at?: string;
	updated_at: string;
	creator_id?: string;
};

export const load = (async ({ params, locals: { supabase, brand } }) => {
	const { pid } = params;

	// For new price, just return empty form
	if (pid === 'new') {
		const form = await superValidate(zod(schema));
		return { price: null, form };
	}

	// Load the price data
	const { data: price, error: priceError } = await supabase
		.from('price')
		.select('*')
		.eq('id', pid)
		.eq('brand_id', brand.id)
		.single();

	if (priceError) {
		console.error('Error loading price:', priceError);
		throw error(404, 'Price not found');
	}

	// Initialize form with price data
	const form = await superValidate(price, zod(schema));

	return { price, form };
}) satisfies ServerLoad;

export const actions = {
	upsert: async ({ params, request, locals: { supabase, brand, user } }) => {
		// Bail early if no user
		if (!user) {
			throw error(401, 'Unauthorized: You must be logged in');
		}

		const form = await superValidate(request, zod(schema));
		const { pid } = params;
		const isNew = pid === 'new';

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			const now = new Date().toISOString();

			const record: PriceInput = {
				...form.data,
				brand_id: brand.id,
				updated_at: now
			};

			// For new records, add creation metadata
			if (isNew) {
				record.id = crypto.randomUUID();
				record.created_at = now;
				record.creator_id = user.id;
			} else {
				record.id = pid; // Ensure ID is included for updates
			}

			const { error: upsertError } = await supabase.from('price').upsert(record).select();

			if (upsertError) {
				console.error('Error upserting price:', upsertError);
				return fail(500, {
					form,
					message: {
						type: 'error',
						text: `Failed to ${isNew ? 'create' : 'update'} price: ${upsertError.message}`
					}
				});
			}

			// Redirect back to prices page with success message
			throw redirect(303, `/private/price?success=${isNew ? 'created' : 'updated'}`);
		} catch (err) {
			if (err instanceof Response) throw err; // Allow redirect to propagate

			console.error('Unexpected error in upsert action:', err);
			return fail(500, {
				form,
				message: {
					type: 'error',
					text: 'An unexpected error occurred while saving the price. Please try again.'
				}
			});
		}
	},

	delete: async ({ params, locals: { supabase, brand, user } }) => {
		// Bail early if no user
		if (!user) {
			throw error(401, 'Unauthorized: You must be logged in');
		}

		const { pid } = params;

		// Cannot delete 'new'
		if (pid === 'new') {
			throw error(400, 'Cannot delete a new price');
		}

		try {
			const { error: deleteError } = await supabase
				.from('price')
				.delete()
				.eq('id', pid)
				.eq('brand_id', brand.id);

			if (deleteError) {
				return fail(500, {
					message: {
						type: 'error',
						text: `Failed to delete price: ${deleteError.message}`
					}
				});
			}

			// Redirect back to prices page with success message
			throw redirect(303, '/private/price?success=deleted');
		} catch (err) {
			if (err instanceof Response) throw err; // Allow redirect to propagate

			console.error('Unexpected error in delete action:', err);
			return fail(500, {
				message: {
					type: 'error',
					text: 'An unexpected error occurred while deleting the price. Please try again.'
				}
			});
		}
	}
} satisfies Actions;
