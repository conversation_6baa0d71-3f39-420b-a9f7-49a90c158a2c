<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { ArrowLeft, Trash2 } from '@lucide/svelte';
	import * as Select from '$lib/components/ui/select';
	import { Input } from '$lib/components/ui/input';
	import type { Database } from '$lib/supabase/database.types';
	import type { SuperValidated } from 'sveltekit-superforms';
	import type { z } from 'zod';
	import { schema } from '../../schema';
	import {
		ensureLocalizedText,
		getLocalizedText,
		type LocalizedText
	} from '$lib/utils/localization';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import { superForm } from 'sveltekit-superforms';
	import { Control, Label, Field, FieldErrors } from 'formsnap';
	import type { Json } from '$lib/supabase/database.types';
	import { goto } from '$app/navigation';
	import { PageContainer } from '$lib/components/layout';
	import * as AlertDialog from '$lib/components/ui/alert-dialog/index.js';

	// Use the database row type directly
	type PriceRow = Database['public']['Tables']['price']['Row'];

	// Component props from page data
	interface Props {
		data: {
			price: PriceRow | null;
			form: SuperValidated<z.infer<typeof schema>>;
		};
	}

	let { data }: Props = $props();
	let { price, form } = $derived(data);

	// Available payment kinds
	const paymentKinds = ['one_time', 'subscription', 'membership'];

	// Available unit kinds
	const unitKinds = ['point', 'day', 'minute', 'month'];

	// Available states
	const states = ['private', 'public', 'hidden', 'archived'];

	// Available colors
	const colorOptions = [
		{ value: 'red', label: 'Red', hex: '#ef4444' },
		{ value: 'orange', label: 'Orange', hex: '#f97316' },
		{ value: 'amber', label: 'Amber', hex: '#f59e0b' },
		{ value: 'yellow', label: 'Yellow', hex: '#eab308' },
		{ value: 'lime', label: 'Lime', hex: '#84cc16' },
		{ value: 'green', label: 'Green', hex: '#22c55e' },
		{ value: 'emerald', label: 'Emerald', hex: '#10b981' },
		{ value: 'teal', label: 'Teal', hex: '#14b8a6' },
		{ value: 'cyan', label: 'Cyan', hex: '#06b6d4' },
		{ value: 'sky', label: 'Sky', hex: '#0ea5e9' },
		{ value: 'blue', label: 'Blue', hex: '#3b82f6' },
		{ value: 'indigo', label: 'Indigo', hex: '#6366f1' },
		{ value: 'violet', label: 'Violet', hex: '#8b5cf6' },
		{ value: 'purple', label: 'Purple', hex: '#a855f7' },
		{ value: 'fuchsia', label: 'Fuchsia', hex: '#d946ef' },
		{ value: 'pink', label: 'Pink', hex: '#ec4899' },
		{ value: 'rose', label: 'Rose', hex: '#f43f5e' }
	];

	// Relative to options
	const relativeToOptions = [
		{ id: 'event_start_at', name: 'Event Start Time' },
		{ id: 'event_end_at', name: 'Event End Time' },
		{ id: 'created_at', name: 'Creation Time' }
	];

	// State for the delete confirmation dialog
	let showDeleteConfirm = $state(false);

	// Local state variables for select values
	let selectedColor = $state(form.data.color_primary_semantic || '');
	let selectedRelativeTo = $state(form.data.cancel_at_relative_to || '');

	// If we have a price, initialize form data with its values
	if (price && form.data) {
		// Ensure we're setting all required language keys
		form.data.title = {
			en: getLocalizedText(price.title as Json, 'en') || 'Untitled',
			zh: getLocalizedText(price.title as Json, 'zh') || '',
			ja: getLocalizedText(price.title as Json, 'ja') || '',
			ko: getLocalizedText(price.title as Json, 'ko') || ''
		};

		form.data.title_short = {
			en: getLocalizedText(price.title_short as Json, 'en') || '',
			zh: getLocalizedText(price.title_short as Json, 'zh') || '',
			ja: getLocalizedText(price.title_short as Json, 'ja') || '',
			ko: getLocalizedText(price.title_short as Json, 'ko') || ''
		};

		form.data.product_classification = {
			en: getLocalizedText(price.product_classification as Json, 'en') || 'Uncategorized',
			zh: getLocalizedText(price.product_classification as Json, 'zh') || '',
			ja: getLocalizedText(price.product_classification as Json, 'ja') || '',
			ko: getLocalizedText(price.product_classification as Json, 'ko') || ''
		};

		form.data.payment_kind = price.payment_kind || '';
		form.data.unit_kind = price.unit_kind || '';
		form.data.unit_step = price.unit_step?.toString() || '';
		form.data.state = price.state || 'private';
		form.data.semantic_order = price.semantic_order || 0;
		form.data.color_primary_semantic = price.color_primary_semantic || '';
		form.data.color_primary_hex = price.color_primary_hex || '';
		form.data.cancel_at_relative_to = price.cancel_at_relative_to || '';
		form.data.cancel_at_far_minute = price.cancel_at_far_minute || null;
		form.data.cancel_at_far_return_ratio = price.cancel_at_far_return_ratio?.toString() || '';
		form.data.cancel_at_near_minute = price.cancel_at_near_minute || null;
		form.data.cancel_at_near_return_ratio = price.cancel_at_near_return_ratio?.toString() || '';
		form.data.cancel_at_return_step = price.cancel_at_return_step?.toString() || '';
	}

	const superFormInstance = superForm(form, {
		taintedMessage: 'You have unsaved changes. Are you sure you want to leave?',
		dataType: 'json',
		onError: (event) => {
			console.error('Form validation error:', event);
		}
	});

	const { enhance, submitting, form: formData, errors, message } = superFormInstance;

	// Update form data when color changes
	function handleColorChange(color: string) {
		selectedColor = color;
		$formData.color_primary_semantic = color;
		$formData.color_primary_hex = colorOptions.find((c) => c.value === color)?.hex || '';
	}

	// Update form data when relative to changes
	function handleRelativeToChange(value: string) {
		selectedRelativeTo = value;
		$formData.cancel_at_relative_to = value;
	}

	// Synchronize local state with form data
	$effect(() => {
		selectedColor = $formData.color_primary_semantic || '';
		selectedRelativeTo = $formData.cancel_at_relative_to || '';
	});

	// Navigate back to the main price list
	function goBack() {
		goto('/private/price');
	}

	// Helper for fixing empty selections
	function colorValue(): string {
		return $formData.color_primary_semantic || '';
	}

	function relativeToValue(): string {
		return $formData.cancel_at_relative_to || '';
	}
</script>

{#snippet actions()}
	<div class="flex items-center gap-2">
		{#if price}
			<form method="POST" action="?/delete">
				<AlertDialog.Root bind:open={showDeleteConfirm}>
					<AlertDialog.Trigger>
						<Button variant="destructive" type="button" size="icon">
							<Trash2 class="h-4 w-4" />
						</Button>
					</AlertDialog.Trigger>
					<AlertDialog.Content>
						<AlertDialog.Header>
							<AlertDialog.Title>Delete Price</AlertDialog.Title>
							<AlertDialog.Description>
								Are you sure you want to delete this price entry? This action cannot be undone.
							</AlertDialog.Description>
						</AlertDialog.Header>
						<AlertDialog.Footer>
							<AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
							<AlertDialog.Action>
								<Button type="submit" variant="destructive">Delete</Button>
							</AlertDialog.Action>
						</AlertDialog.Footer>
					</AlertDialog.Content>
				</AlertDialog.Root>
			</form>
		{/if}
	</div>
{/snippet}

{#snippet content()}
	<div>
		<Button variant="outline" onclick={goBack} class="mb-6">
			<ArrowLeft class="mr-2 h-4 w-4" />
			Back to Price List
		</Button>

		{#if $message}
			<div
				class={`mb-6 rounded p-3 ${$message.type === 'error' ? 'border-red-400 bg-red-100 text-red-700' : 'border-green-400 bg-green-100 text-green-700'}`}
			>
				{$message.text}
			</div>
		{/if}

		<div class="rounded-md border bg-card p-6 shadow-sm">
			<form id="price-form" method="POST" action="?/upsert" use:enhance class="space-y-8">
				<!-- Localized text fields -->
				<div class="space-y-6">
					<LocalizedTextControl form={superFormInstance} name="title" label="Title" />
					<LocalizedTextControl form={superFormInstance} name="title_short" label="Short Title" />
					<LocalizedTextControl
						form={superFormInstance}
						name="product_classification"
						label="Product Classification"
					/>
				</div>

				<!-- Single column layout -->
				<div class="space-y-6">
					<!-- Payment Kind -->
					<Field form={superFormInstance} name="payment_kind">
						<Control>
							{#snippet children({ props })}
								<Label>Payment Kind</Label>
								<Select.Root type="single" bind:value={$formData.payment_kind} name={props.name}>
									<Select.Trigger
										class={`w-full ${$errors.payment_kind ? 'border-red-500' : ''}`}
										{...props}
									>
										{$formData.payment_kind || 'Select payment kind'}
									</Select.Trigger>
									<Select.Content>
										<Select.Group>
											<Select.GroupHeading>Payment Kinds</Select.GroupHeading>
											{#each paymentKinds as kind}
												<Select.Item value={kind} label={kind}>{kind}</Select.Item>
											{/each}
										</Select.Group>
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Control>
						<FieldErrors class="mt-1 text-xs text-red-500" />
					</Field>

					<!-- State -->
					<Field form={superFormInstance} name="state">
						<Control>
							{#snippet children({ props })}
								<Label>State</Label>
								<Select.Root type="single" bind:value={$formData.state} name={props.name}>
									<Select.Trigger
										class={`w-full ${$errors.state ? 'border-red-500' : ''}`}
										{...props}
									>
										{$formData.state || 'Select state'}
									</Select.Trigger>
									<Select.Content>
										<Select.Group>
											<Select.GroupHeading>States</Select.GroupHeading>
											{#each states as state}
												<Select.Item value={state} label={state}>{state}</Select.Item>
											{/each}
										</Select.Group>
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Control>
						<FieldErrors class="mt-1 text-xs text-red-500" />
					</Field>

					<!-- Unit Kind -->
					<Field form={superFormInstance} name="unit_kind">
						<Control>
							{#snippet children({ props })}
								<Label>Unit Kind</Label>
								<Select.Root type="single" bind:value={$formData.unit_kind} name={props.name}>
									<Select.Trigger
										class={`w-full ${$errors.unit_kind ? 'border-red-500' : ''}`}
										{...props}
									>
										{$formData.unit_kind || 'Select unit kind'}
									</Select.Trigger>
									<Select.Content>
										<Select.Group>
											<Select.GroupHeading>Unit Kinds</Select.GroupHeading>
											{#each unitKinds as kind}
												<Select.Item value={kind} label={kind}>{kind}</Select.Item>
											{/each}
										</Select.Group>
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Control>
						<FieldErrors class="mt-1 text-xs text-red-500" />
					</Field>

					<!-- Unit Step -->
					<Field form={superFormInstance} name="unit_step">
						<Control>
							{#snippet children({ props })}
								<Label>Unit Step</Label>
								<Input
									type="text"
									placeholder="Enter unit step"
									class={`w-full ${$errors.unit_step ? 'border-red-500' : ''}`}
									{...props}
								/>
							{/snippet}
						</Control>
						<FieldErrors class="mt-1 text-xs text-red-500" />
					</Field>

					<!-- Semantic Order -->
					<Field form={superFormInstance} name="semantic_order">
						<Control>
							{#snippet children({ props })}
								<Label>Semantic Order</Label>
								<Input
									type="number"
									placeholder="Enter display order"
									class={`w-full ${$errors.semantic_order ? 'border-red-500' : ''}`}
									{...props}
								/>
							{/snippet}
						</Control>
						<FieldErrors class="mt-1 text-xs text-red-500" />
					</Field>

					<!-- Color -->
					<Field form={superFormInstance} name="color_primary_semantic">
						<Control>
							{#snippet children({ props })}
								<Label>Color</Label>
								<Select.Root
									type="single"
									value={selectedColor}
									name={props.name}
									onValueChange={handleColorChange}
								>
									<Select.Trigger
										class={`w-full ${$errors.color_primary_semantic ? 'border-red-500' : ''}`}
										{...props}
									>
										{selectedColor || 'Select color'}
									</Select.Trigger>
									<Select.Content>
										<Select.Group>
											<Select.GroupHeading>Colors</Select.GroupHeading>
											<Select.Item value="" label="None">None</Select.Item>
											{#each colorOptions as color}
												<Select.Item value={color.value} label={color.label}>
													<div class="flex items-center gap-2">
														<div
															class="h-4 w-4 rounded-full"
															style={`background-color: ${color.hex}`}
														></div>
														{color.label}
													</div>
												</Select.Item>
											{/each}
										</Select.Group>
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Control>
						<FieldErrors class="mt-1 text-xs text-red-500" />
					</Field>

					<!-- Color Hex -->
					<Field form={superFormInstance} name="color_primary_hex">
						<Control>
							{#snippet children({ props })}
								<Label>Color Hex</Label>
								<Input
									type="text"
									placeholder="#RRGGBB"
									class={`w-full ${$errors.color_primary_hex ? 'border-red-500' : ''}`}
									{...props}
								/>
							{/snippet}
						</Control>
						<FieldErrors class="mt-1 text-xs text-red-500" />
					</Field>
				</div>

				<!-- Cancellation Policy Section -->
				<div class="space-y-6">
					<h3 class="border-b pb-2 text-lg font-medium">Cancellation Policy</h3>

					<!-- Relative to field -->
					<Field form={superFormInstance} name="cancel_at_relative_to">
						<Control>
							{#snippet children({ props })}
								<Label>Relative to</Label>
								<Select.Root
									type="single"
									value={selectedRelativeTo}
									name={props.name}
									onValueChange={handleRelativeToChange}
								>
									<Select.Trigger
										class={`w-full ${$errors.cancel_at_relative_to ? 'border-red-500' : ''}`}
										{...props}
									>
										{selectedRelativeTo
											? relativeToOptions.find((o) => o.id === selectedRelativeTo)?.name
											: 'Select reference point'}
									</Select.Trigger>
									<Select.Content>
										<Select.Group>
											<Select.GroupHeading>Reference Point</Select.GroupHeading>
											<Select.Item value="" label="None">None</Select.Item>
											{#each relativeToOptions as option}
												<Select.Item value={option.id} label={option.name}
													>{option.name}</Select.Item
												>
											{/each}
										</Select.Group>
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Control>
						<FieldErrors class="mt-1 text-xs text-red-500" />
					</Field>

					<!-- Cancellation policy fields in single column -->
					<div class="space-y-6">
						<!-- Far Cancellation -->
						<Field form={superFormInstance} name="cancel_at_far_minute">
							<Control>
								{#snippet children({ props })}
									<Label>Far Cancellation (minutes)</Label>
									<Input
										type="number"
										placeholder="e.g. 1440 (24 hours)"
										class={`w-full ${$errors.cancel_at_far_minute ? 'border-red-500' : ''}`}
										{...props}
									/>
								{/snippet}
							</Control>
							<FieldErrors class="mt-1 text-xs text-red-500" />
						</Field>

						<!-- Far Return Ratio -->
						<Field form={superFormInstance} name="cancel_at_far_return_ratio">
							<Control>
								{#snippet children({ props })}
									<Label>Far Return Ratio</Label>
									<Input
										type="text"
										placeholder="e.g. 1 (full refund)"
										class={`w-full ${$errors.cancel_at_far_return_ratio ? 'border-red-500' : ''}`}
										{...props}
									/>
								{/snippet}
							</Control>
							<FieldErrors class="mt-1 text-xs text-red-500" />
						</Field>

						<!-- Near Cancellation -->
						<Field form={superFormInstance} name="cancel_at_near_minute">
							<Control>
								{#snippet children({ props })}
									<Label>Near Cancellation (minutes)</Label>
									<Input
										type="number"
										placeholder="e.g. 180 (3 hours)"
										class={`w-full ${$errors.cancel_at_near_minute ? 'border-red-500' : ''}`}
										{...props}
									/>
								{/snippet}
							</Control>
							<FieldErrors class="mt-1 text-xs text-red-500" />
						</Field>

						<!-- Near Return Ratio -->
						<Field form={superFormInstance} name="cancel_at_near_return_ratio">
							<Control>
								{#snippet children({ props })}
									<Label>Near Return Ratio</Label>
									<Input
										type="text"
										placeholder="e.g. 0.5 (half refund)"
										class={`w-full ${$errors.cancel_at_near_return_ratio ? 'border-red-500' : ''}`}
										{...props}
									/>
								{/snippet}
							</Control>
							<FieldErrors class="mt-1 text-xs text-red-500" />
						</Field>
					</div>

					<!-- Return Step field -->
					<Field form={superFormInstance} name="cancel_at_return_step">
						<Control>
							{#snippet children({ props })}
								<Label>Return Step</Label>
								<Input
									type="text"
									placeholder="e.g. 0.5 or 0.25"
									class={`w-full ${$errors.cancel_at_return_step ? 'border-red-500' : ''}`}
									{...props}
								/>
							{/snippet}
						</Control>
						<FieldErrors class="mt-1 text-xs text-red-500" />
					</Field>
				</div>

				<div class="flex justify-end gap-4 border-t pt-4">
					<Button variant="outline" type="button" onclick={goBack}>Cancel</Button>
					<Button type="submit" disabled={$submitting}>
						{$submitting ? 'Saving...' : 'Save Price'}
					</Button>
				</div>
			</form>
		</div>
	</div>
{/snippet}

<PageContainer
	title={price ? `Edit Price: ${getLocalizedText(price.title as Json)}` : 'Create New Price'}
	description={price
		? `Editing price created at ${new Date(price.created_at).toLocaleDateString()}`
		: 'Create a new price entry'}
	{actions}
	{content}
/>
