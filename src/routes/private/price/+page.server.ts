import type { Actions, ServerLoad } from '@sveltejs/kit';
import type { Database } from '$lib/supabase/database.types';
import { schema } from './schema';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { fail, error } from '@sveltejs/kit';
import type { z } from 'zod';

// Define type for price input including DB fields
type PriceInput = z.infer<typeof schema> & {
	id?: string;
	brand_id: string;
	created_at?: string;
	updated_at: string;
	creator_id?: string;
};

export const load = (async ({ depends, locals: { supabase, brand }, url }) => {
	depends('supabase:db:prices');

	// Check for success message from redirects
	const success = url.searchParams.get('success');
	const successMessage = success
		? { type: 'success', text: `Price ${success} successfully!` }
		: null;

	const { data: prices, error: priceError } = await supabase
		.from('price')
		.select('*')
		.eq('brand_id', brand.id)
		.order('semantic_order', { ascending: true });

	if (priceError) {
		console.error('Error loading prices:', priceError);
		return { prices: [], successMessage };
	}

	return { prices, successMessage };
}) satisfies ServerLoad;

export const actions = {
	upsert: async ({ request, locals: { supabase, brand, user }, url }) => {
		// Bail early if no user
		if (!user) {
			throw error(401, 'Unauthorized: You must be logged in');
		}

		const form = await superValidate(request, zod(schema));
		const pid = url.searchParams.get('pid');

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			const isUpdate = !!pid;
			const now = new Date().toISOString();

			const record: PriceInput = {
				...form.data,
				brand_id: brand.id,
				updated_at: now
			};

			// For new records, add creation metadata
			if (!isUpdate) {
				record.id = crypto.randomUUID();
				record.created_at = now;
				record.creator_id = user.id;
			} else {
				record.id = pid; // Ensure ID is included for updates
			}

			const { error: upsertError } = await supabase.from('price').upsert(record).select();

			if (upsertError) {
				console.error('Error upserting price:', upsertError);
				return fail(500, {
					form,
					message: {
						type: 'error',
						text: `Failed to ${isUpdate ? 'update' : 'create'} price: ${upsertError.message}`
					}
				});
			}

			return {
				form,
				message: {
					type: 'success',
					text: `Price ${isUpdate ? 'updated' : 'created'} successfully!`
				}
			};
		} catch (error) {
			console.error('Unexpected error in upsert action:', error);
			return fail(500, {
				form,
				message: {
					type: 'error',
					text: 'An unexpected error occurred while saving the price. Please try again.'
				}
			});
		}
	}
} satisfies Actions;
