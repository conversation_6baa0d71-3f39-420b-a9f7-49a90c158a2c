import { z } from 'zod';

const requiredLocalizedTextSchema = z.object({
	en: z.string().min(1, 'Please enter a title in English'),
	zh: z.string().optional(),
	ja: z.string().optional(),
	ko: z.string().optional()
});

const optionalLocalizedTextSchema = z.object({
	en: z.string().optional(),
	zh: z.string().optional(),
	ja: z.string().optional(),
	ko: z.string().optional()
});

export const schema = z.object({
	title: requiredLocalizedTextSchema,
	title_short: optionalLocalizedTextSchema,
	product_classification: requiredLocalizedTextSchema,
	payment_kind: z.string().min(1, 'Please select a payment kind'),
	unit_kind: z.string().min(1, 'Please select a unit kind'),
	unit_step: z.string().min(1, 'Please enter a unit step'),
	state: z.string().default('private'),
	semantic_order: z.coerce.number().min(0).default(0),
	color_primary_semantic: z.string().optional().nullable(),
	color_primary_hex: z.string().optional().nullable(),
	cancel_at_relative_to: z.string().optional().nullable(),
	cancel_at_far_minute: z.coerce.number().optional().nullable(),
	cancel_at_far_return_ratio: z.string().optional().nullable(),
	cancel_at_near_minute: z.coerce.number().optional().nullable(),
	cancel_at_near_return_ratio: z.string().optional().nullable(),
	cancel_at_return_step: z.string().optional().nullable()
});
