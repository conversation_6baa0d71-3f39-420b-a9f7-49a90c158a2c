import { error, fail, redirect } from '@sveltejs/kit';
import { createHash } from 'crypto';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ url, locals: { supabase, safeGetSession } }) => {
	const { user } = await safeGetSession();
	if (!user) redirect(303, '/login');

	const termId = url.searchParams.get('term_id');
	if (!termId) {
		redirect(303, '/private/term-group');
	}

	// Get the term details
	const { data: term, error: termError } = await supabase
		.from('term')
		.select(
			`
			*,
			brand!inner(id, name_full, owner_profile_id),
			term_kind!inner(title, subtitle),
			term_version(id, version_number)
		`
		)
		.eq('id', termId)
		.single();

	if (termError || !term) {
		error(404, 'Document not found');
	}

	// Check if user owns this brand
	if (term.brand.owner_profile_id !== user.id) {
		error(403, 'You do not have permission to create versions for this document');
	}

	// Calculate next version number
	const maxVersion = Math.max(0, ...term.term_version.map((v: any) => v.version_number));
	const nextVersionNumber = maxVersion + 1;

	return {
		term,
		nextVersionNumber
	};
};

export const actions = {
	default: async ({ request, url, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		const formData = await request.formData();
		const termId = url.searchParams.get('term_id');
		const content = {
			en: formData.get('content[en]') as string,
			zh: formData.get('content[zh]') as string,
			ja: formData.get('content[ja]') as string,
			ko: formData.get('content[ko]') as string
		};
		const publishNow = formData.get('publish_now') === 'on';
		const setAsCurrent = formData.get('set_as_current') === 'on';

		if (!termId || !content.en) {
			return fail(400, {
				error: 'English content is required'
			});
		}

		// Generate content hash from all content
		const contentString = JSON.stringify(content);
		const contentHash = createHash('sha256').update(contentString).digest('hex');

		// Get IP address and user agent
		const ipAddress =
			request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
		const userAgent = request.headers.get('user-agent') || 'unknown';

		// Start a transaction to ensure consistency
		const { data: version, error: versionError } = await supabase
			.from('term_version')
			.insert({
				term_id: termId,
				content,
				content_hash: contentHash,
				published_at: publishNow ? new Date().toISOString() : null,
				is_current: false, // Will be set later if needed
				creator_id: user.id,
				created_ip: ipAddress,
				created_user_agent: userAgent
			})
			.select()
			.single();

		if (versionError) {
			console.error('Error creating version:', versionError);
			return fail(500, {
				error: versionError.message || 'Failed to create version'
			});
		}

		// If set as current is requested and version is published
		if (setAsCurrent && publishNow && version) {
			// First, set all versions for this term to not current
			await supabase.from('term_version').update({ is_current: false }).eq('term_id', termId);

			// Then set the new version as current
			await supabase.from('term_version').update({ is_current: true }).eq('id', version.id);
		}

		// Redirect to the term page
		redirect(303, `/private/term/${termId}`);
	}
} satisfies Actions;
