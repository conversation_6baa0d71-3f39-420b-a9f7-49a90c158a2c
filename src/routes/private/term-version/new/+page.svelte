<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import { Label } from '$lib/components/ui/label';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { ArrowLeft, Info, FileText } from '@lucide/svelte';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';
	import { getLocalizedText } from '$lib/utils/localization';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { z } from 'zod';
	import type { PageData, ActionData } from './$types';

	let { data, form: actionForm }: { data: PageData; form: ActionData } = $props();

	// Create schema
	const schema = z.object({
		content: z.object({
			en: z.string().min(1, 'English content is required'),
			zh: z.string().optional(),
			ja: z.string().optional(),
			ko: z.string().optional()
		})
	});

	const form = superForm(
		{
			content: { en: '', zh: '', ja: '', ko: '' }
		},
		{
			validators: zodClient(schema),
			dataType: 'json'
		}
	);

	const { form: formData } = form;

	// Publishing options
	let publishNow = $state(true);
	let setAsCurrent = $state(true);

	// Show error toast
	$effect(() => {
		if (actionForm?.error) {
			toast.error(actionForm.error);
		}
	});

	// Template content based on term kind
	const getTemplateContent = () => {
		// In a real app, you'd have different templates for each term kind
		// For now, we'll provide a basic template
		return {
			en: `# ${getLocalizedText(data.term.title, 'en')}

## Effective Date: [DATE]

### 1. Acceptance of Terms
By participating in our programs, you acknowledge that you have read, understood, and agree to be bound by these terms.

### 2. [Section Title]
[Your content here]

### 3. [Section Title]
[Your content here]

### 4. Contact Information
If you have any questions about these terms, please contact us at [CONTACT INFO].

---
*Last updated: [DATE]*`,
			zh: '',
			ja: '',
			ko: ''
		};
	};

	// Pre-fill with template if this is the first version
	if (data.term.term_version.length === 0) {
		$formData.content = getTemplateContent();
	}
</script>

{#snippet content()}
	<div class="mx-auto max-w-4xl space-y-6">
		<!-- Header -->
		<div class="flex items-center gap-4">
			<Button href="/private/term/{data.term.id}" variant="ghost" size="icon">
				<ArrowLeft class="h-4 w-4" />
			</Button>
			<div>
				<h1 class="text-2xl font-semibold tracking-tight">Create New Version</h1>
				<p class="text-sm text-muted-foreground">
					{getLocalizedText(data.term.title, 'en')} • Version {data.nextVersionNumber}
				</p>
			</div>
		</div>

		<!-- Form -->
		<form method="POST" use:enhance>
			<div class="space-y-6">
				<!-- Document Info Card -->
				<Card.Root>
					<Card.Header>
						<Card.Title>Document Information</Card.Title>
					</Card.Header>
					<Card.Content class="space-y-4">
						<div class="grid gap-4 sm:grid-cols-2">
							<div>
								<p class="text-sm font-medium text-muted-foreground">Document Type</p>
								<p class="mt-1">{getLocalizedText(data.term.term_kind.title, 'en')}</p>
							</div>
							<div>
								<p class="text-sm font-medium text-muted-foreground">Brand</p>
								<p class="mt-1">{getLocalizedText(data.term.brand.name_full, 'en')}</p>
							</div>
							<div>
								<p class="text-sm font-medium text-muted-foreground">Version Number</p>
								<p class="mt-1 text-lg font-semibold">{data.nextVersionNumber}</p>
							</div>
						</div>
					</Card.Content>
				</Card.Root>

				<!-- Content Editor -->
				<Card.Root>
					<Card.Header>
						<Card.Title>Version Content</Card.Title>
						<Card.Description>
							Write the full legal text for this document. Use markdown for formatting.
						</Card.Description>
					</Card.Header>
					<Card.Content class="space-y-4">
						<LocalizedTextControl
							label="Document Content"
							name="content"
							{form}
							required
							multiline
							placeholder="Enter the full legal text..."
						/>

						<div class="rounded-lg bg-muted/50 p-4">
							<div class="flex gap-2">
								<Info class="mt-0.5 h-4 w-4 text-muted-foreground" />
								<div class="space-y-2 text-sm text-muted-foreground">
									<p class="font-medium">Formatting Tips:</p>
									<ul class="ml-4 list-disc space-y-1">
										<li>Use # for main title, ## for sections, ### for subsections</li>
										<li>Use **bold** for emphasis and *italic* for definitions</li>
										<li>Use numbered lists (1. 2. 3.) for sequential items</li>
										<li>Use bullet points (- or *) for non-sequential lists</li>
										<li>Leave blank lines between paragraphs for better readability</li>
									</ul>
								</div>
							</div>
						</div>
					</Card.Content>
				</Card.Root>

				<!-- Publishing Options -->
				<Card.Root>
					<Card.Header>
						<Card.Title>Publishing Options</Card.Title>
						<Card.Description>Choose how to handle this version after creation</Card.Description>
					</Card.Header>
					<Card.Content class="space-y-4">
						<div class="space-y-3">
							<div class="flex items-center gap-3">
								<Checkbox id="publish_now" name="publish_now" bind:checked={publishNow} />
								<div class="grid gap-0.5">
									<Label for="publish_now" class="cursor-pointer font-normal">
										Publish immediately
									</Label>
									<p class="text-xs text-muted-foreground">
										Make this version available for use right away
									</p>
								</div>
							</div>

							{#if publishNow}
								<div class="ml-7 flex items-center gap-3">
									<Checkbox id="set_as_current" name="set_as_current" bind:checked={setAsCurrent} />
									<div class="grid gap-0.5">
										<Label for="set_as_current" class="cursor-pointer font-normal">
											Set as current version
										</Label>
										<p class="text-xs text-muted-foreground">
											New customers will need to agree to this version
										</p>
									</div>
								</div>
							{/if}
						</div>

						{#if !publishNow}
							<div class="flex gap-2 rounded-lg bg-muted/50 p-3 text-sm">
								<Info class="mt-0.5 h-4 w-4 text-muted-foreground" />
								<p class="text-muted-foreground">
									You can publish this version later from the document details page
								</p>
							</div>
						{/if}
					</Card.Content>
				</Card.Root>

				<!-- Form Actions -->
				<div class="flex justify-end gap-3">
					<Button href="/private/term/{data.term.id}" variant="outline">Cancel</Button>
					<Button type="submit">
						<FileText class="mr-2 h-4 w-4" />
						Create Version
					</Button>
				</div>
			</div>
		</form>
	</div>
{/snippet}

<PageContainer
	title={`Create Version v${data.nextVersionNumber}`}
	description={getLocalizedText(data.term.title, 'en')}
	{content}
/>
