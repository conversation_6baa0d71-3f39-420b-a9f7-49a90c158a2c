import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ params, locals: { supabase, safeGetSession } }) => {
	const { user } = await safeGetSession();
	if (!user) redirect(303, '/login');

	// Get the version with full details
	const { data: version, error: versionError } = await supabase
		.from('term_version')
		.select(
			`
			*,
			creator:creator_id(email),
			term!inner(
				*,
				brand!inner(id, name_full, owner_profile_id),
				term_kind!inner(title, subtitle)
			)
		`
		)
		.eq('id', params.id)
		.single();

	if (versionError || !version) {
		console.error('Error fetching version:', versionError);
		error(404, 'Version not found');
	}

	// Check if user owns this brand
	if (version.term.brand.owner_profile_id !== user.id) {
		error(403, 'You do not have permission to view this version');
	}

	return {
		version
	};
};

export const actions = {
	publish: async ({ params, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		// Publish the version
		const { error } = await supabase
			.from('term_version')
			.update({ published_at: new Date().toISOString() })
			.eq('id', params.id)
			.is('published_at', null);

		if (error) {
			return {
				error: error.message
			};
		}

		return { success: true };
	},

	setCurrent: async ({ params, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		// Get the term ID for this version
		const { data: version } = await supabase
			.from('term_version')
			.select('term_id')
			.eq('id', params.id)
			.single();

		if (!version) {
			return {
				error: 'Version not found'
			};
		}

		// First, set all versions for this term to not current
		await supabase
			.from('term_version')
			.update({ is_current: false })
			.eq('term_id', version.term_id);

		// Then set this version as current
		const { error } = await supabase
			.from('term_version')
			.update({ is_current: true })
			.eq('id', params.id);

		if (error) {
			return {
				error: error.message
			};
		}

		return { success: true };
	},

	deleteVersion: async ({ params, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		// Get the term ID before deleting
		const { data: version } = await supabase
			.from('term_version')
			.select('term_id, is_current')
			.eq('id', params.id)
			.single();

		if (!version) {
			return {
				error: 'Version not found'
			};
		}

		// Don't allow deleting the current version
		if (version.is_current) {
			return {
				error: 'Cannot delete the current version. Set another version as current first.'
			};
		}

		// Delete the version
		const { error } = await supabase.from('term_version').delete().eq('id', params.id);

		if (error) {
			return {
				error: error.message
			};
		}

		// Redirect to the term page
		redirect(303, `/private/term/${version.term_id}`);
	}
} satisfies Actions;
