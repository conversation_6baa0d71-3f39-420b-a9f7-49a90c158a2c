<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import * as AlertDialog from '$lib/components/ui/alert-dialog';
	import * as Tabs from '$lib/components/ui/tabs';
	import { Badge } from '$lib/components/ui/badge';
	import {
		ArrowLeft,
		Trash2,
		CheckCircle2,
		Clock,
		Globe,
		FileCheck,
		Copy,
		Download
	} from '@lucide/svelte';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';
	import { getLocalizedText } from '$lib/utils/localization';
	import { invalidateAll } from '$app/navigation';
	import type { PageData, ActionData } from './$types';
	import { marked } from 'marked';
	import { buttonVariants } from '$lib/components/ui/button';

	let { data, form: actionForm }: { data: PageData; form: ActionData } = $props();

	// States
	let showDeleteDialog = $state(false);

	// Show toast for action results
	$effect(() => {
		if (actionForm?.error) {
			toast.error(actionForm.error);
		} else if (actionForm?.success) {
			toast.success('Changes saved successfully');
			invalidateAll();
		}
	});

	// Format date
	function formatDate(date: string) {
		return new Date(date).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// Copy content to clipboard
	async function copyContent(content: string) {
		try {
			await navigator.clipboard.writeText(content);
			toast.success('Content copied to clipboard');
		} catch (err) {
			toast.error('Failed to copy content');
		}
	}

	// Download content as file
	function downloadContent(content: string, lang: string) {
		const blob = new Blob([content], { type: 'text/markdown' });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = `${getLocalizedText(data.version.term.title, 'en')}_v${data.version.version_number}_${lang}.md`;
		a.click();
		URL.revokeObjectURL(url);
	}

	// Render markdown
	function renderMarkdown(content: string) {
		return marked(content);
	}
</script>

{#snippet content()}
	<div class="space-y-6">
		<!-- Header -->
		<div class="flex items-center justify-between">
			<div class="flex items-center gap-4">
				<Button href="/private/term/{data.version.term_id}" variant="ghost" size="icon">
					<ArrowLeft class="h-4 w-4" />
				</Button>
				<div>
					<h1 class="text-2xl font-semibold tracking-tight">
						Version {data.version.version_number}
					</h1>
					<p class="text-sm text-muted-foreground">
						{getLocalizedText(data.version.term.title, 'en')} •
						{getLocalizedText(data.version.term.brand.name_full, 'en')}
					</p>
				</div>
			</div>
			<div class="flex items-center gap-2">
				{#if data.version.is_current}
					<Badge class="gap-1">
						<CheckCircle2 class="h-3 w-3" />
						Current Version
					</Badge>
				{/if}
				{#if !data.version.published_at}
					<Badge variant="secondary" class="gap-1">
						<Clock class="h-3 w-3" />
						Draft
					</Badge>
				{/if}
			</div>
		</div>

		<!-- Version Info -->
		<Card.Root>
			<Card.Header>
				<Card.Title>Version Information</Card.Title>
			</Card.Header>
			<Card.Content>
				<div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
					<div>
						<p class="text-sm font-medium text-muted-foreground">Created</p>
						<p class="mt-1 text-sm">{formatDate(data.version.created_at)}</p>
						<p class="text-sm text-muted-foreground">by {data.version.creator?.email}</p>
					</div>
					{#if data.version.published_at}
						<div>
							<p class="text-sm font-medium text-muted-foreground">Published</p>
							<p class="mt-1 text-sm">{formatDate(data.version.published_at)}</p>
						</div>
					{/if}
					<div>
						<p class="text-sm font-medium text-muted-foreground">Content Hash</p>
						<p class="mt-1 font-mono text-xs">{data.version.content_hash}</p>
					</div>
					<div>
						<p class="text-sm font-medium text-muted-foreground">Document Type</p>
						<p class="mt-1 text-sm">{getLocalizedText(data.version.term.term_kind.title, 'en')}</p>
					</div>
					{#if data.version.created_ip}
						<div>
							<p class="text-sm font-medium text-muted-foreground">Created From IP</p>
							<p class="mt-1 font-mono text-xs">{data.version.created_ip}</p>
						</div>
					{/if}
				</div>
			</Card.Content>
			{#if !data.version.is_current || !data.version.published_at}
				<Card.Footer class="flex gap-2">
					{#if !data.version.published_at}
						<form method="POST" action="?/publish" use:enhance>
							<Button type="submit" variant="outline">
								<FileCheck class="mr-2 h-4 w-4" />
								Publish Version
							</Button>
						</form>
					{/if}
					{#if data.version.published_at && !data.version.is_current}
						<form method="POST" action="?/setCurrent" use:enhance>
							<Button type="submit">
								<CheckCircle2 class="mr-2 h-4 w-4" />
								Set as Current
							</Button>
						</form>
					{/if}
				</Card.Footer>
			{/if}
		</Card.Root>

		<!-- Content -->
		<Card.Root>
			<Card.Header>
				<div class="flex items-center justify-between">
					<Card.Title>Version Content</Card.Title>
					<Tabs.Root value="en" class="w-auto">
						<Tabs.List>
							<Tabs.Trigger value="en">English</Tabs.Trigger>
							<Tabs.Trigger value="zh">中文</Tabs.Trigger>
							<Tabs.Trigger value="ja">日本語</Tabs.Trigger>
							<Tabs.Trigger value="ko">한국어</Tabs.Trigger>
						</Tabs.List>
					</Tabs.Root>
				</div>
			</Card.Header>
			<Card.Content>
				<Tabs.Root value="en">
					{#each ['en', 'zh', 'ja', 'ko'] as lang}
						<Tabs.Content value={lang} class="space-y-4">
							{#if data.version.content[lang]}
								<div class="flex justify-end gap-2">
									<Button
										variant="outline"
										size="sm"
										onclick={() => copyContent(data.version.content[lang])}
									>
										<Copy class="mr-2 h-4 w-4" />
										Copy
									</Button>
									<Button
										variant="outline"
										size="sm"
										onclick={() => downloadContent(data.version.content[lang], lang)}
									>
										<Download class="mr-2 h-4 w-4" />
										Download
									</Button>
								</div>
								<div class="prose prose-sm max-w-none rounded-lg border bg-muted/20 p-6">
									{@html renderMarkdown(data.version.content[lang])}
								</div>
							{:else}
								<div class="rounded-lg border border-dashed p-8 text-center">
									<Globe class="mx-auto mb-2 h-8 w-8 text-muted-foreground" />
									<p class="text-sm text-muted-foreground">No content available in this language</p>
								</div>
							{/if}
						</Tabs.Content>
					{/each}
				</Tabs.Root>
			</Card.Content>
		</Card.Root>

		<!-- Actions -->
		{#if !data.version.is_current}
			<Card.Root>
				<Card.Header>
					<Card.Title>Actions</Card.Title>
				</Card.Header>
				<Card.Content>
					<AlertDialog.Root bind:open={showDeleteDialog}>
						<AlertDialog.Trigger class={buttonVariants({ variant: 'destructive' })}>
							<Trash2 class="mr-2 h-4 w-4" />
							Delete Version
						</AlertDialog.Trigger>
						<AlertDialog.Content>
							<AlertDialog.Header>
								<AlertDialog.Title>Delete Version?</AlertDialog.Title>
								<AlertDialog.Description>
									This will permanently delete version {data.version.version_number}. This action
									cannot be undone.
								</AlertDialog.Description>
							</AlertDialog.Header>
							<AlertDialog.Footer>
								<AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
								<form method="POST" action="?/deleteVersion" use:enhance>
									<Button type="submit" variant="destructive">Delete Version</Button>
								</form>
							</AlertDialog.Footer>
						</AlertDialog.Content>
					</AlertDialog.Root>
				</Card.Content>
			</Card.Root>
		{/if}
	</div>
{/snippet}

<PageContainer
	title={`Version ${data.version.version_number}`}
	description={getLocalizedText(data.version.term.title, 'en')}
	{content}
/>
