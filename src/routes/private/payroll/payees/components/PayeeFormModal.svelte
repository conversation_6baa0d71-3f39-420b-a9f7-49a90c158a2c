<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Label } from '$lib/components/ui/label';
	import { Input } from '$lib/components/ui/input';
	import * as Select from '$lib/components/ui/select/index.js';
	import { Textarea } from '$lib/components/ui/textarea';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { toast } from 'svelte-sonner';
	import ProfilePicker from '$lib/components/shared/ProfilePicker.svelte';
	import ResponsiveModal from '$lib/components/shared/ResponsiveModal.svelte';
	import { Separator } from '$lib/components/ui/separator';
	import type { Tables } from '$lib/supabase/database.types';

	type PayrollFormula = Tables<'payroll_formula'>;

	// Custom interface for formula assignment with joined payroll_formula data
	interface FormulaAssignmentWithFormula extends Tables<'payroll_formula_assignment'> {
		payroll_formula: PayrollFormula;
	}

	interface Props {
		open: boolean;
		onOpenChange: (open: boolean) => void;
		currencies: Array<{ id: string; [key: string]: any }>;
		groupedFormulas: {
			event: PayrollFormula[];
			product: PayrollFormula[];
			profile: PayrollFormula[];
		};
		payrollProfileId?: string;
		title?: string;
		level?: string;
		currencyCode?: string;
		accountantNotes?: string;
		formulaAssignments?: FormulaAssignmentWithFormula[];
	}

	let {
		open = $bindable(),
		onOpenChange,
		currencies = [],
		groupedFormulas,
		payrollProfileId = '',
		title = '',
		level = '',
		currencyCode = 'USD',
		accountantNotes = '',
		formulaAssignments = []
	}: Props = $props();

	// Determine if we're in edit mode based on the presence of payrollProfileId
	const isEditMode = $derived(!!payrollProfileId);

	let submitting = $state(false);
	let selectedProfileId = $state('');
	let selectedCurrencyCode = $state(currencyCode);
	let selectedEventFormulaId = $state('');
	let selectedProductFormulaId = $state('');
	let selectedProfileFormulaId = $state('');

	// Initialize selected formula IDs based on existing assignments
	$effect(() => {
		if (formulaAssignments?.length) {
			for (const assignment of formulaAssignments) {
				const formula = assignment.payroll_formula;
				if (!formula) continue;

				if (formula.earning_source_kind === 'event') {
					selectedEventFormulaId = formula.id;
				} else if (formula.earning_source_kind === 'product') {
					selectedProductFormulaId = formula.id;
				} else if (formula.earning_source_kind === 'profile') {
					selectedProfileFormulaId = formula.id;
				}
			}
		}
	});

	// Derived trigger content values
	let currencyTriggerContent = $derived(
		selectedCurrencyCode ? selectedCurrencyCode : 'Select currency'
	);
	let eventFormulaTriggerContent = $derived(
		selectedEventFormulaId
			? groupedFormulas.event.find((f) => f.id === selectedEventFormulaId)?.name || 'Select formula'
			: 'Select formula'
	);
	let productFormulaTriggerContent = $derived(
		selectedProductFormulaId
			? groupedFormulas.product.find((f) => f.id === selectedProductFormulaId)?.name ||
					'Select formula'
			: 'Select formula'
	);
	let profileFormulaTriggerContent = $derived(
		selectedProfileFormulaId
			? groupedFormulas.profile.find((f) => f.id === selectedProfileFormulaId)?.name ||
					'Select formula'
			: 'Select formula'
	);

	function handleSuccess(): void {
		onOpenChange(false);
		submitting = false;
		invalidateAll();
		toast.success(isEditMode ? 'Payee updated successfully' : 'Payee added successfully');
	}

	function handleError(error: string): void {
		submitting = false;
		toast.error(`Error ${isEditMode ? 'updating' : 'adding'} payee: ${error}`);
	}

	function getFormAction(): string {
		return isEditMode ? '?/updatePayee' : '?/addPayee';
	}
</script>

<ResponsiveModal
	bind:open
	title={isEditMode ? 'Edit Payee' : 'Add Payee'}
	description={isEditMode
		? 'Update payee information and formula assignments'
		: 'Add a team member to the payroll system'}
	{onOpenChange}
>
	{#snippet children()}
		<form
			method="POST"
			action={getFormAction()}
			use:enhance={({ formData }) => {
				submitting = true;
				return async ({ result }) => {
					if (result.type === 'success') {
						handleSuccess();
					} else if (result.type === 'failure' && result.data) {
						const errorMsg =
							typeof result.data.error === 'string' ? result.data.error : 'Unknown error';
						handleError(errorMsg);
					} else {
						handleError('An error occurred');
					}
				};
			}}
			class="grid gap-6"
		>
			{#if isEditMode}
				<input type="hidden" name="payrollProfileId" value={payrollProfileId} />
			{:else}
				<div class="grid gap-2">
					<Label for="profileId">Team Member</Label>
					<input type="hidden" name="profileId" value={selectedProfileId} />
					<ProfilePicker placeholder="Select team member" bind:selectedProfileId width="w-full" />
				</div>
			{/if}

			<div class="grid gap-2">
				<Label for="title">Title (Optional)</Label>
				<Input
					id="title"
					name="title"
					bind:value={title}
					placeholder="e.g. Instructor, Coach, Assistant"
				/>
			</div>

			<div class="grid gap-2">
				<Label for="level">Level (Optional)</Label>
				<Input
					id="level"
					name="level"
					bind:value={level}
					placeholder="e.g. Senior, Junior, Level 1"
				/>
			</div>

			<div class="grid gap-2">
				<Label for="currencyCode">Currency</Label>
				<input type="hidden" name="currencyCode" value={selectedCurrencyCode} />
				<Select.Root type="single" bind:value={selectedCurrencyCode}>
					<Select.Trigger>
						{currencyTriggerContent}
					</Select.Trigger>
					<Select.Content>
						<Select.Group>
							{#each currencies as currency}
								<Select.Item value={currency.id} label={currency.id}>
									{currency.id}
								</Select.Item>
							{/each}
						</Select.Group>
					</Select.Content>
				</Select.Root>
			</div>

			<div class="grid gap-2">
				<Label for="accountantNotes">Notes (Optional)</Label>
				<Textarea
					id="accountantNotes"
					name="accountantNotes"
					bind:value={accountantNotes}
					placeholder="Any internal notes for accounting purposes"
					rows={3}
				/>
			</div>

			<Separator />

			<div>
				<h3 class="mb-4 text-lg font-medium">Formula Assignments</h3>
				<div class="space-y-4">
					<!-- Event Formula -->
					<div class="grid gap-2">
						<Label for="eventFormulaId">Event Formula (Optional)</Label>
						<input type="hidden" name="eventFormulaId" value={selectedEventFormulaId} />
						<Select.Root type="single" bind:value={selectedEventFormulaId}>
							<Select.Trigger class="w-full">
								{eventFormulaTriggerContent}
							</Select.Trigger>
							<Select.Content>
								<Select.Group>
									<Select.Item value="" label="None">None</Select.Item>
									{#each groupedFormulas.event as formula}
										<Select.Item value={formula.id} label={formula.name}>
											{formula.name}
										</Select.Item>
									{/each}
								</Select.Group>
							</Select.Content>
						</Select.Root>
						<p class="text-xs text-muted-foreground">
							Used to calculate payment for teaching events
						</p>
					</div>

					<!-- Product Formula -->
					<div class="grid gap-2">
						<Label for="productFormulaId">Product Formula (Optional)</Label>
						<input type="hidden" name="productFormulaId" value={selectedProductFormulaId} />
						<Select.Root type="single" bind:value={selectedProductFormulaId}>
							<Select.Trigger class="w-full">
								{productFormulaTriggerContent}
							</Select.Trigger>
							<Select.Content>
								<Select.Group>
									<Select.Item value="" label="None">None</Select.Item>
									{#each groupedFormulas.product as formula}
										<Select.Item value={formula.id} label={formula.name}>
											{formula.name}
										</Select.Item>
									{/each}
								</Select.Group>
							</Select.Content>
						</Select.Root>
						<p class="text-xs text-muted-foreground">
							Used to calculate commissions for product sales
						</p>
					</div>

					<!-- Profile Formula -->
					<div class="grid gap-2">
						<Label for="profileFormulaId">Profile Formula (Optional)</Label>
						<input type="hidden" name="profileFormulaId" value={selectedProfileFormulaId} />
						<Select.Root type="single" bind:value={selectedProfileFormulaId}>
							<Select.Trigger class="w-full">
								{profileFormulaTriggerContent}
							</Select.Trigger>
							<Select.Content>
								<Select.Group>
									<Select.Item value="" label="None">None</Select.Item>
									{#each groupedFormulas.profile as formula}
										<Select.Item value={formula.id} label={formula.name}>
											{formula.name}
										</Select.Item>
									{/each}
								</Select.Group>
							</Select.Content>
						</Select.Root>
						<p class="text-xs text-muted-foreground">
							Used to calculate periodic or performance-based payouts
						</p>
					</div>
				</div>
			</div>

			<Button type="submit" disabled={submitting} class="w-full">
				{submitting
					? isEditMode
						? 'Updating...'
						: 'Adding...'
					: isEditMode
						? 'Update Payee'
						: 'Add Payee'}
			</Button>
		</form>
	{/snippet}
</ResponsiveModal>
