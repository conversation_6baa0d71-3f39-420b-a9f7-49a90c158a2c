<script lang="ts">
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar';
	import { Badge } from '$lib/components/ui/badge';
	import { Button } from '$lib/components/ui/button';
	import { Trash2, Pencil } from '@lucide/svelte';
	import {
		Dialog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle,
		DialogTrigger
	} from '$lib/components/ui/dialog';
	import { toast } from 'svelte-sonner';
	import { enhance } from '$app/forms';
	import type { Tables } from '$lib/supabase/database.types';
	import PayeeFormModal from './PayeeFormModal.svelte';

	type Profile = Tables<'profile'> & {
		given_name: LocalizedText;
		family_name: LocalizedText;
	};

	type Wikipage = {
		id: string;
		title: LocalizedText;
		decorating_profile_id: string;
	};

	type PayrollFormula = Tables<'payroll_formula'>;

	// Custom interface for formula assignment with joined payroll_formula data
	interface FormulaAssignmentWithFormula extends Tables<'payroll_formula_assignment'> {
		payroll_formula: PayrollFormula;
	}

	interface PayrollProfile extends Tables<'payroll_profile'> {
		profile: Profile;
		has_wikipage: boolean;
		wikipage_count: number;
		wikipages: Wikipage[];
		payroll_formula_assignments?: FormulaAssignmentWithFormula[];
	}

	interface Currency {
		id: string;
		name: string;
		symbol: string;
	}

	interface Props {
		payrollProfile: PayrollProfile;
		currencies: Currency[];
		groupedFormulas: {
			event: PayrollFormula[];
			product: PayrollFormula[];
			profile: PayrollFormula[];
		};
		hasWriteAccess: boolean;
		onRemoveSuccess: () => void;
		onRemoveError: (error: string) => void;
		onEditSuccess: () => void;
	}

	const {
		payrollProfile,
		currencies,
		groupedFormulas,
		hasWriteAccess,
		onRemoveSuccess,
		onRemoveError,
		onEditSuccess
	}: Props = $props();

	let confirmRemoveId = $state<string | null>(null);
	let editDialogOpen = $state(false);

	function getFullName(firstName: LocalizedText, lastName: LocalizedText): string {
		const first = getLocalizedText(firstName, 'en');
		const last = getLocalizedText(lastName, 'en');
		return `${first} ${last}`.trim();
	}

	function getInitials(firstName: LocalizedText, lastName: LocalizedText): string {
		const first = getLocalizedText(firstName, 'en');
		const last = getLocalizedText(lastName, 'en');
		const firstInitial = first ? first.charAt(0).toUpperCase() : '';
		const lastInitial = last ? last.charAt(0).toUpperCase() : '';
		return `${firstInitial}${lastInitial}`;
	}

	function formatDate(dateString: string | null): string {
		if (!dateString) return 'N/A';
		return new Date(dateString).toLocaleDateString();
	}
</script>

<div class="flex items-center justify-between rounded-lg border p-4">
	<div class="flex items-center space-x-4">
		<Avatar>
			<AvatarImage
				src={payrollProfile.profile.avatar_url || ''}
				alt={getFullName(payrollProfile.profile.given_name, payrollProfile.profile.family_name)}
			/>
			<AvatarFallback>
				{getInitials(payrollProfile.profile.given_name, payrollProfile.profile.family_name)}
			</AvatarFallback>
		</Avatar>
		<div>
			<p class="font-medium">
				{getFullName(payrollProfile.profile.given_name, payrollProfile.profile.family_name)}
				{#if payrollProfile.unenrolled_at}
					<span class="ml-2 text-sm text-muted-foreground">(Inactive)</span>
				{/if}
			</p>
			<p class="text-sm text-muted-foreground">@{payrollProfile.profile.username}</p>
			<p class="text-sm text-muted-foreground">
				{payrollProfile.profile.auto_user_email || 'No email'}
			</p>
			{#if payrollProfile.title || payrollProfile.level}
				<p class="text-sm">
					{[payrollProfile.title, payrollProfile.level].filter(Boolean).join(' - ')}
				</p>
			{/if}
			{#if payrollProfile.has_wikipage}
				<p class="mt-1 text-xs">
					<span class="rounded-full bg-blue-100 px-2 py-0.5 text-blue-800">
						{payrollProfile.wikipage_count} wikipage{payrollProfile.wikipage_count !== 1 ? 's' : ''}
					</span>
					<span class="ml-2 text-muted-foreground">
						{#if payrollProfile.wikipages && payrollProfile.wikipages.length > 0}
							<span class="font-medium">Titles:</span>
							{payrollProfile.wikipages
								.slice(0, 2)
								.map((w) => getLocalizedText(w.title))
								.join(', ')}
							{#if payrollProfile.wikipages.length > 2}
								<span>, ...</span>
							{/if}
						{/if}
					</span>
				</p>
			{/if}
		</div>
	</div>
	<div class="flex items-center gap-4">
		<div class="text-right">
			<p class="text-sm">
				<span class="font-medium">Enrolled:</span>
				{formatDate(payrollProfile.created_at)}
			</p>
			{#if payrollProfile.unenrolled_at}
				<p class="text-sm">
					<span class="font-medium">Unenrolled:</span>
					{formatDate(payrollProfile.unenrolled_at)}
				</p>
			{/if}
		</div>
		{#if hasWriteAccess && !payrollProfile.unenrolled_at}
			<div class="flex gap-2">
				<!-- Edit Button -->
				<Button
					variant="outline"
					size="icon"
					class="h-8 w-8"
					onclick={() => (editDialogOpen = true)}
				>
					<Pencil class="h-4 w-4" />
				</Button>

				<!-- PayeeFormModal for editing -->
				<PayeeFormModal
					open={editDialogOpen}
					onOpenChange={(open) => (editDialogOpen = open)}
					payrollProfileId={payrollProfile.id}
					title={payrollProfile.title || ''}
					level={payrollProfile.level || ''}
					currencyCode={payrollProfile.currency_code}
					accountantNotes={payrollProfile.accountant_notes || ''}
					formulaAssignments={payrollProfile.payroll_formula_assignments || []}
					{currencies}
					{groupedFormulas}
				/>

				<!-- Remove Button -->
				<Dialog
					open={confirmRemoveId === payrollProfile.id}
					onOpenChange={(open) => {
						if (!open) confirmRemoveId = null;
					}}
				>
					<DialogTrigger>
						<Button
							variant="destructive"
							size="icon"
							class="h-8 w-8"
							onclick={() => (confirmRemoveId = payrollProfile.id)}
						>
							<Trash2 class="h-4 w-4" />
						</Button>
					</DialogTrigger>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>Remove Payee</DialogTitle>
							<DialogDescription>
								Are you sure you want to remove {getFullName(
									payrollProfile.profile.given_name,
									payrollProfile.profile.family_name
								)} from the payroll system?
							</DialogDescription>
						</DialogHeader>
						<div class="py-4">
							<p class="text-sm">This action will either:</p>
							<ul class="ml-6 mt-2 list-disc text-sm">
								<li>Completely remove the payee if they have no payroll history</li>
								<li>Mark the payee as inactive if they have payroll history</li>
							</ul>
						</div>
						<DialogFooter>
							<form
								method="POST"
								action="?/removePayee"
								use:enhance={({ formData }) => {
									return async ({ result }) => {
										if (result.type === 'success') {
											onRemoveSuccess();
										} else if (result.type === 'failure' && result.data) {
											const errorMsg =
												typeof result.data.error === 'string' ? result.data.error : 'Unknown error';
											onRemoveError(errorMsg);
										} else {
											onRemoveError('An error occurred');
										}
									};
								}}
							>
								<input type="hidden" name="payrollProfileId" value={payrollProfile.id} />
								<Button variant="outline" type="button" onclick={() => (confirmRemoveId = null)}>
									Cancel
								</Button>
								<Button variant="destructive" type="submit" class="ml-2">Remove</Button>
							</form>
						</DialogFooter>
					</DialogContent>
				</Dialog>
			</div>
		{/if}
	</div>
</div>
