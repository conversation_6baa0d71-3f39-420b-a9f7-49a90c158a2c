import type { PageServerLoad, Actions } from './$types';
import type { Tables } from '$lib/supabase/database.types';
import type { LocalizedText } from '$lib/utils/localization';

type TeamMember = {
	id: string;
	given_name: LocalizedText;
	family_name: LocalizedText;
	username: string;
	avatar_url?: string | null;
	[key: string]: any;
};

type PayrollFormula = Tables<'payroll_formula'>;
type PayrollProfile = Tables<'payroll_profile'> & {
	profile: {
		id: string;
		given_name: LocalizedText;
		family_name: LocalizedText;
		username: string;
		avatar_url: string | null;
		auto_user_email: string;
	};
	has_wikipage: boolean;
	wikipage_count: number;
	wikipages: Array<{
		id: string;
		title: LocalizedText;
		decorating_profile_id: string;
	}>;
	payroll_formula_assignments?: Array<{
		id: string;
		payroll_formula_id: string;
		auto_payroll_formula_earning_source_kind: string;
		payroll_formula: PayrollFormula;
	}>;
};

export const load: PageServerLoad = async ({ locals }) => {
	const { brand, supabase } = locals;
	const security = locals.security;

	// Fetch payroll profiles with related wikipages
	const { data: payrollProfiles, error: payrollProfilesError } = await supabase
		.from('payroll_profile')
		.select(
			`
			*,
			profile:profile_id (
				id,
				given_name,
				family_name,
				username,
				avatar_url,
				auto_user_email
			),
			wikipages:profile_id (
				wikipage (
					id,
					title,
					decorating_profile_id
				)
			),
			payroll_formula_assignments:payroll_formula_assignment (
				id,
				payroll_formula_id,
				auto_payroll_formula_earning_source_kind,
				payroll_formula:payroll_formula_id (
					id,
					name,
					earning_source_kind,
					event_kind,
					product_kind,
					is_active
				)
			)
		`
		)
		.order('created_at', { ascending: false });

	if (payrollProfilesError) {
		console.error('Error fetching payroll profiles:', payrollProfilesError);
	}

	// Process profiles to add wikipage information
	const profilesWithWikipageInfo =
		payrollProfiles?.map((profile) => {
			const wikipages = profile.wikipages?.wikipage || [];
			return {
				...profile,
				has_wikipage: wikipages.length > 0,
				wikipage_count: wikipages.length,
				wikipages: wikipages
			};
		}) || [];

	// Fetch brand team members who are not yet payees
	const { data: brandTeamMembers, error: brandTeamMembersError } = await supabase.rpc(
		'get_brand_team_members',
		{ brand_id_param: brand.id }
	);

	if (brandTeamMembersError) {
		console.error('Error fetching brand team members:', brandTeamMembersError);
	}

	// Filter out team members who are already payees
	const existingPayeeIds = new Set(payrollProfiles?.map((p) => p.profile_id) || []);
	const availableTeamMembers =
		brandTeamMembers?.filter((m: TeamMember) => !existingPayeeIds.has(m.id)) || [];

	// Fetch currencies
	const { data: currencies, error: currenciesError } = await supabase
		.from('currency')
		.select('*')
		.order('id', { ascending: true });

	if (currenciesError) {
		console.error('Error fetching currencies:', currenciesError);
	}

	// Fetch active formulas
	const { data: formulas, error: formulasError } = await supabase
		.from('payroll_formula')
		.select('*')
		.eq('brand_id', brand.id)
		.eq('is_active', true)
		.order('created_at', { ascending: false });

	if (formulasError) {
		console.error('Error fetching formulas:', formulasError);
	}

	// Group formulas by type
	const groupedFormulas = {
		event: (formulas || []).filter((f) => f.earning_source_kind === 'event'),
		product: (formulas || []).filter((f) => f.earning_source_kind === 'product'),
		profile: (formulas || []).filter((f) => f.earning_source_kind === 'profile')
	};

	return {
		isBrandOwner: security.isBrandOwner,
		hasPayrollWriteAccess: security.hasPermission('brand_payroll_write'),
		payrollProfiles: (profilesWithWikipageInfo as PayrollProfile[]) || [],
		availableTeamMembers: availableTeamMembers as TeamMember[],
		currencies: currencies || [],
		groupedFormulas: groupedFormulas as {
			event: PayrollFormula[];
			product: PayrollFormula[];
			profile: PayrollFormula[];
		}
	};
};

export const actions: Actions = {
	addPayee: async ({ request, locals }) => {
		const { user, brand, supabase } = locals;

		if (!user) {
			return { success: false, error: 'Not authenticated' };
		}

		const formData = await request.formData();
		const profileId = formData.get('profileId')?.toString();
		const title = formData.get('title')?.toString() || null;
		const level = formData.get('level')?.toString() || null;
		const currencyCode = formData.get('currencyCode')?.toString();
		const accountantNotes = formData.get('accountantNotes')?.toString() || null;

		// Get formula assignments from form data
		const eventFormulaId = formData.get('eventFormulaId')?.toString() || null;
		const productFormulaId = formData.get('productFormulaId')?.toString() || null;
		const profileFormulaId = formData.get('profileFormulaId')?.toString() || null;

		if (!profileId || !currencyCode) {
			return { success: false, error: 'Missing required fields' };
		}

		// Create the payroll profile
		const { data: payeeData, error: payeeError } = await supabase
			.from('payroll_profile')
			.insert({
				profile_id: profileId,
				creator_id: user.id,
				title,
				level,
				currency_code: currencyCode,
				accountant_notes: accountantNotes
			})
			.select();

		if (payeeError) {
			console.error('Error adding payee:', payeeError);
			return { success: false, error: payeeError.message };
		}

		// Create formula assignments if formulas were provided
		const payeeId = payeeData[0].id;
		const assignments = [];

		if (eventFormulaId) {
			assignments.push({
				payroll_profile_id: payeeId,
				payroll_formula_id: eventFormulaId
			});
		}

		if (productFormulaId) {
			assignments.push({
				payroll_profile_id: payeeId,
				payroll_formula_id: productFormulaId
			});
		}

		if (profileFormulaId) {
			assignments.push({
				payroll_profile_id: payeeId,
				payroll_formula_id: profileFormulaId
			});
		}

		if (assignments.length > 0) {
			const { error: assignmentError } = await supabase
				.from('payroll_formula_assignment')
				.insert(assignments);

			if (assignmentError) {
				console.error('Error adding formula assignments:', assignmentError);
				// Don't return an error here since the payee was created successfully
			}
		}

		return { success: true, data: payeeData };
	},

	updatePayee: async ({ request, locals }) => {
		const { user, supabase } = locals;

		if (!user) {
			return { success: false, error: 'Not authenticated' };
		}

		const formData = await request.formData();
		const payrollProfileId = formData.get('payrollProfileId')?.toString();
		const title = formData.get('title')?.toString() || null;
		const level = formData.get('level')?.toString() || null;
		const currencyCode = formData.get('currencyCode')?.toString();
		const accountantNotes = formData.get('accountantNotes')?.toString() || null;

		// Get formula assignments from form data
		const eventFormulaId = formData.get('eventFormulaId')?.toString() || null;
		const productFormulaId = formData.get('productFormulaId')?.toString() || null;
		const profileFormulaId = formData.get('profileFormulaId')?.toString() || null;

		if (!payrollProfileId || !currencyCode) {
			return { success: false, error: 'Missing required fields' };
		}

		// Start by updating the payroll profile
		const { data, error } = await supabase
			.from('payroll_profile')
			.update({
				title,
				level,
				currency_code: currencyCode,
				accountant_notes: accountantNotes,
				updated_at: new Date().toISOString()
			})
			.eq('id', payrollProfileId)
			.select();

		if (error) {
			console.error('Error updating payee:', error);
			return { success: false, error: error.message };
		}

		// Delete existing formula assignments
		const { error: deleteError } = await supabase
			.from('payroll_formula_assignment')
			.delete()
			.eq('payroll_profile_id', payrollProfileId);

		if (deleteError) {
			console.error('Error deleting existing formula assignments:', deleteError);
			return { success: false, error: deleteError.message };
		}

		// Create new formula assignments if formulas were provided
		const assignments = [];

		if (eventFormulaId) {
			assignments.push({
				payroll_profile_id: payrollProfileId,
				payroll_formula_id: eventFormulaId
			});
		}

		if (productFormulaId) {
			assignments.push({
				payroll_profile_id: payrollProfileId,
				payroll_formula_id: productFormulaId
			});
		}

		if (profileFormulaId) {
			assignments.push({
				payroll_profile_id: payrollProfileId,
				payroll_formula_id: profileFormulaId
			});
		}

		if (assignments.length > 0) {
			const { error: assignmentError } = await supabase
				.from('payroll_formula_assignment')
				.insert(assignments);

			if (assignmentError) {
				console.error('Error adding formula assignments:', assignmentError);
				return { success: false, error: assignmentError.message };
			}
		}

		return { success: true, data };
	},

	removePayee: async ({ request, locals }) => {
		const { supabase } = locals;

		const formData = await request.formData();
		const payrollProfileId = formData.get('payrollProfileId')?.toString();

		if (!payrollProfileId) {
			return { success: false, error: 'Missing payroll profile ID' };
		}

		// First check if there are any payroll entries for this profile
		const { data: entryData, error: entryError } = await supabase
			.from('payroll_entry')
			.select('id')
			.eq('payroll_profile_id', payrollProfileId)
			.limit(1);

		if (entryError) {
			console.error('Error checking payroll entries:', entryError);
			return { success: false, error: entryError.message };
		}

		if (entryData && entryData.length > 0) {
			// Set unenrolled_at instead of deleting if entries exist
			const { error } = await supabase
				.from('payroll_profile')
				.update({ unenrolled_at: new Date().toISOString() })
				.eq('id', payrollProfileId);

			if (error) {
				console.error('Error unenrolling payee:', error);
				return { success: false, error: error.message };
			}
		} else {
			// Delete payroll_formula_assignments first
			const { error: assignmentError } = await supabase
				.from('payroll_formula_assignment')
				.delete()
				.eq('payroll_profile_id', payrollProfileId);

			if (assignmentError) {
				console.error('Error deleting formula assignments:', assignmentError);
				return { success: false, error: assignmentError.message };
			}

			// Then delete the payroll profile
			const { error } = await supabase.from('payroll_profile').delete().eq('id', payrollProfileId);

			if (error) {
				console.error('Error deleting payee:', error);
				return { success: false, error: error.message };
			}
		}

		return { success: true };
	}
};
