<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { PlusCircle } from '@lucide/svelte';
	import { invalidateAll } from '$app/navigation';
	import { toast } from 'svelte-sonner';
	import type { PageProps } from './$types';
	import PayeeRow from './components/PayeeRow.svelte';
	import PayeeFormModal from './components/PayeeFormModal.svelte';

	// Use PageProps pattern
	let { data }: PageProps = $props();

	// Individual state variables
	let addDialogOpen = $state(false);

	function handleRemoveSuccess(): void {
		invalidateAll();
		toast.success('Payee removed successfully');
	}

	function handleRemoveError(error: string): void {
		toast.error(`Error removing payee: ${error}`);
	}

	function handleEditSuccess(): void {
		invalidateAll();
		toast.success('Payee updated successfully');
	}
</script>

{#snippet content()}
	<div class="grid gap-6">
		<Card>
			<CardHeader>
				<div class="flex items-center justify-between">
					<div>
						<CardTitle>Payees</CardTitle>
						<CardDescription>
							Manage team members who receive payments through the payroll system.
						</CardDescription>
					</div>
					{#if data.hasPayrollWriteAccess}
						<Button onclick={() => (addDialogOpen = true)}>
							<PlusCircle class="mr-2 h-4 w-4" />
							Add Payee
						</Button>

						<PayeeFormModal
							open={addDialogOpen}
							onOpenChange={(open) => (addDialogOpen = open)}
							currencies={data.currencies}
							groupedFormulas={data.groupedFormulas}
						/>
					{/if}
				</div>
			</CardHeader>
			<CardContent>
				{#if data.payrollProfiles.length === 0}
					<div class="py-8 text-center text-muted-foreground">
						No payees found. Add team members to the payroll system to get started.
					</div>
				{:else}
					<div class="grid gap-4">
						{#each data.payrollProfiles as payrollProfile}
							<PayeeRow
								{payrollProfile}
								currencies={data.currencies}
								groupedFormulas={data.groupedFormulas}
								hasWriteAccess={data.hasPayrollWriteAccess}
								onRemoveSuccess={handleRemoveSuccess}
								onRemoveError={handleRemoveError}
								onEditSuccess={handleEditSuccess}
							/>
						{/each}
					</div>
				{/if}
			</CardContent>
		</Card>
	</div>
{/snippet}

<PageContainer title="Payroll Payees" description="Manage payees in the payroll system" {content} />
