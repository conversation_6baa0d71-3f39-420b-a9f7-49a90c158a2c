import type { PageServerLoad } from './$types';
import type { PayrollPeriodData } from '$lib/types/payroll';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	const { brand, supabase } = locals;
	const security = locals.security;

	// Fetch payroll periods for the current brand
	const { data: payrollPeriods, error: payrollPeriodsError } = await supabase
		.from('payroll_period')
		.select('*')
		.eq('brand_id', brand.id)
		.order('end_at', { ascending: false });

	if (payrollPeriodsError) {
		console.error('Error fetching payroll periods:', payrollPeriodsError);
	}

	return {
		isBrandOwner: security.isBrandOwner,
		hasPayrollWriteAccess: security.hasPermission('brand_payroll_write'),
		payrollPeriods: (payrollPeriods as PayrollPeriodData[]) || []
	};
};

export const actions: Actions = {
	deletePayrollPeriod: async ({ request, locals }) => {
		const { brand, supabase, security } = locals;

		// Check if user has permission to delete
		if (!security.isBrandOwner && !security.hasPermission('brand_payroll_write')) {
			return fail(403, { success: false, message: 'Unauthorized to delete payroll periods' });
		}

		const formData = await request.formData();
		const id = formData.get('id')?.toString();

		if (!id) {
			return fail(400, { success: false, message: 'Payroll period ID is required' });
		}

		// Check if the payroll period belongs to the brand
		const { data: payrollPeriod, error: checkError } = await supabase
			.from('payroll_period')
			.select('id')
			.eq('id', id)
			.eq('brand_id', brand.id)
			.single();

		if (checkError || !payrollPeriod) {
			return fail(404, { success: false, message: 'Payroll period not found' });
		}

		// Delete the payroll period
		const { error: deleteError } = await supabase.from('payroll_period').delete().eq('id', id);

		if (deleteError) {
			console.error('Error deleting payroll period:', deleteError);
			return fail(500, { success: false, message: 'Failed to delete payroll period' });
		}

		return { success: true };
	}
};
