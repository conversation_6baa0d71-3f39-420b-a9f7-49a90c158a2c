import type { PageServerLoad, Actions } from './$types';
import type { PayrollFormulaData } from '$lib/types/payroll';

export const load: PageServerLoad = async ({ locals }) => {
	const { brand, supabase } = locals;
	const security = locals.security;

	// Fetch event kinds
	const { data: eventKinds, error: eventKindsError } = await supabase
		.from('event_kind')
		.select('*')
		.order('name', { ascending: true });

	if (eventKindsError) {
		console.error('Error fetching event kinds:', eventKindsError);
	}

	// Fetch product kinds
	const { data: productKinds, error: productKindsError } = await supabase
		.from('product_kind')
		.select('*')
		.order('id', { ascending: true });

	if (productKindsError) {
		console.error('Error fetching product kinds:', productKindsError);
	}

	// Map the product kinds to include a displayName property for the UI
	const productKindsWithDisplayNames = (productKinds || []).map((kind) => ({
		...kind,
		// Use the title JSONB field's 'en' property as a display name, or fallback to ID
		name: kind.title?.en || `Product Type ${kind.id}`
	}));

	// Fetch formulas grouped by type
	const { data: formulas, error: formulasError } = await supabase
		.from('payroll_formula')
		.select('*')
		.eq('brand_id', brand.id)
		.order('created_at', { ascending: false });

	if (formulasError) {
		console.error('Error fetching formulas:', formulasError);
	}

	// Group formulas by type
	const groupedFormulas = {
		event: (formulas || []).filter((f) => f.earning_source_kind === 'event'),
		product: (formulas || []).filter((f) => f.earning_source_kind === 'product'),
		profile: (formulas || []).filter((f) => f.earning_source_kind === 'profile')
	};

	return {
		isBrandOwner: security.isBrandOwner,
		hasPayrollWriteAccess: security.hasPermission('brand_payroll_write'),
		eventKinds: eventKinds || [],
		productKinds: productKindsWithDisplayNames,
		groupedFormulas: groupedFormulas as {
			event: PayrollFormulaData[];
			product: PayrollFormulaData[];
			profile: PayrollFormulaData[];
		}
	};
};

export const actions: Actions = {
	createFormula: async ({ request, locals }) => {
		const { user, brand, supabase } = locals;

		if (!user) {
			return { success: false, error: 'Not authenticated' };
		}

		const formData = await request.formData();
		const name = formData.get('name')?.toString();
		const description = formData.get('description')?.toString() || null;
		const earningSourceKind = formData.get('earningSourceKind')?.toString() as
			| 'event'
			| 'product'
			| 'profile';
		const eventKind = formData.get('eventKind')?.toString() || null;
		const productKind = formData.get('productKind')?.toString() || null;
		const formulaJs = formData.get('formulaJs')?.toString();
		const isDefault = formData.get('isDefault') === 'true';

		if (!name || !earningSourceKind || !formulaJs) {
			return { success: false, error: 'Missing required fields' };
		}

		// Validate that event_kind is provided for event formulas
		if (earningSourceKind === 'event' && !eventKind) {
			return { success: false, error: 'Event kind is required for event formulas' };
		}

		// Validate that product_kind is provided for product formulas
		if (earningSourceKind === 'product' && !productKind) {
			return { success: false, error: 'Product kind is required for product formulas' };
		}

		try {
			// If this is a default formula, unset default flag on other formulas of the same type
			if (isDefault) {
				const { error: updateError } = await supabase
					.from('payroll_formula')
					.update({ is_default: false })
					.eq('brand_id', brand.id)
					.eq('earning_source_kind', earningSourceKind)
					.eq('is_default', true);

				if (updateError) {
					console.error('Error updating existing default formulas:', updateError);
					return { success: false, error: updateError.message };
				}
			}

			// Create the new formula
			const { data, error } = await supabase
				.from('payroll_formula')
				.insert({
					brand_id: brand.id,
					name,
					description,
					earning_source_kind: earningSourceKind,
					event_kind: earningSourceKind === 'event' ? eventKind : null,
					product_kind: earningSourceKind === 'product' ? productKind : null,
					formula_js: formulaJs,
					is_default: isDefault,
					is_active: true
				})
				.select();

			if (error) {
				console.error('Error creating formula:', error);
				return { success: false, error: error.message };
			}

			return { success: true, data };
		} catch (error) {
			console.error('Error in createFormula action:', error);
			return { success: false, error: 'Server error' };
		}
	},

	updateFormula: async ({ request, locals }) => {
		const { supabase } = locals;

		const formData = await request.formData();
		const id = formData.get('id')?.toString();
		const name = formData.get('name')?.toString();
		const description = formData.get('description')?.toString() || null;
		const formulaJs = formData.get('formulaJs')?.toString();
		const isDefault = formData.get('isDefault') === 'true';
		const isActive = formData.get('isActive') === 'true';

		if (!id || !name || !formulaJs) {
			return { success: false, error: 'Missing required fields' };
		}

		try {
			// Get the formula to update
			const { data: formula, error: getError } = await supabase
				.from('payroll_formula')
				.select('*')
				.eq('id', id)
				.single();

			if (getError) {
				console.error('Error getting formula:', getError);
				return { success: false, error: getError.message };
			}

			// If setting as default, unset default flag on other formulas of the same type
			if (isDefault && !formula.is_default) {
				const { error: updateError } = await supabase
					.from('payroll_formula')
					.update({ is_default: false })
					.eq('brand_id', formula.brand_id)
					.eq('earning_source_kind', formula.earning_source_kind)
					.eq('is_default', true);

				if (updateError) {
					console.error('Error updating existing default formulas:', updateError);
					return { success: false, error: updateError.message };
				}
			}

			// Update the formula
			const { data, error } = await supabase
				.from('payroll_formula')
				.update({
					name,
					description,
					formula_js: formulaJs,
					is_default: isDefault,
					is_active: isActive
				})
				.eq('id', id)
				.select();

			if (error) {
				console.error('Error updating formula:', error);
				return { success: false, error: error.message };
			}

			return { success: true, data };
		} catch (error) {
			console.error('Error in updateFormula action:', error);
			return { success: false, error: 'Server error' };
		}
	},

	deleteFormula: async ({ request, locals }) => {
		const { supabase } = locals;

		const formData = await request.formData();
		const id = formData.get('id')?.toString();

		if (!id) {
			return { success: false, error: 'Missing formula ID' };
		}

		try {
			// First check if there are any assignments using this formula
			const { data: assignments, error: assignmentsError } = await supabase
				.from('payroll_formula_assignment')
				.select('id')
				.eq('payroll_formula_id', id)
				.limit(1);

			if (assignmentsError) {
				console.error('Error checking formula assignments:', assignmentsError);
				return { success: false, error: assignmentsError.message };
			}

			if (assignments && assignments.length > 0) {
				return {
					success: false,
					error:
						'Cannot delete formula that is assigned to payees. Please remove all assignments first.'
				};
			}

			// Check if there are any payroll entries using this formula
			const { data: entries, error: entriesError } = await supabase
				.from('payroll_entry')
				.select('id')
				.eq('payroll_formula_id', id)
				.limit(1);

			if (entriesError) {
				console.error('Error checking payroll entries:', entriesError);
				return { success: false, error: entriesError.message };
			}

			if (entries && entries.length > 0) {
				return {
					success: false,
					error:
						'Cannot delete formula that is used in payroll entries. Set it as inactive instead.'
				};
			}

			// Delete the formula
			const { error } = await supabase.from('payroll_formula').delete().eq('id', id);

			if (error) {
				console.error('Error deleting formula:', error);
				return { success: false, error: error.message };
			}

			return { success: true };
		} catch (error) {
			console.error('Error in deleteFormula action:', error);
			return { success: false, error: 'Server error' };
		}
	}
};
