<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { PlusCircle, Pencil, Trash2, CheckCircle, AlertCircle } from '@lucide/svelte';
	import type { PayrollFormulaData } from '$lib/types/payroll';
	import { enhance } from '$app/forms';
	import {
		Dialog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle,
		DialogTrigger
	} from '$lib/components/ui/dialog';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import * as Select from '$lib/components/ui/select/index.js';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Switch } from '$lib/components/ui/switch';
	import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '$lib/components/ui/tabs';
	import { toast } from 'svelte-sonner';
	import type { PageProps } from './$types';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';

	// Constants for formula templates - define these first before they're used in any functions
	const EVENT_FORMULA_TEMPLATE = `// Available inputs:
// - payee_enrolled_at: enrollment date of the payee
// - payee_title: job title of the payee
// - payee_level: experience level of the payee
// - payee_event_role: role in the event (e.g. instructor, assistant)
// - payee_event_role_count: number of staff with same role
// - event_minutes: duration of event in minutes
// - event_start_at: start time of the event
// - event_space_id: ID of the space where event is held
// - event_consumer_total_cost: total paid by consumers
// - event_consumer_registered: number of registered consumers
// - event_consumer_checked_in: number of consumers who checked in

// Calculate payment based on hours and role
const hourlyRate = 25; // Base hourly rate
const hours = event_minutes / 60;
let rate = hourlyRate;

// Adjust rate based on role
if (payee_event_role === 'instructor') {
  rate = 35; // Higher rate for instructors
} else if (payee_event_role === 'assistant') {
  rate = 20; // Lower rate for assistants
}

// Adjust based on level if available
if (payee_level === 'senior') {
  rate += 5;
} else if (payee_level === 'junior') {
  rate -= 3;
}

// Calculate final amount
return hours * rate;`;

	const PRODUCT_FORMULA_TEMPLATE = `// Available inputs:
// - payee_enrolled_at: enrollment date of the payee
// - payee_title: job title of the payee
// - payee_level: experience level of the payee
// - product_id: ID of the product
// - product_kind: type of product
// - product_sale_count: number of units sold
// - product_total_revenue: total revenue from sales
// - product_cost: cost of goods sold

// Calculate commission based on sales
const commissionRate = 0.10; // 10% commission
let commission = product_total_revenue * commissionRate;

// Provide minimum payment
const minimumPayment = 5;
if (commission < minimumPayment && product_sale_count > 0) {
  commission = minimumPayment;
}

return commission;`;

	const PROFILE_FORMULA_TEMPLATE = `// Available inputs:
// - payee_enrolled_at: enrollment date of the payee
// - payee_title: job title of the payee
// - payee_level: experience level of the payee
// - payee_total_event_count: number of events the payee was involved in
// - payee_total_student_count: total number of students taught
// - payee_total_product_sales: total sales amount of products
// - payee_total_hours: total hours worked

// Calculate a bonus based on performance
const baseBonus = 50;
let bonus = baseBonus;

// Adjust based on events
if (payee_total_event_count > 10) {
  bonus += 25;
}

// Adjust based on students
if (payee_total_student_count > 50) {
  bonus += 50;
}

// Adjust based on product sales
if (payee_total_product_sales > 1000) {
  bonus += payee_total_product_sales * 0.05;
}

return bonus;`;

	// Helper functions also defined before use
	function getDefaultFormulaTemplate(formulaType: string): string {
		switch (formulaType) {
			case 'event':
				return EVENT_FORMULA_TEMPLATE;
			case 'product':
				return PRODUCT_FORMULA_TEMPLATE;
			case 'profile':
				return PROFILE_FORMULA_TEMPLATE;
			default:
				return '';
		}
	}

	// Get page data from the load function
	let { data }: PageProps = $props();

	let hasPayrollWriteAccess = $state(data.hasPayrollWriteAccess);
	let eventKinds = $state(data.eventKinds);
	let productKinds = $state(data.productKinds);
	let groupedFormulas = $state(data.groupedFormulas);

	// Individual state variables
	let createDialogOpen = $state(false);
	let editDialogOpen = $state(false);
	let deleteDialogOpen = $state(false);
	let selectedFormula = $state<PayrollFormulaData | null>(null);
	let currentTab = $state('event');

	// Create form state - now safe to use getDefaultFormulaTemplate
	let createName = $state('');
	let createDescription = $state('');
	let createEarningSourceKind = $state('event');
	let createEventKind = $state('');
	let createProductKind = $state('');
	let createFormulaJs = $state(getDefaultFormulaTemplate('event'));
	let createIsDefault = $state(false);

	// Edit form state
	let editId = $state('');
	let editName = $state('');
	let editDescription = $state('');
	let editFormulaJs = $state('');
	let editIsDefault = $state(false);
	let editIsActive = $state(true);

	let submitting = $state(false);
	let currentLocale = $state(getLocale());

	// Helper functions to get correctly formatted localizable text
	function getEventKindName(eventKindId: string | null): string {
		if (!eventKindId) return 'Unknown';
		const eventKind = eventKinds.find((k) => k.id === eventKindId);
		if (!eventKind) return 'Unknown';
		return getLocalizedText(eventKind.name as LocalizedText, currentLocale);
	}

	function getProductKindName(productKindId: string | null): string {
		if (!productKindId) return 'Unknown';
		const productKind = productKinds.find((k) => k.id === productKindId);
		if (!productKind) return 'Unknown';
		return getLocalizedText(productKind.name as LocalizedText, currentLocale);
	}

	// Derived values for select triggers
	let formulaTypeTriggerContent = $derived(
		createEarningSourceKind === 'event'
			? 'Event Formula'
			: createEarningSourceKind === 'product'
				? 'Product Formula'
				: createEarningSourceKind === 'profile'
					? 'Profile Formula'
					: 'Select formula type'
	);

	let eventKindTriggerContent = $derived(
		eventKinds.find((k) => k.id === createEventKind)
			? getLocalizedText(
					eventKinds.find((k) => k.id === createEventKind)?.name as LocalizedText,
					currentLocale
				)
			: 'Select event kind'
	);

	let productKindTriggerContent = $derived(
		productKinds.find((k) => k.id === createProductKind)
			? getLocalizedText(
					productKinds.find((k) => k.id === createProductKind)?.name as LocalizedText,
					currentLocale
				)
			: 'Select product kind'
	);

	function handleFormulaTypeChange(): void {
		createFormulaJs = getDefaultFormulaTemplate(createEarningSourceKind);

		// Reset kind selections when type changes
		if (createEarningSourceKind !== 'event') {
			createEventKind = '';
		}

		if (createEarningSourceKind !== 'product') {
			createProductKind = '';
		}
	}

	function openEditDialog(formula: PayrollFormulaData): void {
		selectedFormula = formula;
		editId = formula.id;
		editName = formula.name;
		editDescription = formula.description || '';
		editFormulaJs = formula.formula_js;
		editIsDefault = formula.is_default;
		editIsActive = formula.is_active;
		editDialogOpen = true;
	}

	function openDeleteDialog(formula: PayrollFormulaData): void {
		selectedFormula = formula;
		deleteDialogOpen = true;
	}

	function resetCreateForm(): void {
		createName = '';
		createDescription = '';
		createEarningSourceKind = 'event';
		createEventKind = '';
		createProductKind = '';
		createFormulaJs = getDefaultFormulaTemplate('event');
		createIsDefault = false;
	}

	function handleCreateSuccess(): void {
		createDialogOpen = false;
		resetCreateForm();
		submitting = false;
		toast.success('Formula created successfully');
	}

	function handleCreateError(error: string): void {
		submitting = false;
		toast.error(`Error creating formula: ${error}`);
	}

	function handleUpdateSuccess(): void {
		editDialogOpen = false;
		selectedFormula = null;
		submitting = false;
		toast.success('Formula updated successfully');
	}

	function handleUpdateError(error: string): void {
		submitting = false;
		toast.error(`Error updating formula: ${error}`);
	}

	function handleDeleteSuccess(): void {
		deleteDialogOpen = false;
		selectedFormula = null;
		toast.success('Formula deleted successfully');
	}

	function handleDeleteError(error: string): void {
		toast.error(`Error deleting formula: ${error}`);
	}
</script>

{#snippet content()}
	<div class="grid gap-6">
		<Card>
			<CardHeader>
				<div class="flex items-center justify-between">
					<div>
						<CardTitle>Payment Formulas</CardTitle>
						<CardDescription>Manage formulas used to calculate payroll amounts.</CardDescription>
					</div>
					{#if hasPayrollWriteAccess}
						<Dialog open={createDialogOpen} onOpenChange={(open) => (createDialogOpen = open)}>
							<DialogTrigger>
								<Button>
									<PlusCircle class="mr-2 h-4 w-4" />
									Create Formula
								</Button>
							</DialogTrigger>
							<DialogContent class="sm:max-w-[700px]">
								<DialogHeader>
									<DialogTitle>Create Payment Formula</DialogTitle>
									<DialogDescription
										>Define a new formula for calculating payments.</DialogDescription
									>
								</DialogHeader>
								<form
									method="POST"
									action="?/createFormula"
									use:enhance={({ formData }) => {
										submitting = true;
										return async ({ result }) => {
											if (result.type === 'success') {
												handleCreateSuccess();
											} else if (result.type === 'failure' && result.data) {
												const errorMsg =
													typeof result.data.error === 'string'
														? result.data.error
														: 'Unknown error';
												handleCreateError(errorMsg);
											} else {
												handleCreateError('An error occurred');
											}
										};
									}}
								>
									<div class="grid gap-4 py-4">
										<div class="grid gap-2">
											<Label for="name">Formula Name</Label>
											<Input
												id="name"
												name="name"
												bind:value={createName}
												placeholder="e.g. Standard Instructor Pay, Product Commission"
												required
											/>
										</div>
										<div class="grid gap-2">
											<Label for="description">Description (Optional)</Label>
											<Textarea
												id="description"
												name="description"
												bind:value={createDescription}
												placeholder="Describe how this formula works and when it should be used"
												rows={2}
											/>
										</div>
										<div class="grid gap-2">
											<Label for="earningSourceKind">Formula Type</Label>
											<input
												type="hidden"
												name="earningSourceKind"
												value={createEarningSourceKind}
											/>
											<Select.Root type="single" bind:value={createEarningSourceKind}>
												<Select.Trigger onclick={() => handleFormulaTypeChange()}>
													{formulaTypeTriggerContent}
												</Select.Trigger>
												<Select.Content>
													<Select.Item value="event" label="Event Formula"
														>Event Formula</Select.Item
													>
													<Select.Item value="product" label="Product Formula"
														>Product Formula</Select.Item
													>
													<Select.Item value="profile" label="Profile Formula"
														>Profile Formula</Select.Item
													>
												</Select.Content>
											</Select.Root>
										</div>

										{#if createEarningSourceKind === 'event'}
											<div class="grid gap-2">
												<Label for="eventKind">Event Kind</Label>
												<input type="hidden" name="eventKind" value={createEventKind} />
												<Select.Root type="single" bind:value={createEventKind}>
													<Select.Trigger>
														{eventKindTriggerContent}
													</Select.Trigger>
													<Select.Content>
														<Select.Group>
															{#each eventKinds as eventKind}
																<Select.Item
																	value={eventKind.id}
																	label={getLocalizedText(
																		eventKind.name as LocalizedText,
																		currentLocale
																	)}
																>
																	{getLocalizedText(eventKind.name as LocalizedText, currentLocale)}
																</Select.Item>
															{/each}
														</Select.Group>
													</Select.Content>
												</Select.Root>
											</div>
										{:else if createEarningSourceKind === 'product'}
											<div class="grid gap-2">
												<Label for="productKind">Product Kind</Label>
												<input type="hidden" name="productKind" value={createProductKind} />
												<Select.Root type="single" bind:value={createProductKind}>
													<Select.Trigger>
														{productKindTriggerContent}
													</Select.Trigger>
													<Select.Content>
														<Select.Group>
															{#each productKinds as productKind}
																<Select.Item
																	value={productKind.id}
																	label={getLocalizedText(
																		productKind.name as LocalizedText,
																		currentLocale
																	)}
																>
																	{getLocalizedText(
																		productKind.name as LocalizedText,
																		currentLocale
																	)}
																</Select.Item>
															{/each}
														</Select.Group>
													</Select.Content>
												</Select.Root>
											</div>
										{/if}

										<div class="grid gap-2">
											<Label for="formulaJs">Formula JavaScript</Label>
											<Textarea
												id="formulaJs"
												name="formulaJs"
												bind:value={createFormulaJs}
												placeholder="JavaScript formula code that returns a numeric value"
												class="font-mono text-sm"
												rows={12}
												required
											/>
											<p class="text-xs text-muted-foreground">
												Use JavaScript to calculate the payment amount. The function should return a
												numeric value.
											</p>
										</div>

										<div class="flex items-center space-x-2">
											<Switch id="isDefault" name="isDefault" bind:checked={createIsDefault} />
											<Label for="isDefault">Set as default formula</Label>
										</div>
									</div>
									<DialogFooter>
										<Button type="submit" disabled={submitting}>
											{submitting ? 'Creating...' : 'Create Formula'}
										</Button>
									</DialogFooter>
								</form>
							</DialogContent>
						</Dialog>
					{/if}
				</div>
			</CardHeader>
			<CardContent>
				<Tabs bind:value={currentTab} class="space-y-4">
					<TabsList>
						<TabsTrigger value="event">Event Formulas</TabsTrigger>
						<TabsTrigger value="product">Product Formulas</TabsTrigger>
						<TabsTrigger value="profile">Profile Formulas</TabsTrigger>
					</TabsList>

					<TabsContent value="event" class="space-y-4">
						{#if groupedFormulas.event.length === 0}
							<div class="py-8 text-center text-muted-foreground">
								No event formulas found. Create a formula to get started.
							</div>
						{:else}
							{#each groupedFormulas.event as formula}
								<div class="rounded-lg border p-4">
									<div class="flex items-start justify-between">
										<div>
											<div class="flex items-center gap-2">
												<h3 class="font-medium">{formula.name}</h3>
												{#if formula.is_default}
													<span
														class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800"
													>
														Default
													</span>
												{/if}
												{#if !formula.is_active}
													<span
														class="inline-flex items-center rounded-full bg-amber-100 px-2.5 py-0.5 text-xs font-medium text-amber-800"
													>
														Inactive
													</span>
												{/if}
											</div>
											<p class="mt-1 text-sm text-muted-foreground">
												Event Kind: {getEventKindName(formula.event_kind)}
											</p>
											{#if formula.description}
												<p class="mt-2 text-sm">{formula.description}</p>
											{/if}
										</div>
										{#if hasPayrollWriteAccess}
											<div class="flex gap-2">
												<Button
													variant="outline"
													size="icon"
													onclick={() => openEditDialog(formula)}
												>
													<Pencil class="h-4 w-4" />
												</Button>
												<Button
													variant="outline"
													size="icon"
													onclick={() => openDeleteDialog(formula)}
												>
													<Trash2 class="h-4 w-4" />
												</Button>
											</div>
										{/if}
									</div>
									<div class="mt-4">
										<pre
											class="overflow-x-auto rounded-md bg-muted p-2 font-mono text-xs">{formula.formula_js}</pre>
									</div>
								</div>
							{/each}
						{/if}
					</TabsContent>

					<TabsContent value="product" class="space-y-4">
						{#if groupedFormulas.product.length === 0}
							<div class="py-8 text-center text-muted-foreground">
								No product formulas found. Create a formula to get started.
							</div>
						{:else}
							{#each groupedFormulas.product as formula}
								<div class="rounded-lg border p-4">
									<div class="flex items-start justify-between">
										<div>
											<div class="flex items-center gap-2">
												<h3 class="font-medium">{formula.name}</h3>
												{#if formula.is_default}
													<span
														class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800"
													>
														Default
													</span>
												{/if}
												{#if !formula.is_active}
													<span
														class="inline-flex items-center rounded-full bg-amber-100 px-2.5 py-0.5 text-xs font-medium text-amber-800"
													>
														Inactive
													</span>
												{/if}
											</div>
											<p class="mt-1 text-sm text-muted-foreground">
												Product Kind: {getProductKindName(formula.product_kind)}
											</p>
											{#if formula.description}
												<p class="mt-2 text-sm">{formula.description}</p>
											{/if}
										</div>
										{#if hasPayrollWriteAccess}
											<div class="flex gap-2">
												<Button
													variant="outline"
													size="icon"
													onclick={() => openEditDialog(formula)}
												>
													<Pencil class="h-4 w-4" />
												</Button>
												<Button
													variant="outline"
													size="icon"
													onclick={() => openDeleteDialog(formula)}
												>
													<Trash2 class="h-4 w-4" />
												</Button>
											</div>
										{/if}
									</div>
									<div class="mt-4">
										<pre
											class="overflow-x-auto rounded-md bg-muted p-2 font-mono text-xs">{formula.formula_js}</pre>
									</div>
								</div>
							{/each}
						{/if}
					</TabsContent>

					<TabsContent value="profile" class="space-y-4">
						{#if groupedFormulas.profile.length === 0}
							<div class="py-8 text-center text-muted-foreground">
								No profile formulas found. Create a formula to get started.
							</div>
						{:else}
							{#each groupedFormulas.profile as formula}
								<div class="rounded-lg border p-4">
									<div class="flex items-start justify-between">
										<div>
											<div class="flex items-center gap-2">
												<h3 class="font-medium">{formula.name}</h3>
												{#if formula.is_default}
													<span
														class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800"
													>
														Default
													</span>
												{/if}
												{#if !formula.is_active}
													<span
														class="inline-flex items-center rounded-full bg-amber-100 px-2.5 py-0.5 text-xs font-medium text-amber-800"
													>
														Inactive
													</span>
												{/if}
											</div>
											{#if formula.description}
												<p class="mt-2 text-sm">{formula.description}</p>
											{/if}
										</div>
										{#if hasPayrollWriteAccess}
											<div class="flex gap-2">
												<Button
													variant="outline"
													size="icon"
													onclick={() => openEditDialog(formula)}
												>
													<Pencil class="h-4 w-4" />
												</Button>
												<Button
													variant="outline"
													size="icon"
													onclick={() => openDeleteDialog(formula)}
												>
													<Trash2 class="h-4 w-4" />
												</Button>
											</div>
										{/if}
									</div>
									<div class="mt-4">
										<pre
											class="overflow-x-auto rounded-md bg-muted p-2 font-mono text-xs">{formula.formula_js}</pre>
									</div>
								</div>
							{/each}
						{/if}
					</TabsContent>
				</Tabs>

				{#if selectedFormula}
					<Dialog
						open={editDialogOpen}
						onOpenChange={(open) => {
							if (!open) {
								editDialogOpen = false;
							}
						}}
					>
						<DialogContent class="sm:max-w-[700px]">
							<DialogHeader>
								<DialogTitle>Edit Formula</DialogTitle>
								<DialogDescription>Update the payment formula settings.</DialogDescription>
							</DialogHeader>
							<form
								method="POST"
								action="?/updateFormula"
								use:enhance={({ formData }) => {
									submitting = true;
									return async ({ result }) => {
										if (result.type === 'success') {
											handleUpdateSuccess();
										} else if (result.type === 'failure' && result.data) {
											const errorMsg =
												typeof result.data.error === 'string' ? result.data.error : 'Unknown error';
											handleUpdateError(errorMsg);
										} else {
											handleUpdateError('An error occurred');
										}
									};
								}}
							>
								<input type="hidden" name="id" value={editId} />
								<div class="grid gap-4 py-4">
									<div class="grid gap-2">
										<Label for="editName">Formula Name</Label>
										<Input
											id="editName"
											name="name"
											bind:value={editName}
											placeholder="e.g. Standard Instructor Pay, Product Commission"
											required
										/>
									</div>
									<div class="grid gap-2">
										<Label for="editDescription">Description (Optional)</Label>
										<Textarea
											id="editDescription"
											name="description"
											bind:value={editDescription}
											placeholder="Describe how this formula works and when it should be used"
											rows={2}
										/>
									</div>
									<div class="grid gap-2">
										<Label for="editFormulaJs">Formula JavaScript</Label>
										<Textarea
											id="editFormulaJs"
											name="formulaJs"
											bind:value={editFormulaJs}
											placeholder="JavaScript formula code that returns a numeric value"
											class="font-mono text-sm"
											rows={12}
											required
										/>
									</div>

									<div class="flex items-center space-x-2">
										<Switch id="editIsDefault" name="isDefault" bind:checked={editIsDefault} />
										<Label for="editIsDefault">Set as default formula</Label>
									</div>

									<div class="flex items-center space-x-2">
										<Switch id="editIsActive" name="isActive" bind:checked={editIsActive} />
										<Label for="editIsActive">Active</Label>
									</div>
								</div>
								<DialogFooter>
									<Button type="submit" disabled={submitting}>
										{submitting ? 'Updating...' : 'Update Formula'}
									</Button>
								</DialogFooter>
							</form>
						</DialogContent>
					</Dialog>

					<Dialog
						open={deleteDialogOpen}
						onOpenChange={(open) => {
							if (!open) {
								deleteDialogOpen = false;
							}
						}}
					>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Delete Formula</DialogTitle>
								<DialogDescription>
									Are you sure you want to delete this formula? This action cannot be undone.
								</DialogDescription>
							</DialogHeader>
							<form
								method="POST"
								action="?/deleteFormula"
								use:enhance={({ formData }) => {
									return async ({ result }) => {
										if (result.type === 'success') {
											handleDeleteSuccess();
										} else if (result.type === 'failure' && result.data) {
											const errorMsg =
												typeof result.data.error === 'string' ? result.data.error : 'Unknown error';
											handleDeleteError(errorMsg);
										} else {
											handleDeleteError('An error occurred');
										}
									};
								}}
							>
								<input type="hidden" name="id" value={selectedFormula.id} />
								<div class="mt-2">
									<p class="text-sm">
										This will permanently delete the formula <span class="font-semibold"
											>{selectedFormula.name}</span
										>.
									</p>
									{#if selectedFormula.is_default}
										<p class="mt-2 text-sm text-red-600">
											<AlertCircle class="mr-1 inline-block h-4 w-4" />
											This is a default formula. Deleting it may cause issues with payroll calculations.
										</p>
									{/if}
								</div>
								<DialogFooter class="mt-6">
									<Button
										variant="outline"
										type="button"
										onclick={() => (deleteDialogOpen = false)}
									>
										Cancel
									</Button>
									<Button variant="destructive" type="submit" class="ml-2">Delete</Button>
								</DialogFooter>
							</form>
						</DialogContent>
					</Dialog>
				{/if}
			</CardContent>
		</Card>
	</div>
{/snippet}

<PageContainer
	title="Payment Formulas"
	description="Manage formulas for payroll calculations"
	{content}
/>
