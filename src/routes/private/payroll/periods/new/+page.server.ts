import { redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	const { supabase } = locals;

	// Fetch available timezones from database
	const { data: timezones, error: timezonesError } = await supabase
		.from('time_zone')
		.select('name, abbrev, utc_offset')
		.order('name');

	if (timezonesError) {
		console.error('Error fetching timezones:', timezonesError);
	}

	return {
		timezones: timezones || []
	};
};

export const actions: Actions = {
	createPeriod: async ({ request, locals }) => {
		const { user, brand, supabase } = locals;

		if (!user) {
			return { success: false, error: 'Not authenticated' };
		}

		const formData = await request.formData();
		const name = formData.get('name')?.toString();
		const startAt = formData.get('startAt')?.toString();
		const endAt = formData.get('endAt')?.toString();

		if (!name || !startAt || !endAt) {
			return { success: false, error: 'Missing required fields' };
		}

		try {
			// Log the incoming data for debugging
			console.log('Creating payroll period with:', {
				name,
				startAt,
				endAt
			});

			// Create the payroll period without the timezone field
			// The timestampz columns will preserve timezone info automatically
			const { data, error } = await supabase
				.from('payroll_period')
				.insert({
					brand_id: brand.id,
					name,
					start_at: startAt,
					end_at: endAt,
					status: 'draft',
					creator_id: user.id
				})
				.select();

			if (error) {
				console.error('Error creating payroll period:', error);
				return { success: false, error: error.message };
			}

			// Return success with the new ID for the client to handle the navigation
			return {
				success: true,
				periodId: data[0].id
			};
		} catch (error) {
			console.error('Error in createPeriod action:', error);
			return { success: false, error: 'Server error' };
		}
	}
};
