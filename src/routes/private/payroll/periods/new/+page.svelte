<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import CalendarIcon from '@lucide/svelte/icons/calendar';
	import GlobeIcon from '@lucide/svelte/icons/globe';
	import Check from '@lucide/svelte/icons/check';
	import ChevronsUpDown from '@lucide/svelte/icons/chevrons-up-down';
	import { tick } from 'svelte';
	import type { DateRange } from 'bits-ui';
	import type { PageData } from './$types';
	import {
		CalendarDate,
		DateFormatter,
		type DateValue,
		getLocalTimeZone,
		toZoned,
		ZonedDateTime
	} from '@internationalized/date';
	import { cn } from '$lib/utils.js';
	import { RangeCalendar } from '$lib/components/ui/range-calendar/index.js';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import * as Command from '$lib/components/ui/command/index.js';

	let { data }: { data: PageData } = $props();

	// Nice date formatting
	const df = new DateFormatter('en-US', {
		dateStyle: 'medium'
	});

	// Detailed date time formatting
	const dtf = new DateFormatter('en-US', {
		dateStyle: 'full',
		timeStyle: 'long'
	});

	// Day of week formatting
	const dowf = new DateFormatter('en-US', {
		weekday: 'long'
	});

	// Get user's current timezone
	const browserTimeZone = getLocalTimeZone();

	// Selected timezone (default to browser's timezone)
	let selectedTimezone = $state(browserTimeZone);

	// Combobox state
	let timezoneOpen = $state(false);
	let triggerRef = $state<HTMLButtonElement>(null!);

	// Get timezone display name
	function getTimezoneDisplayName(tzName: string): string {
		if (tzName === browserTimeZone) {
			return `${tzName} (Browser)`;
		}

		const tz = data.timezones?.find((t) => t.name === tzName);
		if (tz) {
			return `${tz.name} (${tz.abbrev}, UTC${tz.utc_offset.hours >= 0 ? '+' : ''}${tz.utc_offset.hours})`;
		}

		return tzName;
	}

	// Close combobox and focus trigger
	function closeAndFocusTrigger() {
		timezoneOpen = false;
		tick().then(() => {
			triggerRef?.focus();
		});
	}

	// Calculate first day of last month
	const getFirstDayLastMonth = () => {
		const now = new Date();
		const year = now.getFullYear();
		const month = now.getMonth();
		return new CalendarDate(year, month === 0 ? 12 : month, 1);
	};

	// Calculate last day of last month
	const getLastDayLastMonth = () => {
		const now = new Date();
		const year = now.getFullYear();
		const month = now.getMonth();
		const lastDay = new Date(year, month, 0).getDate();
		return new CalendarDate(year, month === 0 ? 12 : month, lastDay);
	};

	// Initialize calendar dates with last month
	let dateRange: DateRange = $state({
		start: getFirstDayLastMonth(),
		end: getLastDayLastMonth()
	});

	let startValue: DateValue | undefined = $state(dateRange.start);
	let submitting = $state(false);
	let name = $state('');
	let userEditedName = $state(false);

	// Convert date values to ISO strings for form submission with proper time components
	function dateValueToISOString(dateValue: DateValue | undefined, isEnd: boolean = false): string {
		if (!dateValue) return '';

		// Extract date components
		const year = dateValue.year;
		const month = dateValue.month.toString().padStart(2, '0');
		const day = dateValue.day.toString().padStart(2, '0');

		// Create an ISO date string with the local date components
		const dateString = `${year}-${month}-${day}`;

		// Set time components based on start/end
		let hours, minutes, seconds, milliseconds;
		if (isEnd) {
			hours = 23;
			minutes = 59;
			seconds = 59;
			milliseconds = 999;
		} else {
			hours = 0;
			minutes = 0;
			seconds = 0;
			milliseconds = 0;
		}

		// Create a date object in the selected timezone
		// We need to use the Date constructor that takes individual components
		// to avoid automatic timezone conversion
		const date = new Date(
			Date.UTC(year, parseInt(month) - 1, parseInt(day), hours, minutes, seconds, milliseconds)
		);

		// Format to ISO string with timezone info for PostgreSQL timestampz
		// PostgreSQL requires ISO 8601 format with timezone
		return date.toISOString();
	}

	// Format date with day and time for display
	function formatDateWithDetails(dateValue: DateValue | undefined, isEnd: boolean = false): string {
		if (!dateValue) return '';

		// Extract date components directly from the date value
		const year = dateValue.year;
		const month = dateValue.month.toString().padStart(2, '0');
		const day = dateValue.day.toString().padStart(2, '0');

		// Create time string for start/end without timezone
		const timeStr = isEnd ? 'T23:59:59.999' : 'T00:00:00.000';

		// Create an ISO 8601 date string with the time but no timezone
		const dateTimeStr = `${year}-${month}-${day}${timeStr}`;

		// Create a Date object
		const date = new Date(dateTimeStr);

		// Format parts separately for better control
		const dayOfWeek = new Intl.DateTimeFormat('en-US', {
			weekday: 'long',
			timeZone: selectedTimezone
		}).format(date);

		const dateFormatted = new Intl.DateTimeFormat('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			timeZone: selectedTimezone
		}).format(date);

		const timeFormatted = new Intl.DateTimeFormat('en-US', {
			hour: '2-digit',
			minute: '2-digit',
			timeZone: selectedTimezone,
			hour12: true
		}).format(date);

		// Format in a cleaner way
		const tzParts = selectedTimezone.split('/');
		const tzDisplay = tzParts.length > 1 ? tzParts[1].replace('_', ' ') : selectedTimezone;

		return `${dayOfWeek}, ${dateFormatted} at ${timeFormatted} (${tzDisplay})`;
	}

	// Simple date formatter for the date picker display
	function formatDateForDisplay(dateValue: DateValue | undefined): string {
		if (!dateValue) return '';

		// Create a date object
		const date = new Date(dateValue.year, dateValue.month - 1, dateValue.day);

		// Format using Intl.DateTimeFormat for proper localization
		return new Intl.DateTimeFormat('en-US', {
			dateStyle: 'medium'
		}).format(date);
	}

	// Derived values for form submission
	let startAt = $derived(dateValueToISOString(dateRange.start, false));
	let endAt = $derived(dateValueToISOString(dateRange.end, true));

	// Derived values for display
	let startDateDisplay = $derived(formatDateWithDetails(dateRange.start, false));
	let endDateDisplay = $derived(formatDateWithDetails(dateRange.end, true));

	// Generate period name based on date range
	function generatePeriodName(start: DateValue | undefined, end: DateValue | undefined): string {
		if (!start || !end) return '';

		// Create date objects for proper formatting
		const startDate = new Date(start.year, start.month - 1, start.day);
		const endDate = new Date(end.year, end.month - 1, end.day);

		// Format dates using Intl API for better localization
		const formatter = new Intl.DateTimeFormat('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});

		const formattedStartDate = formatter.format(startDate);
		const formattedEndDate = formatter.format(endDate);

		// Calculate number of days (inclusive)
		const oneDay = 24 * 60 * 60 * 1000;
		const diffDays = Math.round(Math.abs((endDate.getTime() - startDate.getTime()) / oneDay)) + 1;

		// Format: "Jan 1, 2023 to Jan 31, 2023 (31 days)"
		return `${formattedStartDate} to ${formattedEndDate} (${diffDays} day${diffDays !== 1 ? 's' : ''})`;
	}

	// Auto-generate name when dates change, unless user has edited the name
	$effect(() => {
		if (!userEditedName && dateRange.start && dateRange.end) {
			name = generatePeriodName(dateRange.start, dateRange.end);
		}
	});

	// Mark name as user-edited when name changes directly
	function handleNameInput() {
		userEditedName = true;
	}

	// Update date displays when timezone changes
	$effect(() => {
		// Force recalculation of displays when timezone changes
		startDateDisplay = formatDateWithDetails(dateRange.start, false);
		endDateDisplay = formatDateWithDetails(dateRange.end, true);

		// Update name if user hasn't manually edited it
		if (!userEditedName && dateRange.start && dateRange.end) {
			name = generatePeriodName(dateRange.start, dateRange.end);
		}
	});

	let isFormValid = $derived(name && startAt && endAt && selectedTimezone);
</script>

{#snippet content()}
	<form
		method="POST"
		action="?/createPeriod"
		use:enhance={() => {
			submitting = true;

			return async ({ result }) => {
				submitting = false;

				if (result.type === 'success') {
					const data = (await result.data) || {};
					if (data?.success && data?.periodId) {
						toast.success('Payroll period created successfully');
						await goto(`/private/payroll/periods/${data.periodId}`);
					} else if (data?.error) {
						toast.error(`Error creating payroll period: ${data.error}`);
					}
				} else if (result.type === 'failure') {
					const data = (await result.data) || {};
					toast.error(`Error: ${data?.error || 'Unknown error occurred'}`);
				} else if (result.type === 'error') {
					toast.error('An error occurred while processing your request');
				}
			};
		}}
	>
		<Card>
			<CardHeader>
				<CardTitle>Create Payroll Period</CardTitle>
				<CardDescription>Define a new time period for calculating payroll.</CardDescription>
			</CardHeader>
			<CardContent>
				<div class="grid gap-6">
					<div class="grid gap-2">
						<Label for="dateRange">Select Date Range</Label>
						<Popover.Root>
							<Popover.Trigger
								class={cn(
									buttonVariants({ variant: 'outline' }),
									!dateRange && 'text-muted-foreground',
									'w-full justify-start text-left'
								)}
							>
								<CalendarIcon class="mr-2 size-4" />
								{#if dateRange && dateRange.start}
									{#if dateRange.end}
										{formatDateForDisplay(dateRange.start)} - {formatDateForDisplay(dateRange.end)}
									{:else}
										{formatDateForDisplay(dateRange.start)}
									{/if}
								{:else if startValue}
									{formatDateForDisplay(startValue)}
								{:else}
									Pick a date range
								{/if}
							</Popover.Trigger>
							<Popover.Content class="w-auto p-0" align="start">
								<RangeCalendar
									bind:value={dateRange}
									onStartValueChange={(v) => {
										startValue = v;
									}}
									numberOfMonths={2}
								/>
							</Popover.Content>
						</Popover.Root>

						<!-- Hidden inputs for form submission -->
						<input type="hidden" name="startAt" value={startAt} />
						<input type="hidden" name="endAt" value={endAt} />

						{#if dateRange && dateRange.start && dateRange.end}
							<div class="mt-4 rounded-md border border-border bg-muted/30 p-3">
								<div class="space-y-3">
									<div class="flex items-start gap-4">
										<div class="w-16 shrink-0 text-sm font-semibold text-muted-foreground">
											Start
										</div>
										<div class="text-sm">{startDateDisplay}</div>
									</div>
									<div class="border-t border-border/50"></div>
									<div class="flex items-start gap-4">
										<div class="w-16 shrink-0 text-sm font-semibold text-muted-foreground">
											End
										</div>
										<div class="text-sm">{endDateDisplay}</div>
									</div>
								</div>
							</div>
						{/if}
					</div>

					<div class="grid gap-2">
						<Label for="timezone">Timezone</Label>
						<Popover.Root bind:open={timezoneOpen}>
							<Popover.Trigger bind:ref={triggerRef}>
								{#snippet child({ props })}
									<Button
										variant="outline"
										class="w-full justify-between"
										{...props}
										role="combobox"
										aria-expanded={timezoneOpen}
									>
										<div class="flex items-center">
											<GlobeIcon class="mr-2 size-4" />
											<span>{getTimezoneDisplayName(selectedTimezone)}</span>
										</div>
										<ChevronsUpDown class="opacity-50" />
									</Button>
								{/snippet}
							</Popover.Trigger>
							<Popover.Content class="w-[300px] p-0">
								<Command.Root>
									<Command.Input placeholder="Search timezone..." />
									<Command.List class="max-h-[300px]">
										<Command.Empty>No timezone found.</Command.Empty>

										<Command.Group heading="Browser Timezone">
											<Command.Item
												value={browserTimeZone}
												onSelect={() => {
													selectedTimezone = browserTimeZone;
													closeAndFocusTrigger();
												}}
											>
												<Check
													class={cn(
														'mr-2',
														selectedTimezone !== browserTimeZone && 'text-transparent'
													)}
												/>
												<span>{browserTimeZone}</span>
												<span class="ml-auto text-xs text-muted-foreground">(Browser)</span>
											</Command.Item>
										</Command.Group>

										<Command.Separator />

										<Command.Group heading="All Timezones">
											{#each data.timezones || [] as tz (tz.name)}
												<Command.Item
													value={tz.name}
													onSelect={() => {
														selectedTimezone = tz.name;
														closeAndFocusTrigger();
													}}
												>
													<Check
														class={cn('mr-2', selectedTimezone !== tz.name && 'text-transparent')}
													/>
													<span>{tz.name}</span>
													<span class="ml-auto text-xs text-muted-foreground">
														({tz.abbrev}, UTC{tz.utc_offset.hours >= 0 ? '+' : ''}{tz.utc_offset
															.hours})
													</span>
												</Command.Item>
											{/each}
										</Command.Group>
									</Command.List>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
					</div>

					<div class="grid gap-2">
						<Label for="name">Period Name</Label>
						<Input
							id="name"
							name="name"
							placeholder="e.g. January 2023, Q1 2023"
							bind:value={name}
							oninput={handleNameInput}
							required
						/>
						{#if !userEditedName}
							<p class="text-xs text-muted-foreground">
								Name is automatically generated from the date range. You can edit it if needed.
							</p>
						{/if}
					</div>
				</div>
			</CardContent>
			<CardFooter class="flex justify-between">
				<Button variant="outline" href="/private/payroll">Cancel</Button>
				<Button type="submit" disabled={submitting || !isFormValid}>
					{submitting ? 'Creating...' : 'Create Period'}
				</Button>
			</CardFooter>
		</Card>
	</form>
{/snippet}

<PageContainer
	title="Create Payroll Period"
	description="Define a new time period for payroll calculations"
	{content}
/>
