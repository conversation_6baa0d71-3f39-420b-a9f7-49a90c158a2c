<script lang="ts">
	import { Plus, MapPin, Edit } from '@lucide/svelte';
	import { Clock, Users } from '@lucide/svelte';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { formatCurrency, formatDateTime } from '$lib/utils/formatters';
	import { Badge } from '$lib/components/ui/badge';
	import { Button } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import type { PageData } from './$types';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	// Helper function to check if an entry exists
	function hasEntry(eventId: string): boolean {
		return data.payrollEntries.some((entry) => entry.entry_for_event_id === eventId);
	}

	// Get entry if it exists
	function getEntry(eventId: string) {
		return data.payrollEntries.find((entry) => entry.entry_for_event_id === eventId);
	}

	// Format location similar to events-per-payee page
	function formatLocation(event: any): string {
		// Ensure landmark is always populated (from direct landmark or space.landmark)
		const directLandmark = event.landmark;
		const spaceLandmark = event.space?.landmark;
		const landmarkData = directLandmark || spaceLandmark;

		const locationParts: string[] = [];

		// Add space name if available
		if (event.space?.name_short) {
			locationParts.push(
				typeof event.space.name_short === 'object'
					? getLocalizedText(event.space.name_short as LocalizedText)
					: event.space.name_short
			);
		}

		// Add landmark name if available
		if (landmarkData?.title_short) {
			locationParts.push(
				typeof landmarkData.title_short === 'object'
					? getLocalizedText(landmarkData.title_short as LocalizedText)
					: landmarkData.title_short
			);
		}

		// Add address if available
		if (landmarkData?.address?.auto_normalized_address_local) {
			locationParts.push(landmarkData.address.auto_normalized_address_local);
		}

		return locationParts.length > 0 ? locationParts.join(', ') : '--';
	}

	// Get event title with localization support
	function getEventTitle(event: any): string {
		if (event.metadata?.auto_final_title) {
			return typeof event.metadata.auto_final_title === 'object'
				? getLocalizedText(event.metadata.auto_final_title as LocalizedText)
				: event.metadata.auto_final_title;
		}
		return event.title || 'Untitled Event';
	}

	// Get event subtitle with localization support
	function getEventSubtitle(event: any): string | null {
		if (!event.metadata || !event.metadata.auto_final_subtitle) {
			return null;
		}

		return typeof event.metadata.auto_final_subtitle === 'object'
			? getLocalizedText(event.metadata.auto_final_subtitle as LocalizedText)
			: event.metadata.auto_final_subtitle;
	}

	// Calculate event duration in minutes
	function calculateDuration(startAt: string, endAt: string | null): number | null {
		if (!endAt) return null;
		return Math.round((new Date(endAt).getTime() - new Date(startAt).getTime()) / (1000 * 60));
	}

	// Get the role of this profile in the event
	function getProfileRoles(event: any): any[] {
		if (!event.metadata?.metadata_wikipage || !Array.isArray(event.metadata.metadata_wikipage)) {
			return [];
		}

		return event.metadata.metadata_wikipage.filter(
			(mw: any) => mw.wikipage?.decorating_profile_id === data.profileId
		);
	}
</script>

<Card>
	<CardHeader>
		<CardTitle>Events in this Period</CardTitle>
		<CardDescription>
			{data.profileSpecificEvents?.length || 0} events found for this payee
		</CardDescription>
	</CardHeader>
	<CardContent>
		{#if data.profileSpecificEvents?.length}
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Event</TableHead>
						<TableHead>Role</TableHead>
						<TableHead>Date & Time</TableHead>
						<TableHead>Location</TableHead>
						<TableHead class="text-right">Check-ins</TableHead>
						<TableHead class="text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each data.profileSpecificEvents as event}
						<TableRow>
							<TableCell class="font-medium">
								<div>{getEventTitle(event)}</div>
								{@const subtitle = getEventSubtitle(event)}
								{#if subtitle}
									<div class="text-xs text-muted-foreground">{subtitle}</div>
								{/if}
								<Badge variant="outline" class="mt-1">{event.kind}</Badge>
							</TableCell>
							<TableCell>
								{#each getProfileRoles(event) as wikipage}
									<Badge variant="outline">{wikipage.relation || 'Participant'}</Badge>
								{/each}
							</TableCell>
							<TableCell>
								{@const duration = calculateDuration(event.start_at, event.auto_end_at)}
								<div>{formatDateTime(event.start_at)}</div>
								{#if duration}
									<div class="text-xs text-muted-foreground">{duration} minutes</div>
								{/if}
							</TableCell>
							<TableCell>
								<div class="flex items-center gap-1">
									<MapPin class="h-3 w-3 text-muted-foreground" />
									<span>{formatLocation(event)}</span>
								</div>
							</TableCell>
							<TableCell class="text-right">
								{event.checked_in_consumers_count || 0}
							</TableCell>
							<TableCell class="text-right">
								{#if hasEntry(event.id)}
									{@const entry = getEntry(event.id)}
									<div class="flex flex-col items-end">
										<span class="font-bold">
											{entry &&
												formatCurrency(
													entry.final_amount,
													data.payrollProfile?.currency_code || 'USD'
												)}
										</span>
										<Button
											variant="outline"
											size="sm"
											href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}/event/{event.id}"
										>
											View Calculation
										</Button>
									</div>
								{:else}
									<Button
										variant="outline"
										size="sm"
										href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}/event/{event.id}"
									>
										<Edit class="mr-2 h-4 w-4" />
										Calculate
									</Button>
								{/if}
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		{:else}
			<div class="p-6 text-center text-muted-foreground">
				No events found for this payee in the current period.
			</div>
		{/if}
	</CardContent>
</Card>
