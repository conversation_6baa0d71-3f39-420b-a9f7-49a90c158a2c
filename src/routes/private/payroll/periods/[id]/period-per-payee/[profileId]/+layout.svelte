<script lang="ts">
	import { PageContainer } from '$lib/components/layout';
	import PayrollNav from '../../components/PayrollNav.svelte';
	import { Button } from '$lib/components/ui/button';
	import { formatCurrency, formatDate } from '$lib/utils/formatters';
	import { page } from '$app/state';
	import type { LayoutData } from './$types';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { LocalizedText } from '$lib/utils/localization';

	interface Props {
		data: LayoutData;
		children?: () => any;
	}

	let { data, children }: Props = $props();

	// Calculate duration in days
	const startDate = $derived(data.period?.start_at ? new Date(data.period.start_at) : null);
	const endDate = $derived(data.period?.end_at ? new Date(data.period.end_at) : null);

	const durationInDays = $derived(
		startDate && endDate
			? Math.ceil(Math.abs(endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
			: 0
	);

	const formattedStartDate = $derived(startDate ? formatDate(startDate) : '');
	const formattedEndDate = $derived(endDate ? formatDate(endDate) : '');

	const periodDateRange = $derived(
		startDate && endDate
			? `${formattedStartDate} - ${formattedEndDate} (${durationInDays} days)`
			: 'No date range available'
	);

	// Format the profile name for display with localization
	const profileName = $derived(
		data.payrollProfile?.profile?.given_name
			? `${getLocalizedText(data.payrollProfile.profile.given_name as LocalizedText)} ${getLocalizedText(data.payrollProfile.profile.family_name as LocalizedText)}`
			: data.payrollProfile?.title || 'Payee'
	);

	// Use $page store to reactively track the current path
	const currentPath = $derived(page.url.pathname);

	// Derive the path parts reactively
	const pathParts = $derived(currentPath.split('/'));
	const lastSegment = $derived(pathParts[pathParts.length - 1]);
	const secondLastSegment = $derived(pathParts[pathParts.length - 2]);

	// Simplified active tab check - directly check for the segment name in the URL
	const isActiveTab = (tab: string): boolean => {
		return lastSegment === tab || secondLastSegment === tab;
	};
</script>

<PageContainer
	title={`${data.period?.title || 'Payroll Period'} - ${profileName}`}
	description={periodDateRange}
>
	{#snippet actions()}
		<div></div>
	{/snippet}

	{#snippet content()}
		<div>
			<PayrollNav periodId={data.periodId} payrollProfiles={data.payrollProfiles} />
			<div class="space-y-6">
				<div class="flex items-center justify-between">
					<div class="flex items-center gap-2">
						<h1 class="text-2xl font-bold">
							Period Details for {profileName}
						</h1>
					</div>

					<div class="flex gap-2">
						<Button
							variant="outline"
							href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}/event"
							class={isActiveTab('event') ? 'bg-primary text-primary-foreground' : ''}
						>
							Events
						</Button>
						<Button
							variant="outline"
							href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}/product"
							class={isActiveTab('product') ? 'bg-primary text-primary-foreground' : ''}
						>
							Products
						</Button>
						<Button
							variant="outline"
							href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}/profile"
							class={isActiveTab('profile') ? 'bg-primary text-primary-foreground' : ''}
						>
							Profile
						</Button>
					</div>
				</div>

				{@render children?.()}

				<div class="mt-8 flex items-center justify-between">
					<div>
						<h2 class="text-xl font-bold">Total Calculated Pay</h2>
						<p class="text-sm text-muted-foreground">
							Sum of all entries for this payee in this period
						</p>
					</div>

					<div class="text-2xl font-bold">
						{formatCurrency(
							data.payrollEntries.reduce((sum, entry) => sum + (entry.final_amount || 0), 0),
							data.payrollProfile?.currency_code || 'USD'
						)}
					</div>
				</div>
			</div>
		</div>
	{/snippet}
</PageContainer>
