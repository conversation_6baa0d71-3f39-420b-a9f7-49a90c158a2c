import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, parent }) => {
	try {
		const parentData = await parent();

		// This page doesn't need any specific data beyond what's in the layout
		return {
			...parentData
		};
	} catch (err) {
		console.error('Error loading profile page data:', err);
		throw error(500, 'Failed to load profile data');
	}
};
