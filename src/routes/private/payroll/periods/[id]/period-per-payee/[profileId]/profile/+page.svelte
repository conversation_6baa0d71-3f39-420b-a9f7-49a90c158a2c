<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select/index.js';
	import { formatCurrency, formatDate, formatDateTime } from '$lib/utils/formatters';
	import { ArrowLeft, Calculator, Save, X, AlertTriangle, Trash2, Plus } from '@lucide/svelte';
	import { invalidate } from '$app/navigation';
	import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
	import type { PageData } from './$types';
	import { PageContainer } from '$lib/components/layout';
	import PayrollNav from '../../../components/PayrollNav.svelte';

	interface Props {
		data: PageData;
		form?: any;
	}

	let { data, form }: Props = $props();

	// State for form
	let selectedFormulaId = $state(
		data.existingEntry?.payroll_formula_id || data.assignedFormula?.id || ''
	);
	let formulaAmount = $state(data.existingEntry?.formula_amount || 0);
	let adjustmentAmount = $state(data.existingEntry?.adjustment_amount || 0);
	let adjustmentReason = $state(data.existingEntry?.adjustment_reason || '');
	let calculationComplete = $state(false);
	let showDeleteConfirm = $state(false);
	let errorMessage = $state('');

	// Get the selected formula
	const selectedFormula = $derived(data.availableFormulas.find((f) => f.id === selectedFormulaId));

	// Format the trigger content
	const triggerContent = $derived(selectedFormula?.name ?? 'Select a formula');

	// Calculate the total amount
	const totalAmount = $derived(formulaAmount + adjustmentAmount);

	// Helper function to check if an entry exists
	function hasEntry(): boolean {
		return data.payrollEntries.some((entry) => entry.entry_for_profile_id === data.profileId);
	}

	// Get entry if it exists
	function getEntry() {
		return data.payrollEntries.find((entry) => entry.entry_for_profile_id === data.profileId);
	}

	// Function to run the formula calculation
	function calculateFormula() {
		if (!selectedFormulaId) {
			errorMessage = 'Please select a formula first';
			return;
		}

		try {
			const formula = data.availableFormulas.find((f) => f.id === selectedFormulaId);
			if (!formula) {
				errorMessage = 'Selected formula not found';
				return;
			}

			// Create function from formula JS
			// eslint-disable-next-line no-new-func
			const formulaFunction = new Function('inputs', formula.formula_js);

			// Run the formula with our inputs
			const result = formulaFunction(data.formulaInputs);

			if (typeof result !== 'number' || isNaN(result)) {
				errorMessage = 'Formula did not return a valid number';
				return;
			}

			// Update the formula amount
			formulaAmount = parseFloat(result.toFixed(2));
			calculationComplete = true;
			errorMessage = '';
		} catch (err) {
			console.error('Error running formula:', err);
			errorMessage = `Error calculating formula: ${err instanceof Error ? err.message : 'Unknown error'}`;
		}
	}

	// Format value for display
	function formatValue(value: any): string {
		if (value === null || value === undefined) return 'N/A';
		if (value instanceof Date) return formatDateTime(value);
		if (typeof value === 'string' && (value.includes('T') || value.match(/^\d{4}-\d{2}-\d{2}/))) {
			// Check if it's likely a date string
			try {
				return formatDateTime(value);
			} catch (e) {
				return value;
			}
		}
		if (typeof value === 'number') return value.toString();
		if (typeof value === 'boolean') return value ? 'Yes' : 'No';
		if (typeof value === 'object') return JSON.stringify(value);
		return String(value);
	}

	// Success handler for form submission
	function handleSuccess() {
		invalidate('app:payrollEntries');
		window.location.href = `/private/payroll/periods/${data.periodId}/period-per-payee/${data.profileId}`;
	}

	// Input handlers
	function handleAdjustmentAmountChange(e: Event) {
		const target = e.target as HTMLInputElement;
		adjustmentAmount = parseFloat(target.value) || 0;
	}

	function handleAdjustmentReasonChange(e: Event) {
		const target = e.target as HTMLTextAreaElement;
		adjustmentReason = target.value;
	}
</script>

{#snippet actions()}
	<a href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}">
		<Button variant="outline" size="icon">
			<ArrowLeft class="h-4 w-4" />
		</Button>
	</a>
{/snippet}

{#snippet content()}
	<div class="space-y-6">
		<PayrollNav periodId={data.periodId} payrollProfiles={data.payrollProfiles} />

		<div class="flex items-center justify-between">
			<h1 class="text-2xl font-bold">
				{data.existingEntry ? 'Edit' : 'New'} Profile Calculation
			</h1>

			{#if data.existingEntry}
				<div class="flex items-center gap-2">
					{#if showDeleteConfirm}
						<div class="flex items-center gap-2">
							<span class="text-sm text-destructive">Confirm deletion?</span>
							<form
								method="POST"
								action="?/delete"
								use:enhance={() => {
									return ({ result }) => {
										if (result.type === 'success') {
											handleSuccess();
										}
									};
								}}
							>
								<Button type="submit" variant="destructive" size="sm">Yes, Delete</Button>
							</form>
							<Button
								type="button"
								variant="outline"
								size="sm"
								onclick={() => (showDeleteConfirm = false)}
							>
								Cancel
							</Button>
						</div>
					{:else}
						<Button variant="destructive" size="sm" onclick={() => (showDeleteConfirm = true)}>
							<Trash2 class="mr-2 h-4 w-4" />
							Delete Entry
						</Button>
					{/if}
				</div>
			{/if}
		</div>

		<div class="grid grid-cols-1 gap-6 md:grid-cols-3">
			<div class="space-y-6 md:col-span-2">
				<Card>
					<CardHeader>
						<CardTitle>Profile Details</CardTitle>
						<CardDescription>Information about the profile for this calculation</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-4">
							<div>
								<h3 class="text-lg font-medium">{data.payrollProfile.title}</h3>
								<p class="text-muted-foreground">
									Enrolled since: {formatDate(data.payrollProfile.enrolled_at)}
								</p>
							</div>

							<div class="grid grid-cols-2 gap-4">
								<div>
									<p class="text-sm font-medium text-muted-foreground">Level</p>
									<p>{data.payrollProfile.level || 'N/A'}</p>
								</div>
								<div>
									<p class="text-sm font-medium text-muted-foreground">Currency</p>
									<p>{data.payrollProfile.currency_code}</p>
								</div>
								<div>
									<p class="text-sm font-medium text-muted-foreground">Period</p>
									<p>
										{formatDate(data.period.start_at)} - {formatDate(data.period.end_at)}
									</p>
								</div>
								<div>
									<p class="text-sm font-medium text-muted-foreground">Period Length</p>
									<p>{data.formulaInputs.period_days} days</p>
								</div>
							</div>

							<div class="mt-4">
								<h4 class="font-medium">Period Metrics</h4>
								<div class="mt-2 grid grid-cols-2 gap-4">
									<div>
										<p class="text-sm font-medium text-muted-foreground">Events Hosted</p>
										<p>{data.eventMetrics.eventsHosted}</p>
									</div>
									<div>
										<p class="text-sm font-medium text-muted-foreground">Events Assisted</p>
										<p>{data.eventMetrics.eventsAssisted}</p>
									</div>
									<div>
										<p class="text-sm font-medium text-muted-foreground">Events Attended</p>
										<p>{data.eventMetrics.eventsAttended}</p>
									</div>
									<div>
										<p class="text-sm font-medium text-muted-foreground">Product Sales</p>
										<p>{data.salesMetrics.periodSalesCount}</p>
									</div>
									<div>
										<p class="text-sm font-medium text-muted-foreground">Sales Revenue</p>
										<p>
											{formatCurrency(
												data.salesMetrics.periodSalesRevenue,
												data.payrollProfile.currency_code
											)}
										</p>
									</div>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>

				<form
					method="POST"
					action="?/calculate"
					use:enhance={() => {
						return ({ result }) => {
							if (result.type === 'success') {
								handleSuccess();
							}
						};
					}}
				>
					<Card>
						<CardHeader>
							<CardTitle>Pay Calculation</CardTitle>
							<CardDescription
								>Select a formula and calculate the pay for this profile</CardDescription
							>
						</CardHeader>
						<CardContent class="space-y-4">
							{#if errorMessage}
								<Alert variant="destructive">
									<AlertTriangle class="h-4 w-4" />
									<AlertTitle>Error</AlertTitle>
									<AlertDescription>{errorMessage}</AlertDescription>
								</Alert>
							{/if}

							<div class="space-y-2">
								<Label for="formulaId">Pay Formula</Label>
								<Select.Root
									type="single"
									name="formulaId"
									bind:value={selectedFormulaId}
									onValueChange={() => {
										calculationComplete = false;
									}}
								>
									<Select.Trigger>
										{triggerContent}
									</Select.Trigger>
									<Select.Content>
										<Select.Group>
											{#each data.availableFormulas as formula (formula.id)}
												<Select.Item value={formula.id} label={formula.name}
													>{formula.name}</Select.Item
												>
											{/each}
										</Select.Group>
									</Select.Content>
								</Select.Root>
								{#if selectedFormula}
									<p class="text-sm text-muted-foreground">{selectedFormula.description}</p>
								{/if}
							</div>

							<div class="flex justify-end">
								<Button type="button" variant="outline" onclick={calculateFormula}>
									<Calculator class="mr-2 h-4 w-4" />
									Calculate Amount
								</Button>
							</div>

							<div class="space-y-2">
								<Label for="formulaAmount">Formula Amount</Label>
								<Input
									type="number"
									id="formulaAmount"
									name="formulaAmount"
									value={formulaAmount}
									step="0.01"
									readonly
								/>
								<p class="text-sm text-muted-foreground">
									Amount calculated by the formula (not editable)
								</p>
							</div>

							<div class="space-y-2">
								<Label for="adjustmentAmount">Adjustment Amount</Label>
								<Input
									type="number"
									id="adjustmentAmount"
									name="adjustmentAmount"
									value={adjustmentAmount}
									step="0.01"
									on:input={handleAdjustmentAmountChange}
								/>
								<p class="text-sm text-muted-foreground">
									Optional adjustment to the calculated amount (positive or negative)
								</p>
							</div>

							<div class="space-y-2">
								<Label for="adjustmentReason">Adjustment Reason</Label>
								<Textarea
									id="adjustmentReason"
									name="adjustmentReason"
									value={adjustmentReason}
									on:input={handleAdjustmentReasonChange}
									placeholder="Reason for adjustment (required if adjustment is not zero)"
									disabled={adjustmentAmount === 0}
								/>
							</div>

							<div class="space-y-2">
								<Label>Final Amount</Label>
								<div class="text-xl font-bold">
									{formatCurrency(totalAmount, data.payrollProfile.currency_code)}
								</div>
								<p class="text-sm text-muted-foreground">Formula amount + adjustment amount</p>
							</div>

							<input
								type="hidden"
								name="formulaInputs"
								value={JSON.stringify(data.formulaInputs)}
							/>
						</CardContent>
						<CardFooter class="flex justify-between">
							<a href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}">
								<Button variant="outline" type="button">
									<X class="mr-2 h-4 w-4" />
									Cancel
								</Button>
							</a>
							<Button type="submit" disabled={!calculationComplete}>
								<Save class="mr-2 h-4 w-4" />
								{data.existingEntry ? 'Update' : 'Save'} Calculation
							</Button>
						</CardFooter>
					</Card>
				</form>
			</div>

			<div class="space-y-6">
				<Card>
					<CardHeader>
						<CardTitle>Formula Inputs</CardTitle>
						<CardDescription>Variables available for the calculation formula</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-4">
							{#each Object.entries(data.formulaInputs) as [key, value]}
								<div class="border-b pb-2">
									<p class="text-sm font-medium">{key}</p>
									<p class="text-sm">{formatValue(value)}</p>
								</div>
							{/each}
						</div>
					</CardContent>
				</Card>

				{#if selectedFormula}
					<Card>
						<CardHeader>
							<CardTitle>Formula Code</CardTitle>
							<CardDescription>The JavaScript code that runs for this calculation</CardDescription>
						</CardHeader>
						<CardContent>
							<pre class="max-h-80 overflow-auto rounded-md bg-muted p-4 text-xs">
								{selectedFormula.formula_js}
							</pre>
						</CardContent>
					</Card>
				{/if}
			</div>
		</div>
	</div>
{/snippet}

<Card>
	<CardHeader>
		<CardTitle>Payroll Information</CardTitle>
		<CardDescription>
			Period: {formatDate(data.period.start_at)} to {formatDate(data.period.end_at)}
		</CardDescription>
	</CardHeader>
	<CardContent>
		<div class="grid grid-cols-2 gap-4 md:grid-cols-4">
			<div>
				<p class="text-sm font-medium text-muted-foreground">Payee Title</p>
				<p>{data.payrollProfile?.title || 'N/A'}</p>
			</div>
			<div>
				<p class="text-sm font-medium text-muted-foreground">Level</p>
				<p>{data.payrollProfile?.level || 'N/A'}</p>
			</div>
			<div>
				<p class="text-sm font-medium text-muted-foreground">Enrolled Since</p>
				<p>
					{data.payrollProfile?.enrolled_at ? formatDate(data.payrollProfile.enrolled_at) : 'N/A'}
				</p>
			</div>
			<div>
				<p class="text-sm font-medium text-muted-foreground">Currency</p>
				<p>{data.payrollProfile?.currency_code || 'N/A'}</p>
			</div>
		</div>
	</CardContent>
</Card>

<Card class="mt-6">
	<CardHeader>
		<CardTitle>Profile Salary</CardTitle>
		<CardDescription>Base salary and other profile-based calculations</CardDescription>
	</CardHeader>
	<CardContent>
		<div class="rounded-lg border p-4">
			<div class="flex flex-col justify-between gap-4 md:flex-row md:items-center">
				<div>
					<h3 class="font-medium">Base Profile Calculation</h3>
					<p class="text-sm text-muted-foreground">
						Calculate salary based on profile metrics across this entire period
					</p>
				</div>

				<div>
					{#if hasEntry()}
						{@const entry = getEntry()}
						<div class="flex flex-col items-end">
							<span class="text-lg font-bold"
								>{entry &&
									formatCurrency(
										entry.final_amount,
										data.payrollProfile?.currency_code || 'USD'
									)}</span
							>
							<Button
								variant="outline"
								size="sm"
								href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}/profile/{data.profileId}"
							>
								View Calculation
							</Button>
						</div>
					{:else}
						<Button
							href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}/profile/{data.profileId}"
						>
							<Plus class="mr-2 h-4 w-4" />
							Calculate Base Pay
						</Button>
					{/if}
				</div>
			</div>
		</div>
	</CardContent>
</Card>

<PageContainer
	title="Profile Calculation"
	description="Calculate base payroll for profile characteristics"
	{actions}
	{content}
/>
