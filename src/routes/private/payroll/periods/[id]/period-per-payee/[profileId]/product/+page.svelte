<script lang="ts">
	import { Plus, DollarSign } from '@lucide/svelte';
	import { formatCurrency } from '$lib/utils/formatters';
	import { Badge } from '$lib/components/ui/badge';
	import { Button } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import type { PageData } from './$types';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	// Helper function to check if an entry exists
	function hasEntry(productId: string): boolean {
		return data.payrollEntries.some((entry) => entry.entry_for_product_id === productId);
	}

	// Get entry if it exists
	function getEntry(productId: string) {
		return data.payrollEntries.find((entry) => entry.entry_for_product_id === productId);
	}
</script>

<Card>
	<CardHeader>
		<CardTitle>Products in this Period</CardTitle>
		<CardDescription>
			{data.profileSpecificProducts?.length || 0} products with sales in this period
		</CardDescription>
	</CardHeader>
	<CardContent>
		{#if data.profileSpecificProducts?.length}
			<div class="space-y-4">
				{#each data.profileSpecificProducts as product}
					{#if product.period_sales_count > 0}
						<div
							class="flex flex-col justify-between gap-4 rounded-lg border p-4 md:flex-row md:items-center"
						>
							<div class="flex-1">
								<div class="flex flex-col gap-2 md:flex-row md:items-center">
									<h3 class="font-medium">{product.title}</h3>
									<Badge variant="outline">{product.kind}</Badge>
								</div>
								<div class="mt-1 flex items-center gap-2 text-sm text-muted-foreground">
									<span>Sales: {product.period_sales_count}</span>
								</div>
								<div class="flex items-center gap-2 text-sm text-muted-foreground">
									<DollarSign class="h-3 w-3" />
									<span
										>Revenue: {formatCurrency(
											product.period_sales_revenue,
											data.payrollProfile?.currency_code || 'USD'
										)}</span
									>
								</div>
							</div>

							<div>
								{#if hasEntry(product.id)}
									{@const entry = getEntry(product.id)}
									<div class="flex flex-col items-end">
										<span class="text-lg font-bold"
											>{entry &&
												formatCurrency(
													entry.final_amount,
													data.payrollProfile?.currency_code || 'USD'
												)}</span
										>
										<Button
											variant="outline"
											size="sm"
											href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}/product/{product.id}"
										>
											View Calculation
										</Button>
									</div>
								{:else}
									<Button
										href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}/product/{product.id}"
									>
										<Plus class="mr-2 h-4 w-4" />
										Calculate Pay
									</Button>
								{/if}
							</div>
						</div>
					{/if}
				{/each}
			</div>
		{:else}
			<div class="p-6 text-center text-muted-foreground">
				No products with sales found for this payee in the current period.
			</div>
		{/if}
	</CardContent>
</Card>
