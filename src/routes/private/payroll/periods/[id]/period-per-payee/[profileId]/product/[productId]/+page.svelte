<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select/index.js';
	import { formatCurrency, formatDate, formatDateTime } from '$lib/utils/formatters';
	import {
		ArrowLeft,
		Calculator,
		Save,
		X,
		AlertTriangle,
		Trash2,
		ShoppingCart
	} from '@lucide/svelte';
	import { invalidate } from '$app/navigation';
	import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import type { PageData } from './$types';
	import { PageContainer } from '$lib/components/layout';
	import PayrollNav from '../../../../components/PayrollNav.svelte';

	interface Props {
		data: PageData;
		form?: any;
	}

	let { data, form }: Props = $props();

	// State for form
	let selectedFormulaId = $state(
		data.existingEntry?.payroll_formula_id || data.assignedFormula?.id || ''
	);
	let formulaAmount = $state(data.existingEntry?.formula_amount || 0);
	let adjustmentAmount = $state(data.existingEntry?.adjustment_amount || 0);
	let adjustmentReason = $state(data.existingEntry?.adjustment_reason || '');
	let calculationComplete = $state(false);
	let showDeleteConfirm = $state(false);
	let errorMessage = $state('');
	let showSales = $state(false);

	// Get the selected formula
	const selectedFormula = $derived(data.availableFormulas.find((f) => f.id === selectedFormulaId));

	// Format the trigger content
	const triggerContent = $derived(selectedFormula?.name ?? 'Select a formula');

	// Calculate the total amount
	const totalAmount = $derived(formulaAmount + adjustmentAmount);

	// Function to run the formula calculation
	function calculateFormula() {
		if (!selectedFormulaId) {
			errorMessage = 'Please select a formula first';
			return;
		}

		try {
			const formula = data.availableFormulas.find((f) => f.id === selectedFormulaId);
			if (!formula) {
				errorMessage = 'Selected formula not found';
				return;
			}

			// Create function from formula JS
			// eslint-disable-next-line no-new-func
			const formulaFunction = new Function('inputs', formula.formula_js);

			// Run the formula with our inputs
			const result = formulaFunction(data.formulaInputs);

			if (typeof result !== 'number' || isNaN(result)) {
				errorMessage = 'Formula did not return a valid number';
				return;
			}

			// Update the formula amount
			formulaAmount = parseFloat(result.toFixed(2));
			calculationComplete = true;
			errorMessage = '';
		} catch (err) {
			console.error('Error running formula:', err);
			errorMessage = `Error calculating formula: ${err instanceof Error ? err.message : 'Unknown error'}`;
		}
	}

	// Format value for display
	function formatValue(value: any): string {
		if (value === null || value === undefined) return 'N/A';
		if (value instanceof Date) return formatDateTime(value);
		if (typeof value === 'string' && (value.includes('T') || value.match(/^\d{4}-\d{2}-\d{2}/))) {
			// Check if it's likely a date string
			try {
				return formatDateTime(value);
			} catch (e) {
				return value;
			}
		}
		if (typeof value === 'number') return value.toString();
		if (typeof value === 'boolean') return value ? 'Yes' : 'No';
		if (typeof value === 'object') return JSON.stringify(value);
		return String(value);
	}

	// Success handler for form submission
	function handleSuccess() {
		invalidate('app:payrollEntries');
		window.location.href = `/private/payroll/periods/${data.periodId}/period-per-payee/${data.profileId}`;
	}

	// Toggle sales details
	function toggleSales() {
		showSales = !showSales;
	}

	// Input handlers
	function handleAdjustmentAmountChange(e: Event) {
		const target = e.target as HTMLInputElement;
		adjustmentAmount = parseFloat(target.value) || 0;
	}

	function handleAdjustmentReasonChange(e: Event) {
		const target = e.target as HTMLTextAreaElement;
		adjustmentReason = target.value;
	}
</script>

{#snippet actions()}
	<a href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}">
		<Button variant="outline" size="icon">
			<ArrowLeft class="h-4 w-4" />
		</Button>
	</a>
{/snippet}

{#snippet content()}
	<div class="space-y-6">
		<PayrollNav periodId={data.periodId} payrollProfiles={data.payrollProfiles} />

		<div class="flex items-center justify-between">
			<h1 class="text-2xl font-bold">
				{data.existingEntry ? 'Edit' : 'New'} Product Calculation
			</h1>

			{#if data.existingEntry}
				<div class="flex items-center gap-2">
					{#if showDeleteConfirm}
						<div class="flex items-center gap-2">
							<span class="text-sm text-destructive">Confirm deletion?</span>
							<form
								method="POST"
								action="?/delete"
								use:enhance={() => {
									return ({ result }) => {
										if (result.type === 'success') {
											handleSuccess();
										}
									};
								}}
							>
								<Button type="submit" variant="destructive" size="sm">Yes, Delete</Button>
							</form>
							<Button
								type="button"
								variant="outline"
								size="sm"
								onclick={() => (showDeleteConfirm = false)}
							>
								Cancel
							</Button>
						</div>
					{:else}
						<Button variant="destructive" size="sm" onclick={() => (showDeleteConfirm = true)}>
							<Trash2 class="mr-2 h-4 w-4" />
							Delete Entry
						</Button>
					{/if}
				</div>
			{/if}
		</div>

		<div class="grid grid-cols-1 gap-6 md:grid-cols-3">
			<div class="space-y-6 md:col-span-2">
				<Card>
					<CardHeader>
						<CardTitle>Product Details</CardTitle>
						<CardDescription>Information about the product for this calculation</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-4">
							<div>
								<h3 class="text-lg font-medium">{data.product.title}</h3>
								<p class="text-muted-foreground">
									Created: {formatDateTime(data.product.created_at)}
								</p>
							</div>

							<div class="grid grid-cols-2 gap-4">
								<div>
									<p class="text-sm font-medium text-muted-foreground">Product Kind</p>
									<p>{data.product.kind}</p>
								</div>
								<div>
									<p class="text-sm font-medium text-muted-foreground">Is Creator</p>
									<p>{data.product.profile_id === data.profileId ? 'Yes' : 'No'}</p>
								</div>
								<div>
									<p class="text-sm font-medium text-muted-foreground">Period Sales</p>
									<p>{data.product.period_sales_count}</p>
								</div>
								<div>
									<p class="text-sm font-medium text-muted-foreground">Period Revenue</p>
									<p>
										{formatCurrency(
											data.product.period_sales_revenue,
											data.payrollProfile.currency_code
										)}
									</p>
								</div>
							</div>

							{#if data.periodSales && data.periodSales.length > 0}
								<div>
									<Button variant="outline" size="sm" onclick={toggleSales}>
										<ShoppingCart class="mr-2 h-4 w-4" />
										{showSales ? 'Hide' : 'Show'} Sales Details
									</Button>
								</div>

								{#if showSales}
									<Card>
										<CardHeader>
											<CardTitle>Sales in this Period</CardTitle>
										</CardHeader>
										<CardContent>
											<Table.Root>
												<Table.Header>
													<Table.Row>
														<Table.Head>Date</Table.Head>
														<Table.Head>Amount</Table.Head>
														<Table.Head>Status</Table.Head>
													</Table.Row>
												</Table.Header>
												<Table.Body>
													{#each data.periodSales as sale}
														<Table.Row>
															<Table.Cell>{formatDateTime(sale.created_at)}</Table.Cell>
															<Table.Cell>
																{formatCurrency(sale.amount, data.payrollProfile.currency_code)}
															</Table.Cell>
															<Table.Cell>{sale.status}</Table.Cell>
														</Table.Row>
													{/each}
												</Table.Body>
											</Table.Root>
										</CardContent>
									</Card>
								{/if}
							{/if}
						</div>
					</CardContent>
				</Card>

				<form
					method="POST"
					action="?/calculate"
					use:enhance={() => {
						return ({ result }) => {
							if (result.type === 'success') {
								handleSuccess();
							}
						};
					}}
				>
					<Card>
						<CardHeader>
							<CardTitle>Pay Calculation</CardTitle>
							<CardDescription>
								Select a formula and calculate the pay for this product
							</CardDescription>
						</CardHeader>
						<CardContent class="space-y-4">
							{#if errorMessage}
								<Alert variant="destructive">
									<AlertTriangle class="h-4 w-4" />
									<AlertTitle>Error</AlertTitle>
									<AlertDescription>{errorMessage}</AlertDescription>
								</Alert>
							{/if}

							<div class="space-y-2">
								<Label for="formulaId">Pay Formula</Label>
								<Select.Root
									type="single"
									name="formulaId"
									bind:value={selectedFormulaId}
									onValueChange={() => {
										calculationComplete = false;
									}}
								>
									<Select.Trigger>
										{triggerContent}
									</Select.Trigger>
									<Select.Content>
										<Select.Group>
											{#each data.availableFormulas as formula (formula.id)}
												<Select.Item value={formula.id} label={formula.name}
													>{formula.name}</Select.Item
												>
											{/each}
										</Select.Group>
									</Select.Content>
								</Select.Root>
								{#if selectedFormula}
									<p class="text-sm text-muted-foreground">{selectedFormula.description}</p>
								{/if}
							</div>

							<div class="flex justify-end">
								<Button type="button" variant="outline" onclick={calculateFormula}>
									<Calculator class="mr-2 h-4 w-4" />
									Calculate Amount
								</Button>
							</div>

							<div class="space-y-2">
								<Label for="formulaAmount">Formula Amount</Label>
								<Input
									type="number"
									id="formulaAmount"
									name="formulaAmount"
									value={formulaAmount}
									step="0.01"
									readonly
								/>
								<p class="text-sm text-muted-foreground">
									Amount calculated by the formula (not editable)
								</p>
							</div>

							<div class="space-y-2">
								<Label for="adjustmentAmount">Adjustment Amount</Label>
								<Input
									type="number"
									id="adjustmentAmount"
									name="adjustmentAmount"
									value={adjustmentAmount}
									step="0.01"
									on:input={handleAdjustmentAmountChange}
								/>
								<p class="text-sm text-muted-foreground">
									Optional adjustment to the calculated amount (positive or negative)
								</p>
							</div>

							<div class="space-y-2">
								<Label for="adjustmentReason">Adjustment Reason</Label>
								<Textarea
									id="adjustmentReason"
									name="adjustmentReason"
									value={adjustmentReason}
									on:input={handleAdjustmentReasonChange}
									placeholder="Reason for adjustment (required if adjustment is not zero)"
									disabled={adjustmentAmount === 0}
								/>
							</div>

							<div class="space-y-2">
								<Label>Final Amount</Label>
								<div class="text-xl font-bold">
									{formatCurrency(totalAmount, data.payrollProfile.currency_code)}
								</div>
								<p class="text-sm text-muted-foreground">Formula amount + adjustment amount</p>
							</div>

							<input
								type="hidden"
								name="formulaInputs"
								value={JSON.stringify(data.formulaInputs)}
							/>
						</CardContent>
						<CardFooter class="flex justify-between">
							<a href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}">
								<Button variant="outline" type="button">
									<X class="mr-2 h-4 w-4" />
									Cancel
								</Button>
							</a>
							<Button type="submit" disabled={!calculationComplete}>
								<Save class="mr-2 h-4 w-4" />
								{data.existingEntry ? 'Update' : 'Save'} Calculation
							</Button>
						</CardFooter>
					</Card>
				</form>
			</div>

			<div class="space-y-6">
				<Card>
					<CardHeader>
						<CardTitle>Formula Inputs</CardTitle>
						<CardDescription>Variables available for the calculation formula</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="space-y-4">
							{#each Object.entries(data.formulaInputs) as [key, value]}
								<div class="border-b pb-2">
									<p class="text-sm font-medium">{key}</p>
									<p class="text-sm">{formatValue(value)}</p>
								</div>
							{/each}
						</div>
					</CardContent>
				</Card>

				{#if selectedFormula}
					<Card>
						<CardHeader>
							<CardTitle>Formula Code</CardTitle>
							<CardDescription>The JavaScript code that runs for this calculation</CardDescription>
						</CardHeader>
						<CardContent>
							<pre class="max-h-80 overflow-auto rounded-md bg-muted p-4 text-xs">
								{selectedFormula.formula_js}
							</pre>
						</CardContent>
					</Card>
				{/if}
			</div>
		</div>
	</div>
{/snippet}

<PageContainer
	title="Product Calculation"
	description="Calculate payroll for product sales"
	{actions}
	{content}
/>
