import { error, redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ params, parent, locals }) => {
	const { id, profileId } = params;
	const { supabase, brand } = locals;

	try {
		const parentData = await parent();

		// Fetch payroll entries for this profile in this period
		const { data: payrollEntries, error: entriesError } = await supabase
			.from('payroll_entry')
			.select('*')
			.eq('payroll_period_id', id)
			.eq('payroll_profile_id', profileId);

		if (entriesError) {
			console.error('Error fetching payroll entries:', entriesError);
			// Don't throw error, just return empty array
		}

		// Fetch payroll profile for this user
		const { data: payrollProfile, error: profileError } = await supabase
			.from('payroll_profile')
			.select('*')
			.eq('profile_id', profileId)
			.single();

		if (profileError) {
			console.error('Error fetching payroll profile:', profileError);
			// Don't throw error, proceed with null profile
		}

		// Return data with the profile ID
		return {
			...parentData,
			periodId: id,
			profileId,
			payrollEntries: payrollEntries || [],
			payrollProfile: payrollProfile || null
		};
	} catch (err) {
		console.error('Error loading profile data:', err);
		// If there's an error loading the parent data, redirect to summary
		redirect(303, `/private/payroll/periods/${id}/summary`);
	}
};
