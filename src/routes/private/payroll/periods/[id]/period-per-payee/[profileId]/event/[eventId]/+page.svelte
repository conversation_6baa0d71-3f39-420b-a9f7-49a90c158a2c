<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select/index.js';
	import { formatCurrency, formatDate, formatDateTime } from '$lib/utils/formatters';
	import { ArrowLeft, Calculator, Save, X, AlertTriangle, Trash2 } from '@lucide/svelte';
	import { invalidate } from '$app/navigation';
	import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
	import type { PageData } from './$types';
	import { PageContainer } from '$lib/components/layout';
	import PayrollNav from '../../../../components/PayrollNav.svelte';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';

	interface Props {
		data: PageData;
		form?: any;
	}

	let { data, form }: Props = $props();

	// State for form
	let selectedFormulaId = $state(
		data.existingEntry?.payroll_formula_id || (data.assignedFormula ? data.assignedFormula.id : '')
	);
	let formulaAmount = $state(data.existingEntry?.formula_amount || 0);
	let adjustmentAmount = $state(data.existingEntry?.adjustment_amount || 0);
	let adjustmentReason = $state(data.existingEntry?.adjustment_reason || '');
	let calculationComplete = $state(false);
	let showDeleteConfirm = $state(false);
	let errorMessage = $state('');

	// Get the selected formula
	const selectedFormula = $derived(data.availableFormulas.find((f) => f.id === selectedFormulaId));

	// Format the trigger content
	const triggerContent = $derived(selectedFormula?.name ?? 'Select a formula');

	// Calculate the total amount
	const totalAmount = $derived(formulaAmount + adjustmentAmount);

	// Helper function to get the event title
	function getEventTitle(): string {
		return String(
			String(getLocalizedText(data.event.metadata?.auto_final_title as LocalizedText)) ||
				data.event.title ||
				'Untitled Event'
		);
	}

	function getEventSubtitle(): string {
		return (
			String(getLocalizedText(data.event.metadata?.auto_final_subtitle as LocalizedText)) || ''
		);
	}

	// Helper function to get the location name
	function getLocationName(): string {
		// Check for space name
		if (data.event.space && typeof data.event.space === 'object' && data.event.space.name_short) {
			if (typeof data.event.space.name_short === 'object') {
				return getLocalizedText(data.event.space.name_short as LocalizedText);
			}
			return data.event.space.name_short;
		}

		// Check for landmark name
		if (
			data.event.landmark &&
			typeof data.event.landmark === 'object' &&
			data.event.landmark.title_short
		) {
			if (typeof data.event.landmark.title_short === 'object') {
				return getLocalizedText(data.event.landmark.title_short as LocalizedText);
			}
			return data.event.landmark.title_short;
		}

		return 'N/A';
	}

	// Helper function to get Payee's Role from metadata
	function getPayeeRole(): string {
		// First try to get role from metadata_wikipage relation
		if (data.event.metadata && data.event.metadata.metadata_wikipage) {
			const wikipage = data.event.metadata.metadata_wikipage.find(
				(mw: any) => mw.wikipage && mw.wikipage.decorating_profile_id === data.profileId
			);

			if (wikipage && wikipage.relation) {
				return wikipage.relation;
			}
		}

		// Fallback to the formula inputs value
		return data.formulaInputs.payee_event_role;
	}

	// Function to run the formula calculation
	function calculateFormula() {
		if (!selectedFormulaId) {
			errorMessage = 'Please select a formula first';
			return;
		}

		try {
			const formula = data.availableFormulas.find((f) => f.id === selectedFormulaId);
			if (!formula) {
				errorMessage = 'Selected formula not found';
				return;
			}

			// Create function from formula JS
			// eslint-disable-next-line no-new-func
			const formulaFunction = new Function('inputs', formula.formula_js);

			// Run the formula with our inputs
			const result = formulaFunction(data.formulaInputs);

			if (typeof result !== 'number' || isNaN(result)) {
				errorMessage = 'Formula did not return a valid number';
				return;
			}

			// Update the formula amount
			formulaAmount = parseFloat(result.toFixed(2));
			calculationComplete = true;
			errorMessage = '';
		} catch (err) {
			console.error('Error running formula:', err);
			errorMessage = `Error calculating formula: ${err instanceof Error ? err.message : 'Unknown error'}`;
		}
	}

	// Format value for display
	function formatValue(value: any): string {
		if (value === null || value === undefined) return 'N/A';
		if (value instanceof Date) return formatDateTime(value);
		if (typeof value === 'string' && (value.includes('T') || value.match(/^\d{4}-\d{2}-\d{2}/))) {
			// Check if it's likely a date string
			try {
				return formatDateTime(value);
			} catch (e) {
				return value;
			}
		}
		if (typeof value === 'number') return value.toString();
		if (typeof value === 'boolean') return value ? 'Yes' : 'No';
		if (typeof value === 'object') return JSON.stringify(value);
		return String(value);
	}

	// Success handler for form submission
	function handleSuccess() {
		invalidate('app:payrollEntries');
		window.location.href = `/private/payroll/periods/${data.periodId}/period-per-payee/${data.profileId}/event`;
	}

	// Input handlers
	function handleAdjustmentAmountChange(e: Event) {
		const target = e.target as HTMLInputElement;
		adjustmentAmount = parseFloat(target.value) || 0;
	}

	function handleAdjustmentReasonChange(e: Event) {
		const target = e.target as HTMLTextAreaElement;
		adjustmentReason = target.value;
	}
</script>

<div class="space-y-6">
	<div class="flex items-center justify-between">
		<div class="flex items-center gap-2">
			<a href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}/event">
				<Button variant="outline" size="icon">
					<ArrowLeft class="h-4 w-4" />
				</Button>
			</a>
			<h1 class="text-2xl font-bold">
				{data.existingEntry ? 'Edit' : 'New'} Event Calculation
			</h1>
		</div>

		{#if data.existingEntry}
			<div class="flex items-center gap-2">
				{#if showDeleteConfirm}
					<div class="flex items-center gap-2">
						<span class="text-sm text-destructive">Confirm deletion?</span>
						<form
							method="POST"
							action="?/delete"
							use:enhance={() => {
								return ({ result }) => {
									if (result.type === 'success') {
										handleSuccess();
									}
								};
							}}
						>
							<Button type="submit" variant="destructive" size="sm">Yes, Delete</Button>
						</form>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onclick={() => (showDeleteConfirm = false)}
						>
							Cancel
						</Button>
					</div>
				{:else}
					<Button variant="destructive" size="sm" onclick={() => (showDeleteConfirm = true)}>
						<Trash2 class="mr-2 h-4 w-4" />
						Delete Entry
					</Button>
				{/if}
			</div>
		{/if}
	</div>

	<div class="grid grid-cols-1 gap-6 md:grid-cols-3">
		<div class="space-y-6 md:col-span-2">
			<Card>
				<CardHeader>
					<CardTitle>Event Details</CardTitle>
					<CardDescription>Information about the event for this calculation</CardDescription>
				</CardHeader>
				<CardContent>
					<div class="space-y-4">
						<div>
							<h3 class="text-lg font-medium">{getEventTitle()}</h3>
							<p class="text-muted-foreground">
								{formatDateTime(data.event.start_at)}
								{#if data.event.auto_end_at}
									to {formatDateTime(data.event.auto_end_at)}
								{/if}
							</p>
						</div>

						<div class="grid grid-cols-2 gap-4">
							<div>
								<p class="text-sm font-medium text-muted-foreground">Event Kind</p>
								<p>{data.event.kind}</p>
							</div>
							<div>
								<p class="text-sm font-medium text-muted-foreground">Location</p>
								<p>{getLocationName()}</p>
							</div>
							<div>
								<p class="text-sm font-medium text-muted-foreground">Registered Attendees</p>
								<p>{data.formulaInputs.event_consumer_registered}</p>
							</div>
							<div>
								<p class="text-sm font-medium text-muted-foreground">Checked-in Attendees</p>
								<p>{data.formulaInputs.event_consumer_checked_in}</p>
							</div>
							<div>
								<p class="text-sm font-medium text-muted-foreground">Payee's Role</p>
								<p>{getPayeeRole()}</p>
							</div>
							<div>
								<p class="text-sm font-medium text-muted-foreground">Event Duration</p>
								<p>{data.formulaInputs.event_minutes} minutes</p>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			<form
				method="POST"
				action="?/calculate"
				use:enhance={() => {
					return ({ result }) => {
						if (result.type === 'success') {
							handleSuccess();
						}
					};
				}}
			>
				<Card>
					<CardHeader>
						<CardTitle>Pay Calculation</CardTitle>
						<CardDescription>Select a formula and calculate the pay for this event</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						{#if errorMessage}
							<Alert variant="destructive">
								<AlertTriangle class="h-4 w-4" />
								<AlertTitle>Error</AlertTitle>
								<AlertDescription>{errorMessage}</AlertDescription>
							</Alert>
						{/if}

						<div class="space-y-2">
							<Label for="formulaId">Pay Formula</Label>
							<Select.Root
								type="single"
								name="formulaId"
								bind:value={selectedFormulaId}
								onValueChange={() => {
									calculationComplete = false;
								}}
							>
								<Select.Trigger>
									{triggerContent}
								</Select.Trigger>
								<Select.Content>
									<Select.Group>
										{#each data.availableFormulas as formula (formula.id)}
											<Select.Item value={formula.id} label={formula.name}
												>{formula.name}</Select.Item
											>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if selectedFormula}
								<p class="text-sm text-muted-foreground">{selectedFormula.description}</p>
							{/if}
						</div>

						<div class="flex justify-end">
							<Button type="button" variant="outline" onclick={calculateFormula}>
								<Calculator class="mr-2 h-4 w-4" />
								Calculate Amount
							</Button>
						</div>

						<div class="space-y-2">
							<Label for="formulaAmount">Formula Amount</Label>
							<Input
								type="number"
								id="formulaAmount"
								name="formulaAmount"
								value={formulaAmount}
								step="0.01"
								readonly
							/>
							<p class="text-sm text-muted-foreground">
								Amount calculated by the formula (not editable)
							</p>
						</div>

						<div class="space-y-2">
							<Label for="adjustmentAmount">Adjustment Amount</Label>
							<Input
								type="number"
								id="adjustmentAmount"
								name="adjustmentAmount"
								value={adjustmentAmount}
								step="0.01"
								oninput={handleAdjustmentAmountChange}
							/>
							<p class="text-sm text-muted-foreground">
								Optional adjustment to the calculated amount (positive or negative)
							</p>
						</div>

						<div class="space-y-2">
							<Label for="adjustmentReason">Adjustment Reason</Label>
							<Textarea
								id="adjustmentReason"
								name="adjustmentReason"
								value={adjustmentReason}
								oninput={handleAdjustmentReasonChange}
								placeholder="Reason for adjustment (required if adjustment is not zero)"
								disabled={adjustmentAmount === 0}
							/>
						</div>

						<div class="space-y-2">
							<Label>Final Amount</Label>
							<div class="text-xl font-bold">
								{formatCurrency(totalAmount, data.payrollProfile.currency_code)}
							</div>
							<p class="text-sm text-muted-foreground">Formula amount + adjustment amount</p>
						</div>

						<input type="hidden" name="formulaInputs" value={JSON.stringify(data.formulaInputs)} />
					</CardContent>
					<CardFooter class="flex justify-between">
						<a
							href="/private/payroll/periods/{data.periodId}/period-per-payee/{data.profileId}/event"
						>
							<Button variant="outline" type="button">
								<X class="mr-2 h-4 w-4" />
								Cancel
							</Button>
						</a>
						<Button type="submit" disabled={!calculationComplete}>
							<Save class="mr-2 h-4 w-4" />
							{data.existingEntry ? 'Update' : 'Save'} Calculation
						</Button>
					</CardFooter>
				</Card>
			</form>
		</div>

		<div class="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>Formula Inputs</CardTitle>
					<CardDescription>Variables available for the calculation formula</CardDescription>
				</CardHeader>
				<CardContent>
					<div class="space-y-4">
						{#each Object.entries(data.formulaInputs) as [key, value]}
							<div class="border-b pb-2">
								<p class="text-sm font-medium">{key}</p>
								<p class="text-sm">{formatValue(value)}</p>
							</div>
						{/each}
					</div>
				</CardContent>
			</Card>

			{#if selectedFormula}
				<Card>
					<CardHeader>
						<CardTitle>Formula Code</CardTitle>
						<CardDescription>The JavaScript code that runs for this calculation</CardDescription>
					</CardHeader>
					<CardContent>
						<pre class="max-h-80 overflow-auto rounded-md bg-muted p-4 text-xs">
                            {selectedFormula.formula_js}
                        </pre>
					</CardContent>
				</Card>
			{/if}
		</div>
	</div>
</div>
