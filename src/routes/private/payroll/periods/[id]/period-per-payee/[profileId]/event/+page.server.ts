import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, parent, locals }) => {
	const { id, profileId } = params;
	const { supabase } = locals;

	try {
		const parentData = await parent();

		// Fetch all events in this period
		const { data: profileSpecificEvents, error: profileSpecificError } = await supabase
			.from('event')
			.select(
				`
				id,
				title,
				start_at,
				auto_end_at,
				kind,
				metadata:metadata_id(
					id,
					auto_final_title,
					auto_final_subtitle,
					metadata_wikipage(
						id,
						relation,
						wikipage_id,
						wikipage:wikipage_id(
							id,
							title,
							decorating_profile_id
						)
					)
				),
				space(
					id,
					name_short,
					name_full,
					landmark(
						id,
						title_short,
						address(
							auto_normalized_address_local
						)
					)
				),
				landmark(
					id,
					title_short,
					address(
						auto_normalized_address_local
					)
				),
				event_member(
					id,
					role,
					checked_in_at
				)
			`
			)
			.eq('brand_id', parentData.brand.id)
			.gte('start_at', parentData.period.start_at)
			.lt('start_at', parentData.period.end_at)
			.order('start_at', { ascending: true });

		if (profileSpecificError) {
			console.error('Error fetching profile-specific events:', profileSpecificError);
			// Don't throw error, just return empty array
		}

		// Process the data to count checked-in consumers for each event
		const eventsWithCounts =
			profileSpecificEvents?.map((event) => {
				const checkedInConsumers =
					event.event_member?.filter(
						(member: any) => member.role === 'consumer' && member.checked_in_at !== null
					).length || 0;

				return {
					...event,
					checked_in_consumers_count: checkedInConsumers
				};
			}) || [];

		// Filter events for the specific profile (where this profile is decorated in wikipage)
		const filteredEvents = eventsWithCounts.filter((event: any) => {
			return (
				event.metadata &&
				event.metadata.metadata_wikipage &&
				Array.isArray(event.metadata.metadata_wikipage) &&
				event.metadata.metadata_wikipage.some(
					(mw: any) => mw.wikipage && mw.wikipage.decorating_profile_id === profileId
				)
			);
		});

		return {
			...parentData,
			profileSpecificEvents: filteredEvents
		};
	} catch (err) {
		console.error('Error loading profile-specific events:', err);
		return {
			...(await parent()),
			profileSpecificEvents: []
		};
	}
};
