import { error, fail, redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ params, parent, locals }) => {
	const { id, profileId, productId } = params;
	const { supabase } = locals;

	try {
		const parentData = await parent();

		// Fetch product details
		const { data: product, error: productError } = await supabase
			.from('product')
			.select(
				`
				id,
				title,
				description,
				kind,
				created_at,
				updated_at,
				profile_id,
				brand_id
			`
			)
			.eq('id', productId)
			.single();

		if (productError) {
			console.error('Error fetching product:', productError);
			error(404, 'Product not found');
		}

		// Fetch payroll profile
		const { data: payrollProfile, error: profileError } = await supabase
			.from('payroll_profile')
			.select('*')
			.eq('profile_id', profileId)
			.eq('brand_id', parentData.brand.id)
			.single();

		if (profileError) {
			console.error('Error fetching payroll profile:', profileError);
			error(404, 'Payroll profile not found');
		}

		// Check if there's already an entry for this product
		const { data: existingEntry, error: entryError } = await supabase
			.from('payroll_entry')
			.select('*')
			.eq('payroll_period_id', id)
			.eq('payroll_profile_id', profileId)
			.eq('entry_for_product_id', productId)
			.maybeSingle();

		if (entryError) {
			console.error('Error checking for existing entry:', entryError);
			// Continue without existing entry
		}

		// Set placeholder metrics for product sales
		// In a real implementation, you would fetch sales data from an appropriate source
		const totalSales = 0;
		const totalRevenue = 0;

		// Fetch available formulas for this product kind
		const { data: availableFormulas, error: formulasError } = await supabase
			.from('payroll_formula')
			.select('*')
			.eq('brand_id', parentData.brand.id)
			.eq('earning_source_kind', 'product')
			.eq('product_kind', product.kind);

		if (formulasError) {
			console.error('Error fetching formulas:', formulasError);
			// Continue with empty formulas
		}

		// Fetch assigned formula if any
		const { data: assignedFormula, error: assignedFormulaError } = await supabase
			.from('payroll_formula_assignment')
			.select(
				`
				id,
				payroll_formula_id,
				payroll_formula(
					id,
					name,
					description,
					formula_js
				)
			`
			)
			.eq('profile_id', profileId)
			.eq('auto_payroll_formula_earning_source_kind', 'product')
			.eq('auto_payroll_formula_product_kind', product.kind)
			.maybeSingle();

		if (assignedFormulaError) {
			console.error('Error fetching assigned formula:', assignedFormulaError);
			// Continue without assigned formula
		}

		// Prepare formula inputs
		const formulaInputs = {
			product_id: product.id,
			product_title: product.title,
			product_kind: product.kind,
			product_created_at: product.created_at,
			period_sales_count: totalSales,
			period_sales_revenue: totalRevenue,
			payroll_profile_id: profileId,
			payee_is_creator: product.profile_id === profileId,
			payee_level: payrollProfile.level || 1,
			payee_enrolled_at: payrollProfile.enrolled_at
		};

		return {
			...parentData,
			periodId: id,
			profileId,
			productId,
			product: {
				...product,
				period_sales_count: totalSales,
				period_sales_revenue: totalRevenue
			},
			payrollProfile,
			existingEntry,
			availableFormulas: availableFormulas || [],
			assignedFormula: assignedFormula?.payroll_formula || null,
			formulaInputs
		};
	} catch (err) {
		console.error('Error loading product calculation page:', err);
		redirect(303, `/private/payroll/periods/${id}/period-per-payee/${profileId}`);
	}
};

export const actions: Actions = {
	calculate: async ({ request, params, locals }) => {
		const { id, profileId, productId } = params;
		const { supabase } = locals;

		const formData = await request.formData();
		const formulaId = formData.get('formulaId')?.toString();
		const formulaAmount = parseFloat(formData.get('formulaAmount')?.toString() || '0');
		const adjustmentAmount = parseFloat(formData.get('adjustmentAmount')?.toString() || '0');
		const adjustmentReason = formData.get('adjustmentReason')?.toString() || '';
		const formulaInputs = JSON.parse(formData.get('formulaInputs')?.toString() || '{}');

		if (!formulaId) {
			return fail(400, { error: 'No formula selected' });
		}

		try {
			// Check if formula exists
			const { data: formula, error: formulaError } = await supabase
				.from('payroll_formula')
				.select('*')
				.eq('id', formulaId)
				.single();

			if (formulaError) {
				console.error('Error fetching formula:', formulaError);
				return fail(400, { error: 'Selected formula not found' });
			}

			// Check if entry already exists
			const { data: existingEntry, error: entryError } = await supabase
				.from('payroll_entry')
				.select('id')
				.eq('payroll_period_id', id)
				.eq('payroll_profile_id', profileId)
				.eq('entry_for_product_id', productId)
				.maybeSingle();

			if (entryError) {
				console.error('Error checking for existing entry:', entryError);
			}

			const finalAmount = formulaAmount + adjustmentAmount;
			const entryData = {
				payroll_period_id: id,
				payroll_profile_id: profileId,
				entry_for_product_id: productId,
				entry_type: 'product',
				payroll_formula_id: formulaId,
				formula_amount: formulaAmount,
				adjustment_amount: adjustmentAmount,
				adjustment_reason: adjustmentAmount !== 0 ? adjustmentReason : null,
				final_amount: finalAmount,
				calculation_details: formula.formula_js,
				formula_inputs: formulaInputs
			};

			if (existingEntry) {
				// Update existing entry
				const { error: updateError } = await supabase
					.from('payroll_entry')
					.update(entryData)
					.eq('id', existingEntry.id);

				if (updateError) {
					console.error('Error updating entry:', updateError);
					return fail(500, { error: 'Failed to update calculation' });
				}
			} else {
				// Create new entry
				const { error: insertError } = await supabase.from('payroll_entry').insert(entryData);

				if (insertError) {
					console.error('Error creating entry:', insertError);
					return fail(500, { error: 'Failed to save calculation' });
				}
			}

			return { success: true };
		} catch (err) {
			console.error('Error saving calculation:', err);
			return fail(500, { error: 'An unexpected error occurred' });
		}
	},

	delete: async ({ params, locals }) => {
		const { id, profileId, productId } = params;
		const { supabase } = locals;

		try {
			const { error: deleteError } = await supabase
				.from('payroll_entry')
				.delete()
				.eq('payroll_period_id', id)
				.eq('payroll_profile_id', profileId)
				.eq('entry_for_product_id', productId);

			if (deleteError) {
				console.error('Error deleting entry:', deleteError);
				return fail(500, { error: 'Failed to delete calculation' });
			}

			return { success: true };
		} catch (err) {
			console.error('Error deleting calculation:', err);
			return fail(500, { error: 'An unexpected error occurred' });
		}
	}
};
