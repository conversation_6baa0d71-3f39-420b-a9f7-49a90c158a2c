import { error, fail, redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ params, parent, locals }) => {
	const { id, profileId, eventId } = params;
	const { supabase } = locals;

	try {
		const parentData = await parent();

		// Fetch full event details
		const { data: event, error: eventError } = await supabase
			.from('event')
			.select(
				`
				id,
				title,
				start_at,
				auto_end_at,
				kind,
                metadata:metadata_id(
					id,
					auto_final_title,
					auto_final_subtitle,
					metadata_wikipage(
						id,
						relation,
						wikipage_id,
						wikipage:wikipage_id(
							id,
							title,
							decorating_profile_id
						)
					)
				),
				space(id, name_short, name_full),
				landmark(id, title_short),
				event_member(id, member_profile_id, role, checked_in_at)
			`
			)
			.eq('id', eventId)
			.single();

		if (eventError) {
			console.error('Error fetching event:', eventError);
			error(404, 'Event not found');
		}

		// Fetch payroll profile
		const { data: payrollProfile, error: profileError } = await supabase
			.from('payroll_profile')
			.select('*')
			.eq('profile_id', profileId)
			.single();

		if (profileError) {
			console.error('Error fetching payroll profile:', profileError);
			error(404, 'Payroll profile not found');
		}

		// Check if there's already an entry for this event
		const { data: existingEntry, error: entryError } = await supabase
			.from('payroll_entry')
			.select('*')
			.eq('payroll_period_id', id)
			.eq('payroll_profile_id', profileId)
			.eq('entry_for_event_id', eventId)
			.maybeSingle();

		if (entryError) {
			console.error('Error checking for existing entry:', entryError);
			// Continue without existing entry
		}

		// Determine payee's role in the event
		const payeeEventMember = event.event_member?.find(
			(member: any) => member.member_profile_id === profileId
		);
		const payeeEventRole = payeeEventMember?.role || 'not_participating';

		// Count event participants
		const consumerRegistered =
			event.event_member?.filter((member: any) => member.role === 'consumer').length || 0;
		const consumerCheckedIn =
			event.event_member?.filter(
				(member: any) => member.role === 'consumer' && member.checked_in_at !== null
			).length || 0;

		// Calculate event duration in minutes
		let eventMinutes = 0;
		if (event.start_at && event.auto_end_at) {
			const startTime = new Date(event.start_at);
			const endTime = new Date(event.auto_end_at);
			eventMinutes = Math.round((endTime.getTime() - startTime.getTime()) / 60000);
		}

		// Fetch available formulas for this event kind
		const { data: availableFormulas, error: formulasError } = await supabase
			.from('payroll_formula')
			.select('*')
			.eq('brand_id', parentData.brand.id)
			.eq('earning_source_kind', 'event')
			.eq('event_kind', event.kind);

		if (formulasError) {
			console.error('Error fetching formulas:', formulasError);
			// Continue with empty formulas
		}

		// Fetch assigned formula if any
		const { data: assignedFormula, error: assignedFormulaError } = await supabase
			.from('payroll_formula_assignment')
			.select(
				`
				id,
				payroll_formula_id,
				payroll_formula(
					id,
					name,
					description,
					formula_js
				)
			`
			)
			.eq('payroll_profile_id', profileId)
			.eq('auto_payroll_formula_earning_source_kind', 'event')
			.maybeSingle();

		if (assignedFormulaError) {
			console.error('Error fetching assigned formula:', assignedFormulaError);
			// Continue without assigned formula
		}

		// Prepare formula inputs
		const formulaInputs = {
			event_id: event.id,
			event_title: event.title,
			event_kind: event.kind,
			event_start_at: event.start_at,
			event_end_at: event.auto_end_at,
			event_minutes: eventMinutes,
			event_consumer_registered: consumerRegistered,
			event_consumer_checked_in: consumerCheckedIn,
			payroll_profile_id: profileId,
			payee_event_role: payeeEventRole,
			payee_level: payrollProfile.level || 1,
			payee_enrolled_at: payrollProfile.enrolled_at
		};

		return {
			...parentData,
			periodId: id,
			profileId,
			eventId,
			event,
			payrollProfile,
			existingEntry,
			availableFormulas: availableFormulas || [],
			assignedFormula: assignedFormula?.payroll_formula || null,
			formulaInputs
		};
	} catch (err) {
		console.error('Error loading event calculation page:', err);
		redirect(303, `/private/payroll/periods/${id}/period-per-payee/${profileId}/event`);
	}
};

export const actions: Actions = {
	calculate: async ({ request, params, locals }) => {
		const { id, profileId, eventId } = params;
		const { supabase } = locals;

		const formData = await request.formData();
		const formulaId = formData.get('formulaId')?.toString();
		const formulaAmount = parseFloat(formData.get('formulaAmount')?.toString() || '0');
		const adjustmentAmount = parseFloat(formData.get('adjustmentAmount')?.toString() || '0');
		const adjustmentReason = formData.get('adjustmentReason')?.toString() || '';
		const formulaInputs = JSON.parse(formData.get('formulaInputs')?.toString() || '{}');

		if (!formulaId) {
			return fail(400, { error: 'No formula selected' });
		}

		try {
			// Check if formula exists
			const { data: formula, error: formulaError } = await supabase
				.from('payroll_formula')
				.select('*')
				.eq('id', formulaId)
				.single();

			if (formulaError) {
				console.error('Error fetching formula:', formulaError);
				return fail(400, { error: 'Selected formula not found' });
			}

			// Check if entry already exists
			const { data: existingEntry, error: entryError } = await supabase
				.from('payroll_entry')
				.select('id')
				.eq('payroll_period_id', id)
				.eq('payroll_profile_id', profileId)
				.eq('entry_for_event_id', eventId)
				.maybeSingle();

			if (entryError) {
				console.error('Error checking for existing entry:', entryError);
			}

			const finalAmount = formulaAmount + adjustmentAmount;
			const entryData = {
				payroll_period_id: id,
				payroll_profile_id: profileId,
				entry_for_event_id: eventId,
				entry_type: 'event',
				payroll_formula_id: formulaId,
				formula_amount: formulaAmount,
				adjustment_amount: adjustmentAmount,
				adjustment_reason: adjustmentAmount !== 0 ? adjustmentReason : null,
				final_amount: finalAmount,
				calculation_details: formula.formula_js,
				formula_inputs: formulaInputs
			};

			if (existingEntry) {
				// Update existing entry
				const { error: updateError } = await supabase
					.from('payroll_entry')
					.update(entryData)
					.eq('id', existingEntry.id);

				if (updateError) {
					console.error('Error updating entry:', updateError);
					return fail(500, { error: 'Failed to update calculation' });
				}
			} else {
				// Create new entry
				const { error: insertError } = await supabase.from('payroll_entry').insert(entryData);

				if (insertError) {
					console.error('Error creating entry:', insertError);
					return fail(500, { error: 'Failed to save calculation' });
				}
			}

			return { success: true };
		} catch (err) {
			console.error('Error saving calculation:', err);
			return fail(500, { error: 'An unexpected error occurred' });
		}
	},

	delete: async ({ params, locals }) => {
		const { id, profileId, eventId } = params;
		const { supabase } = locals;

		try {
			const { error: deleteError } = await supabase
				.from('payroll_entry')
				.delete()
				.eq('payroll_period_id', id)
				.eq('payroll_profile_id', profileId)
				.eq('entry_for_event_id', eventId);

			if (deleteError) {
				console.error('Error deleting entry:', deleteError);
				return fail(500, { error: 'Failed to delete calculation' });
			}

			return { success: true };
		} catch (err) {
			console.error('Error deleting calculation:', err);
			return fail(500, { error: 'An unexpected error occurred' });
		}
	}
};
