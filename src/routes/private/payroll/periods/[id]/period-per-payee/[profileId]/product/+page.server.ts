import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, parent, locals }) => {
	const { id, profileId } = params;
	const { supabase } = locals;

	try {
		const parentData = await parent();

		// Fetch products for this profile
		const { data: profileSpecificProducts, error: productsError } = await supabase
			.from('product')
			.select(
				`
				id,
				title,
				kind,
				created_at,
				updated_at,
				profile_id,
				brand_id
			`
			)
			.eq('brand_id', parentData.brand.id)
			.eq('profile_id', profileId);

		if (productsError) {
			console.error('Error fetching profile-specific products:', productsError);
			// Don't throw error, just return empty array
		}

		// Add placeholder sales metrics for products
		const productsWithSales =
			profileSpecificProducts?.map((product) => {
				return {
					...product,
					period_sales_count: 0,
					period_sales_revenue: 0
				};
			}) || [];

		return {
			...parentData,
			profileSpecificProducts: productsWithSales
		};
	} catch (err) {
		console.error('Error loading profile-specific products:', err);
		return {
			...(await parent()),
			profileSpecificProducts: []
		};
	}
};
