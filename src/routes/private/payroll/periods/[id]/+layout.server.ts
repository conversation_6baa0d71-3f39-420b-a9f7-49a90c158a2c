import { error } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ params, locals }) => {
	const { id } = params;
	const { supabase, brand } = locals;

	// Fetch the payroll period to verify access
	const { data: period, error: periodError } = await supabase
		.from('payroll_period')
		.select('*')
		.eq('id', id)
		.single();

	if (periodError) {
		error(404, 'Payroll period not found');
	}

	// Make sure this period belongs to this brand
	if (period.brand_id !== brand.id) {
		error(403, 'You do not have access to this payroll period');
	}

	// Fetch payroll profiles for the dropdown
	const { data: payrollProfiles, error: profilesError } = await supabase
		.from('payroll_profile')
		.select(
			`
			*,
			profile:profile_id (
				id,
				username,
				given_name,
				family_name,
				auto_user_email
			)
		`
		)
		.is('unenrolled_at', null);

	if (profilesError) {
		console.error('Error fetching payroll profiles:', profilesError);
	}

	// Return shared data for all routes
	return {
		period,
		periodId: id,
		payrollProfiles: payrollProfiles || []
	};
};
