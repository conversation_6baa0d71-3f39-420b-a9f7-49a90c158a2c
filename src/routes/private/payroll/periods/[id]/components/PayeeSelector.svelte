<script lang="ts">
	import { goto } from '$app/navigation';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import * as Select from '$lib/components/ui/select/index.js';

	interface Props {
		payrollProfiles: any[];
		periodId: string;
		selectedProfileId?: string;
	}

	let { payrollProfiles = [], periodId, selectedProfileId = '' }: Props = $props();

	// Handle profile change
	function handleProfileChange(value: string): void {
		if (value && value !== selectedProfileId) {
			goto(`/private/payroll/periods/${periodId}/events-per-payee/${value}`);
		}
	}

	// Get full name
	function getFullName(profile: any): string {
		if (!profile) return 'Unknown';

		// Check if name is stored as localized text
		const givenName =
			typeof profile.given_name === 'object'
				? getLocalizedText(profile.given_name as LocalizedText)
				: profile.given_name || '';

		const familyName =
			typeof profile.family_name === 'object'
				? getLocalizedText(profile.family_name as LocalizedText)
				: profile.family_name || '';

		return `${givenName} ${familyName}`.trim() || 'Unknown';
	}

	// Prepare display name for dropdown
	const triggerContent = $derived(
		selectedProfileId && payrollProfiles?.length
			? getFullName(payrollProfiles.find((p) => p.profile_id === selectedProfileId)?.profile)
			: 'Select a payee'
	);
</script>

<div class="mb-6">
	<label for="profile-select" class="mb-2 block text-sm font-medium">Select Payee</label>
	<Select.Root type="single" value={selectedProfileId} onValueChange={handleProfileChange}>
		<Select.Trigger class="w-full md:w-[300px]">
			{triggerContent}
		</Select.Trigger>
		<Select.Content>
			{#each payrollProfiles || [] as profile}
				<Select.Item value={profile.profile_id} label={getFullName(profile.profile)}>
					{getFullName(profile.profile)} - {profile.title || 'No title'}
				</Select.Item>
			{/each}
		</Select.Content>
	</Select.Root>
</div>
