<script lang="ts">
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';

	interface Props {
		entries: Array<any>;
	}

	const { entries }: Props = $props();

	// Format currency
	function formatCurrency(amount: number, currencyCode: string = 'USD'): string {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: currencyCode
		}).format(amount);
	}

	// Get full name
	function getFullName(firstName: string, lastName: string): string {
		return `${firstName} ${lastName}`.trim();
	}

	// Group entries by payee for template functions
	function groupEntriesByPayee() {
		return entries.reduce((acc: Record<string, any>, entry: any) => {
			const payeeId = entry.payroll_profile.profile_id;
			if (!acc[payeeId]) {
				acc[payeeId] = {
					profile: entry.payroll_profile.profile,
					title: entry.payroll_profile.title,
					level: entry.payroll_profile.level,
					currencyCode: entry.payroll_profile.currency_code,
					entries: []
				};
			}

			acc[payeeId].entries.push(entry);
			return acc;
		}, {});
	}

	// Calculate totals by payee for template
	function calculateTotalsByPayee() {
		const entriesByPayee = groupEntriesByPayee();
		return Object.entries(entriesByPayee).reduce(
			(acc: Record<string, any>, [payeeId, payeeData]: [string, any]) => {
				const total = payeeData.entries.reduce((sum: number, entry: any) => {
					return sum + entry.final_amount;
				}, 0);

				acc[payeeId] = {
					...payeeData,
					total
				};

				return acc;
			},
			{}
		);
	}

	// Calculate grand total
	function calculateGrandTotal() {
		const totalsByPayee = calculateTotalsByPayee();
		return Object.values(totalsByPayee).reduce((sum: number, payeeData: any) => {
			return sum + payeeData.total;
		}, 0);
	}
</script>

<Card>
	<CardHeader>
		<CardTitle>Payroll Summary</CardTitle>
		<CardDescription>Overview of payments for all payees in this period.</CardDescription>
	</CardHeader>
	<CardContent>
		{#if entries.length === 0}
			<div class="py-6 text-center text-muted-foreground">
				No payroll entries found. Click "Calculate Payroll" to generate entries based on events and
				formulas.
			</div>
		{:else}
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Payee</TableHead>
						<TableHead>Title</TableHead>
						<TableHead>Entries</TableHead>
						<TableHead class="text-right">Total Amount</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each Object.entries(calculateTotalsByPayee()) as [payeeId, payeeData]}
						<TableRow>
							<TableCell class="font-medium">
								{getFullName(payeeData.profile.given_name, payeeData.profile.family_name)}
							</TableCell>
							<TableCell>{payeeData.title || 'N/A'}</TableCell>
							<TableCell>{payeeData.entries.length}</TableCell>
							<TableCell class="text-right">
								{formatCurrency(payeeData.total, payeeData.currencyCode)}
							</TableCell>
						</TableRow>
					{/each}
					<TableRow>
						<TableCell class="font-semibold" colspan={3}>Grand Total</TableCell>
						<TableCell class="text-right font-semibold">
							{formatCurrency(calculateGrandTotal())}
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>
		{/if}
	</CardContent>
</Card>
