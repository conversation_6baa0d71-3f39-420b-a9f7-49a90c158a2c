<script lang="ts">
	import { Card } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { page } from '$app/stores';
	import * as Select from '$lib/components/ui/select/index.js';
	import { goto } from '$app/navigation';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';

	interface Props {
		periodId: string;
		payrollProfiles?: any[];
	}

	let { periodId, payrollProfiles = [] }: Props = $props();

	// Navigation links
	const navLinks = [
		{
			title: 'Summary',
			href: `/private/payroll/periods/${periodId}/summary`,
			path: 'summary',
			description: 'Overview of payments for all payees in this period'
		},
		{
			title: 'Details',
			href: `/private/payroll/periods/${periodId}/details`,
			path: 'details',
			description: 'Detailed breakdown of all payroll entries'
		},
		{
			title: 'Events',
			href: `/private/payroll/periods/${periodId}/events`,
			path: 'events',
			description: 'Events that occurred during this payroll period'
		}
	];

	// Determine if a nav link is active based on the current path
	function isActive(navPath: string): boolean {
		const currentPath = $page.url.pathname;

		// For regular pages, check that the path segment matches exactly
		const segments = currentPath.split('/');
		const lastSegment = segments[segments.length - 1];

		// Handle case where we're at the exact path
		if (lastSegment === navPath) {
			return true;
		}

		// For events, ensure we're not matching period-per-payee
		if (
			navPath === 'events' &&
			lastSegment === 'events' &&
			!currentPath.includes('period-per-payee')
		) {
			return true;
		}

		return false;
	}

	// Check if we're on an period-per-payee page
	const isPeriodPerPayeePage = $derived($page.url.pathname.includes('/period-per-payee'));

	// Get current profile ID from path if available
	let currentProfileId = $state('');

	$effect(() => {
		if ($page.url.pathname.includes('/period-per-payee')) {
			const segments = $page.url.pathname.split('/');
			// Check if we have a profile ID in the URL
			if (segments.length > 6 && segments[5] === 'period-per-payee') {
				currentProfileId = segments[6];
			} else {
				// If on the base period-per-payee page, get from query param
				currentProfileId = $page.url.searchParams.get('pid') || '';
			}
		} else {
			currentProfileId = '';
		}
	});

	// Handle payee selection
	function handlePayeeChange(profileId: string) {
		if (profileId) {
			goto(`/private/payroll/periods/${periodId}/period-per-payee/${profileId}/event`);
		}
	}

	// Get full name
	function getFullName(profile: any): string {
		if (!profile) return 'Unknown';

		// Check if name is stored as localized text
		const givenName =
			typeof profile.given_name === 'object'
				? getLocalizedText(profile.given_name as LocalizedText)
				: profile.given_name || '';

		const familyName =
			typeof profile.family_name === 'object'
				? getLocalizedText(profile.family_name as LocalizedText)
				: profile.family_name || '';

		return `${givenName} ${familyName}`.trim() || 'Unknown';
	}

	// Get current selected profile
	const currentProfile = $derived(
		currentProfileId && payrollProfiles && payrollProfiles.length > 0
			? payrollProfiles.find((p) => p.profile_id === currentProfileId)
			: null
	);
</script>

<div class="mb-4 grid grid-cols-1 gap-2 md:grid-cols-4 md:gap-4">
	<div class="z-10">
		{#if payrollProfiles.length > 0}
			<Select.Root type="single" value={currentProfileId} onValueChange={handlePayeeChange}>
				<Select.Trigger
					class={`w-full justify-between ${isPeriodPerPayeePage ? 'bg-primary text-primary-foreground' : 'border border-input bg-card'}`}
				>
					{#if currentProfile}
						{getFullName(currentProfile.profile)}
					{:else}
						Select a payee
					{/if}
				</Select.Trigger>
				<Select.Content>
					{#each payrollProfiles as profile}
						<Select.Item value={profile.profile_id} label={getFullName(profile.profile)}>
							{getFullName(profile.profile)}
							{#if profile.title}
								<span class="ml-1 text-xs text-muted-foreground">({profile.title})</span>
							{/if}
						</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		{:else}
			<Button
				variant="outline"
				class="w-full cursor-not-allowed justify-between opacity-60"
				disabled
			>
				No payees available
			</Button>
		{/if}
	</div>

	{#each navLinks as link}
		<Button
			variant={isActive(link.path) ? 'default' : 'outline'}
			href={link.href}
			class="justify-start"
		>
			{link.title}
		</Button>
	{/each}
</div>
