<script lang="ts">
	import { CalendarDays } from '@lucide/svelte';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';

	interface Props {
		events: Array<any>;
		period: any;
	}

	const { events, period }: Props = $props();

	// Format dates
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString();
	}
</script>

<Card>
	<CardHeader>
		<CardTitle>Events</CardTitle>
		<CardDescription>Events that occurred during this payroll period.</CardDescription>
	</CardHeader>
	<CardContent>
		{#if events.length === 0}
			<div class="py-6 text-center text-muted-foreground">
				No events found for this payroll period.
				<div class="mt-4 text-xs text-muted-foreground">
					<p>
						Period date range: {formatDate(period.start_at)} - {formatDate(period.end_at)}
					</p>
					<p>
						Period ISO dates: {new Date(period.start_at).toISOString()} - {new Date(
							period.end_at
						).toISOString()}
					</p>
					<details>
						<summary class="cursor-pointer font-medium">Debug Information</summary>
						<pre class="mt-2 whitespace-pre-wrap text-xs">{JSON.stringify(
								{
									events_count: events.length,
									period_id: period.id,
									period_dates: {
										start: period.start_at,
										end: period.end_at
									}
								},
								null,
								2
							)}</pre>
					</details>
				</div>
			</div>
		{:else}
			<div class="mb-4 text-xs">
				<p>
					Found {events.length} events in period: {formatDate(period.start_at)} - {formatDate(
						period.end_at
					)}
				</p>
			</div>
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Event</TableHead>
						<TableHead>Date</TableHead>
						<TableHead>Duration</TableHead>
						<TableHead class="text-right">Participants</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each events as event}
						<TableRow>
							<TableCell class="font-medium">
								{#if event.metadata?.auto_final_title}
									{getLocalizedText(event.metadata.auto_final_title as LocalizedText)}
								{:else}
									Untitled Event
								{/if}
							</TableCell>
							<TableCell>{formatDate(event.start_at)}</TableCell>
							<TableCell>
								{#if event.auto_end_at}
									{Math.round(
										(new Date(event.auto_end_at).getTime() - new Date(event.start_at).getTime()) /
											(1000 * 60)
									)} min
								{:else}
									N/A
								{/if}
							</TableCell>
							<TableCell class="text-right">
								{event.event_members?.length || 0}
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		{/if}
	</CardContent>
</Card>
