<script lang="ts">
	import { User } from '@lucide/svelte';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import { Button } from '$lib/components/ui/button';

	interface Props {
		entries: Array<any>;
		hasPayrollWriteAccess: boolean;
		periodStatus: string;
		onAdjustEntry: (entry: any) => void;
	}

	const { entries, hasPayrollWriteAccess, periodStatus, onAdjustEntry }: Props = $props();

	// Format currency
	function formatCurrency(amount: number, currencyCode: string = 'USD'): string {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: currencyCode
		}).format(amount);
	}

	// Get full name
	function getFullName(firstName: string, lastName: string): string {
		return `${firstName} ${lastName}`.trim();
	}

	// Group entries by payee for template functions
	function groupEntriesByPayee() {
		return entries.reduce((acc: Record<string, any>, entry: any) => {
			const payeeId = entry.payroll_profile.profile_id;
			if (!acc[payeeId]) {
				acc[payeeId] = {
					profile: entry.payroll_profile.profile,
					title: entry.payroll_profile.title,
					level: entry.payroll_profile.level,
					currencyCode: entry.payroll_profile.currency_code,
					entries: []
				};
			}

			acc[payeeId].entries.push(entry);
			return acc;
		}, {});
	}

	// Calculate totals by payee for template
	function calculateTotalsByPayee() {
		const entriesByPayee = groupEntriesByPayee();
		return Object.entries(entriesByPayee).reduce(
			(acc: Record<string, any>, [payeeId, payeeData]: [string, any]) => {
				const total = payeeData.entries.reduce((sum: number, entry: any) => {
					return sum + entry.final_amount;
				}, 0);

				acc[payeeId] = {
					...payeeData,
					total
				};

				return acc;
			},
			{}
		);
	}
</script>

<Card>
	<CardHeader>
		<CardTitle>Payroll Details</CardTitle>
		<CardDescription>Detailed breakdown of all payroll entries.</CardDescription>
	</CardHeader>
	<CardContent>
		{#if entries.length === 0}
			<div class="py-6 text-center text-muted-foreground">
				No payroll entries found. Click "Calculate Payroll" to generate entries based on events and
				formulas.
			</div>
		{:else}
			{#each Object.entries(groupEntriesByPayee()) as [payeeId, payeeData]}
				<div class="mb-8">
					<div class="mb-3 flex items-center gap-2">
						<User class="h-5 w-5 text-muted-foreground" />
						<h3 class="text-lg font-semibold">
							{getFullName(payeeData.profile.given_name, payeeData.profile.family_name)}
							{#if payeeData.title || payeeData.level}
								<span class="ml-2 text-sm font-normal text-muted-foreground">
									{[payeeData.title, payeeData.level].filter(Boolean).join(' - ')}
								</span>
							{/if}
						</h3>
					</div>

					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Type</TableHead>
								<TableHead>Source</TableHead>
								<TableHead>Formula</TableHead>
								<TableHead class="text-right">Base Amount</TableHead>
								<TableHead class="text-right">Adjustment</TableHead>
								<TableHead class="text-right">Final Amount</TableHead>
								{#if hasPayrollWriteAccess && periodStatus === 'draft'}
									<TableHead class="text-right">Actions</TableHead>
								{/if}
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each payeeData.entries as entry}
								<TableRow>
									<TableCell>
										{entry.entry_type.charAt(0).toUpperCase() + entry.entry_type.slice(1)}
									</TableCell>
									<TableCell>
										{#if entry.entry_type === 'event' && entry.entry_for_event_id}
											Event {entry.entry_for_event_id.substring(0, 8)}...
										{:else if entry.entry_type === 'product' && entry.entry_for_product_id}
											Product {entry.entry_for_product_id.substring(0, 8)}...
										{:else if entry.entry_type === 'profile'}
											Profile-based
										{:else}
											Unknown
										{/if}
									</TableCell>
									<TableCell>{entry.payroll_formula.name}</TableCell>
									<TableCell class="text-right">
										{formatCurrency(entry.formula_amount, payeeData.currencyCode)}
									</TableCell>
									<TableCell class="text-right">
										{#if entry.adjustment_amount !== 0}
											<span
												class:text-red-600={entry.adjustment_amount < 0}
												class:text-green-600={entry.adjustment_amount > 0}
											>
												{formatCurrency(entry.adjustment_amount, payeeData.currencyCode)}
											</span>
										{:else}
											-
										{/if}
									</TableCell>
									<TableCell class="text-right font-medium">
										{formatCurrency(entry.final_amount, payeeData.currencyCode)}
									</TableCell>
									{#if hasPayrollWriteAccess && periodStatus === 'draft'}
										<TableCell class="text-right">
											<Button variant="ghost" size="sm" onclick={() => onAdjustEntry(entry)}>
												Adjust
											</Button>
										</TableCell>
									{/if}
								</TableRow>
								{#if entry.adjustment_reason}
									<TableRow>
										<TableCell
											class="text-xs text-muted-foreground"
											colspan={hasPayrollWriteAccess && periodStatus === 'draft' ? 7 : 6}
										>
											<span class="font-medium">Adjustment reason:</span>
											{entry.adjustment_reason}
										</TableCell>
									</TableRow>
								{/if}
							{/each}
							<TableRow>
								<TableCell
									class="font-semibold"
									colspan={hasPayrollWriteAccess && periodStatus === 'draft' ? 5 : 4}
								>
									Total
								</TableCell>
								<TableCell
									class="text-right font-semibold"
									colspan={hasPayrollWriteAccess && periodStatus === 'draft' ? 2 : 2}
								>
									{formatCurrency(calculateTotalsByPayee()[payeeId].total, payeeData.currencyCode)}
								</TableCell>
							</TableRow>
						</TableBody>
					</Table>
				</div>
			{/each}
		{/if}
	</CardContent>
</Card>
