<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import { Button } from '$lib/components/ui/button';
	import { ArrowLeft } from '@lucide/svelte';
	import PayrollNav from '../components/PayrollNav.svelte';
	import PayrollSummary from '../components/PayrollSummary.svelte';
	import type { PageProps } from './$types';

	// Use PageProps pattern
	let { data }: PageProps = $props();
</script>

{#snippet content()}
	<div class="grid gap-6">
		<!-- Common navigation component -->
		<PayrollNav periodId={data.period.id} payrollProfiles={data.payrollProfiles || []} />

		<PayrollSummary entries={data.entries} />
	</div>
{/snippet}

<PageContainer
	title="Payroll Period Summary"
	description="Overview of payments for all payees in this period"
	{content}
/>
