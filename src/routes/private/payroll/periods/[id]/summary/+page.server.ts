import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
	const { id } = params;
	const { supabase, brand } = locals;

	// Fetch the payroll period
	const { data: period, error: periodError } = await supabase
		.from('payroll_period')
		.select('*')
		.eq('id', id)
		.single();

	if (periodError) {
		error(404, 'Payroll period not found');
	}

	// Make sure this period belongs to this brand
	if (period.brand_id !== brand.id) {
		error(403, 'You do not have access to this payroll period');
	}

	// Fetch payroll entries for this period
	const { data: entries, error: entriesError } = await supabase
		.from('payroll_entry')
		.select(
			`
			*,
			payroll_profile:payroll_profile_id (
				id,
				profile_id,
				title,
				level,
				currency_code,
				profile:profile_id (
					id,
					given_name,
					family_name,
					username
				)
			),
			payroll_formula:payroll_formula_id (
				id,
				name,
				earning_source_kind
			)
		`
		)
		.eq('payroll_period_id', id);

	if (entriesError) {
		error(500, 'Failed to fetch payroll entries');
	}

	return {
		period,
		entries: entries || []
	};
};
