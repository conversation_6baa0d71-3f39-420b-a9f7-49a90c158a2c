<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import {
		Dialog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle,
		DialogTrigger
	} from '$lib/components/ui/dialog';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Pencil, Calculator, ArrowLeft } from '@lucide/svelte';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { toast } from 'svelte-sonner';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import type { PageProps } from './$types';

	// Use PageProps pattern
	let { data }: PageProps = $props();

	let statusDialogOpen = $state(false);
	let calculatingPayroll = $state(false);
	let submitting = $state(false);

	// Format dates
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString();
	}

	// Format dates with time and timezone
	function formatDateWithTime(dateString: string): string {
		return new Date(dateString).toLocaleString('en-US', {
			year: 'numeric',
			month: 'numeric',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit',
			timeZoneName: 'short'
		});
	}

	// Calculate days between two dates (can return decimal)
	function calculateDaysBetween(startDateString: string, endDateString: string): string {
		const startDate = new Date(startDateString);
		const endDate = new Date(endDateString);
		const diffInMs = endDate.getTime() - startDate.getTime();
		const diffInDays = diffInMs / (1000 * 60 * 60 * 24);
		return diffInDays.toFixed(1);
	}

	// Generate status badge classes
	function getStatusBadgeClass(status: string): string {
		switch (status) {
			case 'draft':
				return 'bg-yellow-100 text-yellow-800';
			case 'calculating':
				return 'bg-blue-100 text-blue-800';
			case 'approved':
				return 'bg-green-100 text-green-800';
			case 'paid':
				return 'bg-purple-100 text-purple-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	}

	// Get all possible statuses
	function getAllStatuses() {
		return ['draft', 'calculating', 'approved', 'paid'];
	}

	// Get formatted status labels
	function getStatusLabel(status: string): string {
		switch (status) {
			case 'draft':
				return 'Draft';
			case 'calculating':
				return 'Calculating';
			case 'approved':
				return 'Approved';
			case 'paid':
				return 'Paid';
			default:
				return status.charAt(0).toUpperCase() + status.slice(1);
		}
	}

	function handleStatusUpdateSuccess(): void {
		statusDialogOpen = false;
		submitting = false;
		invalidateAll();
		toast.success('Status updated successfully');
	}

	function handleStatusUpdateError(error: string): void {
		submitting = false;
		toast.error(`Error updating status: ${error}`);
	}

	function handleCalculateSuccess(): void {
		calculatingPayroll = false;
		invalidateAll();
		toast.success('Payroll calculated successfully');
	}

	function handleCalculateError(error: string): void {
		calculatingPayroll = false;
		toast.error(`Error calculating payroll: ${error}`);
	}

	// Navigation links
	const navLinks = [
		{
			title: 'Events Per Payee',
			href: `./events-per-payee`,
			description: 'View events associated with each payee in this period'
		},
		{
			title: 'Summary',
			href: `./summary`,
			description: 'Overview of payments for all payees in this period'
		},
		{
			title: 'Details',
			href: `./details`,
			description: 'Detailed breakdown of all payroll entries'
		},
		{
			title: 'Events',
			href: `./events`,
			description: 'Events that occurred during this payroll period'
		}
	];
</script>

{#snippet content()}
	<div class="grid gap-6">
		<div class="mb-6 flex items-center justify-between">
			{#if data.hasPayrollWriteAccess}
				<Dialog bind:open={statusDialogOpen}>
					<DialogTrigger>
						<Badge
							variant="outline"
							class={`flex cursor-pointer items-center gap-1 px-3 py-1 text-sm hover:bg-muted ${getStatusBadgeClass(data.period.status)}`}
						>
							{getStatusLabel(data.period.status)}
							<Pencil class="ml-1 h-3 w-3" />
						</Badge>
					</DialogTrigger>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>Update Payroll Period Status</DialogTitle>
							<DialogDescription>Change the status of this payroll period.</DialogDescription>
						</DialogHeader>
						<form
							method="POST"
							action="?/updateStatus"
							use:enhance={({ formData }) => {
								submitting = true;
								return async ({ result }) => {
									if (result.type === 'success') {
										handleStatusUpdateSuccess();
									} else {
										const errorData = result.type === 'failure' ? result.data : null;
										const errorMsg =
											errorData && typeof errorData.error === 'string'
												? errorData.error
												: 'Unknown error';
										handleStatusUpdateError(errorMsg);
									}
								};
							}}
						>
							<div class="grid gap-4 py-4">
								<div class="grid gap-2">
									<Label for="status">New Status</Label>
									<select
										id="status"
										name="status"
										class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
										required
									>
										{#each getAllStatuses() as status}
											<option value={status} selected={status === data.period.status}
												>{getStatusLabel(status)}</option
											>
										{/each}
									</select>
								</div>
							</div>
							<DialogFooter>
								<Button type="submit" disabled={submitting}>
									{submitting ? 'Updating...' : 'Update Status'}
								</Button>
							</DialogFooter>
						</form>
					</DialogContent>
				</Dialog>
			{:else}
				<Badge
					variant="outline"
					class={`px-3 py-1 text-sm ${getStatusBadgeClass(data.period.status)}`}
				>
					{getStatusLabel(data.period.status)}
				</Badge>
			{/if}
		</div>

		<!-- Navigation Cards -->
		<div class="grid gap-4 md:grid-cols-2">
			{#each navLinks as link}
				<Card class="transition-colors hover:bg-muted/50">
					<a href={link.href} class="block h-full">
						<CardHeader>
							<CardTitle>{link.title}</CardTitle>
						</CardHeader>
						<CardContent>
							<p class="text-muted-foreground">{link.description}</p>
						</CardContent>
					</a>
				</Card>
			{/each}
		</div>
	</div>
{/snippet}

{#snippet actions()}
	<div class="flex items-center gap-3">
		{#if data.hasPayrollWriteAccess && data.entries.length === 0}
			<form
				method="POST"
				action="?/calculatePayroll"
				use:enhance={() => {
					calculatingPayroll = true;
					return async ({ result }) => {
						if (result.type === 'success') {
							handleCalculateSuccess();
						} else {
							const errorData = result.type === 'failure' ? result.data : null;
							const errorMsg =
								errorData && typeof errorData.error === 'string'
									? errorData.error
									: 'Unknown error';
							handleCalculateError(errorMsg);
						}
					};
				}}
			>
				<Button type="submit" disabled={calculatingPayroll}>
					<Calculator class="mr-2 h-4 w-4" />
					{calculatingPayroll ? 'Calculating...' : 'Calculate Payroll'}
				</Button>
			</form>
		{/if}
	</div>
{/snippet}

<PageContainer
	title={data.period.name}
	description={`${formatDateWithTime(data.period.start_at)} - ${formatDateWithTime(data.period.end_at)} (${calculateDaysBetween(data.period.start_at, data.period.end_at)} days)`}
	{content}
	{actions}
/>
