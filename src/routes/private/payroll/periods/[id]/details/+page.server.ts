import { error } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
	const { id } = params;
	const { supabase, brand, security } = locals;

	// Fetch the payroll period
	const { data: period, error: periodError } = await supabase
		.from('payroll_period')
		.select('*')
		.eq('id', id)
		.single();

	if (periodError) {
		error(404, 'Payroll period not found');
	}

	// Make sure this period belongs to this brand
	if (period.brand_id !== brand.id) {
		error(403, 'You do not have access to this payroll period');
	}

	// Fetch payroll entries for this period
	const { data: entries, error: entriesError } = await supabase
		.from('payroll_entry')
		.select(
			`
			*,
			payroll_profile:payroll_profile_id (
				id,
				profile_id,
				title,
				level,
				currency_code,
				profile:profile_id (
					id,
					given_name,
					family_name,
					username
				)
			),
			payroll_formula:payroll_formula_id (
				id,
				name,
				earning_source_kind
			)
		`
		)
		.eq('payroll_period_id', id);

	if (entriesError) {
		error(500, 'Failed to fetch payroll entries');
	}

	return {
		period,
		entries: entries || [],
		hasPayrollWriteAccess: security.hasPermission('brand_payroll_write')
	};
};

// Adjustment action
export const actions: Actions = {
	adjustPayrollEntry: async ({ request, params, locals }) => {
		const { supabase } = locals;

		const formData = await request.formData();
		const entryId = formData.get('entryId')?.toString();
		const adjustmentAmount = formData.get('adjustmentAmount')?.toString();
		const adjustmentReason = formData.get('adjustmentReason')?.toString() || null;

		if (!entryId || adjustmentAmount === undefined) {
			return { success: false, error: 'Missing required fields' };
		}

		try {
			const numericAdjustment = parseFloat(adjustmentAmount);

			if (isNaN(numericAdjustment)) {
				return { success: false, error: 'Adjustment amount must be a valid number' };
			}

			const { data, error } = await supabase
				.from('payroll_entry')
				.update({
					adjustment_amount: numericAdjustment,
					adjustment_reason: adjustmentReason,
					updated_at: new Date().toISOString()
				})
				.eq('id', entryId)
				.select();

			if (error) {
				console.error('Error adjusting payroll entry:', error);
				return { success: false, error: error.message };
			}

			return { success: true, data };
		} catch (error) {
			console.error('Error in adjustPayrollEntry action:', error);
			return { success: false, error: 'Server error' };
		}
	}
};
