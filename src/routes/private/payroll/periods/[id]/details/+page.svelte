<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import { Button } from '$lib/components/ui/button';
	import { ArrowLeft } from '@lucide/svelte';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import {
		Dialog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle
	} from '$lib/components/ui/dialog';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { toast } from 'svelte-sonner';
	import PayrollDetails from '../components/PayrollDetails.svelte';
	import PayrollNav from '../components/PayrollNav.svelte';
	import type { PageProps } from './$types';

	// Use PageProps pattern
	let { data }: PageProps = $props();

	let adjustDialogOpen = $state(false);
	let selectedEntry = $state<any>(null);
	let adjustmentAmount = $state('0');
	let adjustmentReason = $state('');
	let submitting = $state(false);

	function openAdjustDialog(entry: any): void {
		selectedEntry = entry;
		adjustmentAmount = entry.adjustment_amount.toString();
		adjustmentReason = entry.adjustment_reason || '';
		adjustDialogOpen = true;
	}

	function handleAdjustmentSuccess(): void {
		adjustDialogOpen = false;
		selectedEntry = null;
		submitting = false;
		invalidateAll();
		toast.success('Adjustment saved successfully');
	}

	function handleAdjustmentError(error: string): void {
		submitting = false;
		toast.error(`Error saving adjustment: ${error}`);
	}

	function handleAdjustEntry(entry: any): void {
		// Handle adjustment functionality
		console.log('Adjust entry:', entry);
	}
</script>

{#snippet content()}
	<div class="grid gap-6">
		<!-- Common navigation component -->
		<PayrollNav periodId={data.period.id} payrollProfiles={data.payrollProfiles || []} />

		<PayrollDetails
			entries={data.entries || []}
			hasPayrollWriteAccess={data.hasPayrollWriteAccess || false}
			periodStatus={data.period?.status || 'draft'}
			onAdjustEntry={handleAdjustEntry}
		/>

		{#if selectedEntry}
			<Dialog bind:open={adjustDialogOpen}>
				<DialogContent class="sm:max-w-[425px]">
					<DialogHeader>
						<DialogTitle>Adjust Payment</DialogTitle>
						<DialogDescription>
							Modify the calculated payment amount for this entry.
						</DialogDescription>
					</DialogHeader>
					<form
						method="POST"
						action="?/adjustPayrollEntry"
						use:enhance={({ formData }) => {
							submitting = true;
							return async ({ result }) => {
								if (result.type === 'success') {
									handleAdjustmentSuccess();
								} else {
									const errorData = result.type === 'failure' ? result.data : null;
									const errorMsg =
										errorData && typeof errorData.error === 'string'
											? errorData.error
											: 'Unknown error';
									handleAdjustmentError(errorMsg);
								}
							};
						}}
					>
						<input type="hidden" name="entryId" value={selectedEntry.id} />
						<div class="grid gap-4 py-4">
							<div>
								<div class="mb-2 grid grid-cols-2">
									<div>
										<p class="text-sm text-muted-foreground">Formula Amount</p>
										<p class="font-medium">
											{new Intl.NumberFormat('en-US', {
												style: 'currency',
												currency: selectedEntry.payroll_profile.currency_code
											}).format(selectedEntry.formula_amount)}
										</p>
									</div>
									<div>
										<p class="text-sm text-muted-foreground">Final Amount</p>
										<p class="font-medium">
											{new Intl.NumberFormat('en-US', {
												style: 'currency',
												currency: selectedEntry.payroll_profile.currency_code
											}).format(selectedEntry.formula_amount + parseFloat(adjustmentAmount))}
										</p>
									</div>
								</div>
							</div>
							<div class="grid gap-2">
								<Label for="adjustmentAmount">Adjustment Amount</Label>
								<Input
									id="adjustmentAmount"
									name="adjustmentAmount"
									type="number"
									step="0.01"
									bind:value={adjustmentAmount}
									required
								/>
								<p class="text-xs text-muted-foreground">
									Enter a positive value to increase the payment or a negative value to decrease it.
								</p>
							</div>
							<div class="grid gap-2">
								<Label for="adjustmentReason">Reason for Adjustment (Optional)</Label>
								<Textarea
									id="adjustmentReason"
									name="adjustmentReason"
									bind:value={adjustmentReason}
									placeholder="Explain why this adjustment is necessary"
									rows={3}
								/>
							</div>
						</div>
						<DialogFooter>
							<Button type="submit" disabled={submitting}>
								{submitting ? 'Saving...' : 'Save Adjustment'}
							</Button>
						</DialogFooter>
					</form>
				</DialogContent>
			</Dialog>
		{/if}
	</div>
{/snippet}

<PageContainer
	title="Payroll Details"
	description="Detailed breakdown of all payroll entries"
	{content}
/>
