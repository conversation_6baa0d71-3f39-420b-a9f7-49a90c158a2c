<script lang="ts">
	import { page } from '$app/stores';
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import { Button } from '$lib/components/ui/button';
	import { ArrowLeft } from '@lucide/svelte';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import PayrollNav from '../../../components/PayrollNav.svelte';
	import type { PageProps } from './$types';

	// Use PageProps pattern
	let { data }: PageProps = $props();

	// Get profile ID from URL query params
	const profileId = $derived($page.url.searchParams.get('pid'));

	// Format date for display
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleString(undefined, {
			weekday: 'short',
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: 'numeric',
			minute: '2-digit'
		});
	}

	// Get formatted title
	function getEventTitle(event: any): string {
		if (event.metadata?.auto_final_title) {
			return getLocalizedText(event.metadata.auto_final_title as LocalizedText);
		}
		return 'Untitled Event';
	}
</script>

{#snippet content()}
	<div class="grid gap-6">
		<div class="mb-6 flex items-center justify-between">
			{#if profileId}
				<Button
					variant="outline"
					size="sm"
					href="/private/payroll/periods/{data.periodId}/events-per-payee/{profileId}"
				>
					<ArrowLeft class="mr-2 h-4 w-4" />
					Back to Events for Payee
				</Button>
			{:else}
				<Button variant="outline" size="sm" href="../../events">
					<ArrowLeft class="mr-2 h-4 w-4" />
					Back to Events
				</Button>
			{/if}
		</div>

		<!-- Add PayrollNav component -->
		<PayrollNav periodId={data.periodId} payrollProfiles={data.payrollProfiles || []} />

		<div>
			<h2 class="text-2xl font-bold">Event Calculation</h2>
			<p class="text-muted-foreground">Details about earnings calculation for this event</p>

			<!-- Event calculation content here -->
			<div class="mt-6 rounded-lg border p-6">
				<p class="text-center text-muted-foreground">
					Event calculation details will be displayed here.
				</p>
			</div>
		</div>
	</div>
{/snippet}

<PageContainer
	title="Event Earning Calculation"
	description="Calculation details for this event"
	{content}
/>
