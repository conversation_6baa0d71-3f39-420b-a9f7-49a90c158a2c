import { error } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, url, locals, parent }) => {
	const { id, eventId } = params;
	const profileId = url.searchParams.get('pid');
	const { supabase, brand } = locals;

	// Fetch the payroll period
	const { data: period, error: periodError } = await supabase
		.from('payroll_period')
		.select('*')
		.eq('id', id)
		.single();

	if (periodError) {
		error(404, 'Payroll period not found');
	}

	// Make sure this period belongs to this brand
	if (period.brand_id !== brand.id) {
		error(403, 'You do not have access to this payroll period');
	}

	// Fetch the event
	const { data: event, error: eventError } = await supabase
		.from('event')
		.select(
			`
			id,
			title,
			start_at,
			auto_end_at,
			kind,
			metadata:metadata_id(
				auto_final_title,
				auto_final_subtitle
			)
		`
		)
		.eq('id', eventId)
		.single();

	if (eventError) {
		error(404, 'Event not found');
	}

	// Check if the event falls within the period
	const eventDate = new Date(event.start_at);
	const periodStart = new Date(period.start_at);
	const periodEnd = new Date(period.end_at);

	if (eventDate < periodStart || eventDate > periodEnd) {
		error(400, 'Event does not fall within this payroll period');
	}

	// Fetch payroll entries for this period
	const { data: entries, error: entriesError } = await supabase
		.from('payroll_entry')
		.select(
			`
			*,
			payroll_profile:payroll_profile_id (
				id,
				profile_id,
				title,
				level,
				currency_code,
				profile:profile_id (
					id,
					given_name,
					family_name,
					username
				)
			),
			payroll_formula:payroll_formula_id (
				id,
				name,
				earning_source_kind
			)
		`
		)
		.eq('payroll_period_id', id)
		.eq('entry_for_event_id', eventId);

	if (entriesError) {
		console.error('Error fetching entries:', entriesError);
	}

	// If profile ID is provided, fetch the profile
	let payeeProfile = null;
	if (profileId) {
		const { data: profile, error: profileError } = await supabase
			.from('payroll_profile')
			.select(
				`
				*,
				profile:profile_id (
					id,
					username,
					given_name,
					family_name,
					auto_user_email
				)
			`
			)
			.eq('profile_id', profileId)
			.single();

		if (!profileError) {
			payeeProfile = profile;
		}
	}

	// Prepare calculation details
	const calculationDetails = entries
		? entries
				.filter(
					(entry) =>
						entry.entry_for_event_id === eventId &&
						(!profileId || entry.payroll_profile?.profile_id === profileId)
				)
				.map((entry) => ({
					formula: entry.payroll_formula,
					base_rate: entry.formula_amount,
					multiplier: 1,
					amount: entry.final_amount
				}))
		: [];

	try {
		const parentData = await parent();

		// Return data with proper periodId for client use
		return {
			...parentData,
			periodId: id,
			eventId,
			period,
			event,
			entries: entries || [],
			payeeProfile,
			calculationDetails: calculationDetails.length > 0 ? calculationDetails : null
		};
	} catch (err) {
		console.error('Error loading event calculation:', err);
		error(500, 'Error loading event calculation data');
	}
};

export const actions: Actions = {
	calculateAndSave: async ({ request, locals, params }) => {
		const formData = await request.formData();
		const { id: periodId, eventId } = params;

		const payrollProfileId = formData.get('payrollProfileId') as string;
		const formulaId = formData.get('formulaId') as string;
		const formulaAmount = parseFloat(formData.get('formulaAmount') as string);
		const adjustmentAmount = parseFloat((formData.get('adjustmentAmount') as string) || '0');
		const adjustmentReason = formData.get('adjustmentReason') as string;

		// Parse formula inputs from JSON string
		const formulaInputs = JSON.parse(formData.get('formulaInputs') as string);

		const { supabase } = locals;

		// Check if entry already exists
		const { data: existingEntry } = await supabase
			.from('payroll_entry')
			.select('id')
			.eq('payroll_period_id', periodId)
			.eq('payroll_profile_id', payrollProfileId)
			.eq('entry_for_event_id', eventId)
			.maybeSingle();

		if (existingEntry) {
			// Update existing entry
			const { error: updateError } = await supabase
				.from('payroll_entry')
				.update({
					payroll_formula_id: formulaId,
					formula_amount: formulaAmount,
					adjustment_amount: adjustmentAmount,
					adjustment_reason: adjustmentReason,
					event_formula_inputs: formulaInputs,
					calculation_details: JSON.parse(formData.get('calculationDetails') as string),
					updated_at: new Date().toISOString()
				})
				.eq('id', existingEntry.id);

			if (updateError) {
				return { success: false, error: updateError.message };
			}
		} else {
			// Create new entry
			const { error: insertError } = await supabase.from('payroll_entry').insert({
				payroll_period_id: periodId,
				payroll_profile_id: payrollProfileId,
				payroll_formula_id: formulaId,
				entry_type: 'event',
				entry_for_event_id: eventId,
				formula_amount: formulaAmount,
				adjustment_amount: adjustmentAmount || 0,
				adjustment_reason: adjustmentReason || null,
				event_formula_inputs: formulaInputs,
				calculation_details: JSON.parse(formData.get('calculationDetails') as string)
			});

			if (insertError) {
				return { success: false, error: insertError.message };
			}
		}

		return {
			success: true,
			message: 'Payroll entry saved successfully'
		};
	}
};
