import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
	const { id } = params;
	const { supabase, brand } = locals;

	// Fetch the payroll period
	const { data: period, error: periodError } = await supabase
		.from('payroll_period')
		.select('*')
		.eq('id', id)
		.single();

	if (periodError) {
		error(404, 'Payroll period not found');
	}

	// Make sure this period belongs to this brand
	if (period.brand_id !== brand.id) {
		error(403, 'You do not have access to this payroll period');
	}

	// Fetch events in this period
	const { data: events, error: eventsError } = await supabase
		.from('event')
		.select(
			`
			id,
			title,
			start_at,
			auto_end_at,
			kind,
			metadata:metadata_id!inner (
				auto_final_title,
				auto_final_subtitle
			),
			event_member(count)
		`
		)
		.eq('brand_id', brand.id)
		.gte('start_at', period.start_at)
		.lte('start_at', period.end_at)
		.order('start_at', { ascending: true });

	if (eventsError) {
		error(500, 'Failed to fetch events');
	}

	return {
		period,
		events
	};
};
