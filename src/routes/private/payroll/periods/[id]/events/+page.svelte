<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import { Button } from '$lib/components/ui/button';
	import { ArrowLeft } from '@lucide/svelte';
	import PayrollNav from '../components/PayrollNav.svelte';
	import EventsList from '../components/EventsList.svelte';
	import type { PageProps } from './$types';

	// Use PageProps pattern
	let { data }: PageProps = $props();
</script>

{#snippet content()}
	<div class="grid gap-6">
		<!-- Common navigation component -->
		<PayrollNav periodId={data.period.id} payrollProfiles={data.payrollProfiles || []} />

		<EventsList events={data.events} period={data.period} />
	</div>
{/snippet}

<PageContainer
	title="Payroll Period Events"
	description="Events that occurred during this payroll period"
	{content}
/>
