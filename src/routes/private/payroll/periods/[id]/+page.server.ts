import { redirect, error } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import type { PayrollPeriodData, PayrollEntryData, PayrollProfileData } from '$lib/types/payroll';

export const load: PageServerLoad = async ({ params, locals, url }) => {
	const { id } = params;
	const { user, brand, supabase, security } = locals;

	// Redirect to summary by default
	if (url.pathname === `/private/payroll/periods/${id}`) {
		redirect(303, `/private/payroll/periods/${id}/summary`);
	}

	// Fetch the payroll period
	const { data: period, error: periodError } = await supabase
		.from('payroll_period')
		.select('*')
		.eq('id', id)
		.single();

	if (periodError) {
		console.error('Error fetching payroll period:', periodError);
		error(404, 'Payroll period not found');
	}

	// Make sure the period belongs to this brand
	if (period.brand_id !== brand.id) {
		error(403, 'You do not have access to this payroll period');
	}

	// Fetch payroll entries for this period
	const { data: entries, error: entriesError } = await supabase
		.from('payroll_entry')
		.select(
			`
			*,
			payroll_profile:payroll_profile_id (
				id,
				profile_id,
				title,
				level,
				currency_code,
				profile:profile_id (
					id,
					given_name,
					family_name,
					username
				)
			),
			payroll_formula:payroll_formula_id (
				id,
				name,
				earning_source_kind
			)
		`
		)
		.eq('payroll_period_id', id);

	if (entriesError) {
		console.error('Error fetching payroll entries:', entriesError);
	}

	// Fetch the events that fall within the period's date range
	const { data: events, error: eventsError } = await supabase
		.from('event')
		.select(
			`
			id,
			title,
			start_at,
			auto_end_at,
			kind,
			metadata:metadata_id (
				auto_final_title,
				auto_final_subtitle
			),
			event_member(count)
		`
		)
		.eq('brand_id', brand.id)
		.gte('start_at', period.start_at)
		.lte('start_at', period.end_at)
		.order('start_at', { ascending: true });

	if (eventsError) {
		console.error('Error fetching events:', eventsError);
	}

	return {
		period: period as PayrollPeriodData,
		entries:
			(entries as Array<
				PayrollEntryData & {
					payroll_profile: PayrollProfileData & {
						profile: {
							id: string;
							given_name: string;
							family_name: string;
							username: string;
						};
					};
					payroll_formula: {
						id: string;
						name: string;
						earning_source_kind: 'event' | 'product' | 'profile';
					};
				}
			>) || [],
		events: events || [],
		isBrandOwner: security.isBrandOwner,
		hasPayrollWriteAccess: security.hasPermission('brand_payroll_write')
	};
};

export const actions: Actions = {
	updateStatus: async ({ request, params, locals }) => {
		const { supabase } = locals;
		const periodId = params.id;

		const formData = await request.formData();
		const status = formData.get('status')?.toString();

		if (!status || !['draft', 'calculating', 'approved', 'paid'].includes(status)) {
			return { success: false, error: 'Invalid status' };
		}

		try {
			const { data, error } = await supabase
				.from('payroll_period')
				.update({ status, updated_at: new Date().toISOString() })
				.eq('id', periodId)
				.select();

			if (error) {
				console.error('Error updating payroll period status:', error);
				return { success: false, error: error.message };
			}

			return { success: true, data };
		} catch (error) {
			console.error('Error in updateStatus action:', error);
			return { success: false, error: 'Server error' };
		}
	},

	calculatePayroll: async ({ params, locals }) => {
		const { supabase } = locals;
		const periodId = params.id;

		try {
			// First update status to calculating
			const { error: statusError } = await supabase
				.from('payroll_period')
				.update({
					status: 'calculating',
					updated_at: new Date().toISOString()
				})
				.eq('id', periodId);

			if (statusError) {
				console.error('Error updating period status:', statusError);
				return { success: false, error: statusError.message };
			}

			// Call the stored procedure or RPC to calculate payroll
			const { error: calculateError } = await supabase.rpc('calculate_payroll_for_period', {
				period_id_param: periodId
			});

			if (calculateError) {
				console.error('Error calculating payroll:', calculateError);
				return { success: false, error: calculateError.message };
			}

			// Update status to draft after calculation
			const { error: updateError } = await supabase
				.from('payroll_period')
				.update({
					status: 'draft',
					updated_at: new Date().toISOString()
				})
				.eq('id', periodId);

			if (updateError) {
				console.error('Error updating period status after calculation:', updateError);
				return { success: false, error: updateError.message };
			}

			return { success: true };
		} catch (error) {
			console.error('Error in calculatePayroll action:', error);
			return { success: false, error: 'Server error' };
		}
	}
};
