import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, url }) => {
	// This is a fallback route that handles incorrectly formatted URLs
	// Extract the profileId and construct a correct URL with a valid period ID
	const { profileId } = params;

	// Redirect to a global payroll page that can lookup the profile
	// and find an appropriate period, or to the payroll periods list
	redirect(302, '/private/payroll/periods');
};
