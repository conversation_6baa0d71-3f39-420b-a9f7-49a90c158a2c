<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import { PlusCircle, Calendar, Users, FileCode, MoreHorizontal, Trash } from '@lucide/svelte';
	import type { PageProps } from './$types';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';
	import { invalidateAll } from '$app/navigation';

	// Use PageProps pattern
	let { data, form }: PageProps = $props();

	// For debugging - expanded to show more details
	console.log('PROPS RECEIVED:', {
		isBrandOwner: data.isBrandOwner,
		hasPayrollWriteAccess: data.hasPayrollWriteAccess,
		periodsCount: data.payrollPeriods.length,
		ownerType: typeof data.isBrandOwner,
		writeAccessType: typeof data.hasPayrollWriteAccess
	});

	// Routes don't need to be state variables
	const newPeriodRoute = '/private/payroll/periods/new';
	const formulasRoute = '/private/payroll/formulas';
	const payeesRoute = '/private/payroll/payees';

	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString();
	}

	// No need to convert booleans since the server returns actual booleans
	const showActionButtons = $derived(data.isBrandOwner || data.hasPayrollWriteAccess);
	const canDelete = $derived(data.isBrandOwner || data.hasPayrollWriteAccess);

	// Log the computed values
	console.log('COMPUTED ACCESS:', {
		isBrandOwner: data.isBrandOwner,
		hasPayrollWriteAccess: data.hasPayrollWriteAccess,
		showActionButtons
	});
</script>

{#snippet content()}
	<div class="grid gap-6">
		{#if showActionButtons}
			<div class="flex flex-wrap gap-4">
				<Button href={newPeriodRoute}>
					<PlusCircle class="mr-2 h-4 w-4" />
					New Payroll Period
				</Button>
				<Button href={formulasRoute} variant="outline">
					<FileCode class="mr-2 h-4 w-4" />
					Manage Formulas
				</Button>
				<Button href={payeesRoute} variant="outline">
					<Users class="mr-2 h-4 w-4" />
					Manage Payees
				</Button>
			</div>
		{/if}

		<Card>
			<CardHeader>
				<CardTitle>Payroll Periods</CardTitle>
				<CardDescription>View and manage payroll periods for your organization.</CardDescription>
			</CardHeader>
			<CardContent>
				{#if data.payrollPeriods.length === 0}
					<div class="py-4 text-center text-muted-foreground">
						No payroll periods found. Create a new period to get started.
					</div>
				{:else}
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Name</TableHead>
								<TableHead>Period</TableHead>
								<TableHead>Status</TableHead>
								<TableHead class="text-right">Actions</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{#each data.payrollPeriods as period}
								<TableRow>
									<TableCell class="font-medium">{period.name}</TableCell>
									<TableCell>
										{formatDate(period.start_at)} - {formatDate(period.end_at)}
									</TableCell>
									<TableCell>
										<span
											class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
											class:bg-yellow-100={period.status === 'draft'}
											class:text-yellow-800={period.status === 'draft'}
											class:bg-blue-100={period.status === 'calculating'}
											class:text-blue-800={period.status === 'calculating'}
											class:bg-green-100={period.status === 'approved'}
											class:text-green-800={period.status === 'approved'}
											class:bg-purple-100={period.status === 'paid'}
											class:text-purple-800={period.status === 'paid'}
										>
											{period.status.charAt(0).toUpperCase() + period.status.slice(1)}
										</span>
									</TableCell>
									<TableCell class="text-right">
										<div class="flex items-center justify-end">
											<Button
												href={`/private/payroll/periods/${period.id}`}
												variant="ghost"
												size="sm"
											>
												View
											</Button>

											{#if canDelete}
												<DropdownMenu.Root>
													<DropdownMenu.Trigger>
														{#snippet child({ props })}
															<Button {...props} variant="ghost" size="icon" class="ml-1 h-8 w-8">
																<MoreHorizontal class="h-4 w-4" />
															</Button>
														{/snippet}
													</DropdownMenu.Trigger>
													<DropdownMenu.Content align="end">
														<form
															method="POST"
															action="?/deletePayrollPeriod"
															use:enhance={() => {
																return async ({ result }) => {
																	if (result.type === 'success') {
																		toast.success('Payroll period deleted successfully');
																		await invalidateAll(); // Refresh data after successful deletion
																	} else if (result.type === 'failure' && result.data) {
																		toast.error(
																			(result.data.message as string) ||
																				'Failed to delete payroll period'
																		);
																	} else {
																		toast.error('An error occurred');
																	}
																};
															}}
															id={`delete-form-${period.id}`}
														>
															<input type="hidden" name="id" value={period.id} />
															<DropdownMenu.Item
																class="text-destructive focus:text-destructive"
																onclick={() => {
																	if (
																		confirm('Are you sure you want to delete this payroll period?')
																	) {
																		const form = document.getElementById(
																			`delete-form-${period.id}`
																		) as HTMLFormElement;
																		form?.requestSubmit();
																	}
																}}
															>
																<Trash class="mr-2 h-4 w-4" />
																Delete Period
															</DropdownMenu.Item>
														</form>
													</DropdownMenu.Content>
												</DropdownMenu.Root>
											{/if}
										</div>
									</TableCell>
								</TableRow>
							{/each}
						</TableBody>
					</Table>
				{/if}
			</CardContent>
		</Card>
	</div>
{/snippet}

<PageContainer
	title="Payroll Management"
	description="Manage employee payroll information and transactions"
	{content}
/>
