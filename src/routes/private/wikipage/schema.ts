import { z } from 'zod';

const requiredLocalizedTextSchema = z.object({
	en: z.string().min(1, 'Please enter a title in English'),
	zh: z.string().optional(),
	ja: z.string().optional(),
	ko: z.string().optional()
});

const optionalLocalizedTextSchema = z.object({
	en: z.string().optional(),
	zh: z.string().optional(),
	ja: z.string().optional(),
	ko: z.string().optional()
});

export const schema = z.object({
	title: requiredLocalizedTextSchema,
	brief: optionalLocalizedTextSchema,
	desc: optionalLocalizedTextSchema,
	kind: z.string().min(1, 'Please select a wikipage kind'),
	domain: z.array(z.string()),
	icon: z.string().nullable().optional(),
	instagram_id: z.string().optional(),
	youtube_id: z.string().optional(),
	facebook_id: z.string().optional(),
	twitter_id: z.string().optional(),
	tiktok_id: z.string().optional(),
	semantic_order: z.number().optional()
});
