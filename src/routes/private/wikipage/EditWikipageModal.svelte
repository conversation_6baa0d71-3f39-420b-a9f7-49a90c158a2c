<!-- src/routes/private/wikipage/EditWikipageModal.svelte -->
<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import * as Select from '$lib/components/ui/select';
	import type { Database } from '$lib/supabase/database.types';
	import type { SuperValidated } from 'sveltekit-superforms';
	import type { z } from 'zod';
	import { schema } from './schema';
	import {
		ensureLocalizedText,
		getLocalizedText,
		type LocalizedText
	} from '$lib/utils/localization';
	import ResponsiveModal from '$lib/components/shared/ResponsiveModal.svelte';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { Control, Label, Field, FieldErrors } from 'formsnap';
	import SuperDebug from 'sveltekit-superforms';
	import type { Json } from '$lib/supabase/database.types';
	import { goto, invalidate } from '$app/navigation';
	import { page } from '$app/state';

	// Use the database row type directly
	type WikipageRow = Database['public']['Tables']['wikipage']['Row'] & {
		brand: Database['public']['Tables']['brand']['Row'] | null;
		kind: Database['public']['Tables']['wikipage_kind']['Row'];
	};

	interface Props {
		wikipage: WikipageRow | null;
		kinds: Database['public']['Tables']['wikipage_kind']['Row'][];
		form: SuperValidated<z.infer<typeof schema>>;
		onClose: () => void;
	}

	let { wikipage, kinds, form, onClose }: Props = $props();

	// If we have a wikipage, initialize form data with its values
	if (wikipage && form.data) {
		form.data.title = ensureLocalizedText(wikipage.title as Json);
		form.data.brief = ensureLocalizedText(wikipage.brief as Json);
		form.data.desc = ensureLocalizedText(wikipage.desc as Json);
		form.data.kind = wikipage.kind?.id || '';
	}

	const superFormInstance = superForm(form, {
		applyAction: false,
		taintedMessage: null,
		resetForm: false,
		dataType: 'json',
		onResult: ({ result }) => {
			// Close modal on successful submission
			if (result.type === 'success') {
				// Clear URL parameters and close the modal
				goto(page.url.pathname, {
					replaceState: true,
					keepFocus: true,
					noScroll: true,
					invalidateAll: true
				});
				// Invalidate the wikipages data to refresh the list
				invalidate('supabase:db:wikipages');
			} else if (result.type === 'error') {
				console.error('Form submission error:', result.error);
			}
		},
		onError: ({ result }) => {
			console.error('Form validation error:', result);
		}
	});

	const { enhance, submitting, form: formData, errors, message } = superFormInstance;
</script>

{#snippet footerButtons()}
	<div class="flex justify-end gap-2">
		<form data-sveltekit-replacestate data-sveltekit-noscroll class="inline-block">
			<Button variant="outline" type="submit">Cancel</Button>
		</form>
		<Button type="submit" form="wikipage-form" disabled={$submitting}>
			{$submitting ? 'Saving...' : 'Save'}
		</Button>
	</div>
{/snippet}

<ResponsiveModal
	open={true}
	title={`${wikipage ? 'Edit' : 'Create'} Wikipage`}
	description={wikipage
		? `Editing wikipage "${getLocalizedText(wikipage.title)}" created at ${new Date(wikipage.created_at).toLocaleDateString()}`
		: 'Create a new wikipage'}
	onOpenChange={(open) => {
		if (!open) {
			goto(page.url.pathname, { replaceState: true, keepFocus: true, noScroll: true });
		}
	}}
	footer={footerButtons}
>
	<form
		id="wikipage-form"
		method="POST"
		action={wikipage ? `?/upsert&wid=${wikipage.id}` : '?/upsert'}
		use:enhance
	>
		{#if $message}
			<div
				class={`mb-4 rounded p-3 ${$message.type === 'error' ? 'border-red-400 bg-red-100 text-red-700' : 'border-green-400 bg-green-100 text-green-700'}`}
			>
				{$message.text}
			</div>
		{/if}

		<div class="grid gap-2">
			<LocalizedTextControl form={superFormInstance} name="title" label="Title" />

			<LocalizedTextControl form={superFormInstance} name="brief" label="Brief" multiline />

			<LocalizedTextControl form={superFormInstance} name="desc" label="Description" multiline />

			<Field form={superFormInstance} name="kind">
				<Control>
					{#snippet children({ props })}
						<Label>Kind</Label>
						<Select.Root type="single" bind:value={$formData.kind} name={props.name}>
							<Select.Trigger class={`w-full ${$errors.kind ? 'border-red-500' : ''}`} {...props}>
								{$formData.kind ? kinds.find((k) => k.id === $formData.kind)?.id : 'Select kind'}
							</Select.Trigger>
							<Select.Content>
								<Select.Group>
									<Select.GroupHeading>Kinds</Select.GroupHeading>
									{#each kinds as k}
										<Select.Item value={k.id} label={k.id}>{k.id}</Select.Item>
									{/each}
								</Select.Group>
							</Select.Content>
						</Select.Root>
					{/snippet}
				</Control>
				<FieldErrors class="mt-1 text-xs text-red-500" />
			</Field>
			<SuperDebug data={$formData} />
		</div>
	</form>
</ResponsiveModal>
