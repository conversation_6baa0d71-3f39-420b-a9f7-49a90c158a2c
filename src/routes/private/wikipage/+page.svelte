<!-- src/routes/private/wikipage/+page.svelte -->
<script lang="ts">
	import { invalidate } from '$app/navigation';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button';
	import EditWikipageModal from './EditWikipageModal.svelte';
	import DataTable from '$lib/components/shared/DataTable.svelte';
	import { getLocalizedText, type LocalizedText, type LocaleKey } from '$lib/utils/localization';
	import { PageContainer } from '$lib/components/layout';
	import {
		createSelectColumn,
		createLocalizedColumn,
		createSortableColumn,
		createDateColumn,
		createActionsColumn
	} from '$lib/components/shared/table-utils';
	import type { ColumnDef } from '@tanstack/table-core';
	import type { Database } from '$lib/supabase/database.types';
	import * as Select from '$lib/components/ui/select';
	import type { <PERSON><PERSON> } from '$lib/supabase/database.types';

	// Use the database row type directly
	type Wikipage = Database['public']['Tables']['wikipage']['Row'] & {
		brand: Database['public']['Tables']['brand']['Row'] | null;
		kind: Database['public']['Tables']['wikipage_kind']['Row'];
	};

	let { data } = $props();
	let { wikipages, kinds, form, selectedWikipage } = $derived(data);

	let isLoading = $state(false);
	let errorMessage = $state<string | null>(null);
	let selectedKind = $state<string>('');
	const currentLocale: LocaleKey = 'en';
	let filteredWikipages = $state<Wikipage[]>(wikipages);

	// URL-driven modal state
	let showModal = $derived.by(() => {
		const wid = $page.url.searchParams.get('wid');
		return !!wid;
	});

	function openCreateModal() {
		goto('?wid=new', { keepFocus: true });
	}

	function openEditModal(wikipage: Wikipage) {
		goto(`?wid=${wikipage.id}`, { keepFocus: true });
	}

	function closeModal() {
		// Reset URL to base pathname, removing all parameters
		goto($page.url.pathname, { replaceState: true, keepFocus: true });
		invalidate('supabase:db:wikipages');
	}

	$effect(() => {
		filteredWikipages = selectedKind
			? wikipages.filter((w) => w.kind.id === selectedKind)
			: wikipages;
	});

	const columns: ColumnDef<Wikipage, any>[] = [
		createSelectColumn<Wikipage>(),
		createLocalizedColumn<Wikipage>('title', 'Title', (row) => row.title as Json, currentLocale),
		createLocalizedColumn<Wikipage>('brief', 'Brief', (row) => row.brief as Json, currentLocale),
		createSortableColumn<Wikipage>('kind', 'Kind', (row) => row.kind.id),
		createDateColumn<Wikipage>('created_at', 'Created At', (row) => row.created_at),
		createActionsColumn<Wikipage>({
			label: 'Edit',
			onClick: (row) => openEditModal(row)
		})
	];
</script>

{#snippet actions()}
	<div class="flex items-center gap-4">
		<Select.Root type="single" bind:value={selectedKind}>
			<Select.Trigger class="w-[200px]">
				{kinds.find((k) => k.id === selectedKind)?.id ?? 'All Kinds'}
			</Select.Trigger>
			<Select.Content>
				<Select.Group>
					<Select.GroupHeading>Filter by Kind</Select.GroupHeading>
					<Select.Item value="" label="All">All</Select.Item>
					{#each kinds as kind}
						<Select.Item value={kind.id} label={kind.id}>{kind.id}</Select.Item>
					{/each}
				</Select.Group>
			</Select.Content>
		</Select.Root>

		<Button onclick={openCreateModal}>Create New</Button>
	</div>
{/snippet}

{#snippet content()}
	{#if errorMessage}
		<div class="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700" role="alert">
			{errorMessage}
		</div>
	{/if}

	<DataTable data={filteredWikipages} {columns} onRowAction={openEditModal} />

	{#if showModal}
		<EditWikipageModal
			wikipage={$page.url.searchParams.get('wid') === 'new' ? null : selectedWikipage}
			{kinds}
			form={form!}
			onClose={closeModal}
		/>
	{/if}
{/snippet}

<PageContainer title="Wikipages" description="Manage your wiki pages" {actions} {content} />
