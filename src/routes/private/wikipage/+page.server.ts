import type { Actions, ServerLoad } from '@sveltejs/kit';
import type { Database } from '$lib/supabase/database.types';
import { schema } from './schema';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { fail, error } from '@sveltejs/kit';
import type { z } from 'zod';

// Define type for wiki page input including DB fields
type WikipageInput = z.infer<typeof schema> & {
	id?: string;
	brand_id: string;
	created_at?: string;
	updated_at: string;
	creator_id?: string;
};

export const load = (async ({ depends, locals: { supabase, brand }, url }) => {
	depends('supabase:db:wikipages');

	const wid = url.searchParams.get('wid');

	const { data: wikipages, error: wikiError } = await supabase
		.from('wikipage')
		.select(
			`
      *,
      brand:brand_id (*),
      kind:wikipage_kind (*)
    `
		)
		.eq('brand_id', brand.id)
		.order('created_at', { ascending: false });

	const { data: kinds } = await supabase.from('wikipage_kind').select('*');

	if (wikiError) {
		console.error('Error loading wikipages:', wikiError);
		return { wikipages: [], kinds: [], form: null };
	}

	const form = await superValidate(zod(schema));

	// If wid is present, find the corresponding wikipage
	const selectedWikipage = wid ? wikipages?.find((w) => w.id === wid) : null;

	return { wikipages, kinds: kinds ?? [], form, selectedWikipage };
}) satisfies ServerLoad;

export const actions = {
	upsert: async ({ request, locals: { supabase, brand, user }, url }) => {
		// Bail early if no user
		if (!user) {
			throw error(401, 'Unauthorized: You must be logged in');
		}
		const formData = await request.formData();
		const form = await superValidate(formData, zod(schema));
		const wid = url.searchParams.get('wid');

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			const isUpdate = !!wid;
			const now = new Date().toISOString();

			const record: WikipageInput = {
				...form.data,
				brand_id: brand.id,
				updated_at: now
			};
			record.creator_id = user.id;

			// For new records, add creation metadata
			if (!isUpdate) {
				record.id = crypto.randomUUID();
				record.created_at = now;
			} else {
				record.id = wid; // Ensure ID is included for updates
				record.updated_at = now;
			}

			const { error: upsertError } = await supabase.from('wikipage').upsert(record).select();

			if (upsertError) {
				console.error('Error upserting wikipage:', upsertError);
				return fail(500, {
					form,
					message: {
						type: 'error',
						text: `Failed to ${isUpdate ? 'update' : 'create'} wikipage: ${upsertError.message}`
					}
				});
			}

			return {
				form,
				message: {
					type: 'success',
					text: `Wikipage ${isUpdate ? 'updated' : 'created'} successfully!`
				}
			};
		} catch (error) {
			console.error('Unexpected error in upsert action:', error);
			return fail(500, {
				form,
				message: {
					type: 'error',
					text: 'An unexpected error occurred while saving the wikipage. Please try again.'
				}
			});
		}
	}
} satisfies Actions;
