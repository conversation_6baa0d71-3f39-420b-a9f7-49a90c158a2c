import type { PageServerLoad } from './$types';
import { redirect } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ locals: { supabase, safeGetSession } }) => {
	const { user } = await safeGetSession();
	if (!user) redirect(303, '/login');

	// Get user's brands
	const { data: brands, error: brandsError } = await supabase
		.from('brand')
		.select('id, name_full')
		.eq('owner_profile_id', user.id);

	if (brandsError) {
		console.error('Error fetching brands:', brandsError);
		return {
			brands: [],
			termGroups: []
		};
	}

	// Get term groups for user's brands
	const brandIds = brands?.map((b) => b.id) || [];

	const { data: termGroups, error: groupsError } = await supabase
		.from('term_group')
		.select(
			`
			*,
			brand!inner(name_full),
			term_group_item(count)
		`
		)
		.in('brand_id', brandIds)
		.order('created_at', { ascending: false });

	if (groupsError) {
		console.error('Error fetching term groups:', groupsError);
	}

	return {
		brands: brands || [],
		termGroups: termGroups || []
	};
};
