<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import * as Select from '$lib/components/ui/select';
	import { Label } from '$lib/components/ui/label';
	import { ArrowLeft } from '@lucide/svelte';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';
	import { getLocalizedText } from '$lib/utils/localization';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { z } from 'zod';
	import type { PageData, ActionData } from './$types';

	let { data, form: actionForm }: { data: PageData; form: ActionData } = $props();

	// Create a simple schema for the form
	const schema = z.object({
		brand_id: z.string(),
		title: z.object({
			en: z.string(),
			zh: z.string(),
			ja: z.string(),
			ko: z.string()
		})
	});

	const form = superForm(
		{
			brand_id: data.brands[0]?.id || '',
			title: { en: '', zh: '', ja: '', ko: '' }
		},
		{
			validators: zodClient(schema),
			dataType: 'json'
		}
	);

	const { form: formData } = form;

	let brand_id = $state($formData.brand_id);

	// Keep $formData.brand_id in sync with brand_id
	$effect(() => {
		$formData.brand_id = brand_id;
	});

	// Show error toast if there's an error from the action
	$effect(() => {
		if (actionForm?.error) {
			toast.error(actionForm.error);
		}
	});
</script>

{#snippet content()}
	<div class="mx-auto max-w-2xl space-y-6">
		<!-- Back Button -->
		<div class="flex items-center gap-4">
			<Button href="/private/term-group" variant="ghost" size="icon">
				<ArrowLeft class="h-4 w-4" />
			</Button>
		</div>

		<!-- Form -->
		<form method="POST" use:enhance>
			<Card.Root>
				<Card.Header>
					<Card.Title>Group Details</Card.Title>
					<Card.Description>
						Create a collection of legal documents for your programs and products
					</Card.Description>
				</Card.Header>
				<Card.Content class="space-y-6">
					<!-- Brand Selection -->
					{#if data.brands.length > 1}
						<div class="space-y-2">
							<Label for="brand_id">Brand <span class="text-red-500">*</span></Label>
							<Select.Root type="single" name="brand_id" bind:value={brand_id}>
								<Select.Trigger id="brand_id" class="w-full">
									{#if brand_id}
										{@const selectedBrand = data.brands.find((b) => b.id === brand_id)}
										{#if selectedBrand}
											{getLocalizedText(selectedBrand.name_full, 'en')}
										{:else}
											Select a brand
										{/if}
									{:else}
										Select a brand
									{/if}
								</Select.Trigger>
								<Select.Content>
									{#each data.brands as brand}
										<Select.Item value={brand.id} label={getLocalizedText(brand.name_full, 'en')}>
											{getLocalizedText(brand.name_full, 'en')}
										</Select.Item>
									{/each}
								</Select.Content>
							</Select.Root>
							<input type="hidden" name="brand_id" value={brand_id} />
						</div>
					{:else}
						<input type="hidden" name="brand_id" value={data.brands[0].id} />
					{/if}

					<!-- Title -->
					<div class="space-y-2">
						<LocalizedTextControl label="Group Name" name="title" {form} required />
						<p class="text-xs text-muted-foreground">
							Give this group a descriptive name that helps you identify it
						</p>
					</div>
				</Card.Content>
				<Card.Footer class="flex justify-end gap-3">
					<Button href="/private/term-group" variant="outline">Cancel</Button>
					<Button type="submit">Create Group</Button>
				</Card.Footer>
			</Card.Root>
		</form>
	</div>
{/snippet}

<PageContainer
	title="Create Document Group"
	description="Group related legal documents that customers need to agree to together"
	{content}
/>
