import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { supabase, safeGetSession } }) => {
	const { user } = await safeGetSession();
	if (!user) redirect(303, '/login');

	// Get user's brands
	const { data: brands, error } = await supabase
		.from('brand')
		.select('id, name_full')
		.eq('owner_profile_id', user.id);

	if (error || !brands || brands.length === 0) {
		redirect(303, '/private/term-group');
	}

	return {
		brands
	};
};

export const actions = {
	default: async ({ request, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		const formData = await request.formData();
		const brandId = formData.get('brand_id') as string;
		const title = {
			en: formData.get('title[en]') as string,
			zh: formData.get('title[zh]') as string,
			ja: formData.get('title[ja]') as string,
			ko: formData.get('title[ko]') as string
		};

		// Validate required fields
		if (!brandId || !title.en) {
			return fail(400, {
				error: 'Brand and English title are required'
			});
		}

		// Verify user owns the brand
		const { data: brand, error: brandError } = await supabase
			.from('brand')
			.select('id')
			.eq('id', brandId)
			.eq('owner_profile_id', user.id)
			.single();

		if (brandError || !brand) {
			return fail(403, {
				error: 'You do not have permission to create groups for this brand'
			});
		}

		// Create the term group
		const { data: termGroup, error } = await supabase
			.from('term_group')
			.insert({
				brand_id: brandId,
				title
			})
			.select()
			.single();

		if (error) {
			console.error('Error creating term group:', error);
			return fail(500, {
				error: error.message || 'Failed to create document group'
			});
		}

		// Redirect to the new term group page
		redirect(303, `/private/term-group/${termGroup.id}`);
	}
} satisfies Actions;
