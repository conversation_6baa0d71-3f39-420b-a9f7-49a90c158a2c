<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { Plus, FileText, ChevronRight } from '@lucide/svelte';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();
</script>

{#snippet content()}
	<div class="space-y-6">
		<!-- Header -->
		<div class="flex items-center justify-between">
			<div>
				<h1 class="text-3xl font-semibold tracking-tight">Legal Documents</h1>
				<p class="text-muted-foreground">Manage your terms, policies, and legal agreements</p>
			</div>
			{#if data.brands.length > 0}
				<Button href="/private/term-group/new" class="gap-2">
					<Plus class="h-4 w-4" />
					New Document Group
				</Button>
			{/if}
		</div>

		{#if data.brands.length === 0}
			<!-- No brands message -->
			<Card.Root>
				<Card.Content class="flex flex-col items-center justify-center py-12 text-center">
					<FileText class="mb-4 h-12 w-12 text-muted-foreground" />
					<h3 class="mb-2 text-lg font-semibold">No Brands Found</h3>
					<p class="mb-4 text-sm text-muted-foreground">
						You need to create a brand before managing legal documents.
					</p>
					<Button href="/private/brand/new">Create Your First Brand</Button>
				</Card.Content>
			</Card.Root>
		{:else if data.termGroups.length === 0}
			<!-- Empty state -->
			<Card.Root>
				<Card.Content class="flex flex-col items-center justify-center py-12 text-center">
					<FileText class="mb-4 h-12 w-12 text-muted-foreground" />
					<h3 class="mb-2 text-lg font-semibold">No Document Groups Yet</h3>
					<p class="mb-4 max-w-md text-sm text-muted-foreground">
						Document groups help you organize related legal documents that customers need to agree
						to together.
					</p>
					<Button href="/private/term-group/new" class="gap-2">
						<Plus class="h-4 w-4" />
						Create Your First Group
					</Button>
				</Card.Content>
			</Card.Root>
		{:else}
			<!-- Term groups list -->
			<div class="grid gap-4">
				{#each data.termGroups as group}
					<Card.Root class="transition-colors hover:bg-muted/50">
						<a href="/private/term-group/{group.id}" class="block">
							<Card.Header>
								<div class="flex items-start justify-between">
									<div class="space-y-1">
										<Card.Title class="text-xl">
											{getLocalizedText(group.title, 'en')}
										</Card.Title>
										<Card.Description>
											{getLocalizedText(group.brand.name_full, 'en')}
										</Card.Description>
									</div>
									<div class="flex items-center gap-3">
										<Badge variant="secondary">
											{group.term_group_item[0]?.count || 0} documents
										</Badge>
										<ChevronRight class="h-5 w-5 text-muted-foreground" />
									</div>
								</div>
							</Card.Header>
						</a>
					</Card.Root>
				{/each}
			</div>
		{/if}
	</div>
{/snippet}

<PageContainer
	title="Legal Documents"
	description="Manage your terms, policies, and legal agreements"
	{content}
/>
