<script lang="ts">
	import PageContainer from '$lib/components/layout/PageContainer.svelte';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Select from '$lib/components/ui/select';
	import * as AlertDialog from '$lib/components/ui/alert-dialog';
	import { Badge } from '$lib/components/ui/badge';
	import { Label } from '$lib/components/ui/label';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import {
		ArrowLeft,
		Plus,
		Trash2,
		FileText,
		AlertCircle,
		Edit,
		ExternalLink,
		FileCheck
	} from '@lucide/svelte';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';
	import { getLocalizedText } from '$lib/utils/localization';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { z } from 'zod';
	import { invalidateAll } from '$app/navigation';
	import type { PageData, ActionData } from './$types';

	let { data, form: actionForm }: { data: PageData; form: ActionData } = $props();

	// Modals state
	let editMode = $state(false);
	let showAddTermDialog = $state(false);
	let showDeleteDialog = $state(false);
	let selectedTermId = $state('');
	let isRequired = $state(true);

	// Create form for editing
	const schema = z.object({
		title: z.object({
			en: z.string(),
			zh: z.string().optional(),
			ja: z.string().optional(),
			ko: z.string().optional()
		})
	});

	const form = superForm(
		{
			title: data.termGroup.title
		},
		{
			validators: zodClient(schema),
			dataType: 'json'
		}
	);

	const { form: formData } = form;

	// Show toast for action results
	$effect(() => {
		if (actionForm?.error) {
			toast.error(actionForm.error);
		} else if (actionForm?.success) {
			editMode = false;
			showAddTermDialog = false;
			toast.success('Changes saved successfully');
			invalidateAll();
		}
	});

	// Get current version for a term
	function getCurrentVersion(term: any) {
		return term.term_version?.find((v: any) => v.is_current);
	}
</script>

{#snippet content()}
	<div class="space-y-6">
		<!-- Header -->
		<div class="flex items-center justify-between">
			<div class="flex items-center gap-4">
				<Button href="/private/term-group" variant="ghost" size="icon">
					<ArrowLeft class="h-4 w-4" />
				</Button>
			</div>
			<div class="flex gap-2">
				<Button variant="outline" onclick={() => (editMode = !editMode)}>
					<Edit class="mr-2 h-4 w-4" />
					{editMode ? 'Cancel' : 'Edit'}
				</Button>
				<Button href="/private/term/new?group_id={data.termGroup.id}">
					<Plus class="mr-2 h-4 w-4" />
					New Document
				</Button>
			</div>
		</div>

		<!-- Edit Mode -->
		{#if editMode}
			<form method="POST" action="?/updateGroup" use:enhance>
				<Card.Root>
					<Card.Header>
						<Card.Title>Edit Group Details</Card.Title>
					</Card.Header>
					<Card.Content class="space-y-4">
						<LocalizedTextControl label="Group Name" name="title" {form} required />
					</Card.Content>
					<Card.Footer class="flex justify-between">
						<AlertDialog.Root bind:open={showDeleteDialog}>
							<AlertDialog.Trigger
								class="inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border border-destructive bg-destructive px-4 py-2 text-sm font-medium text-destructive-foreground ring-offset-background transition-colors hover:bg-destructive/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0"
							>
								<Trash2 class="mr-2 h-4 w-4" />
								Delete Group
							</AlertDialog.Trigger>
							<AlertDialog.Content>
								<AlertDialog.Header>
									<AlertDialog.Title>Delete Document Group?</AlertDialog.Title>
									<AlertDialog.Description>
										This will permanently delete this document group. Documents in this group will
										not be deleted.
									</AlertDialog.Description>
								</AlertDialog.Header>
								<AlertDialog.Footer>
									<AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
									<form method="POST" action="?/deleteGroup" use:enhance>
										<Button type="submit" variant="destructive">Delete Group</Button>
									</form>
								</AlertDialog.Footer>
							</AlertDialog.Content>
						</AlertDialog.Root>
						<div class="flex gap-2">
							<Button type="submit">Save Changes</Button>
							<Button variant="outline" onclick={() => (editMode = false)}>Cancel</Button>
						</div>
					</Card.Footer>
				</Card.Root>
			</form>
		{/if}

		<!-- Documents in Group -->
		<div class="space-y-4">
			<div class="flex items-center justify-between">
				<h2 class="text-lg font-semibold">Documents in this Group</h2>
				{#if data.availableTerms.length > 0}
					<Button variant="outline" size="sm" onclick={() => (showAddTermDialog = true)}>
						<Plus class="mr-2 h-4 w-4" />
						Add Existing Document
					</Button>
				{/if}
			</div>

			{#if data.termGroup.term_group_item.length === 0}
				<Card.Root>
					<Card.Content class="flex flex-col items-center justify-center py-12 text-center">
						<FileText class="mb-4 h-12 w-12 text-muted-foreground" />
						<h3 class="mb-2 text-lg font-semibold">No Documents Yet</h3>
						<p class="mb-4 max-w-md text-sm text-muted-foreground">
							Add documents to this group to require customers to agree to them together.
						</p>
						<Button href="/private/term/new?group_id={data.termGroup.id}">
							<Plus class="mr-2 h-4 w-4" />
							Create First Document
						</Button>
					</Card.Content>
				</Card.Root>
			{:else}
				<div class="grid gap-4">
					{#each data.termGroup.term_group_item as item}
						{@const currentVersion = getCurrentVersion(item.term)}
						<Card.Root>
							<Card.Header>
								<div class="flex items-start justify-between">
									<div class="space-y-1">
										<div class="flex items-center gap-2">
											<Card.Title class="text-lg">
												{getLocalizedText(item.term.title, 'en')}
											</Card.Title>
											{#if item.is_required}
												<Badge variant="destructive" class="text-xs">Required</Badge>
											{:else}
												<Badge variant="secondary" class="text-xs">Optional</Badge>
											{/if}
										</div>
										<Card.Description>
											{getLocalizedText(item.term.term_kind.title, 'en')} •
											{getLocalizedText(item.term.term_kind.subtitle, 'en')}
										</Card.Description>
									</div>
									<div class="flex items-center gap-2">
										{#if currentVersion}
											<Badge variant="outline" class="gap-1">
												<FileCheck class="h-3 w-3" />
												v{currentVersion.version_number}
											</Badge>
										{:else}
											<Badge variant="secondary" class="gap-1">
												<AlertCircle class="h-3 w-3" />
												No version
											</Badge>
										{/if}
										<Button href="/private/term/{item.term.id}" variant="ghost" size="icon">
											<ExternalLink class="h-4 w-4" />
										</Button>
										<form method="POST" action="?/removeTerm" use:enhance>
											<input type="hidden" name="term_id" value={item.term.id} />
											<Button
												type="submit"
												variant="ghost"
												size="icon"
												class="text-destructive hover:text-destructive"
											>
												<Trash2 class="h-4 w-4" />
											</Button>
										</form>
									</div>
								</div>
							</Card.Header>
						</Card.Root>
					{/each}
				</div>
			{/if}
		</div>
	</div>
{/snippet}

<PageContainer
	title={getLocalizedText(data.termGroup.title, 'en')}
	description={getLocalizedText(data.termGroup.brand.name_full, 'en')}
	{content}
/>

<!-- Add Term Dialog -->
<Dialog.Root bind:open={showAddTermDialog}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Add Document to Group</Dialog.Title>
			<Dialog.Description>Select an existing document to add to this group</Dialog.Description>
		</Dialog.Header>
		<form method="POST" action="?/addTerm" use:enhance>
			<div class="space-y-4 py-4">
				<div class="space-y-2">
					<Label>Document</Label>
					<Select.Root type="single" bind:value={selectedTermId}>
						<Select.Trigger>
							{#if selectedTermId}
								{@const selectedTerm = data.availableTerms.find((t) => t.id === selectedTermId)}
								{#if selectedTerm}
									{getLocalizedText(selectedTerm.title, 'en')}
								{:else}
									Select a document
								{/if}
							{:else}
								Select a document
							{/if}
						</Select.Trigger>
						<Select.Content>
							{#each data.availableTerms as term}
								<Select.Item value={term.id}>
									<div class="flex flex-col">
										<span>{getLocalizedText(term.title, 'en')}</span>
										<span class="text-xs text-muted-foreground">
											{getLocalizedText(term.term_kind.title, 'en')}
										</span>
									</div>
								</Select.Item>
							{/each}
						</Select.Content>
					</Select.Root>
					<input type="hidden" name="term_id" value={selectedTermId} />
				</div>

				<div class="flex items-center gap-2">
					<Checkbox
						id="is_required"
						checked={isRequired}
						onCheckedChange={(checked) => (isRequired = !!checked)}
					/>
					<Label for="is_required" class="cursor-pointer">
						This document is required for all customers
					</Label>
					<input type="hidden" name="is_required" value={isRequired.toString()} />
				</div>
			</div>
			<Dialog.Footer>
				<Button variant="outline" onclick={() => (showAddTermDialog = false)}>Cancel</Button>
				<Button type="submit">Add Document</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>
