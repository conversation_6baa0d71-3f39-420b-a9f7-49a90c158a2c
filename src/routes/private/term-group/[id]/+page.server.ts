import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ params, locals: { supabase, safeGetSession } }) => {
	const { user } = await safeGetSession();
	if (!user) redirect(303, '/login');

	// Get the term group with all related data
	const { data: termGroup, error: groupError } = await supabase
		.from('term_group')
		.select(
			`
			*,
			brand!inner(id, name_full, owner_profile_id),
			term_group_item(
				*,
				term!inner(
					*,
					term_kind!inner(title, subtitle),
					term_version(
						id,
						version_number,
						published_at,
						is_current,
						created_at
					)
				)
			)
		`
		)
		.eq('id', params.id)
		.single();

	if (groupError || !termGroup) {
		console.error('Error fetching term group:', groupError);
		error(404, 'Document group not found');
	}

	// Check if user owns this brand
	if (termGroup.brand.owner_profile_id !== user.id) {
		error(403, 'You do not have permission to view this document group');
	}

	// Get all terms for this brand that aren't in this group
	const existingTermIds = termGroup.term_group_item.map((item: any) => item.term_id);

	const { data: availableTerms } = await supabase
		.from('term')
		.select(
			`
			*,
			term_kind!inner(title, subtitle)
		`
		)
		.eq('brand_id', termGroup.brand_id)
		.not('id', 'in', existingTermIds.length > 0 ? `(${existingTermIds.join(',')})` : '()');

	return {
		termGroup,
		availableTerms: availableTerms || []
	};
};

export const actions = {
	updateGroup: async ({ request, params, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		const formData = await request.formData();
		const title = {
			en: formData.get('title[en]') as string,
			zh: formData.get('title[zh]') as string,
			ja: formData.get('title[ja]') as string,
			ko: formData.get('title[ko]') as string
		};

		// Update the term group
		const { error } = await supabase.from('term_group').update({ title }).eq('id', params.id);

		if (error) {
			return {
				error: error.message
			};
		}

		return { success: true };
	},

	addTerm: async ({ request, params, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		const formData = await request.formData();
		const termId = formData.get('term_id') as string;
		const isRequired = formData.get('is_required') === 'true';

		if (!termId) {
			return { error: 'Please select a document to add' };
		}

		// Add term to group
		const { error } = await supabase.from('term_group_item').insert({
			term_group_id: params.id,
			term_id: termId,
			is_required: isRequired
		});

		if (error) {
			return {
				error: error.message
			};
		}

		return { success: true };
	},

	removeTerm: async ({ request, params, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		const formData = await request.formData();
		const termId = formData.get('term_id') as string;

		// Remove term from group
		const { error } = await supabase
			.from('term_group_item')
			.delete()
			.eq('term_group_id', params.id)
			.eq('term_id', termId);

		if (error) {
			return {
				error: error.message
			};
		}

		return { success: true };
	},

	deleteGroup: async ({ params, locals: { supabase, safeGetSession } }) => {
		const { user } = await safeGetSession();
		if (!user) redirect(303, '/login');

		// Delete the term group (cascade will handle term_group_items)
		const { error } = await supabase.from('term_group').delete().eq('id', params.id);

		if (error) {
			return {
				error: error.message
			};
		}

		redirect(303, '/private/term-group');
	}
} satisfies Actions;
