import type { Actions, ServerLoad } from '@sveltejs/kit';
import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { batchHolidaySchema } from '../schemas';

export const load: ServerLoad = async () => {
	const form = await superValidate(zod(batchHolidaySchema));
	return { form };
};

export const actions: Actions = {
	default: async ({ request, locals }) => {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;
		const userId = locals.user?.id;

		if (!brandId) {
			return fail(400, { message: 'Brand ID is required' });
		}

		if (!userId) {
			return fail(400, { message: 'User must be authenticated' });
		}

		const form = await superValidate(request, zod(batchHolidaySchema));

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			// Fetch national holidays from Nager.Date API
			const response = await fetch(
				`https://date.nager.at/api/v3/publicholidays/${form.data.year}/${form.data.countryCode}`
			);

			if (!response.ok) {
				form.errors._errors = ['Failed to fetch national holidays for the selected country'];
				return fail(400, { form });
			}

			const nationalHolidays = await response.json();

			// Filter holidays if specific types are selected
			let filteredHolidays = nationalHolidays;
			if (form.data.holidayTypes && form.data.holidayTypes.length > 0) {
				filteredHolidays = nationalHolidays.filter((holiday: any) =>
					holiday.types?.some((type: string) =>
						form.data.holidayTypes!.includes(type.toLowerCase())
					)
				);
			}

			// Prepare holidays for insertion
			const holidaysToInsert = filteredHolidays.map((holiday: any) => ({
				name: form.data.useLocalNames ? holiday.localName || holiday.name : holiday.name,
				date: holiday.date,
				description: holiday.name !== holiday.localName ? holiday.name : null,
				is_national: true,
				country_code: form.data.countryCode,
				brand_id: brandId,
				creator_id: userId
			}));

			if (holidaysToInsert.length === 0) {
				form.errors._errors = ['No holidays found for the selected criteria'];
				return fail(400, { form });
			}

			// Check for existing holidays to avoid duplicates
			const { data: existingHolidays } = await supabase
				.from('holiday')
				.select('date')
				.eq('brand_id', brandId)
				.in(
					'date',
					holidaysToInsert.map((h: { date: string }) => h.date)
				);

			const existingDates = new Set(existingHolidays?.map((h: { date: string }) => h.date) || []);
			const newHolidays = holidaysToInsert.filter(
				(h: { date: string }) => !existingDates.has(h.date)
			);

			if (newHolidays.length === 0) {
				form.errors._errors = ['All selected holidays already exist for this brand'];
				return fail(400, { form });
			}

			const { error: holidayError } = await supabase.from('holiday').insert(newHolidays);

			if (holidayError) {
				console.error('Batch holiday creation error:', holidayError);
				form.errors._errors = ['Failed to create holidays: ' + holidayError.message];
				return fail(400, { form });
			}

			// Redirect back to holidays list with success message
			redirect(
				302,
				`/private/holiday?success=${encodeURIComponent(`Successfully imported ${newHolidays.length} holidays for ${form.data.year}`)}`
			);
		} catch (e) {
			console.error('Error importing holidays:', e);
			return fail(500, {
				form,
				message: 'An unexpected error occurred while importing holidays'
			});
		}
	}
};
