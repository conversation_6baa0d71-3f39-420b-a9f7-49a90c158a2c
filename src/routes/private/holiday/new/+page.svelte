<script lang="ts">
	import { goto } from '$app/navigation';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import * as Select from '$lib/components/ui/select';
	import * as Card from '$lib/components/ui/card';
	import * as Alert from '$lib/components/ui/alert';
	import { PageContainer } from '$lib/components/layout';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { holidaySchema, availableCountries } from '../schemas';
	import { Control, Label, Field, FieldErrors } from 'formsnap';
	import CalendarDays from '@lucide/svelte/icons/calendar-days';
	import ArrowLeft from '@lucide/svelte/icons/arrow-left';

	interface Props {
		data: {
			form: any;
		};
	}

	let { data }: Props = $props();

	const form = superForm(data.form, {
		validators: zodClient(holidaySchema),
		resetForm: false,
		onResult: ({ result }) => {
			if (result.type === 'redirect') {
				// Will be handled by SvelteKit's navigation
				return;
			}
		}
	});

	const { form: formData, enhance, errors, submitting, message } = form;

	function goBack() {
		goto('/private/holiday');
	}

	// Get today's date in YYYY-MM-DD format for min date
	const today = new Date().toISOString().split('T')[0];
</script>

{#snippet actions()}
	<Button variant="outline" onclick={goBack}>
		<ArrowLeft class="mr-2 h-4 w-4" />
		Back to Holidays
	</Button>
{/snippet}

{#snippet content()}
	<div class="mx-auto max-w-2xl">
		<Card.Root>
			<Card.Header>
				<div class="flex items-center gap-2">
					<CalendarDays class="h-5 w-5" />
					<Card.Title>Add New Holiday</Card.Title>
				</div>
				<Card.Description>
					Create a custom holiday for your brand. You can also import national holidays in bulk.
				</Card.Description>
			</Card.Header>

			<Card.Content>
				{#if $message}
					<Alert.Root
						class={`mb-6 ${$message.type === 'error' ? 'border-red-500' : 'border-green-500'}`}
					>
						<Alert.Title>{$message.type === 'error' ? 'Error' : 'Success'}</Alert.Title>
						<Alert.Description>{$message.text}</Alert.Description>
					</Alert.Root>
				{/if}

				{#if $errors._errors && $errors._errors.length > 0}
					<Alert.Root class="mb-6 border-red-500">
						<Alert.Title>Please fix the following errors:</Alert.Title>
						<Alert.Description>
							<ul class="ml-4 list-disc">
								{#each $errors._errors as error}
									<li>{error}</li>
								{/each}
							</ul>
						</Alert.Description>
					</Alert.Root>
				{/if}

				<form method="POST" use:enhance class="space-y-6">
					<Field form name="name">
						<Control>
							{#snippet children({ props })}
								<Label>Holiday Name *</Label>
								<Input
									{...props}
									bind:value={$formData.name}
									placeholder="e.g., Christmas Day, Company Anniversary"
									class={$errors.name ? 'border-red-500' : ''}
								/>
							{/snippet}
						</Control>
						<FieldErrors />
					</Field>

					<Field form name="date">
						<Control>
							{#snippet children({ props })}
								<Label>Date *</Label>
								<Input
									{...props}
									type="date"
									bind:value={$formData.date}
									min={today}
									class={$errors.date ? 'border-red-500' : ''}
								/>
							{/snippet}
						</Control>
						<FieldErrors />
					</Field>

					<Field form name="description">
						<Control>
							{#snippet children({ props })}
								<Label>Description</Label>
								<Textarea
									{...props}
									bind:value={$formData.description}
									placeholder="Optional description of the holiday"
									class={$errors.description ? 'border-red-500' : ''}
									rows={3}
								/>
							{/snippet}
						</Control>
						<FieldErrors />
					</Field>

					<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
						<Field form name="is_national">
							<Control>
								{#snippet children({ props })}
									<div class="flex items-center space-x-2">
										<Checkbox {...props} bind:checked={$formData.is_national} id="is_national" />
										<Label for="is_national">National Holiday</Label>
									</div>
									<p class="text-muted-foreground text-sm">
										Check if this is a national holiday in a specific country
									</p>
								{/snippet}
							</Control>
							<FieldErrors />
						</Field>

						{#if $formData.is_national}
							<Field form name="country_code">
								<Control>
									{#snippet children({ props })}
										<Label>Country</Label>
										<Select.Root
											type="single"
											bind:value={$formData.country_code}
											name={props.name}
										>
											<Select.Trigger class={$errors.country_code ? 'border-red-500' : ''}>
												<Select.Value placeholder="Select country" />
											</Select.Trigger>
											<Select.Content>
												<Select.Group>
													<Select.GroupHeading>Countries</Select.GroupHeading>
													{#each availableCountries as country}
														<Select.Item value={country.code} label={country.name}>
															{country.name} ({country.code})
														</Select.Item>
													{/each}
												</Select.Group>
											</Select.Content>
										</Select.Root>
									{/snippet}
								</Control>
								<FieldErrors />
							</Field>
						{/if}
					</div>

					<div class="flex items-center justify-end gap-4 pt-4">
						<Button type="button" variant="outline" onclick={goBack}>Cancel</Button>
						<Button type="submit" disabled={$submitting}>
							{$submitting ? 'Creating...' : 'Create Holiday'}
						</Button>
					</div>
				</form>
			</Card.Content>
		</Card.Root>
	</div>
{/snippet}

<PageContainer
	title="Add Holiday"
	description="Create a new holiday for your brand"
	{actions}
	{content}
/>
