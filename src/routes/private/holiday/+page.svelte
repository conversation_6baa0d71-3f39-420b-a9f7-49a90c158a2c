<script lang="ts">
	import { invalidate, goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$lib/components/ui/button';
	import { PageContainer } from '$lib/components/layout';
	import DataTable from '$lib/components/shared/DataTable.svelte';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { holidaySchema } from './schemas';
	import * as Select from '$lib/components/ui/select';
	import * as AlertDialog from '$lib/components/ui/alert-dialog';
	import * as Alert from '$lib/components/ui/alert';
	import type { ColumnDef } from '@tanstack/table-core';
	import {
		createSortableColumn,
		createDateColumn,
		createActionsColumn
	} from '$lib/components/shared/table-utils';
	import CalendarDays from '@lucide/svelte/icons/calendar-days';
	import Plus from '@lucide/svelte/icons/plus';
	import Upload from '@lucide/svelte/icons/upload';
	import Trash2 from '@lucide/svelte/icons/trash-2';

	interface Props {
		data: {
			holidays: Array<{
				id: string;
				name: string;
				date: string;
				description: string | null;
				is_national: boolean;
				country_code: string | null;
				created_at: string;
			}>;
			holidayForm: any;
			batchHolidayForm: any;
			selectedYear: number;
		};
	}

	let { data }: Props = $props();

	// Form handling
	const deleteForm = superForm(data.holidayForm, {
		validators: zodClient(holidaySchema),
		resetForm: false,
		onResult: ({ result }) => {
			if (result.type === 'success') {
				showDeleteDialog = false;
				invalidate('supabase:db:holidays');
			}
		}
	});

	const { enhance: enhanceDelete, submitting: submittingDelete } = deleteForm;

	// State management
	let selectedYear = $state(data.selectedYear);
	let showDeleteDialog = $state(false);
	let holidayToDelete = $state<{ id: string; name: string } | null>(null);
	let message = $state<{ type: 'success' | 'error'; text: string } | null>(null);

	// Handle success message from URL parameters
	$effect(() => {
		const successMessage = page.url.searchParams.get('success');
		if (successMessage) {
			message = { type: 'success', text: decodeURIComponent(successMessage) };
			// Remove the success parameter from URL
			const url = new URL(window.location.href);
			url.searchParams.delete('success');
			window.history.replaceState({}, '', url.toString());
		}
	});

	// Year options
	const currentYear = new Date().getFullYear();
	const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i).map((year) => ({
		value: year.toString(),
		label: year.toString()
	}));

	// Navigation functions
	function navigateToCreateHoliday() {
		goto('/private/holiday/new');
	}

	function navigateToImportHolidays() {
		goto('/private/holiday/import');
	}

	function handleYearChange(value: string | undefined) {
		if (value) {
			selectedYear = parseInt(value);
			const url = new URL(window.location.href);
			url.searchParams.set('year', value);
			goto(url.toString(), { replaceState: true });
		}
	}

	// Delete functions
	function openDeleteDialog(holiday: { id: string; name: string }) {
		holidayToDelete = holiday;
		showDeleteDialog = true;
	}

	function closeDeleteDialog() {
		showDeleteDialog = false;
		holidayToDelete = null;
	}

	// Table columns
	const columns: ColumnDef<any, any>[] = [
		createSortableColumn('name', 'Holiday Name', (row) => row.name),
		createDateColumn('date', 'Date', (row) => row.date),
		createSortableColumn('is_national', 'Type', (row) => (row.is_national ? 'National' : 'Custom')),
		createSortableColumn(
			'country_code',
			'Country',
			(row) => row.country_code?.toUpperCase() || '–'
		),
		createSortableColumn('description', 'Description', (row) => row.description || '–'),
		createActionsColumn({
			label: 'Delete',
			variant: 'destructive',
			size: 'sm',
			icon: Trash2,
			onClick: (row) => openDeleteDialog({ id: row.id, name: row.name })
		})
	];

	// Clear message after 5 seconds
	$effect(() => {
		if (message) {
			const timer = setTimeout(() => {
				message = null;
			}, 5000);
			return () => clearTimeout(timer);
		}
	});
</script>

{#snippet actions()}
	<div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
		<div class="flex items-center gap-2">
			<CalendarDays class="h-5 w-5" />
			<Select.Root type="single" value={selectedYear.toString()} onValueChange={handleYearChange}>
				<Select.Trigger class="w-32">
					<Select.Value />
				</Select.Trigger>
				<Select.Content>
					{#each yearOptions as year}
						<Select.Item value={year.value}>{year.label}</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>

		<div class="flex items-center gap-2">
			<Button variant="outline" onclick={navigateToImportHolidays}>
				<Upload class="mr-2 h-4 w-4" />
				Import National Holidays
			</Button>
			<Button onclick={navigateToCreateHoliday}>
				<Plus class="mr-2 h-4 w-4" />
				Add Holiday
			</Button>
		</div>
	</div>
{/snippet}

{#snippet content()}
	{#if message}
		<Alert.Root class={`mb-4 ${message.type === 'error' ? 'border-red-500' : 'border-green-500'}`}>
			<Alert.Title>{message.type === 'error' ? 'Error' : 'Success'}</Alert.Title>
			<Alert.Description>{message.text}</Alert.Description>
		</Alert.Root>
	{/if}

	<div class="rounded-md border">
		<DataTable data={data.holidays} {columns} />
	</div>

	{#if data.holidays.length === 0}
		<div class="flex flex-col items-center justify-center py-12 text-center">
			<CalendarDays class="text-muted-foreground mb-4 h-12 w-12" />
			<h3 class="mb-2 text-lg font-semibold">No holidays found</h3>
			<p class="text-muted-foreground mb-4">
				No holidays are set for {selectedYear}. Add your first holiday or import national holidays.
			</p>
			<div class="flex items-center gap-2">
				<Button variant="outline" onclick={navigateToImportHolidays}>
					Import National Holidays
				</Button>
				<Button onclick={navigateToCreateHoliday}>Add Holiday</Button>
			</div>
		</div>
	{/if}
{/snippet}

<PageContainer
	title="Holidays"
	description="Manage holidays for your brand in {selectedYear}"
	{actions}
	{content}
/>

<!-- Delete Confirmation Dialog -->
<AlertDialog.Root open={showDeleteDialog} onOpenChange={closeDeleteDialog}>
	<AlertDialog.Content>
		<AlertDialog.Header>
			<AlertDialog.Title>Delete Holiday</AlertDialog.Title>
			<AlertDialog.Description>
				Are you sure you want to delete "{holidayToDelete?.name}"? This action cannot be undone.
			</AlertDialog.Description>
		</AlertDialog.Header>
		<AlertDialog.Footer>
			<AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
			<form method="POST" action="?/deleteHoliday" use:enhanceDelete class="inline">
				<input type="hidden" name="holidayId" value={holidayToDelete?.id} />
				<AlertDialog.Action type="submit" disabled={$submittingDelete}>
					{$submittingDelete ? 'Deleting...' : 'Delete'}
				</AlertDialog.Action>
			</form>
		</AlertDialog.Footer>
	</AlertDialog.Content>
</AlertDialog.Root>
