<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { Button } from '$lib/components/ui/button';
	import { PageContainer } from '$lib/components/layout';
	import DataTable from '$lib/components/shared/DataTable.svelte';
	import * as Select from '$lib/components/ui/select';
	import * as Alert from '$lib/components/ui/alert';
	import type { ColumnDef } from '@tanstack/table-core';
	import {
		createSortableColumn,
		createDateColumn
	} from '$lib/components/shared/table-utils';
	import { getLocalizedText } from '$lib/utils/localization';
	import CalendarDays from '@lucide/svelte/icons/calendar-days';

	import Settings from '@lucide/svelte/icons/settings';
	import Database from '@lucide/svelte/icons/database';

	interface Props {
		data: {
			holidayRules: Array<{
				id: string;
				holiday_reference_id: string;
				schedule_kind: string;
				applies_to_all_landmarks: boolean;
				is_active: boolean;
				holiday_reference: {
					id: string;
					name: Record<string, string>;
					holiday_date: string;
					country_code: string;
					is_national: boolean;
				};
				schedule_kind_data: {
					id: string;
					name: Record<string, string>;
					description?: Record<string, string>;
				};
			}>;
			holidayInstances: Array<{
				id: string;
				actual_date: string;
				is_observed: boolean;
				holiday_reference: {
					id: string;
					name: Record<string, string>;
					country_code: string;
				};
			}>;
			stats: {
				total_rules: number;
				active_rules: number;
				closed_days: number;
				reduced_hours_days: number;
				upcoming_holidays: number;
			};
			selectedYear: number;
			// Legacy support
			holidays: any[];
			holidayForm: any;
			batchHolidayForm: any;
		};
	}

	let { data }: Props = $props();

	// No form handling needed for this overview page

	// State management
	let selectedYear = $state(data.selectedYear);
	let message = $state<{ type: 'success' | 'error'; text: string } | null>(null);

	// Handle success message from URL parameters
	$effect(() => {
		const successMessage = page.url.searchParams.get('success');
		if (successMessage) {
			message = { type: 'success', text: decodeURIComponent(successMessage) };
			// Remove the success parameter from URL
			const url = new URL(window.location.href);
			url.searchParams.delete('success');
			window.history.replaceState({}, '', url.toString());
		}
	});

	// Year options
	const currentYear = new Date().getFullYear();
	const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i).map((year) => ({
		value: year.toString(),
		label: year.toString()
	}));

	// Navigation functions
	function navigateToHolidayRules() {
		goto('/private/holiday/rules');
	}

	function navigateToHolidayReference() {
		goto('/private/holiday/reference');
	}

	function handleYearChange(value: string | undefined) {
		if (value) {
			selectedYear = parseInt(value);
			const url = new URL(window.location.href);
			url.searchParams.set('year', value);
			goto(url.toString(), { replaceState: true });
		}
	}

	// Table columns for holiday rules
	const columns: ColumnDef<any, any>[] = [
		createSortableColumn('holiday_name', 'Holiday', (row) => getLocalizedText(row.holiday_reference.name)),
		createDateColumn('holiday_date', 'Date', (row) => row.holiday_reference.holiday_date),
		{
			accessorKey: 'schedule_kind',
			header: 'Business Action',
			cell: ({ row }) => {
				const scheduleKind = row.original.schedule_kind_data;
				const kindName = getLocalizedText(scheduleKind.name);
				const variant = row.original.schedule_kind === 'closed' ? 'destructive' :
							   row.original.schedule_kind === 'reduced' ? 'secondary' : 'default';
				return `<span class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
					variant === 'destructive' ? 'bg-red-100 text-red-800' :
					variant === 'secondary' ? 'bg-yellow-100 text-yellow-800' :
					'bg-green-100 text-green-800'
				}">${kindName}</span>`;
			}
		},
		createSortableColumn('country', 'Country', (row) => row.holiday_reference.country_code.toUpperCase()),
		{
			accessorKey: 'status',
			header: 'Status',
			cell: ({ row }) => {
				const isActive = row.original.is_active;
				return `<span class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
					isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
				}">${isActive ? 'Active' : 'Inactive'}</span>`;
			}
		},
		{
			accessorKey: 'scope',
			header: 'Scope',
			cell: ({ row }) => {
				return row.original.applies_to_all_landmarks ? 'All Locations' : 'Selected Locations';
			}
		}
	];

	// Clear message after 5 seconds
	$effect(() => {
		if (message) {
			const timer = setTimeout(() => {
				message = null;
			}, 5000);
			return () => clearTimeout(timer);
		}
	});
</script>

{#snippet actions()}
	<div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
		<div class="flex items-center gap-2">
			<CalendarDays class="h-5 w-5" />
			<Select.Root type="single" value={selectedYear.toString()} onValueChange={handleYearChange}>
				<Select.Trigger class="w-32">
					{selectedYear}
				</Select.Trigger>
				<Select.Content>
					{#each yearOptions as year}
						<Select.Item value={year.value}>{year.label}</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>

		<div class="flex items-center gap-2">
			<Button variant="outline" onclick={navigateToHolidayReference}>
				<Database class="mr-2 h-4 w-4" />
				Holiday Data
			</Button>
			<Button onclick={navigateToHolidayRules}>
				<Settings class="mr-2 h-4 w-4" />
				Manage Rules
			</Button>
		</div>
	</div>
{/snippet}

{#snippet content()}
	{#if message}
		<Alert.Root class={`mb-4 ${message.type === 'error' ? 'border-red-500' : 'border-green-500'}`}>
			<Alert.Title>{message.type === 'error' ? 'Error' : 'Success'}</Alert.Title>
			<Alert.Description>{message.text}</Alert.Description>
		</Alert.Root>
	{/if}

	<!-- Stats Cards -->
	<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5 mb-6">
		<div class="rounded-lg border p-4">
			<div class="text-2xl font-bold">{data.stats.total_rules}</div>
			<p class="text-sm text-muted-foreground">Total Rules</p>
		</div>
		<div class="rounded-lg border p-4">
			<div class="text-2xl font-bold text-green-600">{data.stats.active_rules}</div>
			<p class="text-sm text-muted-foreground">Active Rules</p>
		</div>
		<div class="rounded-lg border p-4">
			<div class="text-2xl font-bold text-red-600">{data.stats.closed_days}</div>
			<p class="text-sm text-muted-foreground">Closed Days</p>
		</div>
		<div class="rounded-lg border p-4">
			<div class="text-2xl font-bold text-yellow-600">{data.stats.reduced_hours_days}</div>
			<p class="text-sm text-muted-foreground">Reduced Hours</p>
		</div>
		<div class="rounded-lg border p-4">
			<div class="text-2xl font-bold text-blue-600">{data.stats.upcoming_holidays}</div>
			<p class="text-sm text-muted-foreground">Upcoming</p>
		</div>
	</div>

	{#if data.holidayRules.length > 0}
		<div class="rounded-md border">
			<DataTable data={data.holidayRules} {columns} />
		</div>
	{:else}
		<div class="flex flex-col items-center justify-center py-12 text-center">
			<CalendarDays class="text-muted-foreground mb-4 h-12 w-12" />
			<h3 class="mb-2 text-lg font-semibold">No holiday rules configured</h3>
			<p class="text-muted-foreground mb-4">
				Set up holiday rules to define what your business does on holidays in {selectedYear}.
			</p>
			<div class="flex items-center gap-2">
				<Button variant="outline" onclick={navigateToHolidayReference}>
					<Database class="mr-2 h-4 w-4" />
					Import Holiday Data
				</Button>
				<Button onclick={navigateToHolidayRules}>
					<Settings class="mr-2 h-4 w-4" />
					Create Rules
				</Button>
			</div>
		</div>
	{/if}
{/snippet}

<PageContainer
	title="Holiday Management"
	description="Overview of holiday rules and business operations for {selectedYear}"
	{actions}
	{content}
/>
