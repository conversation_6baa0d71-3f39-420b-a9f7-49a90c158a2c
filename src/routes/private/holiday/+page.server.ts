import type { Actions, ServerLoad } from '@sveltejs/kit';
import { fail, error } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { holidaySchema, batchHolidaySchema } from './schemas';

export const load: ServerLoad = async ({ locals, url }) => {
	try {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;
		const year = url.searchParams.get('year') || new Date().getFullYear().toString();

		if (!brandId) {
			throw error(400, 'Brand ID is required');
		}

		// Load brand holidays for the selected year
		const { data: holidays, error: holidaysError } = await supabase
			.from('holiday')
			.select('*')
			.eq('brand_id', brandId)
			.gte('date', `${year}-01-01`)
			.lte('date', `${year}-12-31`)
			.order('date', { ascending: true });

		if (holidaysError) throw holidaysError;

		// Set up the forms
		const holidayForm = await superValidate(zod(holidaySchema));
		const batchHolidayForm = await superValidate(zod(batchHolidaySchema));

		return {
			holidays: holidays || [],
			holidayForm,
			batchHolidayForm,
			selectedYear: parseInt(year)
		};
	} catch (e) {
		console.error('Error loading holidays data:', e);
		throw error(500, 'Failed to load holidays data');
	}
};

export const actions: Actions = {
	createHoliday: async ({ request, locals }) => {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;
		const userId = locals.user?.id;

		if (!brandId) {
			return fail(400, { message: 'Brand ID is required' });
		}

		if (!userId) {
			return fail(400, { message: 'User must be authenticated' });
		}

		const form = await superValidate(request, zod(holidaySchema));

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			const { error: holidayError } = await supabase.from('holiday').insert({
				...form.data,
				brand_id: brandId,
				creator_id: userId
			});

			if (holidayError) {
				console.error('Holiday creation error:', holidayError);
				form.errors._errors = ['Failed to create holiday: ' + holidayError.message];
				return fail(400, { form });
			}

			return {
				form,
				success: true,
				message: 'Holiday created successfully!'
			};
		} catch (e) {
			console.error('Error creating holiday:', e);
			return fail(500, {
				form,
				message: 'An unexpected error occurred while creating the holiday'
			});
		}
	},

	batchCreateHolidays: async ({ request, locals }) => {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;
		const userId = locals.user?.id;

		if (!brandId) {
			return fail(400, { message: 'Brand ID is required' });
		}

		if (!userId) {
			return fail(400, { message: 'User must be authenticated' });
		}

		const form = await superValidate(request, zod(batchHolidaySchema));

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			// Fetch national holidays from Nager.Date API
			const response = await fetch(
				`https://date.nager.at/api/v3/publicholidays/${form.data.year}/${form.data.countryCode}`
			);

			if (!response.ok) {
				form.errors._errors = ['Failed to fetch national holidays for the selected country'];
				return fail(400, { form });
			}

			const nationalHolidays = await response.json();

			// Filter holidays if specific types are selected
			let filteredHolidays = nationalHolidays;
			if (form.data.holidayTypes && form.data.holidayTypes.length > 0) {
				filteredHolidays = nationalHolidays.filter((holiday: any) =>
					holiday.types?.some((type: string) =>
						form.data.holidayTypes!.includes(type.toLowerCase())
					)
				);
			}

			// Prepare holidays for insertion
			const holidaysToInsert = filteredHolidays.map((holiday: any) => ({
				name: form.data.useLocalNames ? holiday.localName || holiday.name : holiday.name,
				date: holiday.date,
				description: holiday.name !== holiday.localName ? holiday.name : null,
				is_national: true,
				country_code: form.data.countryCode,
				brand_id: brandId,
				creator_id: userId
			}));

			// Check for existing holidays to avoid duplicates
			const { data: existingHolidays } = await supabase
				.from('holiday')
				.select('date')
				.eq('brand_id', brandId)
				.in(
					'date',
					holidaysToInsert.map((h) => h.date)
				);

			const existingDates = new Set(existingHolidays?.map((h) => h.date) || []);
			const newHolidays = holidaysToInsert.filter((h) => !existingDates.has(h.date));

			if (newHolidays.length === 0) {
				form.errors._errors = ['All selected holidays already exist for this brand'];
				return fail(400, { form });
			}

			const { error: holidayError } = await supabase.from('holiday').insert(newHolidays);

			if (holidayError) {
				console.error('Batch holiday creation error:', holidayError);
				form.errors._errors = ['Failed to create holidays: ' + holidayError.message];
				return fail(400, { form });
			}

			return {
				form,
				success: true,
				message: `Successfully created ${newHolidays.length} holidays!`
			};
		} catch (e) {
			console.error('Error creating batch holidays:', e);
			return fail(500, {
				form,
				message: 'An unexpected error occurred while creating holidays'
			});
		}
	},

	deleteHoliday: async ({ request, locals }) => {
		const supabase = locals.supabase;
		const brandId = locals.brand?.id;

		if (!brandId) {
			return fail(400, { message: 'Brand ID is required' });
		}

		const formData = await request.formData();
		const holidayId = formData.get('holidayId') as string;

		if (!holidayId) {
			return fail(400, { message: 'Holiday ID is required' });
		}

		try {
			const { error: deleteError } = await supabase
				.from('holiday')
				.delete()
				.eq('id', holidayId)
				.eq('brand_id', brandId);

			if (deleteError) {
				console.error('Holiday deletion error:', deleteError);
				return fail(400, { message: 'Failed to delete holiday: ' + deleteError.message });
			}

			return {
				success: true,
				message: 'Holiday deleted successfully!'
			};
		} catch (e) {
			console.error('Error deleting holiday:', e);
			return fail(500, {
				message: 'An unexpected error occurred while deleting the holiday'
			});
		}
	}
};
