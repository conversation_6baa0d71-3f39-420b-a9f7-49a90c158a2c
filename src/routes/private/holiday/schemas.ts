import { z } from 'zod';

// Individual holiday schema
export const holidaySchema = z.object({
	id: z.string().uuid().optional(),
	name: z.string().min(1, 'Holiday name is required').max(255, 'Name is too long'),
	date: z
		.string()
		.min(1, 'Date is required')
		.refine(
			(date) => {
				const parsedDate = new Date(date);
				return !isNaN(parsedDate.getTime());
			},
			{ message: 'Invalid date format' }
		),
	description: z.string().max(500, 'Description is too long').optional().nullable(),
	is_national: z.boolean().default(false),
	country_code: z.string().optional().nullable()
});

// Batch holiday import schema
export const batchHolidaySchema = z.object({
	year: z.number().int().min(2020).max(2030),
	countryCode: z.string().length(2, 'Country code must be 2 characters'),
	useLocalNames: z.boolean().default(false),
	holidayTypes: z.array(z.string()).optional()
});

// Available countries for holiday import (from Nager.Date API)
export const availableCountries = [
	{ code: 'AD', name: 'Andorra' },
	{ code: 'AL', name: 'Albania' },
	{ code: 'AR', name: 'Argentina' },
	{ code: 'AT', name: 'Austria' },
	{ code: 'AU', name: 'Australia' },
	{ code: 'AW', name: 'Aruba' },
	{ code: 'BA', name: 'Bosnia and Herzegovina' },
	{ code: 'BB', name: 'Barbados' },
	{ code: 'BE', name: 'Belgium' },
	{ code: 'BG', name: 'Bulgaria' },
	{ code: 'BJ', name: 'Benin' },
	{ code: 'BO', name: 'Bolivia' },
	{ code: 'BR', name: 'Brazil' },
	{ code: 'BS', name: 'Bahamas' },
	{ code: 'BW', name: 'Botswana' },
	{ code: 'BY', name: 'Belarus' },
	{ code: 'BZ', name: 'Belize' },
	{ code: 'CA', name: 'Canada' },
	{ code: 'CH', name: 'Switzerland' },
	{ code: 'CL', name: 'Chile' },
	{ code: 'CN', name: 'China' },
	{ code: 'CO', name: 'Colombia' },
	{ code: 'CR', name: 'Costa Rica' },
	{ code: 'CU', name: 'Cuba' },
	{ code: 'CY', name: 'Cyprus' },
	{ code: 'CZ', name: 'Czech Republic' },
	{ code: 'DE', name: 'Germany' },
	{ code: 'DK', name: 'Denmark' },
	{ code: 'DO', name: 'Dominican Republic' },
	{ code: 'EC', name: 'Ecuador' },
	{ code: 'EE', name: 'Estonia' },
	{ code: 'EG', name: 'Egypt' },
	{ code: 'ES', name: 'Spain' },
	{ code: 'FI', name: 'Finland' },
	{ code: 'FO', name: 'Faroe Islands' },
	{ code: 'FR', name: 'France' },
	{ code: 'GA', name: 'Gabon' },
	{ code: 'GB', name: 'United Kingdom' },
	{ code: 'GD', name: 'Grenada' },
	{ code: 'GL', name: 'Greenland' },
	{ code: 'GM', name: 'Gambia' },
	{ code: 'GR', name: 'Greece' },
	{ code: 'GT', name: 'Guatemala' },
	{ code: 'GU', name: 'Guam' },
	{ code: 'GY', name: 'Guyana' },
	{ code: 'HN', name: 'Honduras' },
	{ code: 'HR', name: 'Croatia' },
	{ code: 'HT', name: 'Haiti' },
	{ code: 'HU', name: 'Hungary' },
	{ code: 'ID', name: 'Indonesia' },
	{ code: 'IE', name: 'Ireland' },
	{ code: 'IM', name: 'Isle of Man' },
	{ code: 'IS', name: 'Iceland' },
	{ code: 'IT', name: 'Italy' },
	{ code: 'JE', name: 'Jersey' },
	{ code: 'JM', name: 'Jamaica' },
	{ code: 'JP', name: 'Japan' },
	{ code: 'KR', name: 'South Korea' },
	{ code: 'LI', name: 'Liechtenstein' },
	{ code: 'LS', name: 'Lesotho' },
	{ code: 'LT', name: 'Lithuania' },
	{ code: 'LU', name: 'Luxembourg' },
	{ code: 'LV', name: 'Latvia' },
	{ code: 'MA', name: 'Morocco' },
	{ code: 'MC', name: 'Monaco' },
	{ code: 'MD', name: 'Moldova' },
	{ code: 'ME', name: 'Montenegro' },
	{ code: 'MG', name: 'Madagascar' },
	{ code: 'MK', name: 'North Macedonia' },
	{ code: 'MN', name: 'Mongolia' },
	{ code: 'MS', name: 'Montserrat' },
	{ code: 'MT', name: 'Malta' },
	{ code: 'MW', name: 'Malawi' },
	{ code: 'MX', name: 'Mexico' },
	{ code: 'MZ', name: 'Mozambique' },
	{ code: 'NA', name: 'Namibia' },
	{ code: 'NE', name: 'Niger' },
	{ code: 'NG', name: 'Nigeria' },
	{ code: 'NI', name: 'Nicaragua' },
	{ code: 'NL', name: 'Netherlands' },
	{ code: 'NO', name: 'Norway' },
	{ code: 'NZ', name: 'New Zealand' },
	{ code: 'PA', name: 'Panama' },
	{ code: 'PE', name: 'Peru' },
	{ code: 'PL', name: 'Poland' },
	{ code: 'PR', name: 'Puerto Rico' },
	{ code: 'PT', name: 'Portugal' },
	{ code: 'PY', name: 'Paraguay' },
	{ code: 'RO', name: 'Romania' },
	{ code: 'RS', name: 'Serbia' },
	{ code: 'RU', name: 'Russia' },
	{ code: 'SE', name: 'Sweden' },
	{ code: 'SG', name: 'Singapore' },
	{ code: 'SI', name: 'Slovenia' },
	{ code: 'SJ', name: 'Svalbard and Jan Mayen' },
	{ code: 'SK', name: 'Slovakia' },
	{ code: 'SM', name: 'San Marino' },
	{ code: 'SR', name: 'Suriname' },
	{ code: 'SV', name: 'El Salvador' },
	{ code: 'TN', name: 'Tunisia' },
	{ code: 'TR', name: 'Turkey' },
	{ code: 'UA', name: 'Ukraine' },
	{ code: 'US', name: 'United States' },
	{ code: 'UY', name: 'Uruguay' },
	{ code: 'VA', name: 'Vatican City' },
	{ code: 'VE', name: 'Venezuela' },
	{ code: 'VN', name: 'Vietnam' },
	{ code: 'ZA', name: 'South Africa' },
	{ code: 'ZW', name: 'Zimbabwe' }
];

// Holiday types available for filtering
export const holidayTypes = [
	{ value: 'public', label: 'Public Holiday' },
	{ value: 'bank', label: 'Bank Holiday' },
	{ value: 'school', label: 'School Holiday' },
	{ value: 'authorities', label: 'Authorities Closed' },
	{ value: 'optional', label: 'Optional Holiday' },
	{ value: 'observance', label: 'Observance' }
];
