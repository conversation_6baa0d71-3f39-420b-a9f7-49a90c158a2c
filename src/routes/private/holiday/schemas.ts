import { z } from 'zod';

// =============================================================================
// CORE TYPES FOR NEW HOLIDAY SYSTEM
// =============================================================================

// Localized text type matching the database JSONB structure
export const localizedTextSchema = z.object({
	en: z.string().optional(),
	zh: z.string().optional(),
	ko: z.string().optional(),
	ja: z.string().optional()
});

// Schedule kinds available for holiday rules
export const scheduleKindSchema = z.object({
	id: z.string(),
	name: z.record(z.string()), // JSONB localized text
	description: z.record(z.string()).optional(),
	is_default: z.boolean().default(false)
});

// Holiday reference data (master holidays from APIs)
export const holidayReferenceSchema = z.object({
	id: z.string(), // Use external API ID
	name: z.record(z.string()), // JSONB localized text
	description: z.record(z.string()).optional(),
	country_code: z.string().length(2),
	holiday_date: z.string().date(),
	is_national: z.boolean().default(true),
	recurrence_rule: z.string().optional(),
	source_api: z.string().optional()
});

// Holiday rule (what business does for a holiday)
export const holidayRuleSchema = z.object({
	id: z.string().uuid().optional(),
	brand_id: z.string().uuid(),
	holiday_reference_id: z.string(),
	schedule_kind: z.string(), // references schedule_kind.id
	applies_to_all_landmarks: z.boolean().default(true),
	customer_message: z.record(z.string()).optional(), // JSONB localized text
	staff_message: z.record(z.string()).optional(), // JSONB localized text
	auto_notify_customers: z.boolean().default(false),
	auto_notify_staff: z.boolean().default(true),
	revenue_impact_notes: z.string().optional(),
	is_active: z.boolean().default(true),
	landmark_ids: z.array(z.string().uuid()).optional() // for when not all landmarks
});

// Holiday instance (yearly occurrence)
export const holidayInstanceSchema = z.object({
	id: z.string().uuid().optional(),
	holiday_reference_id: z.string(),
	year: z.number().int(),
	actual_date: z.string().date(),
	is_observed: z.boolean().default(true),
	auto_generated: z.boolean().default(true)
});

// =============================================================================
// FORM SCHEMAS
// =============================================================================

// Create/edit holiday rule form
export const holidayRuleFormSchema = z.object({
	holiday_reference_id: z.string().min(1, 'Please select a holiday'),
	schedule_kind: z.string().min(1, 'Please select what to do for this holiday'),
	applies_to_all_landmarks: z.boolean().default(true),
	landmark_ids: z.array(z.string().uuid()).optional(),
	customer_message_en: z.string().optional(),
	customer_message_zh: z.string().optional(),
	customer_message_ko: z.string().optional(),
	customer_message_ja: z.string().optional(),
	staff_message_en: z.string().optional(),
	staff_message_zh: z.string().optional(),
	staff_message_ko: z.string().optional(),
	staff_message_ja: z.string().optional(),
	auto_notify_customers: z.boolean().default(false),
	auto_notify_staff: z.boolean().default(true),
	revenue_impact_notes: z.string().optional(),
	is_active: z.boolean().default(true)
});

// Import holiday reference data from API
export const importHolidayReferenceSchema = z.object({
	year: z.number().int().min(2020).max(2030),
	country_code: z.string().length(2, 'Country code must be 2 characters'),
	use_local_names: z.boolean().default(false),
	holiday_types: z.array(z.string()).optional(),
	overwrite_existing: z.boolean().default(false)
});

// Available countries for holiday import (from Nager.Date API)
export const availableCountries = [
	{ code: 'AD', name: 'Andorra' },
	{ code: 'AL', name: 'Albania' },
	{ code: 'AR', name: 'Argentina' },
	{ code: 'AT', name: 'Austria' },
	{ code: 'AU', name: 'Australia' },
	{ code: 'AW', name: 'Aruba' },
	{ code: 'BA', name: 'Bosnia and Herzegovina' },
	{ code: 'BB', name: 'Barbados' },
	{ code: 'BE', name: 'Belgium' },
	{ code: 'BG', name: 'Bulgaria' },
	{ code: 'BJ', name: 'Benin' },
	{ code: 'BO', name: 'Bolivia' },
	{ code: 'BR', name: 'Brazil' },
	{ code: 'BS', name: 'Bahamas' },
	{ code: 'BW', name: 'Botswana' },
	{ code: 'BY', name: 'Belarus' },
	{ code: 'BZ', name: 'Belize' },
	{ code: 'CA', name: 'Canada' },
	{ code: 'CH', name: 'Switzerland' },
	{ code: 'CL', name: 'Chile' },
	{ code: 'CN', name: 'China' },
	{ code: 'CO', name: 'Colombia' },
	{ code: 'CR', name: 'Costa Rica' },
	{ code: 'CU', name: 'Cuba' },
	{ code: 'CY', name: 'Cyprus' },
	{ code: 'CZ', name: 'Czech Republic' },
	{ code: 'DE', name: 'Germany' },
	{ code: 'DK', name: 'Denmark' },
	{ code: 'DO', name: 'Dominican Republic' },
	{ code: 'EC', name: 'Ecuador' },
	{ code: 'EE', name: 'Estonia' },
	{ code: 'EG', name: 'Egypt' },
	{ code: 'ES', name: 'Spain' },
	{ code: 'FI', name: 'Finland' },
	{ code: 'FO', name: 'Faroe Islands' },
	{ code: 'FR', name: 'France' },
	{ code: 'GA', name: 'Gabon' },
	{ code: 'GB', name: 'United Kingdom' },
	{ code: 'GD', name: 'Grenada' },
	{ code: 'GL', name: 'Greenland' },
	{ code: 'GM', name: 'Gambia' },
	{ code: 'GR', name: 'Greece' },
	{ code: 'GT', name: 'Guatemala' },
	{ code: 'GU', name: 'Guam' },
	{ code: 'GY', name: 'Guyana' },
	{ code: 'HN', name: 'Honduras' },
	{ code: 'HR', name: 'Croatia' },
	{ code: 'HT', name: 'Haiti' },
	{ code: 'HU', name: 'Hungary' },
	{ code: 'ID', name: 'Indonesia' },
	{ code: 'IE', name: 'Ireland' },
	{ code: 'IM', name: 'Isle of Man' },
	{ code: 'IS', name: 'Iceland' },
	{ code: 'IT', name: 'Italy' },
	{ code: 'JE', name: 'Jersey' },
	{ code: 'JM', name: 'Jamaica' },
	{ code: 'JP', name: 'Japan' },
	{ code: 'KR', name: 'South Korea' },
	{ code: 'LI', name: 'Liechtenstein' },
	{ code: 'LS', name: 'Lesotho' },
	{ code: 'LT', name: 'Lithuania' },
	{ code: 'LU', name: 'Luxembourg' },
	{ code: 'LV', name: 'Latvia' },
	{ code: 'MA', name: 'Morocco' },
	{ code: 'MC', name: 'Monaco' },
	{ code: 'MD', name: 'Moldova' },
	{ code: 'ME', name: 'Montenegro' },
	{ code: 'MG', name: 'Madagascar' },
	{ code: 'MK', name: 'North Macedonia' },
	{ code: 'MN', name: 'Mongolia' },
	{ code: 'MS', name: 'Montserrat' },
	{ code: 'MT', name: 'Malta' },
	{ code: 'MW', name: 'Malawi' },
	{ code: 'MX', name: 'Mexico' },
	{ code: 'MZ', name: 'Mozambique' },
	{ code: 'NA', name: 'Namibia' },
	{ code: 'NE', name: 'Niger' },
	{ code: 'NG', name: 'Nigeria' },
	{ code: 'NI', name: 'Nicaragua' },
	{ code: 'NL', name: 'Netherlands' },
	{ code: 'NO', name: 'Norway' },
	{ code: 'NZ', name: 'New Zealand' },
	{ code: 'PA', name: 'Panama' },
	{ code: 'PE', name: 'Peru' },
	{ code: 'PL', name: 'Poland' },
	{ code: 'PR', name: 'Puerto Rico' },
	{ code: 'PT', name: 'Portugal' },
	{ code: 'PY', name: 'Paraguay' },
	{ code: 'RO', name: 'Romania' },
	{ code: 'RS', name: 'Serbia' },
	{ code: 'RU', name: 'Russia' },
	{ code: 'SE', name: 'Sweden' },
	{ code: 'SG', name: 'Singapore' },
	{ code: 'SI', name: 'Slovenia' },
	{ code: 'SJ', name: 'Svalbard and Jan Mayen' },
	{ code: 'SK', name: 'Slovakia' },
	{ code: 'SM', name: 'San Marino' },
	{ code: 'SR', name: 'Suriname' },
	{ code: 'SV', name: 'El Salvador' },
	{ code: 'TN', name: 'Tunisia' },
	{ code: 'TR', name: 'Turkey' },
	{ code: 'UA', name: 'Ukraine' },
	{ code: 'US', name: 'United States' },
	{ code: 'UY', name: 'Uruguay' },
	{ code: 'VA', name: 'Vatican City' },
	{ code: 'VE', name: 'Venezuela' },
	{ code: 'VN', name: 'Vietnam' },
	{ code: 'ZA', name: 'South Africa' },
	{ code: 'ZW', name: 'Zimbabwe' }
];

// =============================================================================
// TYPESCRIPT TYPES
// =============================================================================

export type LocalizedText = z.infer<typeof localizedTextSchema>;
export type ScheduleKind = z.infer<typeof scheduleKindSchema>;
export type HolidayReference = z.infer<typeof holidayReferenceSchema>;
export type HolidayRule = z.infer<typeof holidayRuleSchema>;
export type HolidayInstance = z.infer<typeof holidayInstanceSchema>;
export type HolidayRuleForm = z.infer<typeof holidayRuleFormSchema>;
export type ImportHolidayReferenceForm = z.infer<typeof importHolidayReferenceSchema>;

// =============================================================================
// CONSTANTS AND OPTIONS
// =============================================================================

// Default schedule kinds that should be available
export const defaultScheduleKinds = [
	{
		id: 'regular',
		name: { en: 'Regular Schedule', ja: '通常スケジュール', ko: '일반 일정', zh: '常规时间表' },
		description: { en: 'Normal weekly schedule', ja: '通常の週間スケジュール', ko: '일반 주간 일정', zh: '正常的每周时间表' },
		is_default: true
	},
	{
		id: 'reduced',
		name: { en: 'Reduced Hours', ja: '短縮営業', ko: '단축 영업', zh: '缩短营业时间' },
		description: { en: 'Limited operating hours', ja: '営業時間短縮', ko: '제한된 운영 시간', zh: '有限的营业时间' },
		is_default: false
	},
	{
		id: 'closed',
		name: { en: 'Closed', ja: '休業', ko: '휴업', zh: '关闭' },
		description: { en: 'Business is closed', ja: '休業', ko: '휴업', zh: '营业关闭' },
		is_default: false
	}
];

// Holiday types available for filtering (from Nager.Date API)
export const holidayTypes = [
	{ value: 'public', label: 'Public Holiday' },
	{ value: 'bank', label: 'Bank Holiday' },
	{ value: 'school', label: 'School Holiday' },
	{ value: 'authorities', label: 'Authorities Closed' },
	{ value: 'optional', label: 'Optional Holiday' },
	{ value: 'observance', label: 'Observance' }
];

// =============================================================================
// LEGACY SCHEMAS (for backward compatibility during migration)
// =============================================================================

// Legacy individual holiday schema (to be deprecated)
export const legacyHolidaySchema = z.object({
	id: z.string().uuid().optional(),
	name: z.string().min(1, 'Holiday name is required').max(255, 'Name is too long'),
	date: z
		.string()
		.min(1, 'Date is required')
		.refine(
			(date) => {
				const parsedDate = new Date(date);
				return !isNaN(parsedDate.getTime());
			},
			{ message: 'Invalid date format' }
		),
	description: z.string().max(500, 'Description is too long').optional().nullable(),
	is_national: z.boolean().default(false),
	country_code: z.string().optional().nullable()
});

// Legacy batch holiday import schema (to be deprecated)
export const legacyBatchHolidaySchema = z.object({
	year: z.number().int().min(2020).max(2030),
	countryCode: z.string().length(2, 'Country code must be 2 characters'),
	useLocalNames: z.boolean().default(false),
	holidayTypes: z.array(z.string()).optional()
});

// Export legacy schemas with new names for backward compatibility
export const holidaySchema = legacyHolidaySchema;
export const batchHolidaySchema = legacyBatchHolidaySchema;
