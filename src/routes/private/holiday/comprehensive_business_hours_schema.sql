-- =============================================================================
-- COMPREHENSIVE BUSINESS HOURS & HOLIDAY MANAGEMENT SYSTEM
-- =============================================================================
-- This schema provides business hours management with automatic holiday handling.
-- Business owners define "what to do" for holidays, and the system handles yearly recurrence.

-- =============================================================================
-- HOUR OPERATION KINDS
-- =============================================================================

-- Types of business operations that have hours
CREATE TABLE IF NOT EXISTS hour_operation_kind (
    id TEXT PRIMARY KEY,
    name JSONB NOT NULL,
    description JSONB,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert default operation kinds
INSERT INTO hour_operation_kind (id, name, description, display_order) VALUES
('public', '{"en": "Public Hours", "ja": "営業時間", "ko": "영업시간"}', '{"en": "Hours visible to customers", "ja": "顧客に表示される時間", "ko": "고객에게 표시되는 시간"}', 1),
('orders', '{"en": "Order Taking", "ja": "注文受付", "ko": "주문 접수"}', '{"en": "When orders can be placed", "ja": "注文可能時間", "ko": "주문 가능 시간"}', 2),
('pickup', '{"en": "Pickup", "ja": "受取", "ko": "픽업"}', '{"en": "Product pickup hours", "ja": "商品受取時間", "ko": "상품 픽업 시간"}', 3),
('delivery', '{"en": "Delivery", "ja": "配達", "ko": "배달"}', '{"en": "Delivery service hours", "ja": "配達サービス時間", "ko": "배달 서비스 시간"}', 4),
('staff', '{"en": "Staff", "ja": "スタッフ", "ko": "직원"}', '{"en": "Staff working hours", "ja": "スタッフ勤務時間", "ko": "직원 근무 시간"}', 5)
ON CONFLICT (id) DO NOTHING;

-- =============================================================================
-- SCHEDULE KINDS
-- =============================================================================

-- Types of schedules that can be defined
CREATE TABLE IF NOT EXISTS schedule_kind (
    id TEXT PRIMARY KEY,
    name JSONB NOT NULL,
    description JSONB,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert schedule kinds
INSERT INTO schedule_kind (id, name, description, is_default) VALUES
('regular', '{"en": "Regular Schedule", "ja": "通常スケジュール", "ko": "일반 일정"}', '{"en": "Normal weekly schedule", "ja": "通常の週間スケジュール", "ko": "일반 주간 일정"}', TRUE),
('reduced', '{"en": "Reduced Hours", "ja": "短縮営業", "ko": "단축 영업"}', '{"en": "Limited operating hours", "ja": "営業時間短縮", "ko": "제한된 운영 시간"}', FALSE),
('closed', '{"en": "Closed", "ja": "休業", "ko": "휴업"}', '{"en": "Business is closed", "ja": "休業", "ko": "휴업"}', FALSE)
ON CONFLICT (id) DO NOTHING;

-- =============================================================================
-- REFERENCE HOLIDAY DATA (populated by scripts/APIs)
-- =============================================================================

-- Master holiday reference table (populated from external APIs like Nager.Date)
CREATE TABLE IF NOT EXISTS holiday_reference (
    id TEXT PRIMARY KEY, -- Use external API ID for consistency
    name JSONB NOT NULL,
    description JSONB,
    country_code TEXT NOT NULL,
    holiday_date DATE NOT NULL,
    is_national BOOLEAN DEFAULT TRUE,
    recurrence_rule TEXT, -- How this holiday recurs (annual, lunar, etc.)
    source_api TEXT, -- e.g., "nager_date"
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Yearly holiday instances (auto-generated from reference + recurrence rules)
CREATE TABLE IF NOT EXISTS holiday_instance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    holiday_reference_id TEXT NOT NULL REFERENCES holiday_reference(id),
    year INTEGER NOT NULL,
    actual_date DATE NOT NULL, -- Calculated date for this year
    is_observed BOOLEAN DEFAULT TRUE, -- Some holidays might not be observed in certain years
    auto_generated BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT holiday_instance_unique UNIQUE (holiday_reference_id, year)
);

-- =============================================================================
-- BUSINESS RULES FOR HOLIDAYS
-- =============================================================================

-- What a business does for specific holidays
CREATE TABLE IF NOT EXISTS holiday_rule (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brand_id UUID NOT NULL REFERENCES brand(id) ON DELETE CASCADE,
    holiday_reference_id TEXT NOT NULL REFERENCES holiday_reference(id),
    schedule_kind TEXT NOT NULL REFERENCES schedule_kind(id),
    
    -- Scope
    applies_to_all_landmarks BOOLEAN DEFAULT TRUE,
    
    -- Communication
    customer_message JSONB,
    staff_message JSONB,
    auto_notify_customers BOOLEAN DEFAULT FALSE,
    auto_notify_staff BOOLEAN DEFAULT TRUE,
    
    -- Business impact
    revenue_impact_notes TEXT,
    
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT holiday_rule_unique UNIQUE (brand_id, holiday_reference_id)
);

-- Which landmarks a holiday rule applies to (when not all landmarks)
CREATE TABLE IF NOT EXISTS holiday_rule_landmark (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    holiday_rule_id UUID NOT NULL REFERENCES holiday_rule(id) ON DELETE CASCADE,
    landmark_id UUID NOT NULL REFERENCES landmark(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT holiday_rule_landmark_unique UNIQUE (holiday_rule_id, landmark_id)
);

-- =============================================================================
-- SCHEDULES AND HOURS
-- =============================================================================

-- Schedules defined for landmarks
CREATE TABLE IF NOT EXISTS hour_schedule (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    landmark_id UUID NOT NULL REFERENCES landmark(id) ON DELETE CASCADE,
    schedule_kind TEXT NOT NULL REFERENCES schedule_kind(id),
    name JSONB NOT NULL,
    description JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    effective_from DATE,
    effective_to DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT hour_schedule_dates_valid CHECK (effective_to IS NULL OR effective_from <= effective_to)
);

-- Unified hour periods for all scheduling needs
CREATE TABLE IF NOT EXISTS hour_period (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    landmark_id UUID NOT NULL REFERENCES landmark(id) ON DELETE CASCADE,
    
    -- What this period applies to
    schedule_id UUID REFERENCES hour_schedule(id) ON DELETE CASCADE, -- Regular weekly schedule
    holiday_instance_id UUID REFERENCES holiday_instance(id) ON DELETE CASCADE, -- Holiday override
    override_date DATE, -- One-off date override
    
    -- When this period applies
    day_of_week INTEGER REFERENCES day_of_week(id), -- For regular schedules
    
    -- What operation this period covers
    operation_kind TEXT NOT NULL REFERENCES hour_operation_kind(id),
    
    -- Time details
    period_name TEXT, -- e.g., "Morning", "Lunch", "Evening"
    period_order INTEGER DEFAULT 1,
    start_time TIME,
    end_time TIME,
    is_closed BOOLEAN DEFAULT FALSE,
    
    -- Notes
    notes TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints: must have exactly one source
    CONSTRAINT hour_period_source_check CHECK (
        (schedule_id IS NOT NULL AND day_of_week IS NOT NULL AND holiday_instance_id IS NULL AND override_date IS NULL) OR
        (holiday_instance_id IS NOT NULL AND schedule_id IS NULL AND day_of_week IS NULL AND override_date IS NULL) OR
        (override_date IS NOT NULL AND schedule_id IS NULL AND day_of_week IS NULL AND holiday_instance_id IS NULL)
    ),
    CONSTRAINT hour_period_time_valid CHECK (start_time < end_time OR is_closed = TRUE),
    CONSTRAINT hour_period_order_positive CHECK (period_order > 0)
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Holiday reference indexes
CREATE INDEX IF NOT EXISTS idx_holiday_reference_country_date ON holiday_reference(country_code, holiday_date);
CREATE INDEX IF NOT EXISTS idx_holiday_instance_year_date ON holiday_instance(year, actual_date);
CREATE INDEX IF NOT EXISTS idx_holiday_instance_reference ON holiday_instance(holiday_reference_id, year);

-- Business rule indexes
CREATE INDEX IF NOT EXISTS idx_holiday_rule_brand ON holiday_rule(brand_id, is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_holiday_rule_holiday ON holiday_rule(holiday_reference_id);

-- Schedule indexes
CREATE INDEX IF NOT EXISTS idx_hour_schedule_landmark ON hour_schedule(landmark_id, schedule_kind);
CREATE INDEX IF NOT EXISTS idx_hour_schedule_active ON hour_schedule(is_active) WHERE is_active = TRUE;

-- Hour period indexes
CREATE INDEX IF NOT EXISTS idx_hour_period_landmark ON hour_period(landmark_id);
CREATE INDEX IF NOT EXISTS idx_hour_period_schedule_day ON hour_period(schedule_id, day_of_week) WHERE schedule_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_hour_period_holiday ON hour_period(holiday_instance_id) WHERE holiday_instance_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_hour_period_override ON hour_period(landmark_id, override_date) WHERE override_date IS NOT NULL;

-- =============================================================================
-- RLS POLICIES
-- =============================================================================

-- Enable RLS
ALTER TABLE hour_operation_kind ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedule_kind ENABLE ROW LEVEL SECURITY;
ALTER TABLE holiday_reference ENABLE ROW LEVEL SECURITY;
ALTER TABLE holiday_instance ENABLE ROW LEVEL SECURITY;
ALTER TABLE holiday_rule ENABLE ROW LEVEL SECURITY;
ALTER TABLE holiday_rule_landmark ENABLE ROW LEVEL SECURITY;
ALTER TABLE hour_schedule ENABLE ROW LEVEL SECURITY;
ALTER TABLE hour_period ENABLE ROW LEVEL SECURITY;

-- Lookup tables - readable by authenticated users
DO $$
DECLARE
    lookup_tables text[] := ARRAY['hour_operation_kind', 'schedule_kind'];
    table_name text;
BEGIN
    FOREACH table_name IN ARRAY lookup_tables
    LOOP
        EXECUTE format('
            DROP POLICY IF EXISTS "Allow read access to %s" ON %s;
            CREATE POLICY "Allow read access to %s" ON %s
                FOR SELECT USING (auth.role() = ''authenticated'');
        ', table_name, table_name, table_name, table_name);
    END LOOP;
END $$;

-- Holiday reference data - readable by all (public holiday info)
DROP POLICY IF EXISTS "Public read access to holiday_reference" ON holiday_reference;
CREATE POLICY "Public read access to holiday_reference" ON holiday_reference
    FOR SELECT USING (TRUE);

DROP POLICY IF EXISTS "Public read access to holiday_instance" ON holiday_instance;
CREATE POLICY "Public read access to holiday_instance" ON holiday_instance
    FOR SELECT USING (TRUE);

-- Business data - brand isolation
DROP POLICY IF EXISTS "Brand isolation for holiday_rule" ON holiday_rule;
CREATE POLICY "Brand isolation for holiday_rule" ON holiday_rule
    USING (brand_id = (auth.jwt() ->> 'brand_id')::UUID);

DROP POLICY IF EXISTS "Brand isolation for holiday_rule_landmark" ON holiday_rule_landmark;
CREATE POLICY "Brand isolation for holiday_rule_landmark" ON holiday_rule_landmark
    USING (
        EXISTS (
            SELECT 1 FROM holiday_rule hr 
            WHERE hr.id = holiday_rule_landmark.holiday_rule_id 
            AND hr.brand_id = (auth.jwt() ->> 'brand_id')::UUID
        )
    );

DROP POLICY IF EXISTS "Brand isolation for hour_schedule" ON hour_schedule;
CREATE POLICY "Brand isolation for hour_schedule" ON hour_schedule
    USING (
        EXISTS (
            SELECT 1 FROM landmark l 
            WHERE l.id = hour_schedule.landmark_id 
            AND l.brand_id = (auth.jwt() ->> 'brand_id')::UUID
        )
    );

DROP POLICY IF EXISTS "Brand isolation for hour_period" ON hour_period;
CREATE POLICY "Brand isolation for hour_period" ON hour_period
    USING (
        EXISTS (
            SELECT 1 FROM landmark l 
            WHERE l.id = hour_period.landmark_id 
            AND l.brand_id = (auth.jwt() ->> 'brand_id')::UUID
        )
    );

-- =============================================================================
-- UTILITY FUNCTIONS
-- =============================================================================

-- Get effective hours for a landmark on a specific date
CREATE OR REPLACE FUNCTION get_effective_hours(
    p_landmark_id UUID,
    p_date DATE,
    p_operation_kind TEXT DEFAULT 'public'
)
RETURNS TABLE (
    period_name TEXT,
    period_order INTEGER,
    start_time TIME,
    end_time TIME,
    is_closed BOOLEAN,
    source TEXT,
    notes TEXT
) AS $$
BEGIN
    -- Check for one-off date overrides first
    RETURN QUERY
    SELECT 
        hp.period_name,
        hp.period_order,
        hp.start_time,
        hp.end_time,
        hp.is_closed,
        'date_override'::TEXT as source,
        hp.notes
    FROM hour_period hp
    WHERE hp.landmark_id = p_landmark_id
    AND hp.override_date = p_date
    AND hp.operation_kind = p_operation_kind
    ORDER BY hp.period_order;
    
    IF FOUND THEN
        RETURN;
    END IF;
    
    -- Check for holiday-based hours
    RETURN QUERY
    SELECT 
        hp.period_name,
        hp.period_order,
        hp.start_time,
        hp.end_time,
        hp.is_closed,
        'holiday'::TEXT as source,
        hp.notes
    FROM hour_period hp
    JOIN holiday_instance hi ON hi.id = hp.holiday_instance_id
    JOIN holiday_rule hr ON hr.holiday_reference_id = hi.holiday_reference_id
    LEFT JOIN holiday_rule_landmark hrl ON hr.id = hrl.holiday_rule_id
    WHERE hp.landmark_id = p_landmark_id
    AND hi.actual_date = p_date
    AND hi.is_observed = TRUE
    AND hr.is_active = TRUE
    AND hp.operation_kind = p_operation_kind
    AND (hr.applies_to_all_landmarks = TRUE OR hrl.landmark_id = p_landmark_id)
    AND EXISTS (SELECT 1 FROM landmark l WHERE l.id = p_landmark_id AND l.brand_id = hr.brand_id)
    ORDER BY hp.period_order;
    
    IF FOUND THEN
        RETURN;
    END IF;
    
    -- Return regular schedule hours
    RETURN QUERY
    SELECT 
        hp.period_name,
        hp.period_order,
        hp.start_time,
        hp.end_time,
        hp.is_closed,
        'regular'::TEXT as source,
        hp.notes
    FROM hour_period hp
    JOIN hour_schedule hs ON hs.id = hp.schedule_id
    WHERE hp.landmark_id = p_landmark_id
    AND hp.day_of_week = EXTRACT(DOW FROM p_date)::INTEGER
    AND hp.operation_kind = p_operation_kind
    AND hs.is_active = TRUE
    AND hs.schedule_kind = 'regular'
    AND (hs.effective_from IS NULL OR hs.effective_from <= p_date)
    AND (hs.effective_to IS NULL OR hs.effective_to >= p_date)
    ORDER BY hp.period_order;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Check if landmark is open at specific time
CREATE OR REPLACE FUNCTION is_open(
    p_landmark_id UUID,
    p_datetime TIMESTAMPTZ,
    p_operation_kind TEXT DEFAULT 'public'
)
RETURNS BOOLEAN AS $$
DECLARE
    check_date DATE;
    check_time TIME;
    period_rec RECORD;
BEGIN
    check_date := p_datetime::DATE;
    check_time := p_datetime::TIME;
    
    FOR period_rec IN 
        SELECT * FROM get_effective_hours(p_landmark_id, check_date, p_operation_kind)
        WHERE is_closed = FALSE
    LOOP
        IF check_time >= period_rec.start_time AND check_time <= period_rec.end_time THEN
            RETURN TRUE;
        END IF;
    END LOOP;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- UNIQUE CONSTRAINTS
-- =============================================================================

-- Ensure unique regular schedule per landmark and kind
CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_schedule_unique_regular 
ON hour_schedule(landmark_id, schedule_kind) 
WHERE schedule_kind = 'regular' AND is_active = TRUE;

-- Ensure unique period order per context
CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_period_schedule_unique_order 
ON hour_period(schedule_id, day_of_week, operation_kind, period_order)
WHERE schedule_id IS NOT NULL;

CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_period_holiday_unique_order 
ON hour_period(holiday_instance_id, operation_kind, period_order)
WHERE holiday_instance_id IS NOT NULL;

CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_period_override_unique_order 
ON hour_period(landmark_id, override_date, operation_kind, period_order)
WHERE override_date IS NOT NULL;

-- =============================================================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================================================

COMMENT ON TABLE holiday_reference IS 'Master holiday data populated from external APIs';
COMMENT ON TABLE holiday_instance IS 'Yearly instances of holidays with calculated dates';
COMMENT ON TABLE holiday_rule IS 'Business rules defining what to do for specific holidays';
COMMENT ON TABLE hour_schedule IS 'Schedules defined for landmarks (regular, reduced, etc.)';
COMMENT ON TABLE hour_period IS 'Unified time periods for all scheduling needs';
COMMENT ON FUNCTION get_effective_hours IS 'Returns effective hours considering holidays and overrides';
COMMENT ON FUNCTION is_open IS 'Checks if a landmark is open for a specific operation'; 