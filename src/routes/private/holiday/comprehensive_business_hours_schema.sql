-- =============================================================================
-- SIMPLIFIED BUSINESS HOURS & HOLIDAY MANAGEMENT SYSTEM
-- =============================================================================
-- This schema provides business hours management with semantic holiday handling.
-- Business owners define "what to do" for holidays once, and it applies every year.

-- Note: Country data is managed in separate country.sql file

-- =============================================================================
-- DAY OF WEEK REFERENCE DATA
-- =============================================================================

-- Day of week reference table (0=Sunday, 6=Saturday)
create table public.day_of_week (
  id integer generated by default as identity not null,
  name_full jsonb not null,
  name_short jsonb not null,
  index_mon_first integer not null,
  index_sun_first integer not null,
  func_name_full_en_first text generated always as (
    coalesce(
      nullif((name_full ->> 'en'::text), ''::text),
      nullif((name_full ->> 'zh'::text), ''::text),
      nullif((name_full ->> 'ko'::text), ''::text),
      nullif((name_full ->> 'ja'::text), ''::text),
      ''::text
    )
  ) stored,
  func_name_short_en_first text generated always as (
    coalesce(
      nullif((name_short ->> 'en'::text), ''::text),
      nullif((name_short ->> 'zh'::text), ''::text),
      nullif((name_short ->> 'ko'::text), ''::text),
      nullif((name_short ->> 'ja'::text), ''::text),
      ''::text
    )
  ) stored,
  constraint day_of_week_pkey primary key (id),
  constraint day_of_week_index_mon_first_key unique (index_mon_first),
  constraint day_of_week_index_sun_first_key unique (index_sun_first)
) tablespace pg_default;

-- Insert day of week data
insert into public.day_of_week (id, name_full, name_short, index_mon_first, index_sun_first) values
(0, '{"en": "Sunday", "ja": "日曜日", "ko": "일요일", "zh": "星期日"}', '{"en": "Sun", "ja": "日", "ko": "일", "zh": "日"}', 6, 0),
(1, '{"en": "Monday", "ja": "月曜日", "ko": "월요일", "zh": "星期一"}', '{"en": "Mon", "ja": "月", "ko": "월", "zh": "一"}', 0, 1),
(2, '{"en": "Tuesday", "ja": "火曜日", "ko": "화요일", "zh": "星期二"}', '{"en": "Tue", "ja": "火", "ko": "화", "zh": "二"}', 1, 2),
(3, '{"en": "Wednesday", "ja": "水曜日", "ko": "수요일", "zh": "星期三"}', '{"en": "Wed", "ja": "水", "ko": "수", "zh": "三"}', 2, 3),
(4, '{"en": "Thursday", "ja": "木曜日", "ko": "목요일", "zh": "星期四"}', '{"en": "Thu", "ja": "木", "ko": "목", "zh": "四"}', 3, 4),
(5, '{"en": "Friday", "ja": "金曜日", "ko": "금요일", "zh": "星期五"}', '{"en": "Fri", "ja": "金", "ko": "금", "zh": "五"}', 4, 5),
(6, '{"en": "Saturday", "ja": "土曜日", "ko": "토요일", "zh": "星期六"}', '{"en": "Sat", "ja": "土", "ko": "토", "zh": "六"}', 5, 6)
on conflict (id) do nothing;

-- =============================================================================
-- SCHEDULE TYPES
-- =============================================================================

-- Types of schedules that can be defined
create table public.schedule_kind (
  id text not null,
  name jsonb not null,
  description jsonb,
  is_default boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint schedule_kind_pkey primary key (id)
) tablespace pg_default;

-- Insert schedule kinds
insert into public.schedule_kind (id, name, description, is_default) values
('regular', '{"en": "Regular Schedule", "ja": "通常スケジュール", "ko": "일반 일정", "zh": "常规时间表"}', '{"en": "Normal weekly schedule", "ja": "通常の週間スケジュール", "ko": "일반 주간 일정", "zh": "正常的每周时间表"}', true),
('reduced', '{"en": "Reduced Hours", "ja": "短縮営業", "ko": "단축 영업", "zh": "缩短营业时间"}', '{"en": "Limited operating hours", "ja": "営業時間短縮", "ko": "제한된 운영 시간", "zh": "有限的营业时间"}', false),
('closed', '{"en": "Closed", "ja": "休業", "ko": "휴업", "zh": "关闭"}', '{"en": "Business is closed", "ja": "休業", "ko": "휴업", "zh": "营业关闭"}', false)
on conflict (id) do nothing;

-- =============================================================================
-- HOLIDAY REFERENCE DATA
-- =============================================================================

-- Master holiday reference table (generic holidays that can be imported)
create table public.holiday (
  id text not null,
  name jsonb not null,
  description jsonb,
  country_iso2 char(2) not null,
  typical_date text, -- MM-DD format for fixed holidays, or description for variable ones
  is_national boolean not null default true,
  recurrence_type text not null default 'annual', -- 'annual', 'lunar', 'variable'
  source_api text, -- e.g., "nager_date", "manual"
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint holiday_pkey primary key (id),
  constraint holiday_country_iso2_fkey foreign key (country_iso2) references public.country (iso2),
  constraint holiday_country_name_unique unique (country_iso2, name)
) tablespace pg_default;

-- Add a table for holiday instances (yearly occurrences) since we need it for the function
create table public.holiday_day (
  id uuid not null default gen_random_uuid(),
  holiday_id text not null,
  year integer not null,
  actual_date date not null, -- Calculated date for this year
  is_observed boolean not null default true,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint holiday_day_pkey primary key (id),
  constraint holiday_day_holiday_id_fkey foreign key (holiday_id) references public.holiday (id) on delete cascade,
  constraint holiday_day_holiday_year_unique unique (holiday_id, year)
) tablespace pg_default;

-- =============================================================================
-- BUSINESS RULES FOR HOLIDAYS
-- =============================================================================

-- What a business does for specific holidays (semantic rules that apply every year)
create table public.holiday_rule (
  id uuid not null default gen_random_uuid(),
  brand_id uuid not null,
  holiday_id text not null,
  schedule_kind text not null,
  message_to_consumer jsonb, -- Localized message for customers
  message_to_staff jsonb, -- Localized message for staff
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint holiday_rule_pkey primary key (id),
  constraint holiday_rule_brand_id_fkey foreign key (brand_id) references public.brand (id) on delete cascade,
  constraint holiday_rule_holiday_id_fkey foreign key (holiday_id) references public.holiday (id) on delete cascade,
  constraint holiday_rule_schedule_kind_fkey foreign key (schedule_kind) references public.schedule_kind (id),
  constraint holiday_rule_brand_holiday_unique unique (brand_id, holiday_id)
) tablespace pg_default;

-- Which landmarks a holiday rule applies to
create table public.holiday_rule_landmark (
  id uuid not null default gen_random_uuid(),
  holiday_rule_id uuid not null,
  landmark_id uuid not null,
  created_at timestamptz not null default now(),
  constraint holiday_rule_landmark_pkey primary key (id),
  constraint holiday_rule_landmark_holiday_rule_id_fkey foreign key (holiday_rule_id) references public.holiday_rule (id) on delete cascade,
  constraint holiday_rule_landmark_landmark_id_fkey foreign key (landmark_id) references public.landmark (id) on delete cascade,
  constraint holiday_rule_landmark_unique unique (holiday_rule_id, landmark_id)
) tablespace pg_default;

-- =============================================================================
-- SCHEDULES
-- =============================================================================

-- Schedules defined for landmarks (both regular weekly and holiday-specific)
create table public.schedule (
  id uuid not null default gen_random_uuid(),
  landmark_id uuid not null,
  schedule_kind text not null,
  holiday_id text, -- If not null, this is a holiday schedule
  name jsonb not null,
  description jsonb,
  effective_from date,
  effective_to date,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint schedule_pkey primary key (id),
  constraint schedule_landmark_id_fkey foreign key (landmark_id) references public.landmark (id) on delete cascade,
  constraint schedule_schedule_kind_fkey foreign key (schedule_kind) references public.schedule_kind (id),
  constraint schedule_holiday_id_fkey foreign key (holiday_id) references public.holiday (id) on delete cascade,
  constraint schedule_dates_valid check (effective_to is null or effective_from <= effective_to),
  constraint schedule_landmark_holiday_unique unique (landmark_id, holiday_id) where holiday_id is not null,
  constraint schedule_landmark_weekly_kind_unique unique (landmark_id, schedule_kind) where holiday_id is null
) tablespace pg_default;

-- Time periods within schedules
create table public.schedule_period (
  id uuid not null default gen_random_uuid(),
  schedule_id uuid not null,
  day_of_week integer, -- References day_of_week.id (0=Sunday, 6=Saturday), null for holiday schedules
  period_name jsonb, -- Customer-facing name, localized
  period_order integer not null default 1,
  start_time time,
  end_time time,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint schedule_period_pkey primary key (id),
  constraint schedule_period_schedule_id_fkey foreign key (schedule_id) references public.schedule (id) on delete cascade,
  constraint schedule_period_day_of_week_fkey foreign key (day_of_week) references public.day_of_week (id),
  constraint schedule_period_time_valid check (start_time < end_time),
  constraint schedule_period_order_positive check (period_order > 0)
) tablespace pg_default;

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Holiday indexes
create index if not exists idx_holiday_country on public.holiday using btree (country_iso2);
create index if not exists idx_holiday_name on public.holiday using gin (name);

-- Holiday day indexes
create index if not exists idx_holiday_day_holiday_year on public.holiday_day using btree (holiday_id, year);
create index if not exists idx_holiday_day_date on public.holiday_day using btree (actual_date);

-- Business rule indexes
create index if not exists idx_holiday_rule_brand on public.holiday_rule using btree (brand_id);
create index if not exists idx_holiday_rule_holiday on public.holiday_rule using btree (holiday_id);

-- Schedule indexes
create index if not exists idx_schedule_landmark on public.schedule using btree (landmark_id);
create index if not exists idx_schedule_landmark_holiday on public.schedule using btree (landmark_id, holiday_id) where holiday_id is not null;
create index if not exists idx_schedule_landmark_weekly on public.schedule using btree (landmark_id, schedule_kind) where holiday_id is null;

-- Schedule period indexes
create index if not exists idx_schedule_period_schedule on public.schedule_period using btree (schedule_id);
create index if not exists idx_schedule_period_day on public.schedule_period using btree (schedule_id, day_of_week) where day_of_week is not null;

-- =============================================================================
-- RLS POLICIES
-- =============================================================================

-- Enable RLS
alter table public.schedule_kind enable row level security;
alter table public.holiday enable row level security;
alter table public.holiday_day enable row level security;
alter table public.holiday_rule enable row level security;
alter table public.holiday_rule_landmark enable row level security;
alter table public.schedule enable row level security;
alter table public.schedule_period enable row level security;

-- Public tables - readable by all
drop policy if exists "Public read access to schedule_kind" on public.schedule_kind;
create policy "Public read access to schedule_kind" on public.schedule_kind
  for select using (true);

drop policy if exists "Public read access to holiday" on public.holiday;
create policy "Public read access to holiday" on public.holiday
  for select using (true);

drop policy if exists "Public read access to holiday_day" on public.holiday_day;
create policy "Public read access to holiday_day" on public.holiday_day
  for select using (true);

-- Business data - brand owner isolation
drop policy if exists "Brand owner isolation for holiday_rule" on public.holiday_rule;
create policy "Brand owner isolation for holiday_rule" on public.holiday_rule
  using (
    exists (
      select 1 from public.brand b
      where b.id = holiday_rule.brand_id
      and b.owner_id = auth.uid()
    )
  );

drop policy if exists "Brand owner isolation for holiday_rule_landmark" on public.holiday_rule_landmark;
create policy "Brand owner isolation for holiday_rule_landmark" on public.holiday_rule_landmark
  using (
    exists (
      select 1 from public.holiday_rule hr
      join public.brand b on b.id = hr.brand_id
      where hr.id = holiday_rule_landmark.holiday_rule_id
      and b.owner_id = auth.uid()
    )
  );

drop policy if exists "Brand owner isolation for schedule" on public.schedule;
create policy "Brand owner isolation for schedule" on public.schedule
  using (
    exists (
      select 1 from public.landmark l
      join public.brand b on b.id = l.brand_id
      where l.id = schedule.landmark_id
      and b.owner_id = auth.uid()
    )
  );

drop policy if exists "Brand owner isolation for schedule_period" on public.schedule_period;
create policy "Brand owner isolation for schedule_period" on public.schedule_period
  using (
    exists (
      select 1 from public.schedule s
      join public.landmark l on l.id = s.landmark_id
      join public.brand b on b.id = l.brand_id
      where s.id = schedule_period.schedule_id
      and b.owner_id = auth.uid()
    )
  );

-- =============================================================================
-- UTILITY FUNCTIONS
-- =============================================================================

-- Get effective schedule for a landmark on a specific date
create or replace function public.get_effective_schedule(
  p_landmark_id uuid,
  p_date date
)
returns table (
  schedule_id uuid,
  schedule_kind text,
  period_name jsonb,
  period_order integer,
  start_time time,
  end_time time,
  source text
)
language plpgsql
security definer
as $$
declare
  holiday_rec record;
  dow integer;
begin
  -- Get day of week (0=Sunday, 6=Saturday)
  dow := extract(dow from p_date)::integer;

  -- Check if there's a holiday on this date that has a rule for this landmark
  select h.id, h.name into holiday_rec
  from public.holiday h
  join public.holiday_day hd on hd.holiday_id = h.id
  join public.holiday_rule hr on hr.holiday_id = h.id
  left join public.holiday_rule_landmark hrl on hrl.holiday_rule_id = hr.id
  where hr.brand_id = (select brand_id from public.landmark where id = p_landmark_id)
  and (hrl.landmark_id = p_landmark_id or hrl.landmark_id is null)
  and hd.actual_date = p_date
  and hd.is_observed = true
  limit 1;

  -- If holiday found, return holiday schedule
  if found then
    return query
    select
      s.id as schedule_id,
      s.schedule_kind,
      sp.period_name,
      sp.period_order,
      sp.start_time,
      sp.end_time,
      'holiday'::text as source
    from public.schedule s
    join public.schedule_period sp on sp.schedule_id = s.id
    where s.landmark_id = p_landmark_id
    and s.holiday_id = holiday_rec.id
    and (s.effective_from is null or s.effective_from <= p_date)
    and (s.effective_to is null or s.effective_to >= p_date)
    order by sp.period_order;

    if found then
      return;
    end if;
  end if;

  -- Return regular weekly schedule
  return query
  select
    s.id as schedule_id,
    s.schedule_kind,
    sp.period_name,
    sp.period_order,
    sp.start_time,
    sp.end_time,
    'weekly'::text as source
  from public.schedule s
  join public.schedule_period sp on sp.schedule_id = s.id
  where s.landmark_id = p_landmark_id
  and s.holiday_id is null
  and sp.day_of_week = dow
  and (s.effective_from is null or s.effective_from <= p_date)
  and (s.effective_to is null or s.effective_to >= p_date)
  order by sp.period_order;
end;
$$;

-- Check if landmark is open at specific time
create or replace function public.is_landmark_open(
  p_landmark_id uuid,
  p_datetime timestamptz
)
returns boolean
language plpgsql
security definer
as $$
declare
  check_date date;
  check_time time;
  period_rec record;
begin
  check_date := p_datetime::date;
  check_time := p_datetime::time;

  for period_rec in
    select * from public.get_effective_schedule(p_landmark_id, check_date)
  loop
    if check_time >= period_rec.start_time and check_time <= period_rec.end_time then
      return true;
    end if;
  end loop;

  return false;
end;
$$;

-- =============================================================================
-- UNIQUE CONSTRAINTS
-- =============================================================================

-- Ensure unique regular schedule per landmark and kind
CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_schedule_unique_regular 
ON hour_schedule(landmark_id, schedule_kind) 
WHERE schedule_kind = 'regular' AND is_active = TRUE;

-- Ensure unique period order per context
CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_period_schedule_unique_order 
ON hour_period(schedule_id, day_of_week, operation_kind, period_order)
WHERE schedule_id IS NOT NULL;

CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_period_holiday_unique_order 
ON hour_period(holiday_instance_id, operation_kind, period_order)
WHERE holiday_instance_id IS NOT NULL;

CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_period_override_unique_order 
ON hour_period(landmark_id, override_date, operation_kind, period_order)
WHERE override_date IS NOT NULL;

-- =============================================================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================================================

COMMENT ON TABLE holiday_reference IS 'Master holiday data populated from external APIs';
COMMENT ON TABLE holiday_instance IS 'Yearly instances of holidays with calculated dates';
COMMENT ON TABLE holiday_rule IS 'Business rules defining what to do for specific holidays';
COMMENT ON TABLE hour_schedule IS 'Schedules defined for landmarks (regular, reduced, etc.)';
COMMENT ON TABLE hour_period IS 'Unified time periods for all scheduling needs';
COMMENT ON FUNCTION get_effective_hours IS 'Returns effective hours considering holidays and overrides';
COMMENT ON FUNCTION is_open IS 'Checks if a landmark is open for a specific operation'; 