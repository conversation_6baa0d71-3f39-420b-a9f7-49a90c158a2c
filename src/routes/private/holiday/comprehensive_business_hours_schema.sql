-- =============================================================================
-- SIMPLIFIED BUSINESS HOURS & HOLIDAY MANAGEMENT SYSTEM
-- =============================================================================
-- This schema provides business hours management with semantic holiday handling.
-- Business owners define "what to do" for holidays once, and it applies every year.

-- =============================================================================
-- COUNTRIES REFERENCE DATA
-- =============================================================================

-- Public country reference data (simplified from countries-states-cities-database)
CREATE TABLE IF NOT EXISTS country (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    iso3 CHAR(3) DEFAULT NULL,
    numeric_code CHAR(3) DEFAULT NULL,
    iso2 CHAR(2) NOT NULL,
    phonecode VARCHAR(255) DEFAULT NULL,
    capital VARCHAR(255) DEFAULT NULL,
    currency VARCHAR(255) DEFAULT NULL,
    currency_name VARCHAR(255) DEFAULT NULL,
    currency_symbol VARCHAR(255) DEFAULT NULL,
    tld VARCHAR(255) DEFAULT NULL,
    native VARCHAR(255) DEFAULT NULL,
    region VARCHAR(255) DEFAULT NULL,
    subregion VARCHAR(255) DEFAULT NULL,
    timezones TEXT DEFAULT NULL,
    translations TEXT DEFAULT NULL,
    latitude DECIMAL(10, 8) DEFAULT NULL,
    longitude DECIMAL(11, 8) DEFAULT NULL,
    emoji VARCHAR(191) DEFAULT NULL,
    emojiU VARCHAR(191) DEFAULT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    flag BOOLEAN NOT NULL DEFAULT TRUE,
    wikiDataId VARCHAR(255) DEFAULT NULL,

    CONSTRAINT country_iso2_unique UNIQUE (iso2),
    CONSTRAINT country_iso3_unique UNIQUE (iso3),
    CONSTRAINT country_numeric_code_unique UNIQUE (numeric_code)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_country_iso2 ON country(iso2);
CREATE INDEX IF NOT EXISTS idx_country_name ON country(name);
CREATE INDEX IF NOT EXISTS idx_country_region ON country(region);

-- Insert common countries for holiday management
INSERT INTO country (name, iso3, numeric_code, iso2, phonecode, capital, currency, currency_name, currency_symbol, tld, native, region, subregion, emoji, emojiU, flag, wikiDataId) VALUES
('United States', 'USA', '840', 'US', '1', 'Washington', 'USD', 'United States dollar', '$', '.us', 'United States', 'Americas', 'Northern America', '🇺🇸', 'U+1F1FA U+1F1F8', true, 'Q30'),
('Canada', 'CAN', '124', 'CA', '1', 'Ottawa', 'CAD', 'Canadian dollar', '$', '.ca', 'Canada', 'Americas', 'Northern America', '🇨🇦', 'U+1F1E8 U+1F1E6', true, 'Q16'),
('United Kingdom', 'GBR', '826', 'GB', '44', 'London', 'GBP', 'British pound', '£', '.uk', 'United Kingdom', 'Europe', 'Northern Europe', '🇬🇧', 'U+1F1EC U+1F1E7', true, 'Q145'),
('Germany', 'DEU', '276', 'DE', '49', 'Berlin', 'EUR', 'Euro', '€', '.de', 'Deutschland', 'Europe', 'Western Europe', '🇩🇪', 'U+1F1E9 U+1F1EA', true, 'Q183'),
('France', 'FRA', '250', 'FR', '33', 'Paris', 'EUR', 'Euro', '€', '.fr', 'France', 'Europe', 'Western Europe', '🇫🇷', 'U+1F1EB U+1F1F7', true, 'Q142'),
('Japan', 'JPN', '392', 'JP', '81', 'Tokyo', 'JPY', 'Japanese yen', '¥', '.jp', '日本', 'Asia', 'Eastern Asia', '🇯🇵', 'U+1F1EF U+1F1F5', true, 'Q17'),
('South Korea', 'KOR', '410', 'KR', '82', 'Seoul', 'KRW', 'South Korean won', '₩', '.kr', '대한민국', 'Asia', 'Eastern Asia', '🇰🇷', 'U+1F1F0 U+1F1F7', true, 'Q884'),
('China', 'CHN', '156', 'CN', '86', 'Beijing', 'CNY', 'Chinese yuan', '¥', '.cn', '中华人民共和国', 'Asia', 'Eastern Asia', '🇨🇳', 'U+1F1E8 U+1F1F3', true, 'Q148'),
('Australia', 'AUS', '036', 'AU', '61', 'Canberra', 'AUD', 'Australian dollar', '$', '.au', 'Australia', 'Oceania', 'Australia and New Zealand', '🇦🇺', 'U+1F1E6 U+1F1FA', true, 'Q408'),
('India', 'IND', '356', 'IN', '91', 'New Delhi', 'INR', 'Indian rupee', '₹', '.in', 'भारत', 'Asia', 'Southern Asia', '🇮🇳', 'U+1F1EE U+1F1F3', true, 'Q668')
ON CONFLICT (iso2) DO NOTHING;

-- =============================================================================
-- SCHEDULE TYPES
-- =============================================================================

-- Types of schedules that can be defined
CREATE TABLE IF NOT EXISTS schedule_kind (
    id TEXT PRIMARY KEY,
    name JSONB NOT NULL,
    description JSONB,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert schedule kinds
INSERT INTO schedule_kind (id, name, description, is_default) VALUES
('regular', '{"en": "Regular Schedule", "ja": "通常スケジュール", "ko": "일반 일정", "zh": "常规时间表"}', '{"en": "Normal weekly schedule", "ja": "通常の週間スケジュール", "ko": "일반 주간 일정", "zh": "正常的每周时间表"}', TRUE),
('reduced', '{"en": "Reduced Hours", "ja": "短縮営業", "ko": "단축 영업", "zh": "缩短营业时间"}', '{"en": "Limited operating hours", "ja": "営業時間短縮", "ko": "제한된 운영 시간", "zh": "有限的营业时间"}', FALSE),
('closed', '{"en": "Closed", "ja": "休業", "ko": "휴업", "zh": "关闭"}', '{"en": "Business is closed", "ja": "休業", "ko": "휴업", "zh": "营业关闭"}', FALSE)
ON CONFLICT (id) DO NOTHING;

-- =============================================================================
-- HOLIDAY REFERENCE DATA
-- =============================================================================

-- Master holiday reference table (generic holidays that can be imported)
CREATE TABLE IF NOT EXISTS holiday (
    id TEXT PRIMARY KEY, -- Use external API ID for consistency (e.g., "christmas", "new-year")
    name JSONB NOT NULL, -- Localized holiday names
    description JSONB,
    country_iso2 CHAR(2) NOT NULL REFERENCES country(iso2),
    typical_date TEXT, -- MM-DD format for fixed holidays, or description for variable ones
    is_national BOOLEAN DEFAULT TRUE,
    recurrence_type TEXT DEFAULT 'annual', -- 'annual', 'lunar', 'variable'
    source_api TEXT, -- e.g., "nager_date", "manual"
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT holiday_country_name_unique UNIQUE (country_iso2, name)
);

-- =============================================================================
-- BUSINESS RULES FOR HOLIDAYS
-- =============================================================================

-- What a business does for specific holidays (semantic rules that apply every year)
CREATE TABLE IF NOT EXISTS holiday_rule (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brand_id UUID NOT NULL REFERENCES brand(id) ON DELETE CASCADE,
    holiday_id TEXT NOT NULL REFERENCES holiday(id),
    schedule_kind TEXT NOT NULL REFERENCES schedule_kind(id),

    -- Communication
    message_to_consumer JSONB, -- Localized message for customers
    message_to_staff JSONB, -- Localized message for staff

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT holiday_rule_unique UNIQUE (brand_id, holiday_id)
);

-- Which landmarks a holiday rule applies to
CREATE TABLE IF NOT EXISTS holiday_rule_landmark (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    holiday_rule_id UUID NOT NULL REFERENCES holiday_rule(id) ON DELETE CASCADE,
    landmark_id UUID NOT NULL REFERENCES landmark(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT holiday_rule_landmark_unique UNIQUE (holiday_rule_id, landmark_id)
);

-- =============================================================================
-- SCHEDULES
-- =============================================================================

-- Schedules defined for landmarks (both regular weekly and holiday-specific)
CREATE TABLE IF NOT EXISTS schedule (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    landmark_id UUID NOT NULL REFERENCES landmark(id) ON DELETE CASCADE,
    schedule_kind TEXT NOT NULL REFERENCES schedule_kind(id),

    -- Schedule type
    schedule_type TEXT NOT NULL CHECK (schedule_type IN ('weekly', 'holiday')),
    holiday_id TEXT REFERENCES holiday(id), -- Only for holiday schedules

    -- Basic info
    name JSONB NOT NULL,
    description JSONB,

    -- Validity
    effective_from DATE,
    effective_to DATE,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT schedule_dates_valid CHECK (effective_to IS NULL OR effective_from <= effective_to),
    CONSTRAINT schedule_holiday_check CHECK (
        (schedule_type = 'weekly' AND holiday_id IS NULL) OR
        (schedule_type = 'holiday' AND holiday_id IS NOT NULL)
    ),
    CONSTRAINT schedule_landmark_holiday_unique UNIQUE (landmark_id, holiday_id) WHERE holiday_id IS NOT NULL,
    CONSTRAINT schedule_landmark_weekly_kind_unique UNIQUE (landmark_id, schedule_kind) WHERE schedule_type = 'weekly'
);

-- Time periods within schedules
CREATE TABLE IF NOT EXISTS schedule_period (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schedule_id UUID NOT NULL REFERENCES schedule(id) ON DELETE CASCADE,

    -- When this period applies (for weekly schedules)
    day_of_week INTEGER CHECK (day_of_week BETWEEN 0 AND 6), -- 0=Sunday, 6=Saturday

    -- Time details
    period_name TEXT, -- e.g., "Morning", "Lunch", "Evening"
    period_order INTEGER DEFAULT 1,
    start_time TIME,
    end_time TIME,
    is_closed BOOLEAN DEFAULT FALSE,

    -- Notes
    notes TEXT,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT schedule_period_time_valid CHECK (start_time < end_time OR is_closed = TRUE),
    CONSTRAINT schedule_period_order_positive CHECK (period_order > 0),
    CONSTRAINT schedule_period_day_check CHECK (
        (day_of_week IS NOT NULL AND EXISTS (SELECT 1 FROM schedule WHERE id = schedule_id AND schedule_type = 'weekly')) OR
        (day_of_week IS NULL AND EXISTS (SELECT 1 FROM schedule WHERE id = schedule_id AND schedule_type = 'holiday'))
    )
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Holiday indexes
CREATE INDEX IF NOT EXISTS idx_holiday_country ON holiday(country_iso2);
CREATE INDEX IF NOT EXISTS idx_holiday_name ON holiday USING GIN (name);

-- Business rule indexes
CREATE INDEX IF NOT EXISTS idx_holiday_rule_brand ON holiday_rule(brand_id);
CREATE INDEX IF NOT EXISTS idx_holiday_rule_holiday ON holiday_rule(holiday_id);

-- Schedule indexes
CREATE INDEX IF NOT EXISTS idx_schedule_landmark ON schedule(landmark_id);
CREATE INDEX IF NOT EXISTS idx_schedule_landmark_type ON schedule(landmark_id, schedule_type);
CREATE INDEX IF NOT EXISTS idx_schedule_holiday ON schedule(holiday_id) WHERE holiday_id IS NOT NULL;

-- Schedule period indexes
CREATE INDEX IF NOT EXISTS idx_schedule_period_schedule ON schedule_period(schedule_id);
CREATE INDEX IF NOT EXISTS idx_schedule_period_day ON schedule_period(schedule_id, day_of_week) WHERE day_of_week IS NOT NULL;

-- =============================================================================
-- RLS POLICIES
-- =============================================================================

-- Enable RLS
ALTER TABLE country ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedule_kind ENABLE ROW LEVEL SECURITY;
ALTER TABLE holiday ENABLE ROW LEVEL SECURITY;
ALTER TABLE holiday_rule ENABLE ROW LEVEL SECURITY;
ALTER TABLE holiday_rule_landmark ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedule ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedule_period ENABLE ROW LEVEL SECURITY;

-- Public tables - readable by all
DROP POLICY IF EXISTS "Public read access to country" ON country;
CREATE POLICY "Public read access to country" ON country
    FOR SELECT USING (TRUE);

DROP POLICY IF EXISTS "Public read access to schedule_kind" ON schedule_kind;
CREATE POLICY "Public read access to schedule_kind" ON schedule_kind
    FOR SELECT USING (TRUE);

DROP POLICY IF EXISTS "Public read access to holiday" ON holiday;
CREATE POLICY "Public read access to holiday" ON holiday
    FOR SELECT USING (TRUE);

-- Business data - brand isolation
DROP POLICY IF EXISTS "Brand isolation for holiday_rule" ON holiday_rule;
CREATE POLICY "Brand isolation for holiday_rule" ON holiday_rule
    USING (brand_id = (auth.jwt() ->> 'brand_id')::UUID);

DROP POLICY IF EXISTS "Brand isolation for holiday_rule_landmark" ON holiday_rule_landmark;
CREATE POLICY "Brand isolation for holiday_rule_landmark" ON holiday_rule_landmark
    USING (
        EXISTS (
            SELECT 1 FROM holiday_rule hr
            WHERE hr.id = holiday_rule_landmark.holiday_rule_id
            AND hr.brand_id = (auth.jwt() ->> 'brand_id')::UUID
        )
    );

DROP POLICY IF EXISTS "Brand isolation for schedule" ON schedule;
CREATE POLICY "Brand isolation for schedule" ON schedule
    USING (
        EXISTS (
            SELECT 1 FROM landmark l
            WHERE l.id = schedule.landmark_id
            AND l.brand_id = (auth.jwt() ->> 'brand_id')::UUID
        )
    );

DROP POLICY IF EXISTS "Brand isolation for schedule_period" ON schedule_period;
CREATE POLICY "Brand isolation for schedule_period" ON schedule_period
    USING (
        EXISTS (
            SELECT 1 FROM schedule s
            JOIN landmark l ON l.id = s.landmark_id
            WHERE s.id = schedule_period.schedule_id
            AND l.brand_id = (auth.jwt() ->> 'brand_id')::UUID
        )
    );

-- =============================================================================
-- UTILITY FUNCTIONS
-- =============================================================================

-- Get effective schedule for a landmark on a specific date
CREATE OR REPLACE FUNCTION get_effective_schedule(
    p_landmark_id UUID,
    p_date DATE
)
RETURNS TABLE (
    schedule_id UUID,
    schedule_kind TEXT,
    period_name TEXT,
    period_order INTEGER,
    start_time TIME,
    end_time TIME,
    is_closed BOOLEAN,
    source TEXT,
    notes TEXT
) AS $$
DECLARE
    holiday_rec RECORD;
    dow INTEGER;
BEGIN
    -- Get day of week (0=Sunday, 6=Saturday)
    dow := EXTRACT(DOW FROM p_date)::INTEGER;

    -- Check if there's a holiday on this date that has a rule for this landmark
    SELECT h.id, h.name INTO holiday_rec
    FROM holiday h
    JOIN holiday_rule hr ON hr.holiday_id = h.id
    LEFT JOIN holiday_rule_landmark hrl ON hrl.holiday_rule_id = hr.id
    WHERE hr.brand_id = (SELECT brand_id FROM landmark WHERE id = p_landmark_id)
    AND (hrl.landmark_id = p_landmark_id OR hrl.landmark_id IS NULL)
    AND h.typical_date = TO_CHAR(p_date, 'MM-DD')
    LIMIT 1;

    -- If holiday found, return holiday schedule
    IF FOUND THEN
        RETURN QUERY
        SELECT
            s.id as schedule_id,
            s.schedule_kind,
            sp.period_name,
            sp.period_order,
            sp.start_time,
            sp.end_time,
            sp.is_closed,
            'holiday'::TEXT as source,
            sp.notes
        FROM schedule s
        JOIN schedule_period sp ON sp.schedule_id = s.id
        WHERE s.landmark_id = p_landmark_id
        AND s.schedule_type = 'holiday'
        AND s.holiday_id = holiday_rec.id
        AND (s.effective_from IS NULL OR s.effective_from <= p_date)
        AND (s.effective_to IS NULL OR s.effective_to >= p_date)
        ORDER BY sp.period_order;

        IF FOUND THEN
            RETURN;
        END IF;
    END IF;

    -- Return regular weekly schedule
    RETURN QUERY
    SELECT
        s.id as schedule_id,
        s.schedule_kind,
        sp.period_name,
        sp.period_order,
        sp.start_time,
        sp.end_time,
        sp.is_closed,
        'weekly'::TEXT as source,
        sp.notes
    FROM schedule s
    JOIN schedule_period sp ON sp.schedule_id = s.id
    WHERE s.landmark_id = p_landmark_id
    AND s.schedule_type = 'weekly'
    AND sp.day_of_week = dow
    AND (s.effective_from IS NULL OR s.effective_from <= p_date)
    AND (s.effective_to IS NULL OR s.effective_to >= p_date)
    ORDER BY sp.period_order;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Check if landmark is open at specific time
CREATE OR REPLACE FUNCTION is_landmark_open(
    p_landmark_id UUID,
    p_datetime TIMESTAMPTZ
)
RETURNS BOOLEAN AS $$
DECLARE
    check_date DATE;
    check_time TIME;
    period_rec RECORD;
BEGIN
    check_date := p_datetime::DATE;
    check_time := p_datetime::TIME;

    FOR period_rec IN
        SELECT * FROM get_effective_schedule(p_landmark_id, check_date)
        WHERE is_closed = FALSE
    LOOP
        IF check_time >= period_rec.start_time AND check_time <= period_rec.end_time THEN
            RETURN TRUE;
        END IF;
    END LOOP;

    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- UNIQUE CONSTRAINTS
-- =============================================================================

-- Ensure unique regular schedule per landmark and kind
CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_schedule_unique_regular 
ON hour_schedule(landmark_id, schedule_kind) 
WHERE schedule_kind = 'regular' AND is_active = TRUE;

-- Ensure unique period order per context
CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_period_schedule_unique_order 
ON hour_period(schedule_id, day_of_week, operation_kind, period_order)
WHERE schedule_id IS NOT NULL;

CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_period_holiday_unique_order 
ON hour_period(holiday_instance_id, operation_kind, period_order)
WHERE holiday_instance_id IS NOT NULL;

CREATE UNIQUE INDEX IF NOT EXISTS idx_hour_period_override_unique_order 
ON hour_period(landmark_id, override_date, operation_kind, period_order)
WHERE override_date IS NOT NULL;

-- =============================================================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================================================

COMMENT ON TABLE holiday_reference IS 'Master holiday data populated from external APIs';
COMMENT ON TABLE holiday_instance IS 'Yearly instances of holidays with calculated dates';
COMMENT ON TABLE holiday_rule IS 'Business rules defining what to do for specific holidays';
COMMENT ON TABLE hour_schedule IS 'Schedules defined for landmarks (regular, reduced, etc.)';
COMMENT ON TABLE hour_period IS 'Unified time periods for all scheduling needs';
COMMENT ON FUNCTION get_effective_hours IS 'Returns effective hours considering holidays and overrides';
COMMENT ON FUNCTION is_open IS 'Checks if a landmark is open for a specific operation'; 