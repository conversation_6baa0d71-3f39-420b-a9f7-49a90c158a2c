// =============================================================================
// HOLIDAY SYSTEM TYPES
// =============================================================================
// Types for the comprehensive business hours & holiday management system

import type { LocalizedText } from '$lib/utils/localization';

// =============================================================================
// REFERENCE DATA
// =============================================================================

export interface DayOfWeek {
	id: number; // 0=Sunday, 6=Saturday
	name_full: LocalizedText;
	name_short: LocalizedText;
	index_mon_first: number;
	index_sun_first: number;
	func_name_full_en_first: string;
	func_name_short_en_first: string;
}

export interface ScheduleKind {
	id: string;
	name: LocalizedText;
	description?: LocalizedText;
	is_default: boolean;
	created_at: string;
	updated_at: string;
}

// =============================================================================
// HOLIDAY REFERENCE DATA
// =============================================================================

export interface Holiday {
	id: string; // External API ID for consistency
	name: LocalizedText;
	description?: LocalizedText;
	country_iso2: string;
	typical_date?: string; // MM-DD format for fixed holidays
	is_national: boolean;
	recurrence_type: string;
	source_api?: string;
	created_at: string;
	updated_at: string;
}

export interface HolidayDay {
	id: string;
	holiday_id: string;
	year: number;
	actual_date: string; // YYYY-MM-DD
	is_observed: boolean;
	created_at: string;
	updated_at: string;

	// Relations
	holiday?: Holiday;
}

// =============================================================================
// BUSINESS RULES
// =============================================================================

export interface HolidayRule {
	id: string;
	brand_id: string;
	holiday_id: string;
	schedule_kind: string;
	message_to_consumer?: LocalizedText;
	message_to_staff?: LocalizedText;
	created_at: string;
	updated_at: string;
	// Joined data
	holiday?: Holiday;
	schedule_kind_data?: ScheduleKind;
	landmark_ids?: string[];
}

export interface HolidayRuleLandmark {
	id: string;
	holiday_rule_id: string;
	landmark_id: string;
	created_at: string;
}

// =============================================================================
// SCHEDULES
// =============================================================================

export interface Schedule {
	id: string;
	landmark_id: string;
	schedule_kind: string;
	holiday_id?: string; // If not null, this is a holiday schedule
	name: LocalizedText;
	description?: LocalizedText;
	effective_from?: string;
	effective_to?: string;
	created_at: string;
	updated_at: string;

	// Relations
	periods?: SchedulePeriod[];
	holiday?: Holiday;
	schedule_kind_data?: ScheduleKind;
}

export interface SchedulePeriod {
	id: string;
	schedule_id: string;
	day_of_week?: number; // References day_of_week.id, null for holiday schedules
	period_name?: LocalizedText; // Customer-facing name, localized
	period_order: number;
	start_time?: string;
	end_time?: string;
	created_at: string;
	updated_at: string;
}

// =============================================================================
// UI TYPES
// =============================================================================

export interface HolidayRuleWithDetails extends HolidayRule {
	holiday: Holiday;
	schedule_kind_data: ScheduleKind;
	affected_landmarks: Array<{
		id: string;
		name: string;
	}>;
}

export interface HolidayCalendarEvent {
	id: string;
	title: string;
	date: string;
	type: 'reference' | 'rule' | 'instance';
	schedule_kind?: string;
	is_observed: boolean;
	country_code?: string;
	has_rule: boolean;
}

// =============================================================================
// FORM TYPES
// =============================================================================

export interface HolidayRuleFormData {
	holiday_id: string;
	schedule_kind: string;
	landmark_ids?: string[];
	message_to_consumer?: {
		en?: string;
		zh?: string;
		ko?: string;
		ja?: string;
	};
	message_to_staff?: {
		en?: string;
		zh?: string;
		ko?: string;
		ja?: string;
	};
}

export interface ImportHolidayFormData {
	year: number;
	country_iso2: string;
	use_local_names: boolean;
	holiday_types?: string[];
	overwrite_existing: boolean;
}

// =============================================================================
// API RESPONSE TYPES
// =============================================================================

export interface NagerDateHoliday {
	date: string;
	localName: string;
	name: string;
	countryCode: string;
	fixed: boolean;
	global: boolean;
	counties?: string[];
	launchYear?: number;
	types: string[];
}

export interface HolidayImportResult {
	imported_count: number;
	skipped_count: number;
	error_count: number;
	imported_holidays: Holiday[];
	errors: string[];
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

export type HolidayViewMode = 'rules' | 'calendar' | 'instances';

export interface HolidayFilters {
	year?: number;
	country_code?: string;
	schedule_kind?: string;
	is_active?: boolean;
	has_rule?: boolean;
}

export interface HolidayStats {
	total_rules: number;
	active_rules: number;
	closed_days: number;
	reduced_hours_days: number;
	upcoming_holidays: number;
}
