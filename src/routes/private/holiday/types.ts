// =============================================================================
// HOLIDAY SYSTEM TYPES
// =============================================================================
// Types for the comprehensive business hours & holiday management system

import type { LocalizedText } from '$lib/utils/localization';

// =============================================================================
// DATABASE TYPES
// =============================================================================

export interface HolidayReference {
	id: string; // External API ID for consistency
	name: LocalizedText;
	description?: LocalizedText;
	country_code: string;
	holiday_date: string; // ISO date string
	is_national: boolean;
	recurrence_rule?: string;
	source_api?: string;
	created_at: string;
	updated_at: string;
}

export interface HolidayInstance {
	id: string;
	holiday_reference_id: string;
	year: number;
	actual_date: string; // ISO date string
	is_observed: boolean;
	auto_generated: boolean;
	created_at: string;
	updated_at: string;
	// Joined data
	holiday_reference?: HolidayReference;
}

export interface ScheduleKind {
	id: string;
	name: LocalizedText;
	description?: LocalizedText;
	is_default: boolean;
	created_at: string;
	updated_at: string;
}

export interface HolidayRule {
	id: string;
	brand_id: string;
	holiday_reference_id: string;
	schedule_kind: string;
	applies_to_all_landmarks: boolean;
	customer_message?: LocalizedText;
	staff_message?: LocalizedText;
	auto_notify_customers: boolean;
	auto_notify_staff: boolean;
	revenue_impact_notes?: string;
	is_active: boolean;
	created_at: string;
	updated_at: string;
	// Joined data
	holiday_reference?: HolidayReference;
	schedule_kind_data?: ScheduleKind;
	landmark_ids?: string[];
}

export interface HolidayRuleLandmark {
	id: string;
	holiday_rule_id: string;
	landmark_id: string;
	created_at: string;
}

// =============================================================================
// UI TYPES
// =============================================================================

export interface HolidayRuleWithDetails extends HolidayRule {
	holiday_reference: HolidayReference;
	schedule_kind_data: ScheduleKind;
	upcoming_instances: HolidayInstance[];
	affected_landmarks: Array<{
		id: string;
		name: string;
	}>;
}

export interface HolidayCalendarEvent {
	id: string;
	title: string;
	date: string;
	type: 'reference' | 'rule' | 'instance';
	schedule_kind?: string;
	is_observed: boolean;
	country_code?: string;
	has_rule: boolean;
}

// =============================================================================
// FORM TYPES
// =============================================================================

export interface HolidayRuleFormData {
	holiday_reference_id: string;
	schedule_kind: string;
	applies_to_all_landmarks: boolean;
	landmark_ids?: string[];
	customer_message?: {
		en?: string;
		zh?: string;
		ko?: string;
		ja?: string;
	};
	staff_message?: {
		en?: string;
		zh?: string;
		ko?: string;
		ja?: string;
	};
	auto_notify_customers: boolean;
	auto_notify_staff: boolean;
	revenue_impact_notes?: string;
	is_active: boolean;
}

export interface ImportHolidayReferenceFormData {
	year: number;
	country_code: string;
	use_local_names: boolean;
	holiday_types?: string[];
	overwrite_existing: boolean;
}

// =============================================================================
// API RESPONSE TYPES
// =============================================================================

export interface NagerDateHoliday {
	date: string;
	localName: string;
	name: string;
	countryCode: string;
	fixed: boolean;
	global: boolean;
	counties?: string[];
	launchYear?: number;
	types: string[];
}

export interface HolidayImportResult {
	imported_count: number;
	skipped_count: number;
	error_count: number;
	imported_holidays: HolidayReference[];
	errors: string[];
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

export type HolidayViewMode = 'rules' | 'calendar' | 'instances';

export interface HolidayFilters {
	year?: number;
	country_code?: string;
	schedule_kind?: string;
	is_active?: boolean;
	has_rule?: boolean;
}

export interface HolidayStats {
	total_rules: number;
	active_rules: number;
	closed_days: number;
	reduced_hours_days: number;
	upcoming_holidays: number;
}
