<script lang="ts">
	import { PageContainer } from '$lib/components/layout';
	import { Button } from '$lib/components/ui/button';
	import { Plus } from '@lucide/svelte';
	import * as Card from '$lib/components/ui/card';
	import * as Table from '$lib/components/ui/table';
	import * as Tabs from '$lib/components/ui/tabs';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as AlertDialog from '$lib/components/ui/alert-dialog';
	import { Badge } from '$lib/components/ui/badge';
	import { Input } from '$lib/components/ui/input';
	import { Pencil, Trash2 } from '@lucide/svelte';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import ProductFeatureAccessForm from './components/ProductFeatureAccessForm.svelte';
	import ProductBuyersList from './components/ProductBuyersList.svelte';
	import { toast } from 'svelte-sonner';
	import { invalidate } from '$app/navigation';
	import type { FeatureAccessProduct } from './+page.server';

	interface PageData {
		products: FeatureAccessProduct[];
		featureAccessOptions: { id: string; title: string }[];
		brand: {
			id: string;
			name_short: LocalizedText;
		};
		supabase: any;
		user: {
			id: string;
		};
		form?: Record<string, any>;
	}

	let { data }: { data: PageData } = $props();

	let showAddProductDialog = $state(false);
	let selectedProduct = $state<FeatureAccessProduct | null>(null);
	let showEditProductDialog = $state(false);
	let showDeleteDialog = $state(false);
	let productToDelete = $state<FeatureAccessProduct | null>(null);
	let searchQuery = $state('');
	let expandedProductId = $state<string | null>(null);

	let filteredProducts = $derived(
		searchQuery
			? data.products.filter((product) =>
					getLocalizedText(product.title, getLocale())
						.toLowerCase()
						.includes(searchQuery.toLowerCase())
				)
			: data.products
	);

	async function handleProductSaved() {
		showAddProductDialog = false;
		showEditProductDialog = false;
		await invalidate('app:featureAccessProducts');
		toast.success('Product saved successfully');
	}

	async function deleteProduct() {
		if (!productToDelete) return;

		try {
			// Delete feature access first
			if (productToDelete.feature_access?.length) {
				const { error: featureAccessError } = await data.supabase
					.from('product_feature_access')
					.delete()
					.eq('id', productToDelete.feature_access[0].id);

				if (featureAccessError) {
					throw new Error(`Failed to delete feature access: ${featureAccessError.message}`);
				}
			}

			// Then delete the product
			const { error: productError } = await data.supabase
				.from('product')
				.delete()
				.eq('id', productToDelete.id);

			if (productError) {
				throw new Error(`Failed to delete product: ${productError.message}`);
			}

			// Close dialog and refresh data
			showDeleteDialog = false;
			productToDelete = null;
			await invalidate('app:featureAccessProducts');
			toast.success('Product deleted successfully');
		} catch (error: any) {
			console.error('Error deleting product:', error);
			toast.error(`Error: ${error.message || 'Unknown error occurred'}`);
		}
	}

	function toggleProductExpansion(productId: string) {
		if (expandedProductId === productId) {
			expandedProductId = null;
		} else {
			expandedProductId = productId;
		}
	}
</script>

{#snippet actions()}
	<Button
		onclick={() => {
			showAddProductDialog = true;
		}}
	>
		<Plus class="mr-2 size-4" />
		Add Product
	</Button>
{/snippet}

{#snippet content()}
	<div class="space-y-4">
		<Card.Root>
			<Card.Header>
				<Card.Title>Feature Access Products</Card.Title>
				<Card.Description>
					Create and manage products that grant feature access to users when purchased
				</Card.Description>
			</Card.Header>
			<Card.Content>
				<div class="mb-4">
					<Input
						type="search"
						placeholder="Search products..."
						bind:value={searchQuery}
						class="max-w-sm"
					/>
				</div>

				{#if filteredProducts.length === 0}
					<div class="text-muted-foreground rounded-md border p-8 text-center">
						{searchQuery ? 'No products match your search' : 'No feature access products found'}
					</div>
				{:else}
					<div class="rounded-md border">
						<Table.Root>
							<Table.Header>
								<Table.Row>
									<Table.Head>Title</Table.Head>
									<Table.Head>Feature Access</Table.Head>
									<Table.Head>Buyers</Table.Head>
									<Table.Head>Created</Table.Head>
									<Table.Head class="text-right">Actions</Table.Head>
								</Table.Row>
							</Table.Header>
							<Table.Body>
								{#each filteredProducts as product}
									<Table.Row
										class={expandedProductId === product.id ? 'border-b-0' : ''}
										onclick={() => toggleProductExpansion(product.id)}
									>
										<Table.Cell>
											{getLocalizedText(product.title, getLocale())}
										</Table.Cell>
										<Table.Cell>
											<Badge variant="outline">
												{product.feature_access[0]?.feature_access_id?.replace(/^brand_/, '')}
											</Badge>
										</Table.Cell>
										<Table.Cell>{product.buyers?.length || 0}</Table.Cell>
										<Table.Cell>{new Date(product.created_at).toLocaleDateString()}</Table.Cell>
										<Table.Cell class="text-right">
											<div class="flex justify-end space-x-2">
												<Button
													variant="ghost"
													size="icon"
													onclick={(e) => {
														e.stopPropagation();
														selectedProduct = product;
														showEditProductDialog = true;
													}}
												>
													<Pencil class="size-4" />
												</Button>
												<Button
													variant="ghost"
													size="icon"
													onclick={(e) => {
														e.stopPropagation();
														productToDelete = product;
														showDeleteDialog = true;
													}}
												>
													<Trash2 class="size-4" />
												</Button>
											</div>
										</Table.Cell>
									</Table.Row>

									{#if expandedProductId === product.id && product.buyers?.length}
										<Table.Row>
											<Table.Cell colspan={5} class="bg-muted/10 p-4">
												<ProductBuyersList buyers={product.buyers} />
											</Table.Cell>
										</Table.Row>
									{/if}
								{/each}
							</Table.Body>
						</Table.Root>
					</div>
				{/if}
			</Card.Content>
		</Card.Root>
	</div>
{/snippet}

<PageContainer
	title="Brand Permissions"
	description="Manage feature access products for your brand"
	{actions}
	{content}
/>

<!-- Add Product Dialog -->
<Dialog.Root open={showAddProductDialog} onOpenChange={(open) => (showAddProductDialog = open)}>
	<Dialog.Content>
		<ProductFeatureAccessForm
			onClose={() => (showAddProductDialog = false)}
			onSaved={handleProductSaved}
			supabase={data.supabase}
			featureAccessOptions={data.featureAccessOptions}
			brandId={data.brand.id}
			userId={data.user.id}
			form={data.form}
		/>
	</Dialog.Content>
</Dialog.Root>

<!-- Edit Product Dialog -->
<Dialog.Root open={showEditProductDialog} onOpenChange={(open) => (showEditProductDialog = open)}>
	<Dialog.Content>
		<ProductFeatureAccessForm
			product={selectedProduct}
			onClose={() => (showEditProductDialog = false)}
			onSaved={handleProductSaved}
			supabase={data.supabase}
			featureAccessOptions={data.featureAccessOptions}
			brandId={data.brand.id}
			userId={data.user.id}
			form={data.form}
		/>
	</Dialog.Content>
</Dialog.Root>

<!-- Delete Confirmation Dialog -->
<AlertDialog.Root open={showDeleteDialog} onOpenChange={(open) => (showDeleteDialog = open)}>
	<AlertDialog.Content>
		<AlertDialog.Header>
			<AlertDialog.Title>Delete Product</AlertDialog.Title>
			<AlertDialog.Description>
				Are you sure you want to delete this product? This action cannot be undone.
				{#if productToDelete?.buyers?.length}
					<div class="text-destructive mt-2">
						Warning: This product has {productToDelete.buyers.length} buyers. Deleting it will revoke
						their access.
					</div>
				{/if}
			</AlertDialog.Description>
		</AlertDialog.Header>
		<AlertDialog.Footer>
			<AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
			<AlertDialog.Action onclick={deleteProduct}>Delete</AlertDialog.Action>
		</AlertDialog.Footer>
	</AlertDialog.Content>
</AlertDialog.Root>
