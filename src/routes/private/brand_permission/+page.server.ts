import { error, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import type { Database } from '$lib/supabase/database.types';
import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';

export interface FeatureAccessProduct {
	id: string;
	title: LocalizedText;
	kind: string;
	created_at: string;
	consumer_group_id: string;
	feature_access: {
		id: string;
		feature_access_id: string;
		feature_access_settings: Record<string, any>;
	}[];
	buyers?: {
		id: string;
		profile: {
			id: string;
			username: string | null;
			given_name: LocalizedText;
			family_name: LocalizedText;
		};
		created_at: string;
	}[];
}

export const load: PageServerLoad = async ({ locals }) => {
	const { user, brand, supabase } = locals;

	// Only allow brand owner to access this page
	if (user?.id !== brand.owner_profile_id) {
		throw error(403, 'You do not have permission to access this page');
	}

	// Load all products with feature access for this brand
	const { data: products, error: productsError } = await supabase
		.from('product')
		.select(
			`
			id, 
			title,
			kind,
			created_at,
			consumer_group_id,
			feature_access:product_feature_access(
				id,
				feature_access_id,
				feature_access_settings
			)
			`
		)
		.eq('brand_id', brand.id)
		.not('feature_access', 'is', null)
		.order('created_at', { ascending: false });

	if (productsError) {
		console.error('Error loading products:', productsError);
		throw error(500, 'Failed to load products');
	}

	// Load all feature access options
	const { data: featureAccessOptions, error: featureAccessError } = await supabase
		.from('feature_access')
		.select('id, name')
		.order('id');

	if (featureAccessError) {
		console.error('Error loading feature access options:', featureAccessError);
	}

	// Map the featureAccessOptions to use the correct properties - without using languageTag
	// Just pass the raw name data and let the client handle localization
	const mappedFeatureAccessOptions = (featureAccessOptions || []).map((option) => ({
		id: option.id,
		title: option.id, // Use ID as fallback title
		name: option.name // Pass the raw localized name data
	}));

	// For each product, load its buyers
	const productsWithBuyers: FeatureAccessProduct[] = await Promise.all(
		(products || []).map(async (product: any) => {
			const { data: buyers, error: buyersError } = await supabase
				.from('order_product')
				.select(
					`
					id,
					created_at,
					profile:consumer_profile_id(
						id, 
						username, 
						given_name,
						family_name
					)
					`
				)
				.eq('auto_product_price_product_id', product.id)
				.is('canceled_at', null)
				.order('created_at', { ascending: false });

			if (buyersError) {
				console.error(`Error loading buyers for product ${product.id}:`, buyersError);
			}

			return {
				...product,
				buyers: buyers || []
			};
		})
	);

	return {
		products: productsWithBuyers,
		featureAccessOptions: mappedFeatureAccessOptions,
		brand
	};
};

// Define a schema for the form
const productSchema = z.object({
	title: z.record(z.string(), z.string().optional())
});

export const actions: Actions = {
	createProduct: async ({ request, locals }) => {
		const { user, brand, supabase } = locals;

		// First read the form data once
		const formData = await request.formData();
		const featureAccessIds = JSON.parse(formData.get('featureAccessIds') as string);

		// Then validate with the formData object
		const form = await superValidate(formData, zod(productSchema));

		// If form validation fails, return the validation errors
		if (!form.valid) {
			return fail(400, { form });
		}

		// Ensure user is authenticated and is the brand owner
		if (!user) {
			return fail(401, { form, success: false, error: 'Not authenticated' });
		}

		if (user.id !== brand.owner_profile_id) {
			return fail(403, {
				form,
				success: false,
				error: 'You do not have permission to perform this action'
			});
		}

		// Use the title from the validated form data
		const title = form.data.title;

		try {
			// Create consumer group for this product
			const { data: newGroup, error: groupError } = await supabase
				.from('group')
				.insert({
					brand_id: brand.id,
					kind: 'consumer_group',
					name: `Consumer Group for ${Object.values(title)[0] || 'Product'}`,
					creator_id: user.id,
					join_rule: 'invite_by_group_admin'
				})
				.select('id')
				.single();

			if (groupError) {
				console.error('Error creating consumer group:', groupError);
				return fail(500, {
					form,
					success: false,
					error: `Failed to create consumer group: ${groupError.message}`
				});
			}

			// Create new product with the consumer group ID
			const { data: newProduct, error: productError } = await supabase
				.from('product')
				.insert({
					title,
					kind: 'membership',
					brand_id: brand.id,
					publishing_state: 'hidden',
					stock: 999999, // Unlimited stock for feature access
					creator_id: user.id,
					consumer_group_id: newGroup.id
				})
				.select('id')
				.single();

			if (productError) {
				console.error('Error creating product:', productError);
				return fail(500, {
					form,
					success: false,
					error: `Failed to create product: ${productError.message}`
				});
			}

			// Create feature access entries
			if (featureAccessIds.length > 0) {
				const featureAccessEntries = featureAccessIds.map((faType: string) => ({
					product_id: newProduct.id,
					feature_access_id: faType,
					auto_product_brand_id: brand.id,
					feature_access_settings: {}
				}));

				const { error: featureError } = await supabase
					.from('product_feature_access')
					.insert(featureAccessEntries);

				if (featureError) {
					console.error('Error creating feature access:', featureError);
					return fail(500, {
						form,
						success: false,
						error: `Failed to create feature access: ${featureError.message}`
					});
				}
			}

			return {
				form,
				success: true,
				productId: newProduct.id
			};
		} catch (error: any) {
			console.error('Error in product creation:', error);
			return fail(500, {
				form,
				success: false,
				error: error.message || 'An unexpected error occurred'
			});
		}
	},

	updateProduct: async ({ request, locals }) => {
		const { user, brand, supabase } = locals;

		// First read the form data once
		const formData = await request.formData();
		const featureAccessIds = JSON.parse(formData.get('featureAccessIds') as string);
		const productId = formData.get('productId') as string;

		// Then validate with the formData object
		const form = await superValidate(formData, zod(productSchema));

		// If form validation fails, return the validation errors
		if (!form.valid) {
			return fail(400, { form });
		}

		// Ensure user is authenticated and is the brand owner
		if (!user) {
			return fail(401, { form, success: false, error: 'Not authenticated' });
		}

		if (user.id !== brand.owner_profile_id) {
			return fail(403, {
				form,
				success: false,
				error: 'You do not have permission to perform this action'
			});
		}

		// Use the title from the validated form data
		const title = form.data.title;

		if (!productId) {
			return fail(400, { form, success: false, error: 'Product ID is required' });
		}

		try {
			// Check if product exists and belongs to the brand
			const { data: existingProduct, error: productQueryError } = await supabase
				.from('product')
				.select('id, brand_id')
				.eq('id', productId)
				.single();

			if (productQueryError || !existingProduct) {
				return fail(404, { form, success: false, error: 'Product not found' });
			}

			if (existingProduct.brand_id !== brand.id) {
				return fail(403, {
					form,
					success: false,
					error: 'You do not have permission to update this product'
				});
			}

			// Update product title
			const { error: updateError } = await supabase
				.from('product')
				.update({ title })
				.eq('id', productId);

			if (updateError) {
				return fail(500, {
					form,
					success: false,
					error: `Failed to update product: ${updateError.message}`
				});
			}

			// Get existing feature access entries
			const { data: existingFeatureAccess, error: featureQueryError } = await supabase
				.from('product_feature_access')
				.select('id, feature_access_id')
				.eq('product_id', productId);

			if (featureQueryError) {
				return fail(500, {
					form,
					success: false,
					error: `Failed to retrieve feature access: ${featureQueryError.message}`
				});
			}

			// Determine which to remove and which to add
			const existingFeatureAccessIds = existingFeatureAccess.map((fa) => fa.feature_access_id);

			// IDs to remove
			const featureAccessIdsToRemove = existingFeatureAccess
				.filter((fa) => !featureAccessIds.includes(fa.feature_access_id))
				.map((fa) => fa.id);

			// IDs to add
			const featureAccessIdsToAdd = featureAccessIds.filter(
				(id: string) => !existingFeatureAccessIds.includes(id)
			);

			// Delete removed feature access entries
			if (featureAccessIdsToRemove.length > 0) {
				const { error: deleteError } = await supabase
					.from('product_feature_access')
					.delete()
					.in('id', featureAccessIdsToRemove);

				if (deleteError) {
					return fail(500, {
						form,
						success: false,
						error: `Failed to remove feature access: ${deleteError.message}`
					});
				}
			}

			// Add new feature access entries
			if (featureAccessIdsToAdd.length > 0) {
				const newFeatureAccessEntries = featureAccessIdsToAdd.map((faType: string) => ({
					product_id: productId,
					feature_access_id: faType,
					auto_product_brand_id: brand.id,
					feature_access_settings: {}
				}));

				const { error: addError } = await supabase
					.from('product_feature_access')
					.insert(newFeatureAccessEntries);

				if (addError) {
					return fail(500, {
						form,
						success: false,
						error: `Failed to add feature access: ${addError.message}`
					});
				}
			}

			return {
				form,
				success: true,
				productId
			};
		} catch (error: any) {
			console.error('Error updating product:', error);
			return fail(500, {
				form,
				success: false,
				error: error.message || 'An unexpected error occurred'
			});
		}
	}
};
