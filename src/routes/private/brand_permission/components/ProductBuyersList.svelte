<script lang="ts">
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import * as Table from '$lib/components/ui/table';
	import { User } from '@lucide/svelte';
	import { Badge } from '$lib/components/ui/badge';

	interface Buyer {
		id: string;
		profile: {
			id: string;
			username: string | null;
			given_name: LocalizedText;
			family_name: LocalizedText;
		};
		created_at: string;
	}

	interface Props {
		buyers?: Buyer[];
	}

	let { buyers = [] }: Props = $props();

	function formatDate(dateString: string): string {
		const date = new Date(dateString);
		return date.toLocaleString();
	}

	function getBuyerName(buyer: Buyer): string {
		if (!buyer.profile) return 'Unknown User';

		const givenName =
			getLocalizedText(buyer.profile.given_name as LocalizedText, getLocale()) || '';
		const familyName =
			getLocalizedText(buyer.profile.family_name as LocalizedText, getLocale()) || '';

		if (givenName || familyName) {
			return `${givenName} ${familyName}`.trim();
		}

		return buyer.profile.username || 'Unnamed User';
	}
</script>

<div>
	<h3 class="mb-2 text-lg font-medium">Buyers ({buyers.length})</h3>

	{#if buyers.length === 0}
		<div class="rounded-md border p-4 text-center text-muted-foreground">
			No buyers found for this product
		</div>
	{:else}
		<div class="rounded-md border">
			<Table.Root>
				<Table.Header>
					<Table.Row>
						<Table.Head>User</Table.Head>
						<Table.Head>Purchased At</Table.Head>
					</Table.Row>
				</Table.Header>
				<Table.Body>
					{#each buyers as buyer}
						<Table.Row>
							<Table.Cell>
								<div class="flex items-center gap-2">
									<User class="size-4 text-muted-foreground" />
									<span>{getBuyerName(buyer)}</span>
								</div>
							</Table.Cell>
							<Table.Cell>{formatDate(buyer.created_at)}</Table.Cell>
						</Table.Row>
					{/each}
				</Table.Body>
			</Table.Root>
		</div>
	{/if}
</div>
