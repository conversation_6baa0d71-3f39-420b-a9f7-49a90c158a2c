<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import * as Select from '$lib/components/ui/select';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { FeatureAccessProduct } from '../+page.server';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import type { Database } from '$lib/supabase/database.types';
	import LocalizedTextControl from '$lib/components/shared/LocalizedTextControl.svelte';
	import { superForm } from 'sveltekit-superforms/client';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { z } from 'zod';
	import { Control, Field, FieldErrors } from 'formsnap';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { invalidateAll } from '$app/navigation';

	interface Props {
		product?: FeatureAccessProduct | null;
		onClose: () => void;
		onSaved: () => void;
		supabase: SupabaseClient<Database>;
		featureAccessOptions: { id: string; title: string; name?: LocalizedText }[];
		brandId: string;
		userId: string;
		form?: Record<string, any>;
	}

	let { product, onClose, onSaved, supabase, featureAccessOptions, brandId, userId, form }: Props =
		$props();

	// Function to handle the close action, we'll use this to make sure we don't close on error
	function handleClose() {
		// Only close if there's no error message
		if (!errorMessage) {
			onClose();
		}
	}

	// Create a form schema for the product title
	const schema = z.object({
		title: z.record(z.string(), z.string().optional())
	});

	// Create a proper form with the right data structure
	const initialData = {
		title: product?.title || { [getLocale()]: '' }
	};

	let isSubmitting = $state(false);
	let errorMessage = $state('');
	let shouldClose = $state(true);

	// Initialize form with title and use the zod adapter
	const superFormObj = superForm(form || initialData, {
		validators: zodClient(schema) as any,
		dataType: 'json',
		onSubmit() {
			isSubmitting = true;
			errorMessage = '';
			shouldClose = true; // Reset the close flag
		},
		onResult({ result }) {
			isSubmitting = false;

			if (result.type === 'failure') {
				shouldClose = false; // Prevent closing on error

				// Parse error from result
				try {
					// Handle both string and object error formats
					if (typeof result.data === 'string') {
						errorMessage = result.data;
					} else if (result.data?.error) {
						errorMessage = result.data.error;
					} else {
						errorMessage = 'An error occurred';
					}
				} catch (e) {
					errorMessage = 'An unexpected error occurred';
				}

				console.log('Form submission error:', result);
			}
		},
		onUpdated({ form }) {
			// Only close if it's a successful submission and we should close
			if (form.valid && shouldClose && !errorMessage) {
				invalidateAll();
				onSaved(); // This closes the form
			}
		}
	});

	const { form: titleForm, errors, enhance } = superFormObj;

	// Get display name for a feature access option
	function getFeatureDisplayName(id: string): string {
		const option = featureAccessOptions.find((opt) => opt.id === id);
		if (!option) return id;

		if (option.name) {
			return getLocalizedText(option.name as LocalizedText, getLocale()) || option.title;
		}

		return option.title;
	}

	// Array to track selected feature access types
	let selectedFeatureAccessIds = $state<string[]>([]);

	// Initialize selections from product if editing
	$effect(() => {
		if (product?.feature_access && product.feature_access.length > 0) {
			console.log('Initializing selections from product:', product.feature_access);
			selectedFeatureAccessIds = product.feature_access.map((fa) => fa.feature_access_id);
		}
	});

	// Toggle a feature access selection
	function toggleFeatureAccess(id: string): void {
		if (selectedFeatureAccessIds.includes(id)) {
			selectedFeatureAccessIds = selectedFeatureAccessIds.filter((item) => item !== id);
		} else {
			selectedFeatureAccessIds = [...selectedFeatureAccessIds, id];
		}
	}
</script>

<div>
	<h2 class="text-lg font-semibold">{product ? 'Edit' : 'Create'} Feature Access Product</h2>

	<form
		class="mt-4 space-y-4"
		method="POST"
		action={product ? '?/updateProduct' : '?/createProduct'}
		use:enhance
	>
		<LocalizedTextControl form={superFormObj} name="title" label="Product Title" />

		<input type="hidden" name="featureAccessIds" value={JSON.stringify(selectedFeatureAccessIds)} />
		{#if product}
			<input type="hidden" name="productId" value={product.id} />
		{/if}

		<div class="space-y-2">
			<Label for="featureAccess">Feature Access Types</Label>

			<div class="space-y-2 rounded-md border p-4">
				{#each featureAccessOptions as option (option.id)}
					<div class="flex items-center space-x-2">
						<Checkbox
							id={`feature-access-${option.id}`}
							checked={selectedFeatureAccessIds.includes(option.id)}
							onCheckedChange={() => toggleFeatureAccess(option.id)}
						/>
						<Label for={`feature-access-${option.id}`} class="cursor-pointer">
							{getFeatureDisplayName(option.id)}
						</Label>
					</div>
				{/each}
			</div>

			<p class="text-xs text-muted-foreground">
				Select one or more feature access types to grant through this product
			</p>
		</div>

		{#if errorMessage}
			<div class="rounded-md bg-destructive/10 p-3 text-sm text-destructive">
				{errorMessage}
			</div>
		{/if}

		<div class="flex items-center gap-2">
			<span class="text-sm text-muted-foreground">Selected: {selectedFeatureAccessIds.length}</span>
		</div>

		<div class="flex justify-end space-x-2 pt-4">
			<Button type="button" variant="outline" onclick={handleClose}>Cancel</Button>
			<Button type="submit" disabled={isSubmitting || selectedFeatureAccessIds.length === 0}>
				{isSubmitting ? 'Saving...' : 'Save'}
			</Button>
		</div>
	</form>
</div>
