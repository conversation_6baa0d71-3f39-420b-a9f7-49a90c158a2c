<!-- src/routes/private/order/EditOrderModal.svelte -->
<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import {
		<PERSON><PERSON>,
		DialogContent,
		<PERSON><PERSON>Header,
		<PERSON>alogTitle,
		DialogFooter
	} from '$lib/components/ui/dialog';
	import { Button } from '$lib/components/ui/button';
	import type { Database } from '$lib/supabase/database.types';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';

	type Order = Database['public']['Tables']['order']['Row'] & {
		order_product: (Database['public']['Tables']['order_product']['Row'] & {
			consumer_profile: {
				family_name: Record<string, string>;
				given_name: Record<string, string>;
			};
			product_price: Database['public']['Tables']['product_price']['Row'] & {
				product: {
					id: string;
					metadata: {
						auto_final_title: Record<string, string>;
						auto_final_subtitle: Record<string, string>;
					};
				};
			};
		})[];
		order_price: (Database['public']['Tables']['order_price']['Row'] & {
			order_price_payment: Database['public']['Tables']['order_price_payment']['Row'][];
			order_price_consumption: Database['public']['Tables']['order_price_consumption']['Row'][];
		})[];
	};

	interface Props {
		selectedOrder: Order;
		onUpdate: () => void;
		onClose: () => void;
	}

	let { selectedOrder, onUpdate, onClose }: Props = $props();

	let isLoading = $state(false);
	let errorMessage = $state<string | null>(null);

	async function handleSave() {
		try {
			isLoading = true;
			errorMessage = null;

			const { error: updateError } = await fetch('/api/orders/' + selectedOrder.id, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ status: selectedOrder.status })
			}).then((res) => res.json());

			if (updateError) throw updateError;

			onUpdate();
		} catch (error) {
			console.error('Error updating order:', error);
			errorMessage = error instanceof Error ? error.message : 'An error occurred';
		} finally {
			isLoading = false;
		}
	}
</script>

<Dialog open={true}>
	<DialogContent>
		<DialogHeader>
			<DialogTitle>Edit Order</DialogTitle>
		</DialogHeader>

		<div class="space-y-4">
			<div>
				<h3 class="mb-2 font-medium">Order Products</h3>
				<ul class="list-inside list-disc space-y-2">
					{#each selectedOrder.order_product as order_product}
						<li>
							{getLocalizedText(
								order_product.product_price.product.metadata.auto_final_title,
								getLocale()
							)}
							{#if order_product.product_price.product.metadata.auto_final_subtitle}
								- {getLocalizedText(
									order_product.product_price.product.metadata.auto_final_subtitle,
									getLocale()
								)}
							{/if}
							({order_product.product_purchased_count} units)
							<br />
							Consumer: {getLocalizedText(
								order_product.consumer_profile.family_name,
								getLocale()
							)}
							{getLocalizedText(order_product.consumer_profile.given_name, getLocale())}
						</li>
					{/each}
				</ul>
			</div>

			<div>
				<Label for="status">Status</Label>
				<Input id="status" bind:value={selectedOrder.status} />
			</div>

			{#if errorMessage}
				<p class="text-sm text-red-500" role="alert">{errorMessage}</p>
			{/if}
		</div>

		<DialogFooter>
			<Button variant="outline" onclick={onClose} disabled={isLoading}>Cancel</Button>
			<Button onclick={handleSave} disabled={isLoading}>
				{isLoading ? 'Saving...' : 'Save'}
			</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>
