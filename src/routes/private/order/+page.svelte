<!-- src/routes/private/order/+page.svelte -->
<script lang="ts">
	import { invalidate, goto } from '$app/navigation';
	import { Button } from '$lib/components/ui/button';
	import { Plus } from '@lucide/svelte';
	import type { Database } from '$lib/supabase/database.types';
	import EditOrderModal from './EditOrderModal.svelte';
	import { getLocalizedText } from '$lib/utils/localization';
	import { fade } from 'svelte/transition';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import DataTable from '$lib/components/shared/DataTable.svelte';
	import { PageContainer } from '$lib/components/layout';
	import {
		createSelectColumn,
		createLocalizedColumn,
		createDateColumn,
		createActionsColumn,
		createSortableColumn
	} from '$lib/components/shared/table-utils';
	import type { ColumnDef } from '@tanstack/table-core';
	import { getLocale } from '$lib/paraglide/runtime';

	interface Props {
		data: {
			orders: Order[];
			count: number;
			page: number;
			pageSize: number;
		};
		supabase: SupabaseClient;
	}

	type Order = Database['public']['Tables']['order']['Row'] & {
		order_product: (Database['public']['Tables']['order_product']['Row'] & {
			consumer_profile: {
				family_name: Record<string, string>;
				given_name: Record<string, string>;
			};
			product_price: Database['public']['Tables']['product_price']['Row'] & {
				product: {
					id: string;
					metadata: {
						auto_final_title: Record<string, string>;
						auto_final_subtitle: Record<string, string>;
					};
				};
			};
		})[];
		order_price: (Database['public']['Tables']['order_price']['Row'] & {
			order_price_payment: Database['public']['Tables']['order_price_payment']['Row'][];
			order_price_consumption: Database['public']['Tables']['order_price_consumption']['Row'][];
			payer: {
				family_name: Record<string, string>;
				given_name: Record<string, string>;
			} | null;
		})[];
	};

	let { data, supabase }: Props = $props();
	let selectedOrder = $state<Order | null>(null);
	let showEditModal = $state(false);
	let isLoading = $state(false);
	let errorMessage = $state<string | null>(null);

	const columns: ColumnDef<Order>[] = [
		createSelectColumn<Order>(),
		createSortableColumn<Order>('id', 'Order ID', (row) => row.id),
		createDateColumn<Order>('created_at', 'Created At', (row) => row.created_at),
		createSortableColumn<Order>('status', 'Status', (row) => row.status),
		createActionsColumn<Order>([
			{
				label: 'Edit',
				onClick: (row) => {
					selectedOrder = row;
					showEditModal = true;
				}
			}
		])
	];

	async function handleSave(data: {
		order: Partial<Database['public']['Tables']['order']['Row']>;
	}) {
		try {
			isLoading = true;
			errorMessage = null;

			if (selectedOrder) {
				const { error: orderError } = await supabase
					.from('order')
					.update(data.order)
					.eq('id', selectedOrder.id);

				if (orderError) throw orderError;
			}

			await invalidate('supabase:db:orders');
			showEditModal = false;
			selectedOrder = null;
		} catch (error) {
			console.error('Error saving order:', error);
			errorMessage = 'Failed to save order';
		} finally {
			isLoading = false;
		}
	}
</script>

{#snippet actions()}
	<div class="flex items-center gap-2">
		<Button variant="default" onclick={() => goto('/private/request/order/create')}>
			<Plus class="mr-2 h-4 w-4" />
			Create Order
		</Button>
	</div>
{/snippet}

{#snippet content()}
	<DataTable
		data={data.orders}
		{columns}
		searchKey="id"
		searchPlaceholder="Search order ID..."
		onRowAction={(row) => {
			selectedOrder = row;
			showEditModal = true;
		}}
		pageSize={data.pageSize}
	></DataTable>

	{#if showEditModal}
		<EditOrderModal
			{selectedOrder}
			{isLoading}
			{errorMessage}
			onClose={() => {
				showEditModal = false;
				selectedOrder = null;
			}}
			onSave={handleSave}
		></EditOrderModal>
	{/if}
{/snippet}

<PageContainer title="Orders" description="View and manage customer orders" {actions} {content} />
