import type { ServerLoad } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';

export const load = (async ({ depends, locals: { supabase }, url }) => {
	depends('orders');

	const page = parseInt(url.searchParams.get('page') || '1');
	const pageSize = 10;
	const from = (page - 1) * pageSize;
	const to = from + pageSize - 1;

	const {
		data: orders,
		error: ordersError,
		count
	} = await supabase
		.from('order')
		.select(
			`
      *,
      order_product (
        *,
        consumer_profile:profile(
          family_name,
          given_name
        ),
        product_price (
          *,
          product (
            id,
            metadata
          )
        )
      ),
      order_price (
        *,
        payer:profile!id (
          family_name,
          given_name
        ),
        order_price_payment (*),
        order_price_consumption (*)
      )
    `,
			{ count: 'exact' }
		)
		.range(from, to)
		.order('created_at', { ascending: false });

	if (ordersError) {
		console.error('Error loading orders:', ordersError);
		throw error(500, ordersError.message);
	}

	return {
		orders,
		count: count ?? 0,
		page,
		pageSize
	};
}) satisfies ServerLoad;
