<script lang="ts">
	import { fly } from 'svelte/transition';
	import { Button } from '$lib/components/ui/button';
	import { ChevronRight, Calendar, MapPin } from '@lucide/svelte';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { PageData } from '../../$types';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();
	let brand = $derived(data.brand);
	let brandName = $derived(brand?.name_full ? getLocalizedText(brand.name_full) : 'Welcome');
</script>

<div class="relative min-h-screen">
	<!-- Hero Section with Brand Info -->
	<section class="from-background via-muted/10 to-background relative bg-gradient-to-b">
		<div class="container mx-auto px-4 py-20">
			<div class="text-center">
				{#if brand?.logo_url}
					<img
						src={brand.logo_url}
						alt={brandName}
						class="mx-auto mb-8 h-24 w-auto object-contain"
						in:fly={{ y: 20, duration: 800, delay: 100 }}
					/>
				{/if}

				<h1
					in:fly={{ y: 20, duration: 800, delay: 200 }}
					class="text-5xl font-bold tracking-tight sm:text-6xl lg:text-7xl"
				>
					<span class="from-primary to-primary/70 bg-gradient-to-r bg-clip-text text-transparent">
						{brandName}
					</span>
				</h1>

				{#if brand?.intro_short}
					<p
						in:fly={{ y: 20, duration: 800, delay: 400 }}
						class="text-muted-foreground mx-auto mt-6 max-w-2xl text-lg"
					>
						{getLocalizedText(brand.intro_short)}
					</p>
				{/if}

				<div in:fly={{ y: 20, duration: 800, delay: 600 }} class="mt-10 flex justify-center gap-4">
					<Button href="/event" size="lg" class="group">
						View Our Events
						<ChevronRight class="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
					</Button>
					{#if brand?.homepage_url}
						<Button href={brand.homepage_url} variant="outline" size="lg" target="_blank">
							Learn More
						</Button>
					{/if}
				</div>
			</div>
		</div>
	</section>

	<!-- Quick Actions -->
	<section class="container mx-auto px-4 py-16">
		<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
			<div class="bg-card hover:bg-accent rounded-lg border p-6 transition-colors">
				<Calendar class="text-primary mb-4 h-8 w-8" />
				<h3 class="mb-2 text-lg font-semibold">Upcoming Events</h3>
				<p class="text-muted-foreground mb-4 text-sm">
					Browse our schedule and register for upcoming classes and workshops.
				</p>
				<Button href="/event" variant="ghost" size="sm" class="group">
					View Schedule
					<ChevronRight class="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" />
				</Button>
			</div>

			<div class="bg-card hover:bg-accent rounded-lg border p-6 transition-colors">
				<MapPin class="text-primary mb-4 h-8 w-8" />
				<h3 class="mb-2 text-lg font-semibold">Our Locations</h3>
				<p class="text-muted-foreground mb-4 text-sm">
					Find our venues and get directions to your next event.
				</p>
				<Button href="/event" variant="ghost" size="sm" class="group">
					View Locations
					<ChevronRight class="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" />
				</Button>
			</div>

			{#if data.user}
				<div class="bg-card hover:bg-accent rounded-lg border p-6 transition-colors">
					<Calendar class="text-primary mb-4 h-8 w-8" />
					<h3 class="mb-2 text-lg font-semibold">My Orders</h3>
					<p class="text-muted-foreground mb-4 text-sm">
						View your registrations and manage your bookings.
					</p>
					<Button href="/private/my-order" variant="ghost" size="sm" class="group">
						View Orders
						<ChevronRight class="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" />
					</Button>
				</div>
			{:else}
				<div class="bg-card hover:bg-accent rounded-lg border p-6 transition-colors">
					<Calendar class="text-primary mb-4 h-8 w-8" />
					<h3 class="mb-2 text-lg font-semibold">Join Us</h3>
					<p class="text-muted-foreground mb-4 text-sm">
						Create an account to register for events and manage your bookings.
					</p>
					<Button href="/auth/sign-up" variant="ghost" size="sm" class="group">
						Sign Up
						<ChevronRight class="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" />
					</Button>
				</div>
			{/if}
		</div>
	</section>

	<!-- Support Section -->
	{#if brand?.support_email || brand?.support_phone || brand?.support_web_url}
		<section class="bg-muted/30">
			<div class="container mx-auto px-4 py-12">
				<h2 class="mb-6 text-center text-2xl font-semibold">Need Help?</h2>
				<div class="flex flex-wrap justify-center gap-6">
					{#if brand.support_email}
						<a
							href="mailto:{brand.support_email}"
							class="text-muted-foreground hover:text-foreground"
						>
							Email: {brand.support_email}
						</a>
					{/if}
					{#if brand.support_phone}
						<a href="tel:{brand.support_phone}" class="text-muted-foreground hover:text-foreground">
							Phone: {brand.support_phone}
						</a>
					{/if}
					{#if brand.support_web_url}
						<a
							href={brand.support_web_url}
							target="_blank"
							class="text-muted-foreground hover:text-foreground"
						>
							Support Center
						</a>
					{/if}
				</div>
			</div>
		</section>
	{/if}
</div>
