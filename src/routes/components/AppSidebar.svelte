<script lang="ts">
	import * as Sidebar from '$lib/components/ui/sidebar';
	import type { LayoutData } from '../$types';
	import { Button } from '$lib/components/ui/button';
	import type {
		NavigationItem,
		BrandFeatureGroup,
		IconType,
		GroupNavigationItem
	} from '$lib/types/navigation';
	import { ICON_MAP, ALWAYS_AVAILABLE_ITEMS } from '$lib/utils/navigation';
	import UserMenu from './UserMenu.svelte';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import { goto } from '$app/navigation';
	import { LogIn } from '@lucide/svelte';

	interface Props {
		data: LayoutData;
		currentPath: string;
		profileFeatures: BrandFeatureGroup[];
		groups: GroupNavigationItem[];
	}

	let { data, currentPath = $bindable(), profileFeatures, groups }: Props = $props();
	let user = $derived(data?.user);

	// Public navigation items
	const publicNavigationItems = [{ title: 'Events', url: '/event', icon: 'Calendar' as IconType }];

	function navigateToSignIn() {
		goto('/auth/sign-in');
	}
</script>

<Sidebar.Root>
	<Sidebar.Header>
		<span
			class="bg-linear-to-br from-foreground to-foreground/60 bg-clip-text text-xl font-bold text-transparent dark:from-foreground dark:to-foreground/40"
		>
			{getLocalizedText(data.brand.name_short, getLocale())}
		</span>
	</Sidebar.Header>
	<Sidebar.Content>
		{#if user}
			<!-- Authenticated user navigation -->
			<Sidebar.Group>
				<Sidebar.GroupContent>
					<Sidebar.Menu>
						<!-- Include Events first -->
						{#each publicNavigationItems as item (item.title)}
							<Sidebar.MenuItem>
								<Sidebar.MenuButton isActive={currentPath?.startsWith(item.url)}>
									{#snippet child({ props })}
										{@const Icon = ICON_MAP[item.icon]}
										<a href={item.url} {...props}>
											<Icon />
											<span>{item.title}</span>
										</a>
									{/snippet}
								</Sidebar.MenuButton>
							</Sidebar.MenuItem>
						{/each}
						<!-- Then include other authenticated items -->
						{#each ALWAYS_AVAILABLE_ITEMS.filter((item) => item.url !== '/event') as item (item.title)}
							<Sidebar.MenuItem>
								<Sidebar.MenuButton isActive={currentPath?.startsWith(item.url)}>
									{#snippet child({ props })}
										{@const Icon = ICON_MAP[item.icon]}
										<a href={item.url} {...props}>
											<Icon />
											<span>{item.title}</span>
										</a>
									{/snippet}
								</Sidebar.MenuButton>
							</Sidebar.MenuItem>
						{/each}
					</Sidebar.Menu>
				</Sidebar.GroupContent>
			</Sidebar.Group>
		{:else}
			<!-- Public navigation items for non-authenticated users -->
			<Sidebar.Group>
				<Sidebar.GroupContent>
					<Sidebar.Menu>
						{#each publicNavigationItems as item (item.title)}
							<Sidebar.MenuItem>
								<Sidebar.MenuButton isActive={currentPath?.startsWith(item.url)}>
									{#snippet child({ props })}
										{@const Icon = ICON_MAP[item.icon]}
										<a href={item.url} {...props}>
											<Icon />
											<span>{item.title}</span>
										</a>
									{/snippet}
								</Sidebar.MenuButton>
							</Sidebar.MenuItem>
						{/each}
					</Sidebar.Menu>
				</Sidebar.GroupContent>
			</Sidebar.Group>
		{/if}

		{#if user}
			{#if groups && groups.length > 0}
				<Sidebar.Group>
					<Sidebar.GroupLabel>Groups</Sidebar.GroupLabel>
					<Sidebar.GroupContent>
						<Sidebar.Menu>
							{#each groups as item}
								<Sidebar.MenuItem>
									<Sidebar.MenuButton isActive={currentPath?.startsWith(item.url)}>
										{#snippet child({ props })}
											{@const Icon = ICON_MAP[item.icon]}
											<a href={item.url} {...props}>
												<Icon />
												<span>{item.title}</span>
											</a>
										{/snippet}
									</Sidebar.MenuButton>
								</Sidebar.MenuItem>
							{/each}
						</Sidebar.Menu>
					</Sidebar.GroupContent>
				</Sidebar.Group>
			{/if}

			{#each profileFeatures as group (group.brand_id)}
				<Sidebar.Group>
					<Sidebar.GroupLabel>
						{getLocalizedText(group.brand_name, getLocale())}
					</Sidebar.GroupLabel>
					<Sidebar.GroupContent>
						<Sidebar.Menu>
							{#each group.features as item (`${group.brand_id}-${item.title}`)}
								<Sidebar.MenuItem>
									<Sidebar.MenuButton isActive={currentPath?.startsWith(item.url)}>
										{#snippet child({ props })}
											{@const Icon = ICON_MAP[item.icon]}
											<a href={item.url} {...props}>
												<Icon />
												<span>{item.title}</span>
											</a>
										{/snippet}
									</Sidebar.MenuButton>
								</Sidebar.MenuItem>
							{/each}
						</Sidebar.Menu>
					</Sidebar.GroupContent>
				</Sidebar.Group>
			{/each}
		{:else}
			<!-- Sign in button for non-authenticated users -->
			<div class="mt-auto p-4">
				<Button variant="outline" class="w-full" onclick={navigateToSignIn}>
					<LogIn class="mr-2 h-4 w-4" />
					Sign In
				</Button>
			</div>
		{/if}
	</Sidebar.Content>

	<Sidebar.Footer>
		{#if user}
			<UserMenu {user} supabase={data.supabase} />
		{/if}
	</Sidebar.Footer>
</Sidebar.Root>
