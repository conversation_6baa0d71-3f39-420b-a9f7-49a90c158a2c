<script lang="ts" module>
	// Preset color schemes that can be reused
	export const SHINE_COLORS = {
		// Soft indigo/purple gradient (default)
		DEFAULT: ['rgba(99, 102, 241, 0.7)', 'rgba(139, 92, 246, 0.5)'],
		// Stronger indigo/purple for emphasis
		EMPHASIS: ['rgba(99, 102, 241, 0.6)', 'rgba(139, 92, 246, 0.3)'],
		// Very subtle white for dark backgrounds
		SUBTLE: ['rgba(255, 255, 255, 0.3)', 'rgba(255, 255, 255, 0.1)'],
		// Golden shimmer for premium features
		GOLD: ['rgba(234, 179, 8, 0.4)', 'rgba(234, 179, 8, 0.1)'],
		// Blue accent for information elements
		BLUE: ['rgba(59, 130, 246, 0.4)', 'rgba(59, 130, 246, 0.2)'],
		// Green for success/confirmation
		SUCCESS: ['rgba(34, 197, 94, 0.4)', 'rgba(34, 197, 94, 0.2)']
	};
</script>

<script lang="ts">
	import { onMount, onDestroy } from 'svelte';

	interface Props {
		/**
		 * Array of colors for the shine effect
		 */
		shineColor?: string[];
		/**
		 * Duration of the animation in seconds
		 */
		duration?: number;
		/**
		 * Border width, default is thin
		 */
		borderWidth?: number;
	}

	let { shineColor = SHINE_COLORS.DEFAULT, duration = 10, borderWidth = 1 }: Props = $props();

	// Styling variables
	const colors = Array.isArray(shineColor) ? shineColor.join(',') : shineColor;
	const backgroundGradient = `radial-gradient(120% 120% at 0% 0%, ${colors}, transparent, transparent)`;
	const maskGradient = `linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)`;

	// JS-based animation as a fallback
	let animatedElement: HTMLDivElement;
	let animationFrame: number;
	let startTime: number;

	function animate(timestamp: number) {
		if (!startTime) startTime = timestamp;
		const progress = ((timestamp - startTime) % (duration * 1000)) / (duration * 1000);

		// Calculate positions for a circular motion
		const x =
			progress < 0.25
				? progress * 4 * 100
				: progress < 0.5
					? 100
					: progress < 0.75
						? 100 - (progress - 0.5) * 4 * 100
						: 0;

		const y =
			progress < 0.25
				? 0
				: progress < 0.5
					? (progress - 0.25) * 4 * 100
					: progress < 0.75
						? 100
						: 100 - (progress - 0.75) * 4 * 100;

		if (animatedElement) {
			animatedElement.style.backgroundPosition = `${x}% ${y}%`;
		}

		animationFrame = requestAnimationFrame(animate);
	}

	onMount(() => {
		// Try JS animation as a backup
		if (animatedElement) {
			animationFrame = requestAnimationFrame(animate);
		}
	});

	onDestroy(() => {
		if (animationFrame) {
			cancelAnimationFrame(animationFrame);
		}
	});
</script>

<div
	bind:this={animatedElement}
	class="shine-border pointer-events-none absolute inset-0 size-full rounded-[inherit]"
	style="
		--border-width: {borderWidth}px;
		--duration: {duration}s;
		background-image: {backgroundGradient};
		background-size: 300% 300%;
		mask: {maskGradient};
		-webkit-mask-composite: xor;
		mask-composite: exclude;
		padding: var(--border-width);
	"
></div>

<style>
	.shine-border {
		animation: shine var(--duration) ease-in-out infinite;
		-webkit-animation: shine var(--duration) ease-in-out infinite;
		animation-play-state: running !important;
	}

	@keyframes shine {
		0% {
			background-position: 0% 0%;
		}
		25% {
			background-position: 100% 0%;
		}
		50% {
			background-position: 100% 100%;
		}
		75% {
			background-position: 0% 100%;
		}
		100% {
			background-position: 0% 0%;
		}
	}

	@-webkit-keyframes shine {
		0% {
			background-position: 0% 0%;
		}
		25% {
			background-position: 100% 0%;
		}
		50% {
			background-position: 100% 100%;
		}
		75% {
			background-position: 0% 100%;
		}
		100% {
			background-position: 0% 0%;
		}
	}
</style>
