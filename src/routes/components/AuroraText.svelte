<script lang="ts">
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';

	interface Props {
		/**
		 * The element type to render as
		 */
		as?: string;
		/**
		 * Custom class names
		 */
		class?: string;
		/**
		 * Content to render
		 */
		children?: () => any;
	}

	let { as = 'span', class: className = '', children = () => '' }: Props = $props();

	// Create a reference to the element
	let element: HTMLElement;
	let mounted = $state(false);

	// CSS custom properties for silver/gray metallic aurora colors
	const colors = {
		color1: '0 0% 95%', // Bright silver
		color2: '210 30% 80%', // Silver-blue
		color3: '220 10% 70%', // Cool gray
		color4: '240 15% 60%' // Steel gray with slight blue undertone
	};

	onMount(() => {
		mounted = true;
	});
</script>

<svelte:element
	this={as}
	bind:this={element}
	class="relative inline-flex overflow-visible bg-linear-to-r from-gray-500 to-gray-600 bg-clip-text text-transparent dark:from-gray-300 dark:to-gray-400 {className}"
>
	{@render children()}

	{#if mounted}
		<span
			class="pointer-events-none absolute inset-0 mix-blend-overlay dark:mix-blend-overlay"
			transition:fade={{ duration: 1000 }}
		>
			<span
				class="animate-aurora-1 pointer-events-none absolute -top-1/2 h-[30vw] w-[30vw]
				bg-[hsl(var(--aurora-color-1,0_0%_95%))] mix-blend-overlay blur-[1rem]"
				style="--aurora-color-1:{colors.color1}"
			></span>
			<span
				class="animate-aurora-2 pointer-events-none absolute right-0 top-0 h-[30vw] w-[30vw]
				bg-[hsl(var(--aurora-color-2,210_30%_80%))] mix-blend-overlay blur-[1rem]"
				style="--aurora-color-2:{colors.color2}"
			></span>
			<span
				class="animate-aurora-3 pointer-events-none absolute bottom-0 left-0 h-[30vw] w-[30vw]
				bg-[hsl(var(--aurora-color-3,220_10%_70%))] mix-blend-overlay blur-[1rem]"
				style="--aurora-color-3:{colors.color3}"
			></span>
			<span
				class="animate-aurora-4 pointer-events-none absolute -bottom-1/2 right-0 h-[30vw] w-[30vw]
				bg-[hsl(var(--aurora-color-4,240_15%_60%))] mix-blend-overlay blur-[1rem]"
				style="--aurora-color-4:{colors.color4}"
			></span>
		</span>
	{/if}
</svelte:element>

<style>
	@keyframes aurora-border {
		0%,
		100% {
			border-radius: 40% 60% 70% 30% / 40% 40% 60% 50%;
		}
		25% {
			border-radius: 40% 60% 70% 30% / 60% 30% 70% 40%;
		}
		50% {
			border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
		}
		75% {
			border-radius: 60% 40% 30% 70% / 60% 40% 50% 40%;
		}
	}

	@keyframes aurora-1 {
		0%,
		100% {
			transform: translate(-70%, -30%) rotate(0deg);
			opacity: 0.7;
		}
		50% {
			transform: translate(-30%, -50%) rotate(180deg);
			opacity: 0.5;
		}
	}

	@keyframes aurora-2 {
		0%,
		100% {
			transform: translate(10%, -30%) rotate(0deg);
			opacity: 0.7;
		}
		50% {
			transform: translate(50%, 10%) rotate(180deg);
			opacity: 0.5;
		}
	}

	@keyframes aurora-3 {
		0%,
		100% {
			transform: translate(-20%, 0%) rotate(0deg);
			opacity: 0.7;
		}
		50% {
			transform: translate(20%, 40%) rotate(180deg);
			opacity: 0.5;
		}
	}

	@keyframes aurora-4 {
		0%,
		100% {
			transform: translate(40%, 20%) rotate(0deg);
			opacity: 0.7;
		}
		50% {
			transform: translate(20%, 40%) rotate(180deg);
			opacity: 0.5;
		}
	}

	/* Animation classes */
	.animate-aurora-1 {
		animation:
			aurora-border 6s ease-in-out infinite,
			aurora-1 12s ease-in-out infinite alternate;
	}
	.animate-aurora-2 {
		animation:
			aurora-border 6s ease-in-out infinite,
			aurora-2 12s ease-in-out infinite alternate;
	}
	.animate-aurora-3 {
		animation:
			aurora-border 6s ease-in-out infinite,
			aurora-3 12s ease-in-out infinite alternate;
	}
	.animate-aurora-4 {
		animation:
			aurora-border 6s ease-in-out infinite,
			aurora-4 12s ease-in-out infinite alternate;
	}
</style>
