<script lang="ts">
	import {
		Bot,
		CalendarDays,
		Settings,
		ShoppingCart,
		MapPin,
		Package,
		Calendar,
		Users,
		MoreHorizontal,
		User as UserIcon,
		LogOut,
		Sun,
		Moon,
		Monitor,
		LayoutDashboard,
		LogIn
	} from '@lucide/svelte';
	import type { LayoutData } from '../$types';
	import * as Select from '$lib/components/ui/select';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import * as Avatar from '$lib/components/ui/avatar';
	import { getLocalizedText } from '$lib/utils/localization';
	import { getLocale } from '$lib/paraglide/runtime';
	import type { BrandFeatureGroup, IconType, GroupNavigationItem } from '$lib/types/navigation';
	import { ICON_MAP, ALWAYS_AVAILABLE_ITEMS } from '$lib/utils/navigation';
	import { goto } from '$app/navigation';
	import { setMode, resetMode } from 'mode-watcher';

	let { data, pathname, profileFeatures, groups } = $props<{
		data: LayoutData;
		pathname: string;
		profileFeatures: BrandFeatureGroup[];
		groups: GroupNavigationItem[];
	}>();

	let selectedBrandId = $state<string | undefined>(
		profileFeatures.length > 0 ? profileFeatures[0].brand_id : undefined
	);

	let currentBrandFeatures = $derived(
		profileFeatures.find((g: BrandFeatureGroup) => g.brand_id === selectedBrandId)?.features || []
	);

	let user = $derived(data.user);
	let userInitials = $derived(user?.email ? user.email.substring(0, 2).toUpperCase() : '??');

	async function signOut() {
		const { error } = await data.supabase.auth.signOut();
		if (error) {
			console.error('[UserMenu] Error signing out:', error.message);
			return;
		}
	}

	// Public items that are available to all users
	const PUBLIC_ITEMS = [
		{
			title: 'Upcoming',
			url: '/event',
			icon: 'CalendarDays' as IconType,
			features: []
		}
	] as const;

	// Public navigation items visible to all users
	const publicItems = [{ title: 'Events', url: '/event', icon: 'Calendar' as IconType }];

	// Determine if the current path matches a navigation item
	function isActiveUrl(url: string): boolean {
		if (pathname === url) return true;
		if (url !== '/' && pathname.startsWith(url + '/')) return true;
		return false;
	}

	function navigateToSignIn() {
		goto('/auth/sign-in');
	}
</script>

<nav
	class="fixed bottom-0 left-0 right-0 z-11 border-t border-border bg-background shadow-lg md:hidden"
>
	<div class="safe-bottom flex justify-around">
		{#if user}
			<!-- Authenticated user navigation -->
			{#each ALWAYS_AVAILABLE_ITEMS as item}
				{@const Icon = ICON_MAP[item.icon as IconType]}
				<a
					href={item.url}
					class="flex flex-col items-center p-2.5 {pathname?.startsWith(item.url)
						? 'text-primary'
						: 'text-muted-foreground'}"
				>
					<Icon class="size-5 stroke-[1.5]" />
					<span class="mt-0.5 text-[10px]">{item.title}</span>
				</a>
			{/each}

			<DropdownMenu.Root>
				<DropdownMenu.Trigger class="flex flex-col items-center p-2.5 text-muted-foreground">
					<MoreHorizontal class="size-5 stroke-[1.5]" />
					<span class="mt-0.5 text-[10px]">More</span>
				</DropdownMenu.Trigger>
				<DropdownMenu.Content class="w-56" side="top">
					<DropdownMenu.Group>
						<DropdownMenu.Item onclick={() => goto('/private/settings')}>
							<UserIcon class="mr-2 h-4 w-4" />
							<span>Profile</span>
						</DropdownMenu.Item>
						<DropdownMenu.Label>Theme</DropdownMenu.Label>
						<DropdownMenu.Item onclick={() => setMode('light')}>
							<Sun class="mr-2 h-4 w-4" />
							<span>Light</span>
						</DropdownMenu.Item>
						<DropdownMenu.Item onclick={() => setMode('dark')}>
							<Moon class="mr-2 h-4 w-4" />
							<span>Dark</span>
						</DropdownMenu.Item>
						<DropdownMenu.Item onclick={() => resetMode()}>
							<Monitor class="mr-2 h-4 w-4" />
							<span>System</span>
						</DropdownMenu.Item>
						<DropdownMenu.Separator />
						<DropdownMenu.Item onclick={signOut}>
							<LogOut class="mr-2 h-4 w-4" />
							<span>Sign out</span>
						</DropdownMenu.Item>
					</DropdownMenu.Group>

					{#each profileFeatures as group (group.brand_id)}
						<DropdownMenu.Separator />
						<DropdownMenu.Label
							>{getLocalizedText(group.brand_name, getLocale())}</DropdownMenu.Label
						>
						{#each group.features as item (`${group.brand_id}-${item.title}`)}
							{@const Icon = ICON_MAP[item.icon as IconType]}
							<DropdownMenu.Item
								onclick={() => goto(item.url)}
								class={pathname?.startsWith(item.url) ? 'bg-muted' : ''}
							>
								<Icon class="mr-2 h-4 w-4" />
								<span>{item.title}</span>
							</DropdownMenu.Item>
						{/each}
					{/each}
				</DropdownMenu.Content>
			</DropdownMenu.Root>
		{:else}
			<!-- Non-authenticated user navigation -->
			{#each PUBLIC_ITEMS as item}
				{@const Icon = ICON_MAP[item.icon as IconType]}
				<a
					href={item.url}
					class="flex flex-col items-center p-2.5 {pathname?.startsWith(item.url)
						? 'text-primary'
						: 'text-muted-foreground'}"
				>
					<Icon class="size-5 stroke-[1.5]" />
					<span class="mt-0.5 text-[10px]">{item.title}</span>
				</a>
			{/each}

			<!-- Sign in button for non-authenticated users -->
			<button
				class="flex flex-col items-center p-2.5 {pathname?.startsWith('/auth')
					? 'text-primary'
					: 'text-muted-foreground'} transition-colors hover:text-primary"
				onclick={navigateToSignIn}
			>
				<LogIn class="size-5 stroke-[1.5]" />
				<span class="mt-0.5 text-[10px]">Sign In</span>
			</button>

			<!-- More menu for non-authenticated users -->
			<DropdownMenu.Root>
				<DropdownMenu.Trigger class="flex flex-col items-center p-2.5 text-muted-foreground">
					<MoreHorizontal class="size-5 stroke-[1.5]" />
					<span class="mt-0.5 text-[10px]">More</span>
				</DropdownMenu.Trigger>
				<DropdownMenu.Content class="w-56" side="top">
					<DropdownMenu.Group>
						<DropdownMenu.Label>Theme</DropdownMenu.Label>
						<DropdownMenu.Item onclick={() => setMode('light')}>
							<Sun class="mr-2 h-4 w-4" />
							<span>Light</span>
						</DropdownMenu.Item>
						<DropdownMenu.Item onclick={() => setMode('dark')}>
							<Moon class="mr-2 h-4 w-4" />
							<span>Dark</span>
						</DropdownMenu.Item>
						<DropdownMenu.Item onclick={() => resetMode()}>
							<Monitor class="mr-2 h-4 w-4" />
							<span>System</span>
						</DropdownMenu.Item>
					</DropdownMenu.Group>
				</DropdownMenu.Content>
			</DropdownMenu.Root>
		{/if}
	</div>
</nav>
