<script lang="ts">
	import { browser } from '$app/environment';
	import { onMount, onDestroy } from 'svelte';
	import { cn } from '$lib/utils';

	interface Props {
		/**
		 * Additional CSS classes for the particles container
		 */
		className?: string;
		/**
		 * Number of particles to display
		 */
		quantity?: number;
		/**
		 * How static the particles are (higher = more static)
		 */
		staticity?: number;
		/**
		 * Ease factor for particle movement (higher = smoother)
		 */
		ease?: number;
		/**
		 * Size of particles
		 */
		size?: number;
		/**
		 * Force refresh the particles
		 */
		refresh?: boolean;
		/**
		 * Color of particles in hex format
		 */
		color?: string;
		/**
		 * Horizontal velocity
		 */
		vx?: number;
		/**
		 * Vertical velocity
		 */
		vy?: number;
		/**
		 * Vertical bias for particles (0-1), lower values concentrate particles more at the top
		 */
		verticalBias?: number;
	}

	let {
		className = '',
		quantity = 100,
		staticity = 50,
		ease = 50,
		size = 0.4,
		refresh = false,
		color = '#ffffff',
		vx = 0,
		vy = 0,
		verticalBias = 0.5
	}: Props = $props();

	// Mouse position state
	let mousePosition = $state({ x: 0, y: 0 });

	// References
	let canvasElement: HTMLCanvasElement;
	let canvasContainer: HTMLDivElement;
	let context: CanvasRenderingContext2D | null = null;
	let mouse = $state({ x: 0, y: 0 });
	let canvasSize = $state({ w: 0, h: 0 });
	let circles: Circle[] = $state([]);
	let rafID: number | null = null;
	let resizeTimeout: ReturnType<typeof setTimeout> | null = null;

	// Device pixel ratio
	const dpr = browser ? window.devicePixelRatio : 1;

	// Circle type definition
	type Circle = {
		x: number;
		y: number;
		translateX: number;
		translateY: number;
		size: number;
		alpha: number;
		targetAlpha: number;
		dx: number;
		dy: number;
		magnetism: number;
	};

	// Convert hex color to RGB
	function hexToRgb(hex: string): number[] {
		hex = hex.replace('#', '');

		if (hex.length === 3) {
			hex = hex
				.split('')
				.map((char) => char + char)
				.join('');
		}

		const hexInt = parseInt(hex, 16);
		const red = (hexInt >> 16) & 255;
		const green = (hexInt >> 8) & 255;
		const blue = hexInt & 255;
		return [red, green, blue];
	}

	// Mouse position tracking
	function trackMousePosition() {
		if (browser) {
			const handleMouseMove = (event: MouseEvent) => {
				mousePosition = { x: event.clientX, y: event.clientY };
			};

			window.addEventListener('mousemove', handleMouseMove);

			return () => {
				window.removeEventListener('mousemove', handleMouseMove);
			};
		}
		return () => {};
	}

	// Update mouse position relative to canvas
	function onMouseMove() {
		if (canvasElement) {
			const rect = canvasElement.getBoundingClientRect();
			const { w, h } = canvasSize;
			const x = mousePosition.x - rect.left - w / 2;
			const y = mousePosition.y - rect.top - h / 2;
			const inside = x < w / 2 && x > -w / 2 && y < h / 2 && y > -h / 2;
			if (inside) {
				mouse = { x, y };
			}
		}
	}

	// Create circle parameters with vertical bias
	function circleParams(): Circle {
		const x = Math.floor(Math.random() * canvasSize.w);

		// Apply vertical bias - lower values of verticalBias mean more particles at the top
		let y;
		if (Math.random() < 0.7) {
			// 70% of particles follow the bias
			// Generate y with exponential bias toward top or according to verticalBias
			const biasedRandom = Math.pow(Math.random(), 1 / verticalBias);
			y = Math.floor(biasedRandom * canvasSize.h * 0.7); // Limit to top 70% of canvas
		} else {
			// Rest are randomly distributed
			y = Math.floor(Math.random() * canvasSize.h);
		}

		const translateX = 0;
		const translateY = 0;
		const pSize = Math.floor(Math.random() * 2) + size;
		const alpha = 0;
		const targetAlpha = parseFloat((Math.random() * 0.6 + 0.1).toFixed(1));
		const dx = (Math.random() - 0.5) * 0.1;
		const dy = (Math.random() - 0.5) * 0.1;
		const magnetism = 0.1 + Math.random() * 4;
		return {
			x,
			y,
			translateX,
			translateY,
			size: pSize,
			alpha,
			targetAlpha,
			dx,
			dy,
			magnetism
		};
	}

	// Resize canvas
	function resizeCanvas() {
		if (canvasContainer && canvasElement && context) {
			canvasSize = {
				w: canvasContainer.offsetWidth,
				h: canvasContainer.offsetHeight
			};

			canvasElement.width = canvasSize.w * dpr;
			canvasElement.height = canvasSize.h * dpr;
			canvasElement.style.width = `${canvasSize.w}px`;
			canvasElement.style.height = `${canvasSize.h}px`;
			context.scale(dpr, dpr);

			// Clear existing particles and create new ones with exact quantity
			circles = [];
			for (let i = 0; i < quantity; i++) {
				const circle = circleParams();
				drawCircle(circle);
			}
		}
	}

	// Draw circle
	function drawCircle(circle: Circle, update = false) {
		if (context) {
			const { x, y, translateX, translateY, size, alpha } = circle;
			const rgb = hexToRgb(color);

			context.translate(translateX, translateY);
			context.beginPath();
			context.arc(x, y, size, 0, 2 * Math.PI);
			context.fillStyle = `rgba(${rgb.join(', ')}, ${alpha})`;
			context.fill();
			context.setTransform(dpr, 0, 0, dpr, 0, 0);

			if (!update) {
				circles = [...circles, circle];
			}
		}
	}

	// Clear canvas context
	function clearContext() {
		if (context) {
			context.clearRect(0, 0, canvasSize.w, canvasSize.h);
		}
	}

	// Draw all particles
	function drawParticles() {
		clearContext();
		for (let i = 0; i < quantity; i++) {
			const circle = circleParams();
			drawCircle(circle);
		}
	}

	// Remap a value from one range to another
	function remapValue(
		value: number,
		start1: number,
		end1: number,
		start2: number,
		end2: number
	): number {
		const remapped = ((value - start1) * (end2 - start2)) / (end1 - start1) + start2;
		return remapped > 0 ? remapped : 0;
	}

	// Initialize canvas
	function initCanvas() {
		resizeCanvas();
		drawParticles();
	}

	// Animation loop
	function animate() {
		clearContext();

		circles = circles.filter((circle: Circle, i: number) => {
			// Handle the alpha value
			const edge = [
				circle.x + circle.translateX - circle.size, // distance from left edge
				canvasSize.w - circle.x - circle.translateX - circle.size, // distance from right edge
				circle.y + circle.translateY - circle.size, // distance from top edge
				canvasSize.h - circle.y - circle.translateY - circle.size // distance from bottom edge
			];
			const closestEdge = edge.reduce((a, b) => Math.min(a, b));
			const remapClosestEdge = parseFloat(remapValue(closestEdge, 0, 20, 0, 1).toFixed(2));

			if (remapClosestEdge > 1) {
				circle.alpha += 0.02;
				if (circle.alpha > circle.targetAlpha) {
					circle.alpha = circle.targetAlpha;
				}
			} else {
				circle.alpha = circle.targetAlpha * remapClosestEdge;
			}

			circle.x += circle.dx + vx;
			circle.y += circle.dy + vy;
			circle.translateX += (mouse.x / (staticity / circle.magnetism) - circle.translateX) / ease;
			circle.translateY += (mouse.y / (staticity / circle.magnetism) - circle.translateY) / ease;

			drawCircle(circle, true);

			// Check if circle is out of canvas
			if (
				circle.x < -circle.size ||
				circle.x > canvasSize.w + circle.size ||
				circle.y < -circle.size ||
				circle.y > canvasSize.h + circle.size
			) {
				// Create a new circle to replace this one
				const newCircle = circleParams();
				drawCircle(newCircle);
				// Return false to remove this circle from the array
				return false;
			}

			return true;
		});

		rafID = window.requestAnimationFrame(animate);
	}

	// Watch for mouse position changes
	$effect(() => {
		if (mousePosition.x || mousePosition.y) {
			onMouseMove();
		}
	});

	// Handle resize events
	function handleResize() {
		if (resizeTimeout) {
			clearTimeout(resizeTimeout);
		}
		resizeTimeout = setTimeout(() => {
			initCanvas();
		}, 200);
	}

	// Setup and cleanup
	onMount(() => {
		if (browser) {
			if (canvasElement) {
				context = canvasElement.getContext('2d');
			}

			// Initialize canvas
			initCanvas();

			// Start animation
			animate();

			// Track mouse position
			const cleanupMouseTracking = trackMousePosition();

			// Handle window resize
			window.addEventListener('resize', handleResize);

			return () => {
				if (rafID != null) {
					window.cancelAnimationFrame(rafID);
				}
				if (resizeTimeout) {
					clearTimeout(resizeTimeout);
				}
				window.removeEventListener('resize', handleResize);
				cleanupMouseTracking();
			};
		}
	});

	// Watch for refresh prop changes
	$effect(() => {
		if (refresh && browser && context) {
			initCanvas();
		}
	});

	// Watch for verticalBias or other property changes
	$effect(() => {
		if (browser && context && (verticalBias !== 0.5 || refresh)) {
			initCanvas();
		}
	});
</script>

<div class={cn('pointer-events-none', className)} bind:this={canvasContainer} aria-hidden="true">
	<canvas bind:this={canvasElement} class="size-full" />
</div>
