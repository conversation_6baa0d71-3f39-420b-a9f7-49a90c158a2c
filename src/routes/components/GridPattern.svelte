<script lang="ts">
	import { generateUUID } from '$lib/utils';
	import { cn } from '$lib/utils';

	interface Props {
		/**
		 * Width of each grid cell
		 */
		width?: number;
		/**
		 * Height of each grid cell
		 */
		height?: number;
		/**
		 * X offset of the pattern
		 */
		x?: number;
		/**
		 * Y offset of the pattern
		 */
		y?: number;
		/**
		 * Stroke dash array for grid lines
		 */
		strokeDasharray?: string;
		/**
		 * Specific squares to highlight in [x, y] format
		 */
		squares?: Array<[number, number]>;
		/**
		 * Additional CSS classes for the SVG
		 */
		className?: string;
	}

	let {
		width = 40,
		height = 40,
		x = -1,
		y = -1,
		strokeDasharray = '0',
		squares = undefined,
		className = ''
	}: Props = $props();

	// Generate a unique ID for the pattern to avoid conflicts
	const id = generateUUID();
</script>

<svg
	aria-hidden="true"
	class={cn(
		'pointer-events-none absolute inset-0 h-full w-full fill-gray-400/30 stroke-gray-400/30',
		className
	)}
>
	<defs>
		<pattern {id} {width} {height} patternUnits="userSpaceOnUse" {x} {y}>
			<path d={`M.5 ${height}V.5H${width}`} fill="none" stroke-dasharray={strokeDasharray} />
		</pattern>
	</defs>
	<rect width="100%" height="100%" stroke-width="0" fill={`url(#${id})`} />
	{#if squares}
		<svg {x} {y} class="overflow-visible">
			{#each squares as [squareX, squareY] (squareX + '-' + squareY)}
				<rect
					stroke-width="0"
					width={width - 1}
					height={height - 1}
					x={squareX * width + 1}
					y={squareY * height + 1}
				/>
			{/each}
		</svg>
	{/if}
</svg>
