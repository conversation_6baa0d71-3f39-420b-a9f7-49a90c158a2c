<script lang="ts">
	import { LogOut, User as User<PERSON><PERSON>, ChevronUp, Sun, Moon, Monitor, Setting<PERSON> } from '@lucide/svelte';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import * as Avatar from '$lib/components/ui/avatar/index.js';
	import { goto } from '$app/navigation';
	import { setMode, resetMode } from 'mode-watcher';
	import type { SupabaseClient, User } from '@supabase/supabase-js';

	let { user, supabase } = $props();

	let userInitials = $derived(user?.email ? user.email.substring(0, 2).toUpperCase() : '??');

	async function signOut() {
		console.log('[UserMenu] Starting sign out process');
		const { error } = await supabase.auth.signOut();
		if (error) {
			console.error('[UserMenu] Error signing out:', error.message);
			return;
		}
		console.log('[UserMenu] Sign out successful');
	}
</script>

{#if user}
	<div class="rounded-lg bg-muted">
		<DropdownMenu.Root>
			<DropdownMenu.Trigger class="w-full">
				<div class="flex items-center gap-2 p-2">
					<Avatar.Root class="shrink-0">
						<Avatar.Image src={user?.user_metadata?.avatar_url} alt={user?.email || ''} />
						<Avatar.Fallback class="bg-primary text-primary-foreground">
							{userInitials}
						</Avatar.Fallback>
					</Avatar.Root>
					<div class="flex min-w-0 flex-col text-left">
						<span class="truncate text-sm font-medium leading-none">
							{user?.user_metadata?.full_name || user?.email?.split('@')[0]}
						</span>
						{#if user?.email}
							<span class="mt-1 truncate text-xs leading-none text-muted-foreground">
								{user.email}
							</span>
						{/if}
					</div>
					<ChevronUp class="ml-auto h-4 w-4" />
				</div>
			</DropdownMenu.Trigger>
			<DropdownMenu.Content class="w-56" side="top">
				<DropdownMenu.Group>
					<DropdownMenu.Item onclick={() => goto('/private/settings')}>
						<UserIcon class="mr-2 h-4 w-4" />
						<span>Profile</span>
					</DropdownMenu.Item>
					<!-- <DropdownMenu.Item onclick={() => goto('/private/settings')}>
						<Settings class="mr-2 h-4 w-4" />
						<span>Settings</span>
					</DropdownMenu.Item>
					<DropdownMenu.Separator /> -->
					<DropdownMenu.Label>Theme</DropdownMenu.Label>
					<DropdownMenu.Item onclick={() => setMode('light')}>
						<Sun class="mr-2 h-4 w-4" />
						<span>Light</span>
					</DropdownMenu.Item>
					<DropdownMenu.Item onclick={() => setMode('dark')}>
						<Moon class="mr-2 h-4 w-4" />
						<span>Dark</span>
					</DropdownMenu.Item>
					<DropdownMenu.Item onclick={() => resetMode()}>
						<Monitor class="mr-2 h-4 w-4" />
						<span>System</span>
					</DropdownMenu.Item>
					<DropdownMenu.Separator />
					<DropdownMenu.Item onclick={signOut}>
						<LogOut class="mr-2 h-4 w-4" />
						<span>Sign out</span>
					</DropdownMenu.Item>
				</DropdownMenu.Group>
			</DropdownMenu.Content>
		</DropdownMenu.Root>
	</div>
{/if}
