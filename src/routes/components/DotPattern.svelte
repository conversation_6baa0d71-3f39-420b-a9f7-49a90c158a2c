<script lang="ts">
	import { browser } from '$app/environment';
	import { onMount, onDestroy } from 'svelte';
	import { generateUUID } from '$lib/utils';

	/**
	 * DotPattern Component
	 *
	 * A Svelte component that creates an animated or static dot pattern background using SVG.
	 * The pattern automatically adjusts to fill its container and can optionally display glowing dots.
	 */
	interface Props {
		/**
		 * The horizontal spacing between dots
		 */
		width?: number;
		/**
		 * The vertical spacing between dots
		 */
		height?: number;
		/**
		 * The x-offset of the entire pattern
		 */
		x?: number;
		/**
		 * The y-offset of the entire pattern
		 */
		y?: number;
		/**
		 * The x-offset of individual dots
		 */
		cx?: number;
		/**
		 * The y-offset of individual dots
		 */
		cy?: number;
		/**
		 * The radius of each dot
		 */
		cr?: number;
		/**
		 * Additional CSS classes to apply to the SVG container
		 */
		className?: string;
		/**
		 * Whether dots should have a glowing animation effect
		 */
		glow?: boolean;
		/**
		 * Maximum number of dots to render (for performance)
		 */
		maxDots?: number;
	}

	let {
		width = 16,
		height = 16,
		x = 0,
		y = 0,
		cx = 1,
		cy = 1,
		cr = 1,
		className = '',
		glow = false,
		maxDots = 300 // Limit dots for performance
	}: Props = $props();

	// Generate a unique ID for the gradient
	const gradientId = generateUUID();

	// Container ref and dimensions
	let containerRef: SVGSVGElement;
	let dimensions = $state({ width: 0, height: 0 });

	// Store for dots
	let dots = $state<{ x: number; y: number; delay: number; duration: number }[]>([]);

	// Update dimensions and recalculate dots
	function updateDimensions() {
		if (containerRef && browser) {
			const { width: rectWidth, height: rectHeight } = containerRef.getBoundingClientRect();
			dimensions = { width: rectWidth || 1000, height: rectHeight || 1000 };

			console.log('Container dimensions:', dimensions);

			// Calculate total number of columns and rows
			const columnsCount = Math.ceil(dimensions.width / width);
			const rowsCount = Math.ceil(dimensions.height / height);

			console.log('Columns:', columnsCount, 'Rows:', rowsCount);

			// Create dots in a grid pattern, exactly matching the original React implementation
			const totalDots = columnsCount * rowsCount;
			const dotLimit = maxDots > 0 ? Math.min(totalDots, maxDots) : totalDots;

			if (totalDots > 0) {
				// For normal usage, generate all dots
				let allDots = [];
				for (let i = 0; i < totalDots; i++) {
					// These calculations match exactly the original React component
					const col = i % columnsCount;
					const row = Math.floor(i / columnsCount);

					allDots.push({
						x: col * width + cx,
						y: row * height + cy,
						delay: Math.random() * 5,
						duration: Math.random() * 3 + 2
					});
				}

				// If we have more dots than maxDots, randomly select dots to display
				if (maxDots > 0 && totalDots > maxDots) {
					const selectedDots = [];
					const indices = new Set<number>();

					// Randomly select maxDots indices
					while (indices.size < dotLimit) {
						indices.add(Math.floor(Math.random() * totalDots));
					}

					// Create dots from selected indices
					for (const index of indices) {
						selectedDots.push(allDots[index]);
					}

					dots = selectedDots;
				} else {
					dots = allDots;
				}

				console.log(`Generated ${dots.length} dots out of ${totalDots} total positions`);
			}
		}
	}

	// Setup resize observer
	let resizeObserver: ResizeObserver;

	onMount(() => {
		if (browser) {
			console.log('DotPattern mounted');

			// Initial update with a small delay to ensure container is ready
			setTimeout(() => {
				updateDimensions();
			}, 100);

			// Setup resize observer
			resizeObserver = new ResizeObserver(() => {
				console.log('Resize observed');
				updateDimensions();
			});

			if (containerRef) {
				resizeObserver.observe(containerRef);
			}

			// Fallback to window resize
			window.addEventListener('resize', updateDimensions);
		}
	});

	onDestroy(() => {
		if (browser) {
			// Cleanup
			if (resizeObserver && containerRef) {
				resizeObserver.unobserve(containerRef);
				resizeObserver.disconnect();
			}
			window.removeEventListener('resize', updateDimensions);
		}
	});
</script>

<svg
	bind:this={containerRef}
	aria-hidden="true"
	class="pointer-events-none absolute inset-0 h-full w-full {className}"
	width="100%"
	height="100%"
	xmlns="http://www.w3.org/2000/svg"
>
	<defs>
		<radialGradient id={gradientId}>
			<stop offset="0%" stop-color="currentColor" stop-opacity="1" />
			<stop offset="100%" stop-color="currentColor" stop-opacity="0" />
		</radialGradient>
	</defs>

	{#each dots as dot, i (i)}
		{#if glow}
			<!-- Animated glowing dots -->
			<circle
				cx={dot.x}
				cy={dot.y}
				r={cr}
				fill="url(#{gradientId})"
				class="glow-dot"
				style="--delay: {dot.delay}s; --duration: {dot.duration}s;"
			/>
		{:else}
			<!-- Static dots -->
			<circle cx={dot.x} cy={dot.y} r={cr} fill="currentColor" />
		{/if}
	{/each}
</svg>

<style>
	@keyframes glow {
		0% {
			opacity: 0.4;
			transform: scale(1);
		}
		50% {
			opacity: 1;
			transform: scale(1.5);
		}
		100% {
			opacity: 0.4;
			transform: scale(1);
		}
	}

	.glow-dot {
		animation: glow var(--duration, 3s) ease-in-out infinite alternate;
		animation-delay: var(--delay, 0s);
	}
</style>
