<script module lang="ts">
	import { goto } from '$app/navigation';

	if (import.meta.env.DEV && typeof window !== 'undefined') {
		let reloadTimeout: ReturnType<typeof setTimeout>;
		window.addEventListener('vite:ws-disconnect', () => {
			reloadTimeout = setTimeout(() => {
				window.location.reload();
			}, 1000);
		});

		window.addEventListener('vite:ws-connect', () => {
			clearTimeout(reloadTimeout);
		});
	}
</script>

<script lang="ts">
	import '../app.css';
	import * as Sidebar from '$lib/components/ui/sidebar';
	import * as Drawer from '$lib/components/ui/drawer';
	import AppSidebar from './components/AppSidebar.svelte';
	import AppTabBar from './components/AppTabBar.svelte';
	import { invalidate } from '$app/navigation';
	import { Toaster } from '$lib/components/ui/sonner';
	import { ModeWatcher } from 'mode-watcher';
	import { page } from '$app/state';
	import { MediaQuery } from 'svelte/reactivity';
	import { RenderScan } from 'svelte-render-scan';
	import { dev } from '$app/environment';

	let { data, children } = $props();
	let { user, supabase } = $derived(data || {});
	let currentPath = $derived(page.url.pathname);
	const isDesktop = new MediaQuery('(min-width: 768px)', true);
	let isDesktopView = $derived(isDesktop.current);
	let showSidebar = $derived(
		currentPath.startsWith('/private/') || currentPath.startsWith('/event')
	);

	$effect(() => {
		if (!supabase) return;

		const {
			data: { subscription }
		} = supabase.auth.onAuthStateChange(async (event, session) => {
			console.debug('[Layout] Auth state changed:', event, session);

			if (event === 'INITIAL_SESSION') {
				console.log('[Layout] Initial session loaded');
			} else if (event === 'SIGNED_IN') {
				console.log('[Layout] User signed in');
			} else if (event === 'SIGNED_OUT') {
				console.log('[Layout] User signed out, redirecting to event page');
				goto('/auth/sign-in', { invalidateAll: true, replaceState: true });
			} else if (event === 'PASSWORD_RECOVERY') {
				console.log('[Layout] Password recovery initiated');
			} else if (event === 'TOKEN_REFRESHED') {
				console.log('[Layout] Token refreshed');
				invalidate('supabase:auth');
			} else if (event === 'USER_UPDATED') {
				console.log('[Layout] User updated');
				invalidate('supabase:auth');
			}
		});

		return () => {
			subscription.unsubscribe();
		};
	});
</script>

<ModeWatcher />
{#if dev}
	<RenderScan />
{/if}
<div data-vaul-drawer-wrapper class="bg-background">
	{#if isDesktopView && showSidebar}
		<Sidebar.Provider>
			{#if data?.profileFeaturesPromise && data?.groupsPromise}
				{#await Promise.all([data.profileFeaturesPromise, data.groupsPromise])}
					<AppSidebar {data} {currentPath} profileFeatures={[]} groups={[]} />
				{:then [profileFeatures, groups]}
					<AppSidebar {data} {currentPath} {profileFeatures} {groups} />
				{:catch error}
					<div class="text-destructive p-4">Error loading data: {error.message}</div>
				{/await}
			{:else}
				<AppSidebar {data} {currentPath} profileFeatures={[]} groups={[]} />
			{/if}
			<Sidebar.Inset>
				<main class="flex-1">
					{@render children()}
				</main>
			</Sidebar.Inset>
		</Sidebar.Provider>
	{:else}
		<main class="flex-1 {showSidebar ? 'pb-20' : ''}">
			{@render children()}
			{#if showSidebar}
				{#if data?.profileFeaturesPromise && data?.groupsPromise}
					{#await Promise.all([data.profileFeaturesPromise, data.groupsPromise])}
						<AppTabBar {data} pathname={currentPath} profileFeatures={[]} groups={[]} />
					{:then [profileFeatures, groups]}
						<AppTabBar {data} pathname={currentPath} {profileFeatures} {groups} />
					{:catch error}
						<div class="text-destructive p-4">Error loading data: {error.message}</div>
					{/await}
				{:else}
					<AppTabBar {data} pathname={currentPath} profileFeatures={[]} groups={[]} />
				{/if}
			{/if}
		</main>
	{/if}
</div>
<Toaster richColors closeButton position="top-right" />
