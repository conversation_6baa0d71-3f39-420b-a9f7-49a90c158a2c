<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';

	const errorMessages = {
		404: 'Not Found',
		500: 'Internal Server Error',
		403: 'Forbidden'
	} as const;

	const errorMessage =
		page.error?.message ||
		errorMessages[page.status as keyof typeof errorMessages] ||
		'Unknown Error';

	async function goBack() {
		const ref = document.referrer;
		await goto(ref.length > 0 ? ref : '/');
	}
	async function goHome() {
		await goto('/', { replaceState: true });
	}
</script>

<div class="flex min-h-[calc(100vh-4rem)] items-center justify-center p-4">
	<div class="max-w-[400px] space-y-1 text-center">
		<h1
			class="flex items-center justify-center gap-6 font-mono text-3xl font-medium tracking-tight"
		>
			<span class="text-foreground/70">{page.status}</span>
			<span class="text-muted-foreground">{errorMessage}</span>
		</h1>
		<div class="space-y-6">
			<p class="text-sm text-muted-foreground">
				If you believe this is a mistake, please try refreshing the page.
			</p>
			<div class="flex justify-center gap-3">
				<Button onclick={goBack} variant="ghost" class="gap-2">← Go Back</Button>
				<Button onclick={goHome} variant="default" class="min-w-[120px]">Return Home</Button>
			</div>
		</div>
	</div>
</div>
