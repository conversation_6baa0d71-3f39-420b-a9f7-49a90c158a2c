// Unified styling system for email templates

// Base font stack
export const fontFamily = 'system-ui, -apple-system, "Segoe UI", Roboto, sans-serif';

// Simplified color palette
export const colors = {
	primary: '#0f766e',
	text: {
		dark: '#1f2937',
		medium: '#4b5563',
		light: '#6b7280',
		lightest: '#9ca3af'
	},
	background: {
		page: '#f9fafb',
		content: '#ffffff',
		code: '#f1f5f9'
	}
};

// Standardized font sizes
export const fontSize = {
	heading: '24px',
	body: '16px',
	small: '14px',
	tiny: '12px'
};

// Standard link style
export const linkStyle = {
	fontFamily,
	color: colors.primary,
	textDecoration: 'underline',
	fontWeight: '500'
};

// Shared button style
export const buttonStyle = {
	backgroundColor: colors.primary,
	borderRadius: '6px',
	color: 'white',
	fontWeight: '600',
	padding: '10px 24px',
	cursor: 'pointer',
	border: 'none',
	fontSize: fontSize.body,
	fontFamily,
	textDecoration: 'none',
	lineHeight: '1.5',
	display: 'inline-block',
	margin: '16px auto',
	textAlign: 'center' as const
};

// Common text styles for reuse
export const textStyles = {
	heading: {
		fontFamily,
		fontSize: fontSize.heading,
		fontWeight: '700',
		marginTop: '0',
		marginBottom: '24px',
		textAlign: 'center' as const,
		color: colors.text.dark
	},
	paragraph: {
		fontFamily,
		fontSize: fontSize.body,
		lineHeight: '24px',
		margin: '16px 0',
		color: colors.text.medium
	},
	centered: {
		textAlign: 'center' as const
	},
	small: {
		fontFamily,
		fontSize: fontSize.small,
		color: colors.text.light,
		marginTop: '16px'
	},
	code: {
		fontFamily: 'monospace',
		fontSize: fontSize.heading,
		color: colors.primary
	}
};

// Common style definitions
export const hrStyle = {
	borderColor: '#e5e7eb',
	margin: '24px 0'
};

export const urlStyle = {
	fontSize: fontSize.small,
	color: colors.primary,
	marginTop: '8px',
	wordBreak: 'break-all',
	fontFamily: 'monospace'
};
