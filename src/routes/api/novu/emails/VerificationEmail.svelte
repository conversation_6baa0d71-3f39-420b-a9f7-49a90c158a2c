<script lang="ts">
	import { Hr, Section, Text, Link } from '@keycloakify/svelte-email';
	import EmailContainer from './EmailContainer.svelte';
	import {
		fontFamily,
		colors,
		fontSize,
		buttonStyle,
		textStyles,
		hrStyle,
		urlStyle,
		linkStyle
	} from './emailStyles';

	type LocalizedText = {
		[key: string]: string;
	};

	interface Props {
		userName: string;
		verificationCode: string;
		verificationUrl: string;
		brand: App.Locals['brand'];
		locale?: string;
		expiryHours?: number;
	}

	let {
		userName,
		verificationCode,
		verificationUrl,
		brand,
		locale = 'en',
		expiryHours = 24
	}: Props = $props();

	// Use reusable styles from container
	const paragraphStyle = textStyles.paragraph;
	const smallTextStyle = textStyles.small;
	const codeStyle = {
		fontFamily: 'monospace',
		fontSize: fontSize.heading,
		fontWeight: 'bold',
		letterSpacing: '0.25em',
		color: colors.primary,
		margin: '0',
		padding: '16px 0',
		textAlign: 'center'
	};

	// Custom URL style with monospace font specifically for displaying URLs
	const customUrlStyle = {
		...urlStyle,
		...linkStyle,
		fontFamily: 'monospace' // Override the font for URL display
	};
</script>

<EmailContainer
	{brand}
	{locale}
	preview_text="Verify your email address"
	heading="Verify Your Email Address"
>
	<Text style={paragraphStyle}>
		Hi {userName},
	</Text>

	<Text style={paragraphStyle}>
		Thank you for signing up. To complete your registration, please verify your email address by
		using the verification code below or clicking the button.
	</Text>

	<Text style={codeStyle}>
		{verificationCode}
	</Text>

	<Text style={paragraphStyle}>Or click the button below to verify your email address:</Text>

	<Text style={textStyles.centered}>
		<Link href={verificationUrl} style={buttonStyle}>Verify Email Address</Link>
	</Text>

	<Hr style={hrStyle} />

	<Text style={smallTextStyle}>
		This verification code will expire in {expiryHours} hours. If you didn't request this email, please
		ignore it.
	</Text>

	<Text style={smallTextStyle}>
		If you're having trouble clicking the button above, copy and paste the URL below into your web
		browser:
	</Text>

	<Text style={customUrlStyle}>
		<Link href={verificationUrl} style={customUrlStyle}>{verificationUrl}</Link>
	</Text>
</EmailContainer>
