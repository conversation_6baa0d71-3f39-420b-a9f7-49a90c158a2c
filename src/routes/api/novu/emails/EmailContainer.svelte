<script lang="ts">
	import {
		Container,
		Head,
		Html,
		Img,
		Link,
		Preview,
		Section,
		Text,
		Hr,
		Heading
	} from '@keycloakify/svelte-email';
	import type { Snippet } from 'svelte';
	import { fontFamily, colors, fontSize, linkStyle, hrStyle, textStyles } from './emailStyles';

	type LocalizedText = {
		[key: string]: string;
	};

	interface Props {
		brand: App.Locals['brand'];
		preview_text: string;
		locale?: string;
		children: Snippet;
		heading?: string;
	}

	let { brand, preview_text, locale = 'en', children, heading }: Props = $props();

	// Helper to get localized text
	function getLocalizedText(textObj: LocalizedText, locale: string): string {
		return textObj[locale] || textObj['en'] || Object.values(textObj)[0] || '';
	}

	// Get branded text
	const brandNameShort = getLocalizedText(brand?.name_short || { en: 'Our Platform' }, locale);
	const brandNameFull = getLocalizedText(brand?.name_full || { en: 'Our Platform' }, locale);
	const portalUrl = brand?.portal_url || 'https://example.com';

	// Get current year for copyright
	const currentYear = new Date().getFullYear();

	// Email styles
	const mainStyle = {
		backgroundColor: colors.background.page,
		fontFamily
	};

	const containerStyle = {
		margin: '0 auto',
		padding: '24px 16px',
		maxWidth: '560px'
	};

	const logoStyle = {
		margin: '0 auto 24px',
		maxHeight: '36px',
		display: 'block',
		textAlign: 'center' as const
	};

	const brandTextStyle = {
		fontFamily,
		fontWeight: '600',
		fontSize: fontSize.body,
		color: colors.text.medium,
		margin: '0 0 24px',
		letterSpacing: '-0.01em',
		textAlign: 'center' as const,
		backgroundColor: colors.background.page,
		padding: '12px 0px',
		width: '100%',
		borderRadius: '8px'
	};

	const headingStyle = {
		...textStyles.heading,
		textAlign: 'left' as const
	};

	const footerTextStyle = {
		fontFamily,
		fontSize: fontSize.small,
		color: colors.text.light,
		lineHeight: '20px',
		textAlign: 'center' as const
	};

	const footerLinkStyle = {
		...linkStyle,
		color: colors.text.medium
	};

	const disclaimerStyle = {
		fontFamily,
		fontSize: fontSize.tiny,
		color: colors.text.lightest,
		lineHeight: '18px',
		marginTop: '16px',
		textAlign: 'center' as const
	};

	const footerLinksContainerStyle = {
		margin: '12px 0 20px',
		textAlign: 'center' as const
	};
</script>

<Html>
	<Head>
		<meta name="color-scheme" content="light" />
		<meta name="supported-color-schemes" content="light" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	</Head>
	<Preview preview={preview_text} />
	<Section style={mainStyle}>
		<Container style={containerStyle}>
			<!-- Header with Brand Logo -->
			{#if brand?.logo_url}
				<Img src={brand.logo_url} alt={brandNameShort} width="96" height="36" style={logoStyle} />
			{:else}
				<Text style={brandTextStyle}>
					{brandNameShort}
				</Text>
			{/if}

			<!-- Main heading if provided -->
			{#if heading}
				<Heading as="h1" style={headingStyle}>{heading}</Heading>
			{/if}

			<!-- Main Content -->
			{@render children()}

			<Hr style={hrStyle} />

			<!-- Footer -->
			<Text style={footerTextStyle}>
				© {currentYear}
				{brandNameFull}. All rights reserved.
			</Text>

			<Text style={footerLinksContainerStyle}>
				<Link href={portalUrl} style={footerLinkStyle}>Visit Portal</Link>
				&nbsp;&nbsp;•&nbsp;&nbsp;
				<Link href={`${portalUrl}/privacy`} style={footerLinkStyle}>Privacy Policy</Link>
				&nbsp;&nbsp;•&nbsp;&nbsp;
				<Link href={`${portalUrl}/terms`} style={footerLinkStyle}>Terms of Service</Link>
			</Text>

			<Text style={disclaimerStyle}>
				This email was sent to you because you have an account with {brandNameShort}. If you didn't
				request this email, you can safely ignore it.
			</Text>
		</Container>
	</Section>
</Html>
