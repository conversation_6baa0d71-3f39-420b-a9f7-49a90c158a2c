<script lang="ts">
	import { Hr, Section, Text, Link } from '@keycloakify/svelte-email';
	import EmailContainer from './EmailContainer.svelte';
	import {
		fontFamily,
		colors,
		fontSize,
		buttonStyle,
		textStyles,
		hrStyle,
		urlStyle,
		linkStyle
	} from './emailStyles';

	type LocalizedText = {
		[key: string]: string;
	};

	interface Props {
		userName: string;
		resetUrl: string;
		brand: App.Locals['brand'];
		locale?: string;
		expiryHours?: number;
	}

	let { userName, resetUrl, brand, locale = 'en', expiryHours = 1 }: Props = $props();

	// Use reusable styles
	const paragraphStyle = textStyles.paragraph;
	const smallTextStyle = textStyles.small;

	// Custom URL style with monospace font specifically for displaying URLs
	const customUrlStyle = {
		...urlStyle,
		...linkStyle,
		fontFamily: 'monospace' // Override the font for URL display
	};
</script>

<EmailContainer
	{brand}
	{locale}
	preview_text="Reset your password"
	heading="Password Reset Request"
>
	<Text style={paragraphStyle}>
		Hi {userName},
	</Text>

	<Text style={paragraphStyle}>
		We received a request to reset your password. If you didn't make this request, you can safely
		ignore this email.
	</Text>

	<Text style={paragraphStyle}>To reset your password, click the button below:</Text>

	<Text style={textStyles.centered}>
		<Link href={resetUrl} style={buttonStyle}>Reset Password</Link>
	</Text>

	<Hr style={hrStyle} />

	<Text style={smallTextStyle}>
		This link will expire in {expiryHours}
		{expiryHours === 1 ? 'hour' : 'hours'}. If you didn't request a password reset, no action is
		required.
	</Text>

	<Text style={smallTextStyle}>
		If you're having trouble clicking the button above, copy and paste the URL below into your web
		browser:
	</Text>

	<Text style={customUrlStyle}>
		<Link href={resetUrl} style={customUrlStyle}>{resetUrl}</Link>
	</Text>

	<Text style={smallTextStyle}>
		If you did not request a password reset, please contact our support team immediately.
	</Text>
</EmailContainer>
