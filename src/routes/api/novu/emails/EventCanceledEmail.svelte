<script lang="ts">
	import { Hr, Section, Text, Link } from '@keycloakify/svelte-email';
	import EmailContainer from './EmailContainer.svelte';
	import {
		fontFamily,
		colors,
		fontSize,
		buttonStyle,
		textStyles,
		hrStyle,
		urlStyle,
		linkStyle
	} from './emailStyles';

	interface Props {
		userName: string;
		localizedEventTitle: string;
		localizedEventSubtitle?: string;
		formattedDateTime: string;
		localizedCancellationReason: string;
		localizedHostMessage?: string;
		localizedRefundInfo: string;
		localizedCompensationInfo?: string;
		supportUrl: string;
		brand: App.Locals['brand'];
		locale: string;
	}

	let {
		userName,
		localizedEventTitle,
		localizedEventSubtitle = '',
		formattedDateTime,
		localizedCancellationReason,
		localizedHostMessage = '',
		localizedRefundInfo,
		localizedCompensationInfo = '',
		supportUrl,
		brand,
		locale = 'en'
	}: Props = $props();

	// Use reusable styles
	const paragraphStyle = textStyles.paragraph;
	const smallTextStyle = textStyles.small;

	// Define event info container style
	const eventInfoStyle = {
		...paragraphStyle,
		backgroundColor: colors.background.code,
		padding: '16px',
		borderRadius: '4px',
		margin: '16px 0'
	};

	// Section titles style
	const sectionTitleStyle = {
		...paragraphStyle,
		fontWeight: '600',
		marginBottom: '8px',
		color: colors.text.dark
	};

	// Support link style
	const supportLinkStyle = {
		...linkStyle
	};
</script>

<EmailContainer
	{brand}
	{locale}
	preview_text="Important: Event Cancellation Notice"
	heading="Event Cancellation Notice"
>
	<Text style={paragraphStyle}>
		Hi {userName},
	</Text>

	<Text style={paragraphStyle}>
		We regret to inform you that the following event has been canceled:
	</Text>

	<Text style={eventInfoStyle}>
		<strong>{localizedEventTitle}</strong><br />
		{#if localizedEventSubtitle}
			{localizedEventSubtitle}<br />
		{/if}
		{formattedDateTime}
	</Text>

	<Text style={sectionTitleStyle}>Reason for Cancellation:</Text>
	<Text style={paragraphStyle}>
		{localizedCancellationReason}
	</Text>

	{#if localizedHostMessage}
		<Text style={sectionTitleStyle}>Message from the Host:</Text>
		<Text style={paragraphStyle}>
			{localizedHostMessage}
		</Text>
	{/if}

	<Hr style={hrStyle} />

	<Text style={sectionTitleStyle}>Refund Information:</Text>
	<Text style={paragraphStyle}>
		{localizedRefundInfo}
	</Text>

	{#if localizedCompensationInfo}
		<Text style={sectionTitleStyle}>Compensation:</Text>
		<Text style={paragraphStyle}>
			{localizedCompensationInfo}
		</Text>
	{/if}

	<Hr style={hrStyle} />

	<Text style={smallTextStyle}>
		We apologize for any inconvenience this cancellation may have caused. If you have any questions,
		please contact our <Link href={supportUrl} style={supportLinkStyle}>support team</Link>.
	</Text>
</EmailContainer>
