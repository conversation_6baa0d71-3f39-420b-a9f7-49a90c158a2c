<script lang="ts">
	import { Section, Text, Link } from '@keycloakify/svelte-email';
	import EmailContainer from './EmailContainer.svelte';
	import { fontFamily, colors, buttonStyle, textStyles, linkStyle } from './emailStyles';

	type LocalizedText = {
		[key: string]: string;
	};

	interface Props {
		userName: string;
		actionUrl: string;
		brand: App.Locals['brand'];
		locale?: string;
	}

	let { userName, actionUrl, brand, locale = 'en' }: Props = $props();

	// Use reusable styles
	const paragraphStyle = {
		...textStyles.paragraph,
		margin: '0 0 24px 0',
		lineHeight: '26px'
	};

	const footerTextStyle = {
		...textStyles.small,
		lineHeight: '22px',
		marginTop: '24px',
		marginBottom: '0'
	};

	// Style for support link
	const supportLinkStyle = {
		...linkStyle
	};
</script>

<EmailContainer
	{brand}
	{locale}
	preview_text="Welcome to Our Platform!"
	heading={`Welcome to Our Platform, ${userName}!`}
>
	<Text style={paragraphStyle}>
		We're excited to have you on board. Our platform helps you manage your tasks efficiently and
		collaborate with your team.
	</Text>

	<Text style={textStyles.centered}>
		<Link href={actionUrl} style={buttonStyle}>Get Started</Link>
	</Text>

	<Text style={footerTextStyle}>
		If you have any questions, feel free to reach out to our <Link
			href={`${brand.portal_url}`}
			style={supportLinkStyle}>support team</Link
		>.
	</Text>
</EmailContainer>
