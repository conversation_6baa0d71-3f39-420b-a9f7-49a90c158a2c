import { serve } from '@novu/framework/sveltekit';
import { Client as NovuFrameworkClient } from '@novu/framework';
import { env } from '$env/dynamic/private';
import { welcomeWorkflow } from './workflows/welcome';
import { verificationWorkflow } from './workflows/verification';
import { passwordResetWorkflow } from './workflows/password-reset';
import { eventCanceledWorkflow } from './workflows/event-canceled';
// Configure Novu with our workflows
export const { GET, POST, OPTIONS } = serve({
	client: new NovuFrameworkClient({
		secretKey: env.NOVU_SECRET_KEY,
		strictAuthentication: false
	}),
	workflows: [welcomeWorkflow, verificationWorkflow, passwordResetWorkflow, eventCanceledWorkflow]
});

// Note: The brand info from locals will be provided within the payload
// when the endpoint is called with the necessary data.
