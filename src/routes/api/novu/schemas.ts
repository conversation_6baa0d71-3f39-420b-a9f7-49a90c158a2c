import { z } from 'zod';

// Define schema for brand with LocalizedText type
export const LocalizedTextSchema = z.record(z.string()).nullable();

// Define the common brand schema that matches App.Locals['brand'] type
export const BrandSchema = z.object({
	id: z.string(),
	logo_url: z.string(),
	name_full: LocalizedTextSchema,
	name_short: LocalizedTextSchema,
	portal_url: z.string(),
	portal_url_dev: z.string()
});

// Common validation schemas
export const CommonValidators = {
	userName: z.string().min(1, 'Username is required'),
	email: z.string().email('Valid email is required'),
	locale: z.string()
};

// Helper function to get localized brand name
export const getBrandName = (
	brand: z.infer<typeof BrandSchema> | null | undefined,
	locale: string,
	type: 'short' | 'full' = 'full'
): string => {
	if (!brand) {
		throw new Error('Brand is required for notifications');
	}

	// Select the appropriate name property based on type
	const nameProperty = type === 'short' ? brand.name_short : brand.name_full;

	if (!nameProperty) {
		throw new Error(`Brand ${type} name is required for notifications`);
	}

	// Get the localized name with fallbacks
	const localizedName =
		nameProperty[locale] || nameProperty['en'] || Object.values(nameProperty)[0];

	if (!localizedName) {
		throw new Error(`No ${type} name available in any language for this brand`);
	}

	return localizedName;
};
