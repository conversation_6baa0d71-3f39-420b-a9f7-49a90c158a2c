import { workflow } from '@novu/framework';
import { render } from '@keycloakify/svelte-email';
import { z } from 'zod';
import { format, parseISO } from 'date-fns';
import type { Locale } from 'date-fns';
import { enUS, ja, ko, zhCN } from 'date-fns/locale';
import EventCanceledEmail from '../emails/EventCanceledEmail.svelte';
import { BrandSchema, CommonValidators, getBrandName } from '../schemas';
import { getLocalizedText, type LocaleKey, type LocalizedText } from '$lib/utils/localization';
import type { Json } from '$lib/supabase/database.types';

// Helper to extract language code from locale
const getLanguageCode = (locale: string | undefined): LocaleKey => {
	if (!locale) return 'en';
	// Extract first two characters which represent the language code
	const langCode = locale.substring(0, 2).toLowerCase();

	// Make sure the language code is a valid LocaleKey
	if (['en', 'ja', 'ko', 'zh'].includes(langCode)) {
		return langCode as LocaleKey;
	}
	return 'en';
};

// Get date-fns locale based on language code
const getDateLocale = (locale: LocaleKey): Locale => {
	switch (locale) {
		case 'ja':
			return ja;
		case 'ko':
			return ko;
		case 'zh':
			return zhCN;
		default:
			return enUS;
	}
};

// Format a timestamp with day of week, date, time, and timezone
const formatDateTime = (timestamp: string, locale: LocaleKey): string => {
	try {
		const date = parseISO(timestamp);
		const dateLocale = getDateLocale(locale);

		// Format: "Monday, January 1, 2023 at 2:00 PM (GMT+9)"
		return format(date, "EEEE, MMMM d, yyyy 'at' h:mm a (OOOO)", {
			locale: dateLocale
		});
	} catch (error) {
		console.error('Error formatting date:', error);
		return timestamp; // Return original if parsing fails
	}
};

// Default object for schema to prevent null/undefined issues
const DEFAULT_LOCALIZED_TEXT = { en: 'Preview Text' };
const DEFAULT_BRAND = {
	id: 'preview-brand',
	logo_url: 'https://placehold.co/200x100?text=Preview',
	name_full: { en: 'Preview Brand' },
	name_short: { en: 'Preview' },
	portal_url: 'https://example.com',
	portal_url_dev: 'https://example.com'
};

// Create a proper Zod schema for LocalizedText
const LocalizedTextSchema = z
	.object({
		en: z.string()
	})
	.catchall(z.string());

// Create an empty LocalizedText schema that matches type but allows empty object default
const OptionalLocalizedTextSchema = z.object({}).catchall(z.string()).or(LocalizedTextSchema);

export const eventCanceledWorkflow = workflow(
	'event:canceled',
	async ({ step, payload, subscriber }) => {
		try {
			// Send event cancellation email
			await step.email(
				'send-event-canceled-email',
				async (controls) => {
					try {
						// Extract properties from payload - schema guarantees non-null values
						const event_title = payload.event_title;
						const event_subtitle = payload.event_subtitle;
						const event_date_time = payload.event_date_time;
						const cancellation_reason = payload.cancellation_reason;
						const host_message = payload.host_message;
						const refund_info = payload.refund_info;
						const compensation_info = payload.compensation_info;
						const brand = payload.brand;

						// Get subscriber data
						const locale = getLanguageCode(subscriber?.locale);
						const firstName = subscriber?.firstName || '';
						const lastName = subscriber?.lastName || '';
						const subscriberId = subscriber?.subscriberId || 'unknown-user';
						const userName = firstName
							? lastName
								? `${firstName} ${lastName}`
								: firstName
							: subscriberId;

						// Get localized text using getLocalizedText
						let localizedEventTitle,
							localizedEventSubtitle,
							localizedCancellationReason,
							localizedHostMessage,
							localizedRefundInfo,
							localizedCompensationInfo;

						try {
							localizedEventTitle = getLocalizedText(event_title, locale);
							localizedEventSubtitle = getLocalizedText(event_subtitle, locale);
							localizedCancellationReason = getLocalizedText(cancellation_reason, locale);
							localizedHostMessage = getLocalizedText(host_message, locale);
							localizedRefundInfo = getLocalizedText(refund_info, locale);
							localizedCompensationInfo = getLocalizedText(compensation_info, locale);
						} catch (error) {
							console.error('Error getting localized text:', error);
							// Fallbacks for localized text
							localizedEventTitle = 'Event';
							localizedEventSubtitle = '';
							localizedCancellationReason = 'The event has been canceled.';
							localizedHostMessage = '';
							localizedRefundInfo = 'Contact support for refund information.';
							localizedCompensationInfo = '';
						}

						// Format date/time according to locale
						const formattedDateTime = formatDateTime(event_date_time, locale);

						// Create support URL
						const supportUrl = brand.portal_url ? `${brand.portal_url}/support` : '';

						// Render the event canceled email component
						const body = render({
							template: EventCanceledEmail,
							props: {
								userName,
								localizedEventTitle,
								localizedEventSubtitle,
								formattedDateTime,
								localizedCancellationReason,
								localizedHostMessage,
								localizedRefundInfo,
								localizedCompensationInfo,
								supportUrl,
								brand: brand as unknown as App.Locals['brand'],
								locale
							}
						});

						// Get brand names for subject template
						let brandNameFull, brandNameShort;
						try {
							brandNameFull = getBrandName(brand, locale, 'full');
							brandNameShort = getBrandName(brand, locale, 'short');
						} catch (error) {
							console.error('Error getting brand name:', error);
							brandNameFull = 'Brand';
							brandNameShort = 'Brand';
						}

						return {
							subject: controls.subject
								.replace('{{brandNameFull}}', brandNameFull)
								.replace('{{eventTitle}}', localizedEventTitle)
								.replace('{{brandNameShort}}', brandNameShort),
							body
						};
					} catch (error) {
						console.error('Error processing email notification:', error);
						// Provide minimal fallback content in case of errors
						return {
							subject: 'Event Canceled',
							body: 'Your event has been canceled. Please contact support for more information.'
						};
					}
				},
				{
					controlSchema: z.object({
						subject: z.string().default('[{{brandNameShort}}] Canceled: {{eventTitle}}')
					})
				}
			);

			// Send SMS notification (simplified implementation)
			await step.sms(
				'send-event-canceled-sms',
				async (controls) => {
					try {
						// Extract properties from payload - schema guarantees non-null values
						const event_title = payload.event_title;
						const event_date_time = payload.event_date_time;
						const brand = payload.brand;

						// Get subscriber data
						const locale = getLanguageCode(subscriber?.locale);
						const firstName = subscriber?.firstName || '';
						const lastName = subscriber?.lastName || '';
						const subscriberId = subscriber?.subscriberId || 'unknown-user';
						const userName = firstName
							? lastName
								? `${firstName} ${lastName}`
								: firstName
							: subscriberId;

						// Get localized text
						let localizedEventTitle = 'Event';
						try {
							localizedEventTitle = getLocalizedText(event_title, locale);
						} catch (error) {
							console.error('Error getting localized text:', error);
							// Fallback already set
						}

						// Format date/time according to locale
						const formattedDateTime = formatDateTime(event_date_time, locale);

						// Get brand names
						let brandNameFull = 'Brand';
						let brandNameShort = 'Brand';
						try {
							brandNameFull = getBrandName(brand, locale, 'full');
							brandNameShort = getBrandName(brand, locale, 'short');
						} catch (error) {
							console.error('Error getting brand name:', error);
							// Fallbacks already set
						}

						// Ensure we have a template
						const template =
							controls.template ||
							'[{{brandNameShort}}] Important: {{userName}}, your {{eventTitle}} on {{eventDateTime}} has been canceled.';

						// Return the SMS with properly replaced template variables
						return {
							body: template
								.replace('{{userName}}', userName)
								.replace('{{eventTitle}}', localizedEventTitle)
								.replace('{{eventDateTime}}', formattedDateTime)
								.replace('{{brandNameFull}}', brandNameFull)
								.replace('{{brandNameShort}}', brandNameShort)
						};
					} catch (error) {
						console.error('Error processing SMS notification:', error);
						// Provide minimal fallback content in case of errors
						return {
							body: 'Your event has been canceled. Please contact support for more information.'
						};
					}
				},
				{
					controlSchema: z.object({
						template: z
							.string()
							.default(
								'[{{brandNameShort}}] Important: {{userName}}, your {{eventTitle}} on {{eventDateTime}} has been canceled. Please check your email for details from {{brandNameFull}}.'
							)
					})
				}
			);

			// Add other notification types (push, in-app) with proper error handling here if needed
		} catch (error) {
			console.error('Error in event canceled workflow:', error);
			// Workflow-level error handling - log errors and allow the workflow to finish gracefully
		}
	},
	{
		payloadSchema: z.object({
			event_title: LocalizedTextSchema.default(DEFAULT_LOCALIZED_TEXT).describe(
				'Event title in multiple languages'
			),
			event_subtitle: OptionalLocalizedTextSchema.default(DEFAULT_LOCALIZED_TEXT).describe(
				'Event subtitle in multiple languages'
			),
			event_date_time: z.string().default(new Date().toISOString()).describe('Event date and time'),
			cancellation_reason: LocalizedTextSchema.default(DEFAULT_LOCALIZED_TEXT).describe(
				'Cancellation reason in multiple languages'
			),
			host_message: OptionalLocalizedTextSchema.default(DEFAULT_LOCALIZED_TEXT).describe(
				'Host message in multiple languages'
			),
			refund_info: LocalizedTextSchema.default(DEFAULT_LOCALIZED_TEXT).describe(
				'Refund information in multiple languages'
			),
			compensation_info: OptionalLocalizedTextSchema.default(DEFAULT_LOCALIZED_TEXT).describe(
				'Compensation information in multiple languages'
			),
			brand: BrandSchema.default(DEFAULT_BRAND).describe('Brand information')
		}),
		name: 'Event Cancellation',
		description: 'Critical notification sent when an event is canceled',
		tags: ['critical', 'event', 'cancellation'],
		preferences: {
			all: { enabled: true, readOnly: true },
			channels: {
				inApp: { enabled: true },
				email: { enabled: true },
				sms: { enabled: true },
				push: { enabled: true },
				chat: { enabled: true }
			}
		}
	}
);
