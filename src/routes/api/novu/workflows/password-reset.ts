import { workflow } from '@novu/framework';
import { render } from '@keycloakify/svelte-email';
import { z } from 'zod';
import PasswordResetEmail from '../emails/PasswordResetEmail.svelte';
import { BrandSchema, CommonValidators, getBrandName } from '../schemas';

// Define the Brand type to match App.Locals['brand']
type Brand = {
	id: string;
	logo_url: string;
	name_full: Record<string, string>;
	name_short: Record<string, string>;
	portal_url: string;
	portal_url_dev: string;
};

// Default values for Novu studio preview (explicitly typed)
const defaultBrand: Brand = {
	id: 'default-brand-id',
	logo_url: '',
	name_full: { en: 'Our Full Platform Name' },
	name_short: { en: 'Our Platform' },
	portal_url: 'https://example.com',
	portal_url_dev: 'https://dev.example.com'
};

export const passwordResetWorkflow = workflow(
	'password-reset-workflow',
	async ({ step, payload }) => {
		// Send password reset email
		await step.email(
			'send-password-reset-email',
			async (controls) => {
				// Ensure payload properties exist with defaults
				const userName = payload.userName || 'User';
				const resetToken = payload.resetToken || 'example-token';
				const email = payload.email || '<EMAIL>';
				const locale = payload.locale || 'en';
				const expiryHours = payload.expiryHours || 1;

				// Use the payload brand if it exists, otherwise use default
				const brand = (payload.brand as Brand) || defaultBrand;

				// Generate the reset URL if not provided
				const resetUrl =
					payload.resetUrl ||
					`${brand.portal_url}/reset-password?token=${resetToken}&email=${encodeURIComponent(email)}`;

				// Get brand name from helper function
				const brandName = getBrandName(brand, locale);

				// Render the password reset email component
				const body = render({
					template: PasswordResetEmail,
					props: {
						userName,
						resetUrl,
						brand,
						locale,
						expiryHours
					}
				});

				return {
					subject: controls.subject.replace('{{brandName}}', brandName),
					body
				};
			},
			{
				controlSchema: z.object({
					subject: z.string().default('Reset your password for {{brandName}}')
				})
			}
		);
	},
	{
		payloadSchema: z.object({
			...CommonValidators,
			resetToken: z.string().min(1, 'Reset token is required').default('example-token'),
			resetUrl: z.string().url().optional(),
			brand: BrandSchema,
			expiryHours: z.number().positive().optional().default(1)
		})
	}
);
