import { workflow } from '@novu/framework';
import { render } from '@keycloakify/svelte-email';
import { z } from 'zod';
import WelcomeEmail from '../emails/WelcomeEmail.svelte';
import { BrandSchema, CommonValidators, getBrandName } from '../schemas';

// Define the Brand type to match App.Locals['brand']
type Brand = {
	id: string;
	logo_url: string;
	name_full: Record<string, string>;
	name_short: Record<string, string>;
	portal_url: string;
	portal_url_dev: string;
};

// Default values for Novu studio preview (explicitly typed)
const defaultBrand: Brand = {
	id: 'default-brand-id',
	logo_url: '',
	name_full: { en: 'Our Full Platform Name' },
	name_short: { en: 'Our Platform' },
	portal_url: 'https://example.com',
	portal_url_dev: 'https://dev.example.com'
};

export const welcomeWorkflow = workflow(
	'welcome-workflow',
	async ({ step, payload }) => {
		// Send welcome email
		await step.email(
			'send-welcome-email',
			async (controls) => {
				// Ensure payload properties exist with defaults
				const userName = payload.userName || 'User';
				const actionUrl = payload.actionUrl || 'https://example.com/dashboard';
				const locale = payload.locale || 'en';

				// Use the payload brand if it exists, otherwise use default
				// Force type to Brand to avoid TypeScript errors
				const brand = (payload.brand as Brand) || defaultBrand;

				// Get brand name from helper function
				const brandName = getBrandName(brand, locale);

				// Render the welcome email component using the correct API
				const body = render({
					template: WelcomeEmail,
					props: {
						userName,
						actionUrl,
						brand,
						locale
					}
				});

				return {
					subject: controls.subject.replace('{{brandName}}', brandName),
					body
				};
			},
			{
				controlSchema: z.object({
					subject: z.string().default('Welcome to {{brandName}}!')
				})
			}
		);
	},
	{
		payloadSchema: z.object({
			...CommonValidators,
			actionUrl: z
				.string()
				.url('Valid action URL is required')
				.default('https://example.com/dashboard'),
			brand: BrandSchema
		})
	}
);
