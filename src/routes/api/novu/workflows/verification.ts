import { workflow } from '@novu/framework';
import { render } from '@keycloakify/svelte-email';
import { z } from 'zod';
import VerificationEmail from '../emails/VerificationEmail.svelte';
import { BrandSchema, CommonValidators, getBrandName } from '../schemas';

// Define the Brand type to match App.Locals['brand']
type Brand = {
	id: string;
	logo_url: string;
	name_full: Record<string, string>;
	name_short: Record<string, string>;
	portal_url: string;
	portal_url_dev: string;
};

// Default values for Novu studio preview (explicitly typed)
const defaultBrand: Brand = {
	id: 'default-brand-id',
	logo_url: '',
	name_full: { en: 'Our Full Platform Name' },
	name_short: { en: 'Our Platform' },
	portal_url: 'https://example.com',
	portal_url_dev: 'https://dev.example.com'
};

export const verificationWorkflow = workflow(
	'verification-workflow',
	async ({ step, payload }) => {
		// Send verification email
		await step.email(
			'send-verification-email',
			async (controls) => {
				// Ensure payload properties exist with defaults
				const userName = payload.userName || 'User';
				const verificationCode = payload.verificationCode || '123456';
				const email = payload.email || '<EMAIL>';
				const locale = payload.locale || 'en';
				const expiryHours = payload.expiryHours || 24;

				// Use the payload brand if it exists, otherwise use default
				const brand = (payload.brand as Brand) || defaultBrand;

				// Generate the verification URL if not provided
				const verificationUrl =
					payload.verificationUrl ||
					`${brand.portal_url}/verify?code=${verificationCode}&email=${encodeURIComponent(email)}`;

				// Get brand name from helper function
				const brandName = getBrandName(brand, locale);

				// Render the verification email component
				const body = render({
					template: VerificationEmail,
					props: {
						userName,
						verificationCode,
						verificationUrl,
						brand,
						locale,
						expiryHours
					}
				});

				return {
					subject: controls.subject.replace('{{brandName}}', brandName),
					body
				};
			},
			{
				controlSchema: z.object({
					subject: z.string().default('Verify your email for {{brandName}}')
				})
			}
		);
	},
	{
		payloadSchema: z.object({
			...CommonValidators,
			verificationCode: z.string().min(4, 'Verification code is required').default('123456'),
			verificationUrl: z.string().url().optional(),
			brand: BrandSchema,
			expiryHours: z.number().positive().optional().default(24)
		})
	}
);
