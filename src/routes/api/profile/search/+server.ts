import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import type { LocalizedText } from '$lib/utils/localization';

export interface UserProfile {
	id: string;
	given_name: LocalizedText;
	family_name: LocalizedText;
	username: string;
	avatar_url: string | null;
	auto_user_email?: string;
	auto_user_phone?: string;
}

export const GET: RequestHandler = async ({ url, locals }) => {
	try {
		const { supabase, brand } = locals;

		// Check if brand ID exists
		if (!brand?.id) {
			return json({ error: 'Brand ID is required' }, { status: 400 });
		}

		// Get search parameters
		const searchQuery = url.searchParams.get('q')?.trim().toLowerCase();
		const userId = url.searchParams.get('id')?.trim();

		// Log search parameters for debugging
		console.log('[API:Profile:Search] Search parameters:', { searchQuery, userId });

		// Build query for profiles, explicitly include all fields we need
		let query = supabase.from('profile').select(`
			id,
			given_name,
			family_name,
			username,
			avatar_url,
			auto_user_email,
			auto_user_phone
		`);

		// If searching by ID, return just that user
		if (userId) {
			const { data, error } = await query.eq('id', userId).limit(1);

			if (error) {
				console.error('[API:Profile:Search] Error fetching profile by ID:', error);
				return json({ error: 'Failed to fetch profile' }, { status: 500 });
			}

			console.log('[API:Profile:Search] Found profile by ID:', data);
			return json({ profiles: data as UserProfile[] });
		}

		// Apply search filters if search query exists
		if (searchQuery) {
			// Build Postgres-compatible query for each field we want to search
			const filters = [];

			// Add text field filters with comprehensive logging
			console.log(
				'[API:Profile:Search] Adding search filter for username:',
				`username.ilike.%${searchQuery}%`
			);
			filters.push(`username.ilike.%${searchQuery}%`);

			console.log(
				'[API:Profile:Search] Adding search filter for email:',
				`auto_user_email.ilike.%${searchQuery}%`
			);
			filters.push(`auto_user_email.ilike.%${searchQuery}%`);

			console.log(
				'[API:Profile:Search] Adding search filter for phone:',
				`auto_user_phone.ilike.%${searchQuery}%`
			);
			filters.push(`auto_user_phone.ilike.%${searchQuery}%`);

			// For JSONB fields, we need to use the correct syntax
			// Use ->> operator to extract values as text from JSONB
			console.log(
				'[API:Profile:Search] Adding search filter for given name:',
				`given_name->>en.ilike.%${searchQuery}%`
			);
			filters.push(`given_name->>en.ilike.%${searchQuery}%`);

			console.log(
				'[API:Profile:Search] Adding search filter for family name:',
				`family_name->>en.ilike.%${searchQuery}%`
			);
			filters.push(`family_name->>en.ilike.%${searchQuery}%`);

			// Apply the filters with OR
			query = query.or(filters.join(','));
		}

		// Execute query with ordering and limit
		const { data, error } = await query.order('created_at', { ascending: false }).limit(20); // Limit results to avoid large payloads

		if (error) {
			console.error('[API:Profile:Search] Error fetching profiles:', error);
			return json({ error: 'Failed to fetch profiles' }, { status: 500 });
		}

		console.log(
			`[API:Profile:Search] Found ${data?.length || 0} profiles with query:`,
			searchQuery
		);

		// Debug first few results if available
		if (data && data.length > 0) {
			console.log('[API:Profile:Search] Sample result:', {
				id: data[0].id,
				username: data[0].username,
				email: data[0].auto_user_email,
				hasEmail: !!data[0].auto_user_email
			});
		}

		// Apply additional client-side filtering if needed for complex cases
		let finalProfiles = data;
		if (searchQuery) {
			finalProfiles = finalProfiles.filter((profile) => {
				const email = profile.auto_user_email?.toLowerCase() || '';
				const phone = profile.auto_user_phone?.toLowerCase() || '';
				const username = profile.username?.toLowerCase() || '';
				const givenName =
					typeof profile.given_name === 'object' && profile.given_name?.en
						? profile.given_name.en.toLowerCase()
						: '';
				const familyName =
					typeof profile.family_name === 'object' && profile.family_name?.en
						? profile.family_name.en.toLowerCase()
						: '';

				const matches =
					!searchQuery ||
					email.includes(searchQuery) ||
					phone.includes(searchQuery) ||
					username.includes(searchQuery) ||
					givenName.includes(searchQuery) ||
					familyName.includes(searchQuery);

				// Log matching status for debugging
				if (email.includes(searchQuery)) {
					console.log(
						`[API:Profile:Search] Email match found: "${email}" contains "${searchQuery}"`
					);
				}

				return matches;
			});
		}

		console.log(
			`[API:Profile:Search] After client filtering: ${finalProfiles?.length || 0} profiles`
		);
		return json({ profiles: finalProfiles as UserProfile[] });
	} catch (e) {
		console.error('[API:Profile:Search] Server error:', e);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
