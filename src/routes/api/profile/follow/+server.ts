import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, locals: { supabase, user } }) => {
	if (!user) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	const { profileId } = await request.json();

	if (!profileId) {
		return json({ error: 'Profile ID is required' }, { status: 400 });
	}

	if (profileId === user.id) {
		return json({ error: 'Cannot follow yourself' }, { status: 400 });
	}

	try {
		// Call the toggle_follow function
		const { data, error } = await supabase.rpc('toggle_follow', {
			follower_id: user.id,
			following_id: profileId
		});

		if (error) {
			console.error('Error toggling follow:', error);
			return json({ error: 'Failed to toggle follow' }, { status: 500 });
		}

		// Get updated stats
		const { data: stats } = await supabase.rpc('get_profile_stats', { profile_id: profileId });

		return json({
			success: true,
			isFollowing: data,
			stats: stats?.[0] || null
		});
	} catch (error) {
		console.error('Unexpected error:', error);
		return json({ error: 'An unexpected error occurred' }, { status: 500 });
	}
};
