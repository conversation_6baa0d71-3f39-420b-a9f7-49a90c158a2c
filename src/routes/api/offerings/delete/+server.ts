import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, locals: { supabase, user } }) => {
	if (!user) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		const { creation_request_id, perform_action } = await request.json();

		if (!creation_request_id) {
			return json({ error: 'Missing creation_request_id' }, { status: 400 });
		}

		// Call the delete offering function
		const { data, error } = await supabase.rpc('client_delete_offering', {
			input_creation_request_id: creation_request_id,
			input_perform_action: perform_action || false
		});

		if (error) {
			console.error('Error deleting offering:', error);
			return json({ error: error.message }, { status: 500 });
		}

		return json({ data });
	} catch (error) {
		console.error('Unexpected error:', error);
		return json({ error: 'An unexpected error occurred' }, { status: 500 });
	}
};
