import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { json } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';

export const GET: RequestHandler = async ({ url, locals: { supabase } }) => {
	const brandId = url.searchParams.get('brand_id');
	const kinds = url.searchParams.getAll('kind');
	const search = url.searchParams.get('search');

	if (!brandId) {
		return error(400, 'brand_id is required');
	}

	// Build the query
	let query = supabase
		.from('wikipage')
		.select('id, title, kind, brief')
		.eq('brand_id', brandId)
		.order('created_at', { ascending: false });

	// Filter by kinds if provided
	if (kinds.length > 0) {
		query = query.in('kind', kinds);
	}

	// Add search filter if provided
	if (search) {
		// Search in title JSONB field - this searches across all language values
		query = query.or(`title.ilike.%${search}%`);
	}

	const { data, error: queryError } = await query;

	if (queryError) {
		console.error('Error fetching wikipages:', queryError);
		return error(500, 'Failed to fetch wikipages');
	}

	return json(data ?? []);
};
