import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url, locals: { supabase } }) => {
	const brandId = url.searchParams.get('brandId');

	if (!brandId) {
		return json({ error: 'Brand ID is required' }, { status: 400 });
	}

	try {
		const { data: prices, error } = await supabase
			.from('price')
			.select(
				`
				id,
				title,
				title_short,
				semantic_order,
				state,
				unit_kind,
				price_option (
					id,
					title,
					units,
					money_int,
					currency_code
				)
			`
			)
			.eq('brand_id', brandId)
			.in('state', ['public', 'private']) // Filter for active prices
			.order('semantic_order', { ascending: true });

		if (error) {
			console.error('Error fetching prices:', error);
			return json({ error: 'Failed to fetch prices' }, { status: 500 });
		}

		// Transform the data to group price options by price and sort options by money_int
		const pricesWithOptions = (prices || []).map((price) => ({
			...price,
			price_option: (price.price_option || []).sort((a: any, b: any) => a.money_int - b.money_int)
		}));

		return json({ prices: pricesWithOptions });
	} catch (error) {
		console.error('Error in prices API:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
