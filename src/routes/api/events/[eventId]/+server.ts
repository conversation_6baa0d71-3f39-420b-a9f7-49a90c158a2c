import { error, json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
import { getLocale } from '$lib/paraglide/runtime';

// Define types for the database response
interface EventData {
	id: string;
	start_at: string;
	duration_minute: number;
	metadata: {
		id: string;
		auto_final_title: LocalizedText;
		auto_final_subtitle: LocalizedText;
		promo_image_url: string | null;
		promo_video_url: string | null;
		promo_webpage_url: string | null;
		metadata_wikipage: Array<{
			relation: string;
			wikipage: {
				id: string;
				title: LocalizedText;
				decorating_profile_id: string | null;
			};
		}>;
	};
	space: {
		name_short: LocalizedText;
		name_full: LocalizedText;
		landmark: {
			id: string;
			title_short: LocalizedText;
			address: {
				auto_normalized_address_local: string | null;
			};
		} | null;
	} | null;
	landmark: {
		id: string;
		title_short: LocalizedText;
		address: {
			auto_normalized_address_local: string | null;
		};
	} | null;
	event_product: Array<{
		id: string;
		kind: string;
		event: {
			id: string;
			start_at: string;
			duration_minute: number;
			metadata: {
				auto_final_title: LocalizedText;
				auto_final_subtitle: LocalizedText;
			};
			landmark: {
				id: string;
				title_short: LocalizedText;
			} | null;
		};
		product: {
			id: string;
			title: LocalizedText | null;
			auto_stock_available: number | null;
			open_to_buy_at: string | null;
			auto_first_event_start_at: string | null;
			auto_last_event_end_at: string | null;
			product_price: Array<{
				id: string;
				cost_units: number;
				start_at: string | null;
				end_at: string | null;
				stock: number | null;
				auto_cancel_at_far: string | null;
				auto_cancel_at_far_return_units: number | null;
				auto_cancel_at_near: string | null;
				auto_cancel_at_near_return_units: number | null;
				price: {
					title: LocalizedText;
					title_short: LocalizedText;
					product_classification: LocalizedText;
					color_primary_semantic: string | null;
				};
			}>;
			order_product: Array<{
				id: string;
				canceled_at: string | null;
				auto_units_owed: number;
				consumer_profile_id: string;
			}> | null;
			event_product: Array<{
				id: string;
				kind: string;
				event: {
					id: string;
					start_at: string;
					duration_minute: number;
					metadata: {
						auto_final_title: LocalizedText;
						auto_final_subtitle: LocalizedText;
					};
					landmark: {
						id: string;
						title_short: LocalizedText;
					} | null;
				};
			}> | null;
		};
	}>;
}

export const GET: RequestHandler = async ({ params, locals: { supabase, brand, user } }) => {
	const { eventId } = params;

	if (!brand?.id) {
		throw error(400, 'Brand is required');
	}

	if (!eventId) {
		throw error(400, 'Event ID is required');
	}

	const currentLocale = getLocale();

	try {
		// Fetch complete event data
		const { data: eventData, error: eventError } = await supabase
			.from('event')
			.select(
				`
				id,
				start_at,
				duration_minute,
				metadata (
					id,
					auto_final_title,
					auto_final_subtitle,
					promo_image_url,
					promo_video_url,
					promo_webpage_url,
					metadata_wikipage (
						relation,
						wikipage (
							id,
							title,
							decorating_profile_id
						)
					)
				),
				space (
					name_short,
					name_full,
					landmark (
						id,
						title_short,
						address (
							auto_normalized_address_local
						)
					)
				),
				landmark (
					id,
					title_short,
					address (
						auto_normalized_address_local
						)
				),
				event_product (
					id,
					kind,
					event (
						id,
						start_at,
						duration_minute,
						metadata (
							auto_final_title,
							auto_final_subtitle
						),
						landmark (
							id,
							title_short
						)
					),
					product!inner (
						id,
						title,
						auto_stock_available,
						open_to_buy_at,
						auto_first_event_start_at,
						auto_last_event_end_at,
						product_price!inner (
							id,
							cost_units,
							start_at,
							end_at,
							stock,
							auto_cancel_at_far,
							auto_cancel_at_far_return_units,
							auto_cancel_at_near,
							auto_cancel_at_near_return_units,
							price!product_price_price_id_fkey (
								title,
								title_short,
								product_classification,
								color_primary_semantic
							)
						),
						order_product (
							id,
							canceled_at,
							auto_units_owed,
							consumer_profile_id
						),
						event_product (
							id,
							kind,
							event (
								id,
								start_at,
								duration_minute,
								metadata (
									auto_final_title,
									auto_final_subtitle
								),
								landmark (
									id,
									title_short
								)
							)
						)
					)
				)
				`
			)
			.eq('id', eventId)
			.eq('brand_id', brand.id)
			.single();

		if (eventError) {
			console.error('Error loading event:', eventError);
			throw error(500, 'Failed to load event');
		}

		if (!eventData) {
			throw error(404, 'Event not found');
		}

		// Type-cast the event data for TypeScript
		const typedEventData = eventData as unknown as EventData;

		// Log success
		console.log(`Retrieved event ${eventId} from database`);

		// Format the event data like in page.server.ts
		const directLandmark = typedEventData.landmark;
		const spaceLandmark = typedEventData.space?.landmark || null;
		const landmarkData = directLandmark || spaceLandmark;

		// Create a consistent landmark object for the UI
		const landmark = landmarkData
			? {
					id: landmarkData.id,
					title_short: landmarkData.title_short,
					address: landmarkData.address
				}
			: undefined;

		// Create location string for backward compatibility
		const locationParts = [];

		// Add space name if available
		if (typedEventData.space?.name_short) {
			locationParts.push(getLocalizedText(typedEventData.space.name_short));
		}

		// Add landmark name if available
		if (landmark?.title_short) {
			locationParts.push(getLocalizedText(landmark.title_short));
		}

		// Add address if available
		if (landmark?.address?.auto_normalized_address_local) {
			locationParts.push(landmark.address.auto_normalized_address_local);
		}

		const location = locationParts.length > 0 ? locationParts.join(', ') : '--';

		const userId = user?.id;

		// Format the event data
		const formattedEvent = {
			...typedEventData,
			title: getLocalizedText(typedEventData.metadata.auto_final_title),
			subtitle: getLocalizedText(typedEventData.metadata.auto_final_subtitle),
			landmark,
			location,
			host:
				typedEventData.metadata.metadata_wikipage
					.filter((w) => w.relation === 'instructor')
					.map((w) => getLocalizedText(w.wikipage.title))
					.filter(Boolean)
					.join(', ') || '--',
			genre:
				typedEventData.metadata.metadata_wikipage
					.filter((w) => w.relation === 'dance_genre')
					.map((w) => getLocalizedText(w.wikipage.title))
					.filter(Boolean)[0] || '--',
			difficulty:
				typedEventData.metadata.metadata_wikipage
					.filter((w) => w.relation === 'dance_level')
					.map((w) => getLocalizedText(w.wikipage.title))
					.filter(Boolean)[0] || 'All Levels',
			labels: typedEventData.metadata.metadata_wikipage
				.filter((w) => w.relation === 'special_label')
				.map((w) => getLocalizedText(w.wikipage.title))
				.filter(Boolean),
			choreographers: typedEventData.metadata.metadata_wikipage
				.filter((w) => w.relation === 'dance_choreographer')
				.map((w) => getLocalizedText(w.wikipage.title))
				.filter(Boolean),
			artists: typedEventData.metadata.metadata_wikipage
				.filter((w) => w.relation === 'artist')
				.map((w) => getLocalizedText(w.wikipage.title))
				.filter(Boolean),
			special_events: typedEventData.metadata.metadata_wikipage
				.filter((w) => w.relation === 'special_event')
				.map((w) => getLocalizedText(w.wikipage.title))
				.filter(Boolean),
			products: (typedEventData.event_product || []).map((event_product) => ({
				...event_product,
				product: {
					...event_product.product,
					title: event_product.product.title ? getLocalizedText(event_product.product.title) : null,
					event_count: typedEventData.event_product.filter(
						(ep) => ep.product.id === event_product.product.id && ep.kind === 'product_primary'
					).length,
					first_event_start_at: event_product.product.auto_first_event_start_at,
					last_event_end_at: event_product.product.auto_last_event_end_at,
					// Check if user has already registered for this product
					user_registered: userId
						? (event_product.product.order_product || []).some(
								(order) =>
									order.canceled_at === null &&
									order.auto_units_owed <= 0 &&
									order.consumer_profile_id === userId
							)
						: false,
					product_prices: (event_product.product.product_price || []).map((price) => ({
						...price,
						title: getLocalizedText(price.price.title),
						title_short: getLocalizedText(price.price.title_short),
						product_classification: getLocalizedText(
							price.price.product_classification,
							currentLocale
						),
						color_primary_semantic: price.price.color_primary_semantic
					}))
				}
			}))
		};

		return json(formattedEvent);
	} catch (err) {
		console.error('Error fetching event:', err);
		if (err instanceof Response) throw err;
		throw error(500, 'Failed to fetch event');
	}
};
