import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { fetchEvents } from '../../private/check-in/event-utils';

export const GET: RequestHandler = async ({ url, locals: { supabase, brand } }) => {
	const startParam = url.searchParams.get('start');
	const endParam = url.searchParams.get('end');
	const brandIdParam = url.searchParams.get('brandId');

	if (!startParam || !endParam) {
		return json({ error: 'Start and end dates are required' }, { status: 400 });
	}

	try {
		const startDate = new Date(startParam);
		const endDate = new Date(endParam);
		const brandId = brandIdParam || brand?.id;

		if (!brandId) {
			return json({ error: 'Brand ID is required' }, { status: 400 });
		}

		const events = await fetchEvents({
			supabase,
			startDate,
			endDate,
			brandId
		});

		return json({ events });
	} catch (error) {
		console.error('Error fetching events:', error);
		return json({ error: 'Failed to fetch events' }, { status: 500 });
	}
};
