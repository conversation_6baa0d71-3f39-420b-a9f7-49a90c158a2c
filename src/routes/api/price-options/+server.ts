import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url, locals: { supabase } }) => {
	const priceId = url.searchParams.get('priceId');

	if (!priceId) {
		return json({ error: 'Price ID is required' }, { status: 400 });
	}

	try {
		const { data: priceOptions, error } = await supabase
			.from('price_option')
			.select(
				`
				id,
				title,
				units,
				money_int,
				currency_code
			`
			)
			.eq('price_id', priceId)
			.order('money_int', { ascending: true });

		if (error) {
			console.error('Error fetching price options:', error);
			return json({ error: 'Failed to fetch price options' }, { status: 500 });
		}

		return json({ priceOptions: priceOptions || [] });
	} catch (error) {
		console.error('Error in price options API:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
