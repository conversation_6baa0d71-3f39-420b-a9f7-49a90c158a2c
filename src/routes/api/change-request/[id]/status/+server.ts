import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const PATCH: RequestHandler = async ({ params, request, locals: { supabase, user } }) => {
	if (!user) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	const { id } = params;
	const { status } = await request.json();

	if (!status) {
		return json({ error: 'Status is required' }, { status: 400 });
	}

	const { error } = await supabase.from('change_request').update({ status }).eq('id', id);

	if (error) {
		console.error('Error updating change request status:', error);
		return json({ error: 'Failed to update status' }, { status: 500 });
	}

	return json({ success: true });
};
