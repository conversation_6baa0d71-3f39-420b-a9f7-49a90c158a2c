import type { Request<PERSON>and<PERSON> } from './$types';
import { json } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';

export const GET: RequestHandler = async ({ url, locals: { supabase } }) => {
	const brandId = url.searchParams.get('brand_id');
	const search = url.searchParams.get('search');

	if (!brandId) {
		return error(400, 'brand_id is required');
	}

	// Build the query with landmark join
	let query = supabase
		.from('space')
		.select(
			`
			id,
			name_full,
			name_short,
			capacity,
			landmark:landmark_id (
				id,
				title_full,
				title_short
			)
		`
		)
		.eq('brand_id', brandId)
		.order('created_at', { ascending: false });

	// Add search filter if provided
	if (search) {
		// Search in space name or landmark title
		query = query.or(`name_full.ilike.%${search}%,name_short.ilike.%${search}%`);
	}

	const { data, error: queryError } = await query;

	if (queryError) {
		console.error('Error fetching spaces:', queryError);
		return error(500, 'Failed to fetch spaces');
	}

	return json(data ?? []);
};
