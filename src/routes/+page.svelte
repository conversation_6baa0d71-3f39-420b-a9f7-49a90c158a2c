<script lang="ts">
	import { fly } from 'svelte/transition';
</script>

<div class="from-background to-muted/20 relative min-h-screen bg-linear-to-b">
	<div class="container mx-auto flex min-h-screen items-center justify-center px-4">
		<div class="space-y-6 text-center">
			<h1
				in:fly={{ y: 20, duration: 800, delay: 200 }}
				class="text-6xl font-semibold tracking-tight sm:text-7xl lg:text-8xl"
			>
				<span class="from-foreground to-foreground/80 bg-linear-to-b bg-clip-text text-transparent">
					Welcome to Hana
				</span>
			</h1>
			<p
				in:fly={{ y: 20, duration: 800, delay: 400 }}
				class="text-muted-foreground mx-auto max-w-xl text-lg"
			>
				Your business management platform
			</p>
		</div>
	</div>
</div>

<style>
</style>
