import type { PageServerLoad } from './$types';
import { error, fail } from '@sveltejs/kit';
import type { Database } from '$lib/supabase/database.types';
import { startOfDay, addDays } from 'date-fns';
import { getLocalizedText } from '$lib/utils/localization';
import { getLocale } from '$lib/paraglide/runtime';
import type { PostgrestSingleResponse } from '@supabase/supabase-js';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Json } from '$lib/supabase/database.types';
import type { Actions } from './$types';
import type {
	FindOptionsResponse,
	ConsumerProfileUI,
	ConsumerProductPriceUI,
	UnitsBuyingOptionUI,
	MoneyBuyingOptionUI,
	OrderPriceUI,
	PriceOptionCombination,
	Landmark,
	LocalizedText
} from './types';

type MetadataWikipageRelation =
	| 'artist'
	| 'instructor'
	| 'show'
	| 'dance_level'
	| 'dance_genre'
	| 'dance_choreographer'
	| 'special_event'
	| 'special_label';

interface ProductPrice {
	id: string;
	cost_units: number;
	price: {
		title: Json;
		title_short: Json;
		product_classification: Json;
		color_primary_semantic: string;
	};
	auto_cancel_at_far: string | null;
	auto_cancel_at_far_return_units: number | null;
	auto_cancel_at_near: string | null;
	auto_cancel_at_near_return_units: number | null;
	start_at: string;
	end_at: string | null;
	stock: number | null;
}

interface EventViewModel {
	id: string;
	start_at: string;
	duration_minute: number;
	metadata: {
		auto_final_title: Json;
		auto_final_subtitle: Json;
		promo_image_url: string | null;
		promo_video_url: string | null;
		promo_video_thumbnail_url: string | null;
		promo_webpage_url: string | null;
		wikipages: Array<{
			relation: MetadataWikipageRelation;
			wikipage: {
				title: Json;
			};
		}>;
	};
	space: {
		name_short: Json;
		name_full: Json;
		landmark: {
			id: string;
			title_short: Json;
			address: {
				auto_normalized_address_local: string | null;
			} | null;
		} | null;
	} | null;
	landmark?: {
		id: string;
		title_short: Json;
		address: {
			auto_normalized_address_local: string | null;
		} | null;
	};
	title: string;
	subtitle: string;
	location: string;
	host: string;
	genre: string;
	difficulty: string;
	labels: string[];
	choreographers: string[];
	artists: string[];
	special_events: string[];
	products: Array<{
		id: string;
		kind: string | null;
		product: {
			id: string;
			auto_stock_available: number;
			open_to_buy_at: string | null;
			title: Json | null;
			product_prices: ProductPrice[];
			event_count: number;
			first_event_start_at: string | null;
			last_event_end_at: string | null;
		};
	}>;
}

export const load = async ({ depends, locals: { supabase, brand, user }, url }) => {
	depends('events');

	if (!brand?.id) {
		throw error(400, 'Brand is required');
	}

	const currentLocale = getLocale();
	//const startAt = url.searchParams.get('startAt') || addDays(new Date(), 0).toISOString();
	const startAt = url.searchParams.get('startAt') || startOfDay(new Date()).toISOString();
	const endAt = url.searchParams.get('endAt') || addDays(new Date(startAt), 120).toISOString();

	// User ID is now optional
	const userId = user?.id;

	const { data: events, error: eventsError } = await supabase
		.from('event')
		.select(
			`
			id,
			start_at,
			duration_minute,
			metadata (
				id,
				auto_final_title,
				auto_final_subtitle,
				promo_image_url,
				promo_video_url,
				promo_video_thumbnail_url,
				promo_webpage_url,
				metadata_wikipage (
					relation,
					wikipage (
						id,
						title,
						decorating_profile_id
					)
				)
			),
			space (
				name_short,
				name_full,
				landmark (
					id,
					title_short,
					address ( 
						auto_normalized_address_local,
						time_zone
					)
				)
			),
			landmark (
				id,
				title_short,
				address (
					auto_normalized_address_local,
					time_zone
					)
			),
			event_product (
				id,
				kind,
				event (
					id,
					start_at,
					duration_minute,
					metadata (
						auto_final_title,
						auto_final_subtitle
					),
					landmark (
						id,
						title_short
					)
				),
				product!inner (
					id,
					title,
					auto_stock_available,
					open_to_buy_at,
					auto_first_event_start_at,
					auto_last_event_end_at,
					waiting_group:group!waiting_group_id (
						id,
						group_member (
							id,
							user_id
						)
					),
					product_price!inner (
						id,
						cost_units,
						start_at,
						end_at,
						stock,
						auto_cancel_at_far,
						auto_cancel_at_far_return_units,
						auto_cancel_at_near,
						auto_cancel_at_near_return_units,
						order_requirement (
							id,
							require_payer_owning_product_id,
							require_consumer_owning_product_id,
							consumer_product_count_max,
							payer_product_count_max,
							product_count_in_days,
							require_passcode,
							title_short
						),
						price!product_price_price_id_fkey (
							title,
							title_short,
							product_classification,
							color_primary_semantic
						)
					),
					order_product (
						id,
						canceled_at,
						auto_units_owed,
						consumer_profile_id
					),
					event_product (
						id,
						kind,
						event (
							id,
							start_at,
							duration_minute,
							metadata (
								auto_final_title,
								auto_final_subtitle
							),
							space (
								name_short,
								name_full,
								landmark (
									id,
									title_short,
									address (
										auto_normalized_address_local
									)
								)
							),
							landmark (
								id,
								title_short,
								address (
									auto_normalized_address_local
									)
							)
						)
					)
				)
			)
			`
		)
		.eq('publishing_state', 'published')
		.eq('brand_id', brand.id)
		.gte('auto_end_at', startAt)
		.lte('start_at', endAt)
		.order('start_at', { ascending: true });

	if (eventsError) {
		console.error('Error loading events:', eventsError);
		throw error(500, 'Failed to load events');
	}

	// Log the number of events retrieved from the database
	console.log(
		`Retrieved ${events?.length || 0} events from database with time range: ${startAt} to ${endAt}`
	);

	if (!events) {
		return { events: [] };
	}

	return {
		events: events.map((event: any) => {
			// Ensure landmark is always populated (from direct landmark or space.landmark)
			const directLandmark = event.landmark;
			const spaceLandmark = event.space?.landmark;
			const landmarkData = directLandmark || spaceLandmark;

			// Create a consistent landmark object for the UI
			const landmark: Landmark | undefined = landmarkData
				? {
						id: landmarkData.id,
						title_short: landmarkData.title_short as Record<string, string>,
						address: landmarkData.address
					}
				: undefined;

			// Create location string for backward compatibility
			const locationParts: string[] = [];

			// Add space name if available
			if (event.space?.name_short) {
				locationParts.push(getLocalizedText(event.space.name_short as Record<string, string>));
			}

			// Add landmark name if available
			if (landmark?.title_short) {
				locationParts.push(getLocalizedText(landmark.title_short));
			}

			// Add address if available
			if (landmark?.address?.auto_normalized_address_local) {
				locationParts.push(landmark.address.auto_normalized_address_local);
			}

			const location = locationParts.length > 0 ? locationParts.join(', ') : '--';

			return {
				...event,
				title: getLocalizedText(event.metadata.auto_final_title as Record<string, string>),
				subtitle: getLocalizedText(event.metadata.auto_final_subtitle as Record<string, string>),
				landmark,
				location,
				host:
					event.metadata.metadata_wikipage
						.filter((w: any) => w.relation === 'instructor')
						.map((w: any) => getLocalizedText(w.wikipage.title))
						.filter(Boolean)
						.join(', ') || '--',
				genre:
					event.metadata.metadata_wikipage
						.filter((w: any) => w.relation === 'dance_genre')
						.map((w: any) => getLocalizedText(w.wikipage.title))
						.filter(Boolean)[0] || '--',
				difficulty:
					event.metadata.metadata_wikipage
						.filter((w: any) => w.relation === 'dance_level')
						.map((w: any) => getLocalizedText(w.wikipage.title))
						.filter(Boolean)[0] || 'All Levels',
				labels: event.metadata.metadata_wikipage
					.filter((w: any) => w.relation === 'special_label')
					.map((w: any) => getLocalizedText(w.wikipage.title))
					.filter(Boolean),
				choreographers: event.metadata.metadata_wikipage
					.filter((w: any) => w.relation === 'dance_choreographer')
					.map((w: any) => getLocalizedText(w.wikipage.title))
					.filter(Boolean),
				artists: event.metadata.metadata_wikipage
					.filter((w: any) => w.relation === 'artist')
					.map((w: any) => getLocalizedText(w.wikipage.title))
					.filter(Boolean),
				special_events: event.metadata.metadata_wikipage
					.filter((w: any) => w.relation === 'special_event')
					.map((w: any) => getLocalizedText(w.wikipage.title))
					.filter(Boolean),
				products: (event.event_product || []).map((event_product: any) => ({
					...event_product,
					product: {
						...event_product.product,
						title: event_product.product.title
							? getLocalizedText(event_product.product.title)
							: null,
						event_count: event.event_product.filter(
							(ep: any) =>
								ep.product.id === event_product.product.id && ep.kind === 'product_primary'
						).length,
						first_event_start_at: event_product.product.auto_first_event_start_at,
						last_event_end_at: event_product.product.auto_last_event_end_at,
						// Check if user has already registered for this product
						// A registration is valid if:
						// 1. Order is not canceled (canceled_at is null)
						// 2. auto_units_owed is 0 or less (fully paid)
						// 3. consumer_profile_id matches the current user's ID (not just a gift order)
						user_registered: userId
							? (event_product.product.order_product || []).some(
									(order: any) =>
										order.canceled_at === null &&
										order.auto_units_owed <= 0 &&
										order.consumer_profile_id === userId
								)
							: false,
						product_prices: (event_product.product.product_price || []).map((price: any) => ({
							...price,
							title: getLocalizedText(price.price.title),
							title_short: getLocalizedText(price.price.title_short),
							product_classification: getLocalizedText(
								price.price.product_classification,
								currentLocale
							),
							color_primary_semantic: price.price.color_primary_semantic,
							order_requirement: price.order_requirement
								? {
										...price.order_requirement,
										title_short: price.order_requirement.title_short
											? getLocalizedText(
													price.order_requirement.title_short as Record<string, string>
												)
											: null
									}
								: null
						}))
					}
				}))
			};
		})
	};
};
