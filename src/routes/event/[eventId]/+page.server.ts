import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
import { getLocale } from '$lib/paraglide/runtime';

export const load = async ({ params, locals: { supabase, brand, user }, url, depends }) => {
	depends('event-detail');
	const eventId = params.eventId;
	const currentLocale = getLocale();

	if (!brand?.id) {
		throw error(400, 'Brand is required');
	}

	// Fetch event details with related data
	const { data: event, error: eventError } = await supabase
		.from('event')
		.select(
			`
			id,
			start_at,
			duration_minute,
			metadata (
				id,
				auto_final_title,
				auto_final_subtitle,
				promo_image_url,
				promo_video_url,
				promo_video_thumbnail_url,
				promo_webpage_url,
				promo_message,
				custom_attribute,
				kind,
				metadata_wikipage (
					relation,
					wikipage (
						id,
						title,
						decorating_profile_id
					)
				),
				metadata_track (
					id,
					track (
						id,
						title,
						genre,
						web_page_url
					)
				)
			),
			space (
				name_short,
				name_full,
				landmark (
					id,
					title_short,
					address (
						auto_normalized_address_local,
						time_zone
					)
				)
			),
			landmark (
				id,
				title_short,
				address (
					auto_normalized_address_local,
					time_zone
				)
			),
			event_product (
				id,
				kind,
				event (
					id,
					start_at,
					duration_minute,
					metadata (
						auto_final_title,
						auto_final_subtitle
					),
					space (
						name_short,
						name_full,
						landmark (
							id,
							title_short,
							address (
								auto_normalized_address_local
							)
						)
					),
					landmark (
						id,
						title_short,
						address (
							auto_normalized_address_local
						)
					)
				),
				product!inner (
					id,
					title,
					auto_stock_available,
					open_to_buy_at,
					auto_first_event_start_at,
					auto_last_event_end_at,
					metadata_id,
					metadata (
						id,
						auto_final_title,
						auto_final_subtitle,
						promo_image_url,
						promo_video_url,
						promo_video_thumbnail_url,
						promo_webpage_url,
						promo_message,
						custom_attribute,
						kind,
						metadata_wikipage (
							relation,
							wikipage (
								id,
								title,
								decorating_profile_id
							)
						),
						metadata_track (
							id,
							track (
								id,
								title,
								genre,
								web_page_url
							)
						)
					),
					waiting_group:group!waiting_group_id (
						id,
						group_member (
							id,
							user_id
						)
					),
					product_price!inner (
						id,
						cost_units,
						start_at,
						end_at,
						stock,
						auto_cancel_at_far,
						auto_cancel_at_far_return_units,
						auto_cancel_at_near,
						auto_cancel_at_near_return_units,
						order_requirement (
							id,
							require_payer_owning_product_id,
							require_consumer_owning_product_id,
							consumer_product_count_max,
							payer_product_count_max,
							product_count_in_days,
							require_passcode,
							title_short
						),
						price!product_price_price_id_fkey (
							title,
							title_short,
							product_classification,
							color_primary_semantic
						)
					),
					order_product (
						id,
						canceled_at,
						auto_units_owed,
						consumer_profile_id
					),
					event_product (
						id,
						kind,
						event (
							id,
							start_at,
							duration_minute,
							metadata (
								auto_final_title,
								auto_final_subtitle
							),
							space (
								name_short,
								name_full,
								landmark (
									id,
									title_short,
									address (
										auto_normalized_address_local
									)
								)
							),
							landmark (
								id,
								title_short,
								address (
									auto_normalized_address_local
								)
							)
						)
					)
				)
			)
			`
		)
		.eq('id', eventId)
		.eq('publishing_state', 'published')
		.eq('brand_id', brand.id)
		.single();

	if (eventError) {
		console.error('Error loading event:', eventError);
		throw error(404, 'Event not found');
	}

	if (!event) {
		throw error(404, 'Event not found');
	}

	// Handle the fact that Supabase returns arrays for joined data
	// Extract single objects from arrays while keeping original structure
	const metadata = Array.isArray(event.metadata) ? event.metadata[0] : event.metadata;
	const space = Array.isArray(event.space) ? event.space[0] : event.space;
	const eventLandmark = Array.isArray(event.landmark) ? event.landmark[0] : event.landmark;

	// Process space landmark if it's an array
	const spaceLandmark =
		space?.landmark && Array.isArray(space.landmark) ? space.landmark[0] : space?.landmark;

	// Determine which landmark to use (direct or from space)
	const landmark = eventLandmark || spaceLandmark;

	// Create location string for UI
	const locationParts: string[] = [];
	if (space?.name_short) {
		locationParts.push(getLocalizedText(space.name_short as LocalizedText, currentLocale));
	}
	if (landmark?.title_short) {
		locationParts.push(getLocalizedText(landmark.title_short as LocalizedText, currentLocale));
	}
	const landmarkAddress = Array.isArray(landmark?.address)
		? landmark.address[0]
		: landmark?.address;
	if (landmarkAddress?.auto_normalized_address_local) {
		locationParts.push(landmarkAddress.auto_normalized_address_local);
	}
	const location = locationParts.length > 0 ? locationParts.join(', ') : '--';

	// Process metadata wikipages for the event
	const metadata_wikipage = metadata?.metadata_wikipage || [];

	// Create properly structured event object
	const processedEvent = {
		...event,
		// Keep original structure but ensure single objects
		metadata,
		space,
		landmark: eventLandmark,
		// UI convenience fields
		title: getLocalizedText(metadata?.auto_final_title as LocalizedText, currentLocale),
		subtitle: getLocalizedText(metadata?.auto_final_subtitle as LocalizedText, currentLocale),
		location,
		host:
			metadata_wikipage
				.filter((w: any) => w.relation === 'instructor')
				.map((w: any) => getLocalizedText(w.wikipage.title as LocalizedText, currentLocale))
				.filter(Boolean)
				.join(', ') || '--',
		genre:
			metadata_wikipage
				.filter((w: any) => w.relation === 'dance_genre')
				.map((w: any) => getLocalizedText(w.wikipage.title as LocalizedText, currentLocale))
				.filter(Boolean)[0] || '--',
		difficulty:
			metadata_wikipage
				.filter((w: any) => w.relation === 'dance_level')
				.map((w: any) => getLocalizedText(w.wikipage.title as LocalizedText, currentLocale))
				.filter(Boolean)[0] || 'All Levels',
		labels: metadata_wikipage
			.filter((w: any) => w.relation === 'special_label')
			.map((w: any) => getLocalizedText(w.wikipage.title as LocalizedText, currentLocale))
			.filter(Boolean),
		choreographers: metadata_wikipage
			.filter((w: any) => w.relation === 'dance_choreographer')
			.map((w: any) => getLocalizedText(w.wikipage.title as LocalizedText, currentLocale))
			.filter(Boolean),
		artists: metadata_wikipage
			.filter((w: any) => w.relation === 'artist')
			.map((w: any) => getLocalizedText(w.wikipage.title as LocalizedText, currentLocale))
			.filter(Boolean),
		special_events: metadata_wikipage
			.filter((w: any) => w.relation === 'special_event')
			.map((w: any) => getLocalizedText(w.wikipage.title as LocalizedText, currentLocale))
			.filter(Boolean),

		// Process event_product with original structure
		event_product: (event.event_product || []).map((event_product: any) => {
			// Handle product metadata which might be an array
			const productMetadata = Array.isArray(event_product.product.metadata)
				? event_product.product.metadata[0]
				: event_product.product.metadata;

			// Add UI convenience fields to product
			const product = {
				...event_product.product,
				// Keep original metadata structure
				metadata: productMetadata,
				// UI convenience fields
				title_localized: event_product.product.title
					? getLocalizedText(event_product.product.title as LocalizedText, currentLocale)
					: null,
				event_count: event_product.product.event_product.filter(
					(ep: any) => ep.kind === 'product_primary'
				).length,
				user_registered: user?.id
					? (event_product.product.order_product || []).some(
							(order: any) =>
								order.canceled_at === null &&
								order.auto_units_owed <= 0 &&
								order.consumer_profile_id === user.id
						)
					: false,

				// Process product_price with localized fields
				product_price: (event_product.product.product_price || []).map((price: any) => ({
					...price,
					// UI convenience fields
					title_localized: getLocalizedText(price.price.title as LocalizedText, currentLocale),
					title_short_localized: getLocalizedText(
						price.price.title_short as LocalizedText,
						currentLocale
					),
					product_classification_localized: getLocalizedText(
						price.price.product_classification as LocalizedText,
						currentLocale
					),
					color_primary_semantic: price.price.color_primary_semantic,
					order_requirement: price.order_requirement
						? {
								...price.order_requirement,
								title_short_localized: price.order_requirement.title_short
									? getLocalizedText(
											price.order_requirement.title_short as LocalizedText,
											currentLocale
										)
									: null
							}
						: null
				}))
			};

			// Add UI convenience fields to product metadata if it exists
			if (productMetadata) {
				product.metadata = {
					...productMetadata,
					// UI convenience fields
					title_localized: getLocalizedText(
						productMetadata.auto_final_title as LocalizedText,
						currentLocale
					),
					subtitle_localized: getLocalizedText(
						productMetadata.auto_final_subtitle as LocalizedText,
						currentLocale
					)
				};
			}

			return {
				...event_product,
				product
			};
		})
	};

	return {
		event: processedEvent,
		userId: user?.id,
		user,
		brand
	};
};
