<!-- Event Detail Page -->
<script lang="ts">
	import type { PageData } from './$types';
	import { PageContainer } from '$lib/components/layout';
	import {
		Calendar,
		Clock,
		MapPin,
		Users,
		Music,
		Tag,
		Award,
		Star,
		Info,
		Share2,
		Check,
		Loader2,
		CalendarPlus,
		Map
	} from '@lucide/svelte';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import * as Card from '$lib/components/ui/card';
	import * as Tabs from '$lib/components/ui/tabs';
	import { parseISO } from 'date-fns';
	import { formatDistanceToNow, isPast, isFuture } from 'date-fns';
	import { goto, invalidate, invalidateAll } from '$app/navigation';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import type { ProductDetails } from '../types';
	import UnregisterModal from '../components/UnregisterModal.svelte';
	import AddressMapLink from '../components/AddressMapLink.svelte';
	import Markdown from '$lib/components/shared/Markdown.svelte';
	import EventVideo from '../components/EventVideo.svelte';
	import EventPlaceholder from '../components/EventPlaceholder.svelte';
	import AddToCalendar from '../components/modal-components/AddToCalendar.svelte';
	import EventStatusBanner from '../components/EventStatusBanner.svelte';
	import InlineRegistration from '../components/InlineRegistration.svelte';
	import ProductSelector from '../components/modal-components/ProductSelector.svelte';
	import { formatDate, formatTime, formatDateTimeRange } from '$lib/utils/datetime';
	import { getEventTimezone } from '$lib/utils/event-timezone';
	import TimezoneInfo from '$lib/components/shared/TimezoneInfo.svelte';
	import { getBrowserTimezone, isTimezoneDifferent } from '$lib/utils/datetime';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	// Unregister modal states
	let showUnregisterModal = $state(false);
	let selectedOrderProductId = $state('');
	let isLoadingUnregister = $state(false);
	let cancellationData = $state<any>(null);

	// Loading state for waitlist actions
	let isLoading = $state(false);

	// Product selection state - default to first product
	let selectedProductId = $state(data.event.event_product[0]?.product?.id || '');

	// Get the currently selected product
	let selectedEventProduct = $derived(
		data.event.event_product.find((ep) => ep.product.id === selectedProductId) ||
			data.event.event_product[0]
	);

	// Get event timezone
	const eventTimezone = $derived(
		getEventTimezone(data.event) || Intl.DateTimeFormat().resolvedOptions().timeZone
	);

	async function refreshEventData() {
		await invalidateAll();
	}

	async function handleUnregister(orderProductId: string) {
		selectedOrderProductId = orderProductId;
		isLoadingUnregister = true;

		try {
			// Fetch cancellation info before showing modal
			const response = await fetch(`/event/private/cancel-order?orderProductId=${orderProductId}`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json'
				}
			});

			const result = await response.json();

			if (result.type === 'error') {
				throw new Error(result.error.message);
			}

			// Store the fetched data
			cancellationData = result.data;

			// Only show modal after data is loaded
			showUnregisterModal = true;
		} catch (err) {
			console.error('Error checking cancellation eligibility:', err);
		} finally {
			isLoadingUnregister = false;
		}
	}

	// Format event date and time
	function formatEventDateTime(dateStr: string | null, duration: number) {
		if (!dateStr) return { date: 'TBA', time: '', endTime: '' };
		const date = new Date(dateStr);
		const endDate = new Date(date.getTime() + duration * 60 * 1000);
		return {
			date: formatDate(date, { timeZone: eventTimezone }),
			time: formatTime(date, { timeZone: eventTimezone }),
			endTime: formatTime(endDate, { timeZone: eventTimezone })
		};
	}

	function formatEventTime(dateStr: string | null): string {
		if (!dateStr) return 'TBA';
		return formatTime(dateStr, { timeZone: eventTimezone });
	}

	// Check if product is available
	function isProductAvailable(product: any): boolean {
		return (
			product.auto_stock_available > 0 &&
			(!product.open_to_buy_at || new Date(product.open_to_buy_at) <= new Date())
		);
	}

	// Get registered order product ID
	function getRegisteredOrderProductId(product: any): string | null {
		if (!product.user_registered || !product.order_product) return null;

		for (const orderProduct of product.order_product) {
			if (
				orderProduct.canceled_at === null &&
				orderProduct.auto_units_owed <= 0 &&
				orderProduct.consumer_profile_id === data.userId
			) {
				return orderProduct.id;
			}
		}
		return null;
	}

	// Check if user is on waitlist
	function isUserOnWaitlist(product: any): boolean {
		if (!data.userId || !product.waiting_group?.group_member) return false;
		return product.waiting_group.group_member.some((member: any) => member.user_id === data.userId);
	}

	// Handle join/leave waitlist
	async function handleWaitlist(product: any, action: 'join' | 'leave') {
		if (!data.userId || !data.supabase) return;

		isLoading = true;

		try {
			if (action === 'join') {
				const { data: result, error } = await data.supabase.rpc('new_product_group_member', {
					input_product_id: product.id,
					input_user_id: data.userId,
					input_group_kind: 'waiting_group'
				});

				if (error) throw error;
			} else {
				const { data: result, error } = await data.supabase.rpc('delete_group_member', {
					input_group_id: product.waiting_group.id,
					input_user_id: data.userId
				});

				if (error) throw error;
			}

			// Force page reload to ensure UI updates (like EventCard does)
			window.location.reload();
		} catch (err) {
			console.error(`Error ${action}ing waitlist:`, err);
		} finally {
			isLoading = false;
		}
	}

	// Check if this is the current event being highlighted
	function isCurrentEvent(eventId: string): boolean {
		return eventId === data.event.id;
	}

	// Format event number
	function getEventNumber(product: any, eventStartAt: string): number {
		if (!product.first_event_start_at || !product.last_event_end_at) return 0;
		const eventDate = parseISO(eventStartAt);
		const firstEventDate = parseISO(product.first_event_start_at);
		const lastEventDate = parseISO(product.last_event_end_at);

		return (
			Math.floor(
				((eventDate.getTime() - firstEventDate.getTime()) /
					(lastEventDate.getTime() - firstEventDate.getTime())) *
					(product.event_count - 1)
			) + 1
		);
	}

	const dateTimeInfo = $derived(
		formatEventDateTime(data.event.start_at, data.event.duration_minute)
	);
</script>

{#snippet contentSnippet()}
	<div class="space-y-6">
		<!-- Hero Section -->
		<div class="flex flex-col gap-6 lg:flex-row">
			<!-- Left: Image/Video Only -->
			<div class="lg:w-[240px]">
				<div class="aspect-video overflow-hidden rounded-xl lg:aspect-square">
					{#if data.event.metadata.promo_video_url}
						<EventVideo
							src={data.event.metadata.promo_video_url}
							poster={data.event.metadata.promo_video_thumbnail_url ||
								data.event.metadata.promo_image_url}
							title={data.event.title}
						/>
					{:else if data.event.metadata.promo_image_url}
						<img
							src={data.event.metadata.promo_image_url}
							alt={data.event.title}
							class="h-full w-full object-cover"
						/>
					{:else}
						<EventPlaceholder title={data.event.title} height="100%" />
					{/if}
				</div>
			</div>

			<!-- Right: All Content -->
			<div class="flex-1 space-y-4">
				<!-- Essential Info -->
				<div class="space-y-4">
					<!-- Date & Time with Add to Calendar -->
					<div class="flex items-start gap-3">
						<Calendar class="text-muted-foreground mt-1 h-5 w-5 shrink-0" />
						<div class="min-w-0 flex-1">
							<div class="font-semibold">{dateTimeInfo.date}</div>
							<div class="text-muted-foreground flex items-center gap-2 text-sm">
								<span>
									{dateTimeInfo.time}
									{#if dateTimeInfo.endTime && dateTimeInfo.endTime !== dateTimeInfo.time}
										- {dateTimeInfo.endTime}
									{/if} • {data.event.duration_minute} min
								</span>
								{#if data.event.start_at && isTimezoneDifferent(eventTimezone, getBrowserTimezone())}
									<TimezoneInfo
										eventDate={data.event.start_at}
										{eventTimezone}
										buttonClass="inline-flex h-4 w-4 items-center justify-center rounded-full text-muted-foreground/60 hover:bg-muted/20 hover:text-muted-foreground"
									>
										<Info class="h-3.5 w-3.5" />
									</TimezoneInfo>
								{/if}
								<!-- Add to Calendar Button -->
								{#if data.event.event_product && data.event.event_product.length > 0}
									<AddToCalendar product={data.event.event_product[0].product} />
								{/if}
							</div>
						</div>
					</div>

					<!-- Location with Map Link -->
					<div class="flex items-start gap-3">
						<MapPin class="text-muted-foreground mt-1 h-5 w-5 shrink-0" />
						<div class="min-w-0 flex-1">
							<!-- Venue Name -->
							{#if data.event.landmark || data.event.space?.landmark}
								{@const landmark = data.event.landmark || data.event.space?.landmark}
								{@const spaceName = data.event.space?.name_short
									? getLocalizedText(data.event.space.name_short as LocalizedText)
									: ''}
								{@const landmarkTitle = landmark?.title_short
									? getLocalizedText(landmark.title_short as LocalizedText)
									: ''}
								{@const venueParts = [spaceName, landmarkTitle].filter(Boolean)}
								{#if venueParts.length > 0}
									<div class="font-semibold">
										{venueParts.join(', ')}
									</div>
								{/if}

								<!-- Address with Map Link -->
								{@const landmarkAddress = Array.isArray(landmark?.address)
									? landmark.address[0]
									: landmark?.address}
								{#if landmarkAddress?.auto_normalized_address_local}
									<div class="text-muted-foreground flex items-center gap-2 text-sm">
										<span>{landmarkAddress.auto_normalized_address_local}</span>
										<AddressMapLink event={data.event}>
											<Button
												variant="ghost"
												size="sm"
												title="View location on map"
												class="text-muted-foreground hover:text-foreground ml-1"
											>
												<Map class="size-4" />
												<span class="ml-1 hidden text-xs sm:inline">View on map</span>
											</Button>
										</AddressMapLink>
									</div>
								{/if}
							{/if}
						</div>
					</div>

					<!-- Quick Details Row -->
					<div class="flex flex-wrap gap-4 text-sm">
						{#if data.event.host && data.event.host !== '--'}
							<div class="flex items-center gap-2">
								<Users class="text-muted-foreground h-4 w-4" />
								<span class="font-medium">{data.event.host}</span>
							</div>
						{/if}

						{#if data.event.genre && data.event.genre !== '--'}
							<div class="flex items-center gap-2">
								<Music class="text-muted-foreground h-4 w-4" />
								<span class="font-medium">{data.event.genre}</span>
							</div>
						{/if}

						{#if data.event.difficulty && data.event.difficulty !== 'All Levels'}
							<div class="flex items-center gap-2">
								<Award class="text-muted-foreground h-4 w-4" />
								<span class="font-medium">{data.event.difficulty}</span>
							</div>
						{:else}
							<div class="flex items-center gap-2">
								<Award class="text-muted-foreground h-4 w-4" />
								<span class="font-medium">All Levels</span>
							</div>
						{/if}
					</div>
				</div>

				<!-- Badges and Share -->
				<div class="flex items-center justify-between">
					<div class="flex-1">
						<!-- Badges -->
						{#if data.event.metadata.kind === 'placeholder' || data.event.labels.length > 0 || data.event.choreographers.length > 0 || data.event.artists.length > 0 || data.event.special_events.length > 0}
							<div class="flex flex-wrap gap-2">
								{#if data.event.metadata.kind === 'placeholder'}
									<Badge variant="secondary" class="gap-1">
										<Info class="h-3 w-3" />
										Draft Content
									</Badge>
								{/if}
								{#each data.event.labels as label}
									<Badge variant="secondary">
										<Tag class="mr-1 h-3 w-3" />
										{label}
									</Badge>
								{/each}
								{#each data.event.choreographers as choreographer}
									<Badge variant="secondary">
										<Star class="mr-1 h-3 w-3" />
										{choreographer}
									</Badge>
								{/each}
								{#each data.event.artists as artist}
									<Badge variant="secondary">
										<Music class="mr-1 h-3 w-3" />
										{artist}
									</Badge>
								{/each}
								{#each data.event.special_events as specialEvent}
									<Badge variant="secondary">
										<Star class="mr-1 h-3 w-3" />
										{specialEvent}
									</Badge>
								{/each}
							</div>
						{/if}
					</div>

					<Button
						variant="outline"
						size="icon"
						class="shrink-0"
						onclick={() => {
							navigator.share({
								title: data.event.title,
								text: data.event.subtitle,
								url: window.location.href
							});
						}}
					>
						<Share2 class="h-4 w-4" />
					</Button>
				</div>
			</div>
		</div>

		<!-- Status Banner -->
		<EventStatusBanner
			startAt={data.event.start_at}
			durationMinute={data.event.duration_minute}
			event={data.event}
			className=""
		/>

		<!-- Promo Message -->
		{#if data.event.metadata.promo_message}
			{@const promoText = getLocalizedText(data.event.metadata.promo_message as LocalizedText)}
			{#if promoText && promoText !== '--'}
				<div class="bg-muted/30 rounded-xl p-4">
					<div class="prose prose-sm dark:prose-invert max-w-none">
						<Markdown content={promoText} />
					</div>
				</div>
			{/if}
		{/if}

		<!-- Registration Section -->
		<div class="space-y-4">
			<h2 class="text-xl font-semibold">Registration</h2>

			<!-- Product Selection (if multiple products available) -->
			{#if data.event.event_product && data.event.event_product.length > 1}
				<ProductSelector
					products={data.event.event_product}
					{selectedProductId}
					onProductSelect={(productId) => (selectedProductId = productId)}
				/>
			{/if}

			<!-- Registration for Selected Product -->
			{#if selectedEventProduct}
				{@const product = selectedEventProduct.product}
				<div class="rounded-xl border p-4 sm:p-6">
					<!-- Check if user is already registered -->
					{#if product.user_registered}
						{@const orderProductId = getRegisteredOrderProductId(product)}
						{#if data.userId}
							<InlineRegistration
								product={product as unknown as ProductDetails}
								userId={data.userId}
								brand={data.brand}
								isRegistered={true}
								onUnregister={() => orderProductId && handleUnregister(orderProductId)}
								onRegistrationSuccess={refreshEventData}
							/>
						{/if}
					{:else if !isProductAvailable(product)}
						<!-- Waitlist buttons for sold out products -->
						<div class="mt-4">
							{#if isUserOnWaitlist(product)}
								<Button
									variant="outline"
									class="w-full sm:max-w-xs"
									onclick={() => handleWaitlist(product, 'leave')}
									disabled={isLoading}
								>
									{#if isLoading}
										<Loader2 class="mr-2 h-4 w-4 animate-spin" />
										Loading...
									{:else}
										Leave Waitlist
									{/if}
								</Button>
							{:else}
								<Button
									variant="outline"
									class="w-full sm:max-w-xs"
									onclick={() => handleWaitlist(product, 'join')}
									disabled={isLoading}
								>
									{#if isLoading}
										<Loader2 class="mr-2 h-4 w-4 animate-spin" />
										Loading...
									{:else}
										Join Waitlist
									{/if}
								</Button>
							{/if}
						</div>
					{:else}
						<!-- Inline Registration Component for available products -->
						{#if data.userId}
							<InlineRegistration
								product={product as unknown as ProductDetails}
								userId={data.userId}
								brand={data.brand}
								onRegistrationSuccess={refreshEventData}
							/>
						{:else}
							<!-- Show login prompt if user not authenticated -->
							<div class="p-4 text-center">
								<p class="text-muted-foreground mb-3 text-sm">
									Please log in to view registration options and purchase.
								</p>
								<Button href="/auth/sign-in?eid={product.id}">Login & Register</Button>
							</div>
						{/if}
					{/if}
				</div>
			{/if}
		</div>

		<!-- Additional Event Details -->
		{#if (data.event.metadata.metadata_wikipage && data.event.metadata.metadata_wikipage.length > 0) || (data.event.metadata.metadata_track && data.event.metadata.metadata_track.length > 0)}
			<div class="space-y-8">
				{#if data.event.metadata.metadata_wikipage && data.event.metadata.metadata_wikipage.length > 0}
					{@const groupedWikipages = data.event.metadata.metadata_wikipage.reduce(
						(acc: Record<string, any[]>, item: any) => {
							// Skip info already shown at the top to avoid duplication
							if (item.relation === 'instructor') return acc;
							if (item.relation === 'dance_genre') return acc;
							if (item.relation === 'dance_level') return acc;
							if (!acc[item.relation]) acc[item.relation] = [];
							acc[item.relation].push(item.wikipage);
							return acc;
						},
						{}
					)}
					{#if Object.keys(groupedWikipages).length > 0}
						<div class="space-y-4">
							<h3 class="text-lg font-semibold">Additional Details</h3>
							<div class="space-y-3">
								{#each Object.entries(groupedWikipages) as [relation, wikipages]}
									<div class="flex items-start gap-3">
										<div
											class="text-muted-foreground w-24 text-xs font-medium tracking-wider uppercase"
										>
											{relation.replace(/_/g, ' ')}
										</div>
										<div class="flex-1">
											{wikipages
												.map((wp) => getLocalizedText(wp.title as LocalizedText))
												.join(', ')}
										</div>
									</div>
								{/each}
							</div>
						</div>
					{/if}
				{/if}

				<!-- Music Tracks -->
				{#if data.event.metadata.metadata_track && data.event.metadata.metadata_track.length > 0}
					<div class="space-y-4">
						<h3 class="text-lg font-semibold">Music</h3>
						<div class="space-y-3">
							{#each data.event.metadata.metadata_track as trackItem}
								{@const track = Array.isArray(trackItem.track)
									? trackItem.track[0]
									: trackItem.track}
								<div class="flex items-center gap-3">
									<Music class="text-muted-foreground h-4 w-4" />
									<div class="min-w-0 flex-1">
										<div class="font-medium">
											{getLocalizedText(track.title as LocalizedText)}
										</div>
										{#if track.genre}
											<div class="text-muted-foreground text-sm">{track.genre}</div>
										{/if}
									</div>
								</div>
							{/each}
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>
{/snippet}

<PageContainer
	title={data.event.title}
	description={data.event.subtitle}
	content={contentSnippet}
/>

<!-- Unregister Modal -->
{#if showUnregisterModal && selectedOrderProductId && cancellationData}
	<UnregisterModal
		orderProductId={selectedOrderProductId}
		eventTitle={data.event.title}
		onClose={() => (showUnregisterModal = false)}
		onSuccess={async () => {
			showUnregisterModal = false;
			await refreshEventData();
		}}
		{cancellationData}
	/>
{/if}
