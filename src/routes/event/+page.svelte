<!-- Main event page component -->
<script lang="ts">
	import type { PageData } from './$types';
	import EventCalendarHeader from './components/EventCalendarHeader.svelte';
	import EventTabFilter from './components/EventTabFilter.svelte';
	import EventList from './components/EventList.svelte';
	import { invalidate } from '$app/navigation';
	import { PageContainer } from '$lib/components/layout';

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();
	// Get userId from PageData
	const userId = data.user?.id;

	// selectedDate is null for "All events" view
	let selectedDate = $state<Date | null>(null);
	let currentMonth = $state(new Date());

	function handleDateSelect(date: Date | null) {
		selectedDate = date;
	}

	$effect(() => {
		if (selectedDate) {
			const month = new Date(selectedDate);
			month.setDate(1);
			currentMonth = month;
		} else {
			currentMonth = new Date();
		}
	});
</script>

{#snippet actionsSnippet()}
	<EventCalendarHeader {currentMonth} {selectedDate} onDateSelect={handleDateSelect} />
{/snippet}

{#snippet contentSnippet()}
	<div class="relative min-h-[calc(100vh-4rem)]">
		<!-- Tab navigation -->
		<EventTabFilter {selectedDate} events={data.events} onDateSelect={handleDateSelect} />

		<EventList
			events={data.events}
			{selectedDate}
			supabase={data.supabase}
			{userId}
			brand={data.brand}
		/>
	</div>
{/snippet}

<PageContainer title="Upcoming" actions={actionsSnippet} content={contentSnippet} />
