<!-- Event filter with category tabs -->
<script lang="ts">
	import * as ToggleGroup from '$lib/components/ui/toggle-group';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import * as Avatar from '$lib/components/ui/avatar';
	import { MediaQuery } from 'svelte/reactivity';
	import { Button } from '$lib/components/ui/button';
	import { Label } from '$lib/components/ui/label';
	import { RadioGroup, RadioGroupItem } from '$lib/components/ui/radio-group';
	import { slide } from 'svelte/transition';
	import { MoreVertical } from '@lucide/svelte';
	import ResponsiveModal from '$lib/components/shared/ResponsiveModal.svelte';

	interface Props {
		selectedFilter: string;
		onFilterChange: (filter: string) => void;
	}

	let { selectedFilter = 'all', onFilterChange }: Props = $props();
	let filterDialogOpen = $state(false);
	const isDesktop = new MediaQuery('(min-width: 768px)');

	// Mock filter data
	const locations = [
		'All Locations',
		'Cupertino Studio A',
		'Cupertino Studio B',
		'Cupertino Studio C'
	];
	const genres = ['All Genres', 'Hip Hop', 'Chinese Fusion', 'Contemporary', 'Ballet'];
	const levels = ['All Levels', 'Foundation', 'Beginner', 'Intermediate', 'Advanced'];

	const filters = [
		{ id: 'all', label: 'All' },
		{ id: 'today', label: 'Today' },
		{ id: 'upcoming', label: 'Upcoming' },
		{ id: 'featured', label: 'Featured' }
	];

	let selectedLocation = $state('All Locations');
	let selectedGenre = $state('All Genres');
	let selectedLevel = $state('All Levels');

	function handleFilterSelect(value: string | undefined) {
		// Always maintain a selected filter, default to current selection if undefined
		onFilterChange(value || selectedFilter);
	}
</script>

<div class="flex items-center gap-2">
	<ToggleGroup.Root
		type="single"
		value={selectedFilter}
		onValueChange={(newValue) => newValue && onFilterChange(newValue)}
		class="relative flex items-center gap-1"
	>
		{#each filters as filter}
			<ToggleGroup.Item
				value={filter.id}
				onclick={(e) => filter.id === selectedFilter && e.preventDefault()}
				class="relative flex h-9 items-center justify-center rounded-lg px-3 text-sm font-medium transition-all hover:bg-muted data-[state=on]:bg-primary data-[state=on]:pr-8 data-[state=on]:text-primary-foreground"
			>
				<div
					class="flex items-center data-[state=on]:w-full"
					data-state={filter.id === selectedFilter ? 'on' : 'off'}
				>
					<Avatar.Root
						class="h-5 w-5 shrink-0 bg-muted data-[state=on]:bg-primary-foreground/20"
						data-state={filter.id === selectedFilter ? 'on' : 'off'}
					>
						<Avatar.Fallback
							class="text-xs text-white"
							data-state={filter.id === selectedFilter ? 'on' : 'off'}
						>
							{filter.label[0]}
						</Avatar.Fallback>
					</Avatar.Root>
					<div
						class="max-w-0 overflow-hidden whitespace-nowrap transition-[max-width] duration-200 data-[state=on]:ml-2 data-[state=on]:max-w-[80px]"
						data-state={filter.id === selectedFilter ? 'on' : 'off'}
					>
						{filter.label}
					</div>
				</div>
				{#if filter.id === selectedFilter}
					<div
						transition:slide={{ duration: 150 }}
						class="absolute right-2 flex h-full items-center text-primary-foreground"
					>
						<Button
							variant="ghost"
							size="icon"
							class="h-5 w-5 hover:bg-primary-foreground/20"
							onclick={() => (filterDialogOpen = true)}
						>
							<MoreVertical class="h-4 w-4" />
						</Button>
					</div>
				{/if}
			</ToggleGroup.Item>
		{/each}
	</ToggleGroup.Root>
</div>

<ResponsiveModal
	open={filterDialogOpen}
	title="Filter Events"
	onOpenChange={(isOpen) => (filterDialogOpen = isOpen)}
>
	<div class="grid gap-6">
		<div class="grid gap-2">
			<Label>Location</Label>
			<RadioGroup bind:value={selectedLocation}>
				{#each locations as location}
					<div class="flex items-center space-x-2">
						<RadioGroupItem value={location} id={location} />
						<Label for={location}>{location}</Label>
					</div>
				{/each}
			</RadioGroup>
		</div>
		<div class="grid gap-2">
			<Label>Genre</Label>
			<RadioGroup bind:value={selectedGenre}>
				{#each genres as genre}
					<div class="flex items-center space-x-2">
						<RadioGroupItem value={genre} id={genre} />
						<Label for={genre}>{genre}</Label>
					</div>
				{/each}
			</RadioGroup>
		</div>
		<div class="grid gap-2">
			<Label>Level</Label>
			<RadioGroup bind:value={selectedLevel}>
				{#each levels as level}
					<div class="flex items-center space-x-2">
						<RadioGroupItem value={level} id={level} />
						<Label for={level}>{level}</Label>
					</div>
				{/each}
			</RadioGroup>
		</div>
	</div>
</ResponsiveModal>
