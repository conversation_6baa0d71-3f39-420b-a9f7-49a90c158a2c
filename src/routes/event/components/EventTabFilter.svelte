<script lang="ts">
	import * as Tabs from '$lib/components/ui/tabs/index.js';
	import {
		startOfWeek,
		addDays,
		format,
		isSameDay,
		getDay,
		parseISO,
		isToday as isDateToday
	} from 'date-fns';
	import type { PageData } from '../$types';
	import { mode } from 'mode-watcher';

	interface Props {
		selectedDate: Date | null;
		events: PageData['events'];
		onDateSelect: (date: Date | null) => void;
	}

	let { selectedDate, events, onDateSelect }: Props = $props();

	// Generate the current week starting from Monday
	const weekStart = $derived(startOfWeek(new Date(), { weekStartsOn: 1 }));
	const weekDays = $derived([
		{ label: 'All', date: null },
		{ label: 'Mon', date: addDays(weekStart, 0), full: 'Monday' },
		{ label: 'Tue', date: addDays(weekStart, 1), full: 'Tuesday' },
		{ label: 'Wed', date: addDays(weekStart, 2), full: 'Wednesday' },
		{ label: 'Thu', date: addDays(weekStart, 3), full: 'Thursday' },
		{ label: 'Fri', date: addDays(weekStart, 4), full: 'Friday' },
		{ label: 'Sat', date: addDays(weekStart, 5), full: 'Saturday' },
		{ label: 'Sun', date: addDays(weekStart, 6), full: 'Sunday' }
	]);

	// Determine active tab based on selected date
	const activeTab = $derived(
		selectedDate === null
			? 'All'
			: (weekDays.find((day) => day.date && isSameDay(day.date, selectedDate))?.label ?? 'All')
	);

	// Count events for each day of the week
	const eventCountsByDay = $derived(() => {
		const counts = new Map([
			['All', events.length],
			['Mon', 0],
			['Tue', 0],
			['Wed', 0],
			['Thu', 0],
			['Fri', 0],
			['Sat', 0],
			['Sun', 0]
		]);

		events.forEach((event) => {
			if (event.start_at) {
				const eventDate = parseISO(event.start_at);
				const day = getDay(eventDate);
				// Convert from 0-6 (Sun-Sat) to our tab labels
				const dayLabels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
				const dayLabel = dayLabels[day];
				counts.set(dayLabel, (counts.get(dayLabel) || 0) + 1);
			}
		});

		return counts;
	});

	function handleTabChange(value: string) {
		const day = weekDays.find((day) => day.label === value);
		if (day) {
			onDateSelect(day.date);
		}
	}
</script>

<div class="bg-background sticky top-0 z-20 w-full shadow-sm">
	<Tabs.Root value={activeTab} onValueChange={handleTabChange} class="w-full">
		<div class="no-scrollbar overflow-x-auto">
			<Tabs.List
				class={mode.current === 'dark'
					? 'grid h-auto w-full grid-cols-8 rounded-lg border border-white/10 bg-white/5 backdrop-blur-md'
					: 'grid h-auto w-full grid-cols-8 rounded-lg border border-black/10 bg-black/5 backdrop-blur-md'}
			>
				{#each weekDays as day}
					{@const isActive = day.label === activeTab}
					{@const isDayToday = day.date && isDateToday(day.date)}
					<Tabs.Trigger
						value={day.label}
						class="data-[state=active]:text-primary relative flex flex-col items-center justify-center px-4 py-2 text-sm"
					>
						<span class={isDayToday ? 'text-primary font-semibold' : ''}>{day.label}</span>
						<span class="text-muted-foreground mt-1 text-xs">
							{eventCountsByDay().get(day.label) || 0}
						</span>
					</Tabs.Trigger>
				{/each}
			</Tabs.List>
		</div>
	</Tabs.Root>
</div>

<style>
	/* Hide scrollbar but keep functionality */
	.no-scrollbar {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}
	.no-scrollbar::-webkit-scrollbar {
		display: none;
	}
</style>
