<!-- Video player component -->
<script lang="ts">
	import { onDestroy } from 'svelte';

	interface Props {
		src: string;
		title: string;
		poster: string;
	}

	let { src, title, poster }: Props = $props();
	let videoElement: HTMLVideoElement;

	onDestroy(() => {
		if (videoElement) {
			videoElement.pause();
			videoElement.src = '';
			videoElement.load();
		}
	});
</script>

<video
	bind:this={videoElement}
	controls
	playsInline
	{src}
	{poster}
	{title}
	class="bg-muted-foreground/10"
>
	<track kind="captions" src="" label="English" srclang="en" />
	Your browser does not support the video tag.
</video>

<style>
	video {
		width: 100%;
		height: 100%;
		object-fit: contain;
		border-radius: 0;
	}
</style>
