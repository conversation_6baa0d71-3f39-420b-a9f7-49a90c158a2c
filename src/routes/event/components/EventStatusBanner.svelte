<!-- Event Status Banner Component -->
<script lang="ts">
	import { parseISO, differenceInSeconds, addHours, addMinutes } from 'date-fns';
	import { Timer, Clock, Sparkles, Navigation } from '@lucide/svelte';
	import { onMount } from 'svelte';
	import { fade, slide } from 'svelte/transition';
	import { cubicOut } from 'svelte/easing';
	import { dev as envDev } from '$app/environment';
	import AddressMapLink from './AddressMapLink.svelte';

	interface Props {
		startAt: string | null;
		durationMinute: number;
		className?: string;
		event?: any; // For AddressMapLink component
	}

	let { startAt, durationMinute, className = '', event }: Props = $props();

	// Reactive state
	let currentTime = $state(new Date());
	// Dev mode
	let dev = $state(false);
	let intervalId: ReturnType<typeof setInterval> | undefined;

	// Update current time every second
	onMount(() => {
		intervalId = setInterval(() => {
			currentTime = new Date();
		}, 1000);

		return () => {
			if (intervalId) clearInterval(intervalId);
		};
	});

	type EventStatus = 'future' | 'upcoming' | 'warmup' | 'inProgress' | 'ended';

	interface StatusInfo {
		status: EventStatus;
		progress: number;
		shouldShow: boolean;
		isInProgress: boolean;
		isUpcoming: boolean;
		isInWarmup: boolean;
		statusText: string;
		remainingSeconds: number;
	}

	// Dev mode: cycle through different statuses for testing
	let devStatusIndex = $state(0);
	const devStatuses: EventStatus[] = ['upcoming', 'warmup', 'inProgress'];

	$effect(() => {
		if (dev) {
			const interval = setInterval(() => {
				devStatusIndex = (devStatusIndex + 1) % devStatuses.length;
			}, 5000); // Change status every 5 seconds

			return () => clearInterval(interval);
		}
	});

	// Calculate event status and progress
	const statusInfo = $derived<StatusInfo>(
		dev
			? (() => {
					// Dev mode: force show status for testing
					const forcedStatus = devStatuses[devStatusIndex];
					let progress = 0;
					let remainingSeconds = 0;
					let statusText = '';

					switch (forcedStatus) {
						case 'upcoming':
							progress = 0.7; // 70% progress towards event start
							remainingSeconds = 1234; // About 20 minutes
							statusText = 'Starts in ';
							break;
						case 'warmup':
							progress = 0.1; // Just started
							statusText = 'Warm up';
							break;
						case 'inProgress':
							progress = 0.4; // 40% through the event
							statusText = 'In progress';
							break;
					}

					return {
						status: forcedStatus,
						progress,
						shouldShow: true,
						isInProgress: forcedStatus === 'inProgress' || forcedStatus === 'warmup',
						isUpcoming: forcedStatus === 'upcoming',
						isInWarmup: forcedStatus === 'warmup',
						statusText,
						remainingSeconds
					};
				})()
			: !startAt
				? {
						status: 'future' as EventStatus,
						progress: 0,
						shouldShow: false,
						isInProgress: false,
						isUpcoming: false,
						isInWarmup: false,
						statusText: '',
						remainingSeconds: 0
					}
				: (() => {
						const startDate = parseISO(startAt);
						const threeHoursAway = addHours(startDate, -3);
						const warmupEndTime = addMinutes(startDate, 15);
						const endDate = new Date(startDate.getTime() + durationMinute * 60 * 1000);

						let status: EventStatus;
						let progress = 0;
						let remainingSeconds = 0;

						if (currentTime >= endDate) {
							status = 'ended';
							progress = 1;
						} else if (currentTime >= startDate && currentTime < warmupEndTime) {
							status = 'warmup';
							// Overall progress from start to end
							progress =
								(currentTime.getTime() - startDate.getTime()) /
								(endDate.getTime() - startDate.getTime());
						} else if (currentTime >= startDate) {
							status = 'inProgress';
							progress =
								(currentTime.getTime() - startDate.getTime()) /
								(endDate.getTime() - startDate.getTime());
						} else if (currentTime >= threeHoursAway) {
							status = 'upcoming';
							// Progress is inverse of remaining time until start (1 when close, 0 when far)
							remainingSeconds = differenceInSeconds(startDate, currentTime);
							progress = 1 - remainingSeconds / (3 * 3600);
						} else {
							status = 'future';
							progress = 0;
						}

						// Clamp progress between 0 and 1
						progress = Math.max(0, Math.min(1, progress));

						const shouldShow = ['upcoming', 'warmup', 'inProgress'].includes(status);
						const isInProgress = status === 'inProgress' || status === 'warmup';
						const isUpcoming = status === 'upcoming';
						const isInWarmup = status === 'warmup';

						let statusText = '';
						switch (status) {
							case 'inProgress':
								statusText = 'In progress';
								break;
							case 'warmup':
								statusText = 'Warm up';
								break;
							case 'upcoming':
								statusText = 'Starts in ';
								break;
						}

						return {
							status,
							progress,
							shouldShow,
							isInProgress,
							isUpcoming,
							isInWarmup,
							statusText,
							remainingSeconds
						};
					})()
	);

	// Format remaining time
	function formatRemainingTime(seconds: number): string {
		const hours = Math.floor(seconds / 3600);
		const minutes = Math.floor((seconds % 3600) / 60);
		const secs = seconds % 60;

		if (hours > 0) {
			return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
		}
		return `${minutes}:${secs.toString().padStart(2, '0')}`;
	}

	// Icon animation classes
	const iconClasses = $derived(() => {
		if (statusInfo.isInWarmup) {
			return 'text-orange-500 animate-pulse';
		} else if (statusInfo.isInProgress) {
			return 'text-primary animate-bounce';
		} else if (statusInfo.isUpcoming) {
			return 'text-primary/80 animate-pulse';
		}
		return '';
	});
</script>

{#if statusInfo.shouldShow}
	<div
		class="w-full {className}"
		in:slide={{ duration: 300, easing: cubicOut }}
		out:slide={{ duration: 200, easing: cubicOut }}
	>
		<!-- Status content -->
		<div
			class="relative overflow-hidden rounded-lg border backdrop-blur-sm {statusInfo.isInWarmup
				? 'border-orange-400/40 bg-linear-to-r from-orange-50/60 to-orange-100/40 dark:from-orange-950/30 dark:to-orange-900/20'
				: statusInfo.isInProgress
					? 'border-primary/40 bg-linear-to-r from-primary/10 to-primary/5'
					: 'from-primary/8 to-primary/3 border-primary/25 bg-linear-to-r'}"
		>
			<!-- Progress track -->
			<div
				class="absolute inset-0 {statusInfo.isInWarmup
					? 'bg-linear-to-r from-orange-500/15 to-orange-400/10'
					: statusInfo.isInProgress
						? 'to-primary/8 bg-linear-to-r from-primary/15'
						: 'from-primary/12 to-primary/6 bg-linear-to-r'}"
			></div>

			<!-- Progress bar -->
			<div class="absolute inset-0">
				<div
					class="relative h-full transition-all duration-500 ease-out {statusInfo.isInWarmup
						? 'bg-linear-to-r from-orange-500/70 via-orange-400/60 to-orange-500/50'
						: statusInfo.isInProgress
							? 'bg-linear-to-r from-primary/70 via-primary/55 to-primary/45'
							: 'bg-linear-to-r from-primary/60 via-primary/45 to-primary/35'}"
					style="width: {statusInfo.progress * 100}%"
				>
					<!-- Enhanced shine effect -->
					<div class="absolute inset-0 bg-linear-to-r from-white/0 via-white/25 to-white/0"></div>
					<!-- Secondary highlight -->
					<div
						class="absolute inset-x-0 top-0 h-1/2 bg-linear-to-r from-white/0 via-white/15 to-white/0"
					></div>
				</div>
			</div>

			<!-- Content -->
			<div class="relative flex items-center justify-between px-3 py-2">
				<!-- Status label with icon and timer -->
				<div class="flex items-center gap-2">
					<!-- Icon -->
					<div class="h-4 w-4 {iconClasses}">
						{#if statusInfo.isInWarmup}
							<Clock class="h-full w-full" />
						{:else if statusInfo.isInProgress}
							<Sparkles class="h-full w-full" />
						{:else if statusInfo.isUpcoming}
							<Timer class="h-full w-full" />
						{/if}
					</div>

					<!-- Status text -->
					<span class="text-sm font-medium">
						{#if dev}
							<span class="font-semibold text-orange-600 drop-shadow-sm dark:text-orange-400"
								>[DEV]
							</span>
						{/if}
						<span
							class="text-foreground drop-shadow-sm {statusInfo.isInWarmup
								? 'text-orange-800 dark:text-orange-200'
								: 'text-foreground'}">{statusInfo.statusText}</span
						>
						{#if statusInfo.isUpcoming}
							<span
								class="font-mono font-semibold text-foreground drop-shadow-sm {statusInfo.isInWarmup
									? 'text-orange-900 dark:text-orange-100'
									: 'text-foreground'}"
							>
								{formatRemainingTime(statusInfo.remainingSeconds)}
							</span>
						{/if}
					</span>
				</div>

				<!-- Navigation or Progress indicator -->
				<div class="flex items-center gap-2">
					{#if event}
						<AddressMapLink {event}>
							<div
								class="flex items-center gap-1.5 rounded-md px-2 py-1 text-xs font-medium text-muted-foreground transition-colors hover:bg-black/5 hover:text-foreground dark:hover:bg-white/5"
							>
								<Navigation class="h-3.5 w-3.5" />
								<span class="hidden sm:inline">Navigate</span>
							</div>
						</AddressMapLink>
					{:else}
						<div class="flex items-center gap-2 text-xs text-muted-foreground">
							{#if statusInfo.isInProgress}
								<span class="font-medium">{Math.round(statusInfo.progress * 100)}%</span>
							{/if}
							<div class="h-1.5 w-1.5 rounded-full bg-current opacity-40"></div>
						</div>
					{/if}
				</div>
			</div>
		</div>
	</div>
{/if}

<style>
	@keyframes pulse {
		0%,
		100% {
			opacity: 1;
		}
		50% {
			opacity: 0.5;
		}
	}

	@keyframes bounce {
		0%,
		100% {
			transform: translateY(-25%);
			animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
		}
		50% {
			transform: translateY(0);
			animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
		}
	}

	.animate-pulse {
		animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}

	.animate-bounce {
		animation: bounce 1s infinite;
	}
</style>
