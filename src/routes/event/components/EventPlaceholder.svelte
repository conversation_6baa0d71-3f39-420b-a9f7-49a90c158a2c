<script lang="ts">
	interface Props {
		title: string;
		className?: string;
		height?: string;
	}

	let { title, className = '', height = '100%' }: Props = $props();

	// Generate a unique but stable color based on the title
	function generateGradientColors(text: string): [string, string, string] {
		// Simple hash function to generate a number from text
		const hash = Array.from(text).reduce(
			(hash, char) => ((hash << 5) - hash + char.charCodeAt(0)) | 0,
			0
		);

		// Use the hash to generate three different hues for a more dynamic gradient
		const hue1 = Math.abs(hash % 360);
		const hue2 = (hue1 + 40 + (hash % 180)) % 360;
		const hue3 = (hue1 + 120 + (hash % 90)) % 360;

		// Keep saturation and lightness low for subtlety
		return [`hsl(${hue1}, 35%, 85%)`, `hsl(${hue2}, 40%, 75%)`, `hsl(${hue3}, 30%, 80%)`];
	}

	const [color1, color2, color3] = generateGradientColors(title);

	// Generate the first two letters or icon from the title
	const initialLetters = title
		.split(' ')
		.slice(0, 2)
		.map((word) => word.charAt(0).toUpperCase())
		.join('');
</script>

<div
	class={`flex w-full items-center justify-center overflow-hidden text-4xl font-semibold text-white/90 ${className} relative`}
	style={`background: linear-gradient(135deg, ${color1}, ${color2}, ${color3}); height: ${height};`}
>
	<div class="absolute inset-0 opacity-30">
		<div
			class="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(255,255,255,0.15),transparent_50%)]"
		></div>
		<div
			class="absolute inset-0 bg-[radial-gradient(circle_at_80%_80%,rgba(255,255,255,0.1),transparent_40%)]"
		></div>
	</div>

	<div class="relative flex h-full w-full flex-col items-center justify-center space-y-2 py-8">
		<div
			class="flex h-16 w-16 items-center justify-center rounded-full bg-white/10 backdrop-blur-sm"
		>
			<span class="text-3xl">{initialLetters}</span>
		</div>
	</div>
</div>
