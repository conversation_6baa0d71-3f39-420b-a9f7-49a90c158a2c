<!-- Payment Option Selector Component -->
<script lang="ts">
	import { getLocalizedText } from '$lib/utils/localization';
	import type {
		LocalizedText,
		ConsumerItem,
		OrderPriceRecord,
		ProductPriceItem,
		PriceOptionCombination,
		ProductDetails
	} from '../../types';
	import UnitsBuyingOptions from './UnitsBuyingOptions.svelte';
	import MoneyBuyingOptions from './MoneyBuyingOptions.svelte';
	import CancellationPolicy from './CancellationPolicy.svelte';

	interface Props {
		consumerItem: ConsumerItem;
		isWalkinMode: boolean;
		selectedOptionId: string | null;
		onOptionSelect: (optionId: string) => void;
		selectedProduct?: ProductDetails | null;
		brand: App.Locals['brand'];
	}

	let {
		consumerItem = $bindable(),
		isWalkinMode = $bindable(),
		selectedOptionId = $bindable(),
		onOptionSelect,
		selectedProduct = $bindable(),
		brand = $bindable()
	}: Props = $props();

	// Use proper Svelte 5 runes syntax for product prices - clearer naming
	let productPriceItems = $derived(consumerItem.product_prices || []);
	let showProcessingFeeNotice = $derived(
		!isWalkinMode &&
			brand.stripe_fee_paid_by === 'payer' &&
			productPriceItems.some((item) => (item.price_option_combinations ?? []).length > 0)
	);
</script>

{#each productPriceItems as productPriceItem}
	<div class="w-full overflow-hidden border-t pt-6 first:border-t-0 first:pt-0">
		<h3 class="break-words text-lg font-medium">
			{getLocalizedText(productPriceItem.product_price?.price?.title as LocalizedText)}
		</h3>

		<p class="mt-1 text-sm text-muted-foreground">
			This {(consumerItem.product_count ?? 1) > 1 ? 'bundle' : 'session'} costs {(
				productPriceItem.consumer_total_units_cost ?? 0
			).toFixed(1)} points.
		</p>

		{#if isWalkinMode && (productPriceItem.consumer_total_units_remaining ?? 0) < 0}
			<div
				class="rounded-lg border border-red-200 bg-red-50 p-3 text-sm text-red-800 dark:border-red-800 dark:bg-red-950/30 dark:text-red-300"
			>
				<p><strong>Insufficient points for walk-in registration.</strong></p>
				<p class="mt-1">
					This profile currently has {(
						productPriceItem.consumer_total_units_available ?? 0
					).toFixed(1)} points, but needs {(
						productPriceItem.consumer_total_units_cost ?? 0
					).toFixed(1)} points to register.
				</p>
				<p class="mt-1">
					Missing: {Math.abs(productPriceItem.consumer_total_units_remaining ?? 0).toFixed(1)} points.
				</p>
			</div>
		{:else}
			<p class="mt-1 text-sm text-muted-foreground">
				{#if (productPriceItem.consumer_total_units_available ?? 0) > 0}
					{isWalkinMode ? 'Profile' : 'You'}
					{isWalkinMode ? 'has' : 'have'}
					{(productPriceItem.consumer_total_units_available ?? 0).toFixed(1)} points
					{#if (productPriceItem.consumer_total_units_remaining ?? 0) < 0}
						and need {Math.abs(productPriceItem.consumer_total_units_remaining ?? 0).toFixed(1)} more.
					{:else}
						available.
					{/if}
				{:else}
					{isWalkinMode ? 'This profile needs' : 'You need'} to purchase points to register.
				{/if}
			</p>
		{/if}

		<!-- Buying options container with proper constraints -->
		<div class="mt-4 w-full overflow-hidden">
			{#if productPriceItem.order_prices && productPriceItem.order_prices.length > 0 && (productPriceItem.consumer_total_units_remaining ?? 0) >= 0}
				<UnitsBuyingOptions
					options={productPriceItem.order_prices.map((orderPriceItem: any) => {
						return {
							id: orderPriceItem.order_price?.id ?? '',
							order_price_record: orderPriceItem.order_price as OrderPriceRecord
						};
					})}
					{selectedOptionId}
					onSelect={onOptionSelect}
				/>
			{:else if (productPriceItem.price_option_combinations ?? []).length > 0 && !isWalkinMode}
				<MoneyBuyingOptions
					options={(productPriceItem.price_option_combinations ?? []).map(
						(priceOptionCombo: PriceOptionCombination) => priceOptionCombo.options ?? []
					)}
					{selectedOptionId}
					onSelect={(ids: string[]) => {
						console.log('MoneyBuyingOptions - Selected IDs:', ids);
						onOptionSelect(ids.join(','));
					}}
				/>
			{/if}
		</div>

		{#if productPriceItem.product_price?.auto_cancel_at_far || productPriceItem.product_price?.auto_cancel_at_near}
			<div class="mt-6">
				<h4 class="mb-2 text-sm font-medium">Cancellation Policy</h4>
				<CancellationPolicy
					firstEventStartDate={new Date(selectedProduct?.auto_first_event_start_at || new Date())}
					autoCancelAtFar={productPriceItem.product_price?.auto_cancel_at_far
						? new Date(productPriceItem.product_price.auto_cancel_at_far)
						: undefined}
					autoCancelAtFarReturnUnits={productPriceItem.product_price
						?.auto_cancel_at_far_return_units ?? undefined}
					autoCancelAtNear={productPriceItem.product_price?.auto_cancel_at_near
						? new Date(productPriceItem.product_price.auto_cancel_at_near)
						: undefined}
					autoCancelAtNearReturnUnits={productPriceItem.product_price
						?.auto_cancel_at_near_return_units ?? undefined}
				/>
			</div>
		{/if}
	</div>
{/each}

<!-- Processing Fee Notice - Only show for non-walkin mode, when money options exist, and brand is configured for payer to pay fees -->
{#if showProcessingFeeNotice}
	<div
		class="mt-6 rounded-lg border border-slate-200 bg-slate-50 p-3 text-xs text-slate-700 dark:border-slate-800 dark:bg-slate-900/40 dark:text-slate-400"
	>
		<p>
			<strong>Note:</strong> A non-refundable bank processing fee will be added at checkout. This fee
			is collected by the payment processor and goes directly to cover transaction costs.
		</p>
	</div>
{/if}
