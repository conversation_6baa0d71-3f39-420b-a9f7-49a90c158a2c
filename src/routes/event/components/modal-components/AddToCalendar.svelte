<script lang="ts">
	import { CalendarPlus } from '@lucide/svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import type { ProductDetails } from '../../types';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { LocalizedText } from '../../types';

	interface Props {
		product: ProductDetails;
	}

	let { product }: Props = $props();

	// Sort events by start_at date
	const sortedEvents = $derived(() => {
		if (!product?.event_product) return [];

		return [...product.event_product]
			.filter((ep) => ep.kind === 'product_primary')
			.sort((a, b) => {
				const dateA = new Date(a.event.start_at).getTime();
				const dateB = new Date(b.event.start_at).getTime();
				return dateA - dateB;
			});
	});
	// Helper function to get address from the landmark
	function getLandmarkAddress(event: any): string | null {
		// First try direct access to address data
		if (event.landmark?.address?.auto_normalized_address_local) {
			return event.landmark.address.auto_normalized_address_local;
		}

		if (event.space?.landmark?.address?.auto_normalized_address_local) {
			return event.space.landmark.address.auto_normalized_address_local;
		}

		return null;
	}

	function generateICalendarFile() {
		if (sortedEvents().length === 0) return;

		// Start constructing iCalendar content
		let icalContent = [
			'BEGIN:VCALENDAR',
			'VERSION:2.0',
			'PRODID:-//HanaWeb//Calendar Events//EN',
			'CALSCALE:GREGORIAN',
			'METHOD:PUBLISH'
		];

		// Add each event
		sortedEvents().forEach((eventProduct) => {
			const event = eventProduct.event;
			if (!event) return;

			// Parse dates
			const startDate = new Date(event.start_at);
			const endDate = new Date(startDate.getTime() + (event.duration_minute || 60) * 60 * 1000);

			// Format dates for iCalendar (YYYYMMDDTHHMMSSZ)
			const formatDate = (date: Date) => {
				return date.toISOString().replace(/[-:]/g, '').replace(/\.\d+/g, '');
			};

			// Create unique identifier
			const uid = `event-${event.id}@hana.app`;

			// Get event details with fallbacks
			const title = getLocalizedText(event.metadata?.auto_final_title as LocalizedText) || 'Event';
			const landmarkName =
				getLocalizedText(event.landmark?.title_short as LocalizedText) || 'Event Location';

			// Get address if available
			const address = getLandmarkAddress(event);

			// Format location for best calendar app compatibility - use just address for Apple
			const location = address || landmarkName;

			const description =
				getLocalizedText(event.metadata?.auto_final_subtitle as LocalizedText) || '';

			// Add event to calendar
			icalContent = [
				...icalContent,
				'BEGIN:VEVENT',
				`UID:${uid}`,
				`DTSTAMP:${formatDate(new Date())}`,
				`DTSTART:${formatDate(startDate)}`,
				`DTEND:${formatDate(endDate)}`,
				`SUMMARY:${title}`,
				`LOCATION:${location}`,
				`DESCRIPTION:${description}`,
				'END:VEVENT'
			];
		});

		// Close the calendar
		icalContent.push('END:VCALENDAR');

		// Create downloadable file
		const blob = new Blob([icalContent.join('\r\n')], {
			type: 'text/calendar;charset=utf-8'
		});

		// Create a more descriptive filename using event information
		const firstEventTitle = getLocalizedText(
			sortedEvents()[0].event.metadata?.auto_final_title as LocalizedText
		);
		const firstEventDate = new Date(sortedEvents()[0].event.start_at);
		const formattedDate = firstEventDate.toISOString().split('T')[0]; // YYYY-MM-DD format
		const eventCount = sortedEvents().length;

		// Create filename pattern: EventName_YYYY-MM-DD.ics or EventName_YYYY-MM-DD_Plus2Events.ics
		let filename = `${firstEventTitle}_${formattedDate}`;
		if (eventCount > 1) {
			filename += `_Plus${eventCount - 1}Events`;
		}
		filename = filename.replace(/[/\\?%*:|"<>]/g, '-') + '.ics'; // Replace invalid filename characters

		const link = document.createElement('a');
		link.href = URL.createObjectURL(blob);
		link.download = filename;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}

	function addToCalendarGoogle() {
		if (sortedEvents().length === 0) return;

		// Only supporting first event for Google Calendar due to limitations
		const firstEvent = sortedEvents()[0].event;

		const startDate = new Date(firstEvent.start_at);
		const endDate = new Date(startDate.getTime() + (firstEvent.duration_minute || 60) * 60 * 1000);

		// Format dates for Google Calendar (YYYYMMDDTHHMMSSZ)
		const formatDateGoogle = (date: Date) => {
			return date.toISOString().replace(/[-:]/g, '').replace(/\.\d+/g, '');
		};

		const title =
			getLocalizedText(firstEvent.metadata?.auto_final_title as LocalizedText) || 'Event';
		const landmarkName = getLocalizedText(firstEvent.landmark?.title_short as LocalizedText) || '';

		// Get address if available
		const address = getLandmarkAddress(firstEvent);

		// For Google Calendar, include both landmark name and address if available
		// or just the name if address isn't available
		const location = address ? `${landmarkName}, ${address}` : landmarkName;

		const description =
			getLocalizedText(firstEvent.metadata?.auto_final_subtitle as LocalizedText) || '';

		const googleUrl = new URL('https://calendar.google.com/calendar/render');
		googleUrl.searchParams.append('action', 'TEMPLATE');
		googleUrl.searchParams.append('text', title);
		googleUrl.searchParams.append(
			'dates',
			`${formatDateGoogle(startDate)}/${formatDateGoogle(endDate)}`
		);
		googleUrl.searchParams.append('details', description);
		googleUrl.searchParams.append('location', location);
		googleUrl.searchParams.append('sf', 'true');
		googleUrl.searchParams.append('output', 'xml');

		window.open(googleUrl.toString(), '_blank');
	}

	function handleAddToCalendar() {
		// Detect Apple devices (iOS, iPadOS, macOS)
		const isAppleDevice = /iPhone|iPad|iPod|Mac/.test(navigator.userAgent);

		if (isAppleDevice) {
			// Use downloadable .ics file which works better on Apple devices
			generateICalendarFile();
		} else {
			// For other devices, try Google Calendar first
			addToCalendarGoogle();
		}
	}
</script>

<Button
	variant="ghost"
	size="sm"
	onclick={handleAddToCalendar}
	title="Add events to calendar"
	class="ml-1 text-muted-foreground hover:text-foreground"
>
	<CalendarPlus class="size-4" />
	<span class="ml-1 hidden text-xs sm:inline">Add to Calendar</span>
</Button>
