<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import * as RadioGroup from '$lib/components/ui/radio-group/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { getLocalizedText } from '$lib/utils/localization';
	import { Check, Clock } from '@lucide/svelte';
	import type { PriceOptionItem } from '../../types';

	interface Props {
		options: PriceOptionItem[][];
		selectedOptionId: string | null;
		onSelect: (ids: string[]) => void;
	}

	let { options, selectedOptionId, onSelect }: Props = $props();

	function formatMoney(moneyInt: number | undefined | null, currency: string = 'USD'): string {
		if (typeof moneyInt !== 'number') return '$0';
		const amount = moneyInt / 100;
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency,
			minimumFractionDigits: Number.isInteger(amount) ? 0 : 2
		}).format(amount);
	}

	function getTotalMoney(options: PriceOptionItem[]): { total: number; currency: string } {
		return options.reduce(
			(acc, option) => ({
				total: acc.total + (option.price_option?.money_int ?? 0),
				currency: option.price_option?.currency_code ?? 'USD'
			}),
			{ total: 0, currency: 'USD' }
		);
	}

	function getTotalPoints(options: PriceOptionItem[]): number {
		return options.reduce((acc, option) => acc + (option.total_units ?? 0), 0);
	}

	function formatValidity(option: PriceOptionItem): string {
		if (!option.price_option?.valid_for_minute) return '';

		// Convert minutes to appropriate time unit
		const minutes = option.price_option.valid_for_minute;

		if (minutes >= 525600) {
			// 365 days in minutes
			const years = Math.floor(minutes / 525600);
			return `${years} ${years === 1 ? 'year' : 'years'}`;
		} else if (minutes >= 43200) {
			// 30 days in minutes
			const months = Math.floor(minutes / 43200);
			return `${months} ${months === 1 ? 'month' : 'months'}`;
		} else if (minutes >= 1440) {
			// 1 day in minutes
			const days = Math.floor(minutes / 1440);
			return `${days} ${days === 1 ? 'day' : 'days'}`;
		} else if (minutes >= 60) {
			// 1 hour in minutes
			const hours = Math.floor(minutes / 60);
			return `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
		} else {
			return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`;
		}
	}

	function getOptionSummary(optionArray: PriceOptionItem[]): string {
		const titles = optionArray
			.map((option) => option.price_option && getLocalizedText(option.price_option.title))
			.filter(Boolean);

		// If all titles are the same, just return one with the count
		const uniqueTitles = [...new Set(titles)];
		if (uniqueTitles.length === 1 && titles.length > 1) {
			return `${titles.length}x ${uniqueTitles[0]}`;
		}

		return titles.join(', ');
	}
</script>

<div class="w-full overflow-x-hidden">
	<RadioGroup.Root
		value={selectedOptionId ?? ''}
		onValueChange={(value) => onSelect(value.split(','))}
	>
		<div class="space-y-4">
			{#each options ?? [] as optionArray, i}
				{#if optionArray?.length > 0}
					{@const total = getTotalMoney(optionArray)}
					{@const totalPoints = getTotalPoints(optionArray)}
					{@const optionId = optionArray.map((opt) => opt.price_option?.id).join(',')}
					{@const isSelected = selectedOptionId === optionId}
					<div class="w-full">
						<div
							class="group flex items-center gap-4 rounded-lg border bg-card p-4 text-card-foreground transition-all hover:border-primary/50 hover:shadow-sm {isSelected
								? 'border-primary/70 bg-primary/5 dark:bg-primary/10'
								: ''}"
						>
							<RadioGroup.Item value={optionId} id={optionId} class="h-5 w-5 shrink-0" />
							<Label for={optionId} class="flex min-w-0 flex-1 cursor-pointer overflow-hidden">
								<div class="flex min-w-0 flex-1 flex-col gap-4 sm:flex-row sm:justify-between">
									<div class="min-w-0 overflow-hidden">
										<div class="flex flex-wrap items-center gap-2">
											<h3 class="text-lg font-medium {isSelected ? 'text-primary' : ''}">
												{formatMoney(total.total, total.currency)}
											</h3>
											<div
												class="rounded-full bg-muted px-2.5 py-0.5 text-xs font-medium text-muted-foreground"
											>
												{totalPoints} points
											</div>
										</div>
										<div class="mt-2 overflow-hidden text-sm text-muted-foreground">
											<p class="truncate font-medium">{getOptionSummary(optionArray)}</p>
										</div>
									</div>

									<div
										class="flex shrink-0 flex-col items-start justify-center space-y-2 sm:items-end"
									>
										{#each optionArray as option}
											{#if option?.price_option && formatValidity(option)}
												<div
													class="flex items-center gap-2 rounded-full bg-muted/50 px-2.5 py-1 text-xs {isSelected
														? 'bg-primary/10 dark:bg-primary/20'
														: ''}"
												>
													<Clock size={12} class="shrink-0 text-muted-foreground" />
													{#if optionArray.length > 1}
														<span class="truncate font-medium text-muted-foreground"
															>{getLocalizedText(option.price_option.title)}:</span
														>
													{/if}
													<span
														class="whitespace-nowrap font-semibold {isSelected
															? 'text-primary'
															: ''}"
													>
														{formatValidity(option)}
													</span>
												</div>
											{/if}
										{/each}
									</div>
								</div>
							</Label>
						</div>
					</div>
				{/if}
			{/each}
		</div>
	</RadioGroup.Root>
</div>
