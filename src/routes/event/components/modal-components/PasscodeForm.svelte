<!-- Passcode Form Component -->
<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Loader2 } from '@lucide/svelte';

	interface Props {
		onSubmit: (passcode: string) => Promise<void>;
	}

	let { onSubmit }: Props = $props();

	let passcode = $state('');
	let isSubmitting = $state(false);
	let error = $state<string | null>(null);

	async function handleSubmit(e: Event) {
		e.preventDefault();

		if (!passcode.trim()) {
			error = 'Please enter a passcode';
			return;
		}

		error = null;
		isSubmitting = true;

		try {
			await onSubmit(passcode);
			passcode = ''; // Clear the input after successful submission
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to verify passcode';
		} finally {
			isSubmitting = false;
		}
	}
</script>

<div class="space-y-4">
	<p class="font-medium">Enter Passcode</p>

	<form class="flex gap-2" onsubmit={handleSubmit}>
		<Input
			placeholder="Enter passcode"
			bind:value={passcode}
			disabled={isSubmitting}
			class="flex-1"
			autocomplete="off"
		/>

		<Button type="submit" disabled={isSubmitting}>
			{#if isSubmitting}
				<span class="flex items-center">
					<Loader2 class="mr-2 h-4 w-4 animate-spin" />
					Verifying
				</span>
			{:else}
				Submit
			{/if}
		</Button>
	</form>

	{#if error}
		<p class="text-sm text-destructive">{error}</p>
	{/if}

	<p class="text-sm text-muted-foreground">
		Enter the passcode to access booking options for this product.
	</p>
</div>
