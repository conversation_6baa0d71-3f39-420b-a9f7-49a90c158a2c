<script lang="ts">
	import { format, parseISO, isPast } from 'date-fns';
	import { slide } from 'svelte/transition';
	import { getLocalizedText } from '$lib/utils/localization';
	import { Badge } from '$lib/components/ui/badge';
	import { Star } from '@lucide/svelte';

	interface Props {
		event: {
			id: string;
			start_at: string;
			duration_minute: number;
			metadata: {
				auto_final_title: any;
				auto_final_subtitle: any;
			};
			landmark?: {
				id: string;
				title_short: any;
			};
			space?: {
				name_short: any;
			};
		};
		showBorder?: boolean;
		sessionNumber?: number;
		isCurrentEvent?: boolean;
	}

	let { event, showBorder = false, sessionNumber, isCurrentEvent = false }: Props = $props();

	const date = $derived(parseISO(event.start_at));
	const isEventPast = $derived(isPast(date));
</script>

<div
	class="flex gap-3 {showBorder ? 'border-muted/30 border-t pt-4' : ''} {isEventPast
		? 'opacity-60'
		: ''}"
	transition:slide
>
	<div class="flex-none">
		<!-- Date card with session number header -->
		<div class="bg-background w-13 overflow-hidden rounded-lg border">
			{#if sessionNumber}
				<!-- Session number header with full-width background -->
				<div class="bg-muted flex h-4 items-center justify-center">
					<span class="text-muted-foreground text-[10px] font-medium">
						{sessionNumber}
					</span>
				</div>
			{/if}
			<!-- Date content area -->
			<div
				class="flex {sessionNumber
					? 'h-13'
					: 'h-17'} flex-col items-center justify-center text-center"
			>
				<span class="text-primary text-[11px] font-medium uppercase">{format(date, 'MMM')}</span>
				<span class="text-lg leading-none font-bold">{format(date, 'd')}</span>
				<span class="text-muted-foreground mt-0.5 text-[10px]">{format(date, 'EEE')}</span>
			</div>
		</div>
	</div>
	<div class="min-w-0 flex-1">
		<div class="flex items-start justify-between gap-4">
			<div class="min-w-0">
				<p class="line-clamp-2 text-sm font-medium {isEventPast ? 'text-muted-foreground' : ''}">
					{getLocalizedText(event.metadata?.auto_final_title)}
				</p>
			</div>
			<div class="flex-none text-right text-xs">
				<span class={isEventPast ? 'text-muted-foreground' : ''}>
					{format(date, 'h:mm a')}
					<span class="text-muted-foreground ml-1">· {event.duration_minute}m</span>
				</span>
			</div>
		</div>
		<div class="mt-0.5 flex items-start justify-between gap-4">
			<div class="min-w-0 space-y-0.5">
				{#if event.space?.name_short || event.landmark?.title_short}
					<p class="text-muted-foreground truncate text-xs">
						{event.space?.name_short ? getLocalizedText(event.space.name_short) : ''}
						{event.landmark?.title_short ? getLocalizedText(event.landmark.title_short) : ''}
					</p>
				{/if}
				{#if event.metadata?.auto_final_subtitle}
					<p class="text-muted-foreground truncate text-xs">
						{getLocalizedText(event.metadata?.auto_final_subtitle)}
					</p>
				{/if}
			</div>
			<div class="flex-none">
				<div class="space-y-0.5 text-xs">
					{#if isCurrentEvent}
						<div class="flex justify-end">
							<span
								title="This is the event you clicked from"
								class="text-muted-foreground flex items-center gap-1"
							>
								<Star class="h-3 w-3" />
								<span class="hidden text-xs sm:inline">Viewing</span>
							</span>
						</div>
					{/if}
					{#if isEventPast}
						<div class="flex justify-end">
							<span class="text-muted-foreground text-xs">Ended</span>
						</div>
					{/if}
				</div>
			</div>
		</div>
	</div>
</div>
