<script lang="ts">
	import { Card } from '$lib/components/ui/card';
	import { formatDistanceToNow, formatDistance } from 'date-fns';

	interface CancellationPeriod {
		start: Date;
		end: Date;
		type: 'firstPeriod' | 'secondPeriod' | 'thirdPeriod';
		refundAmount?: number;
	}

	interface Props {
		firstEventStartDate: Date;
		autoCancelAtFar: Date | undefined;
		autoCancelAtFarReturnUnits: number | undefined;
		autoCancelAtNear: Date | undefined;
		autoCancelAtNearReturnUnits: number | undefined;
	}

	let {
		firstEventStartDate,
		autoCancelAtFar,
		autoCancelAtFarReturnUnits,
		autoCancelAtNear,
		autoCancelAtNearReturnUnits
	}: Props = $props();

	let periods = $derived(calculatePeriods());
	let currentPeriod = $derived(getCurrentPeriod());

	function calculatePeriods(): CancellationPeriod[] {
		const now = new Date();
		const periods: CancellationPeriod[] = [];

		if (autoCancelAtFar && autoCancelAtFar > now) {
			periods.push({
				start: now,
				end: autoCancelAtFar,
				type: 'firstPeriod',
				refundAmount: autoCancelAtFarReturnUnits
			});
		}

		if (autoCancelAtNear && autoCancelAtNear > now) {
			periods.push({
				start: autoCancelAtFar || now,
				end: autoCancelAtNear,
				type: 'secondPeriod',
				refundAmount: autoCancelAtNearReturnUnits
			});
		}

		if (firstEventStartDate > now) {
			periods.push({
				start: autoCancelAtNear || autoCancelAtFar || now,
				end: firstEventStartDate,
				type: 'thirdPeriod'
			});
		}

		return periods;
	}

	function getCurrentPeriod(): CancellationPeriod | null {
		const now = new Date();
		return periods.find((period) => now >= period.start && now < period.end) || null;
	}

	function formatDate(date: Date): string {
		return date.toLocaleString(undefined, {
			month: 'numeric',
			day: 'numeric',
			hour: 'numeric',
			minute: '2-digit'
		});
	}

	function formatDuration(start: Date, end: Date): string {
		const hours = Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60));
		if (hours >= 24) {
			const days = Math.round(hours / 24);
			return `${days} ${days === 1 ? 'day' : 'days'}`;
		}
		return `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
	}

	function getColorForPeriod(type: CancellationPeriod['type']): string {
		switch (type) {
			case 'firstPeriod':
				return 'bg-green-500';
			case 'secondPeriod':
				return 'bg-yellow-500';
			case 'thirdPeriod':
				return 'bg-gray-500';
		}
	}

	function formatRefundPolicy(date: Date | undefined, returnUnits: number | undefined): string {
		if (!date || typeof returnUnits !== 'number') return '';
		return `Cancel before ${formatDuration(date, firstEventStartDate)} before event start to get ${returnUnits}pts refund`;
	}
</script>

<Card class="p-4">
	{#if firstEventStartDate <= new Date()}
		<p class="text-sm text-muted-foreground">
			Refunds are no longer available since the first session has already started
		</p>
	{:else}
		<div class="space-y-4">
			<!-- Timeline visualization -->
			<div class="relative h-8">
				{#each periods as period, i}
					<div
						class="absolute h-2 {getColorForPeriod(period.type)} {i === 0 ? 'rounded-l' : ''} {i ===
						periods.length - 1
							? 'rounded-r'
							: ''}"
						style="left: {((period.start.getTime() - periods[0].start.getTime()) /
							(firstEventStartDate.getTime() - periods[0].start.getTime())) *
							100}%;
                   width: {((period.end.getTime() - period.start.getTime()) /
							(firstEventStartDate.getTime() - periods[0].start.getTime())) *
							100}%;"
					></div>
				{/each}

				<!-- Current time marker -->
				<div
					class="absolute h-8 w-0.5 bg-red-500 transition-all"
					style="left: {((new Date().getTime() - periods[0].start.getTime()) /
						(firstEventStartDate.getTime() - periods[0].start.getTime())) *
						100}%;"
				>
					<div class="mt-8 text-xs text-red-500">Now</div>
				</div>

				<!-- Event start marker -->
				<div class="absolute h-8 w-0.5 bg-blue-500 transition-all" style="left: 100%;">
					<div class="absolute mt-8 -translate-x-full text-xs text-blue-500">Start</div>
				</div>
			</div>

			<!-- Period details -->
			<div class="space-y-2">
				{#each periods as period}
					<div class="flex items-center gap-2">
						<div class={`h-3 w-3 rounded-full ${getColorForPeriod(period.type)}`}></div>
						<div class="flex-1">
							{#if period.refundAmount !== undefined}
								<span class="text-sm">
									{period.refundAmount} point{period.refundAmount === 1 ? '' : 's'} refund until {formatDuration(
										period.end,
										firstEventStartDate
									)} before start
								</span>
							{:else}
								<span class="text-sm">No refunds available</span>
							{/if}
						</div>
						<div class="text-sm text-muted-foreground">
							{formatDate(period.end)}
						</div>
					</div>
				{/each}
			</div>
		</div>
	{/if}
</Card>
