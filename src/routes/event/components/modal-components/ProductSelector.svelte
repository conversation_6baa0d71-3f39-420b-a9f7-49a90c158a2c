<!-- Product Selector Component -->
<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { buttonVariants } from '$lib/components/ui/button/index.js';
	import { ChevronDown } from '@lucide/svelte';
	import type { PageData } from '../../$types';

	interface Props {
		products: PageData['events'][number]['products'];
		selectedProductId: string;
		onProductSelect: (productId: string) => void;
	}

	let {
		products = $bindable(),
		selectedProductId = $bindable(),
		onProductSelect
	}: Props = $props();

	// Using $state instead of $derived for the map
	let productDetailsMap = $state(new Map<string, { eventCount: number; pointsCost: number }>());

	// Update the map whenever products change
	$effect(() => {
		const newMap = new Map<string, { eventCount: number; pointsCost: number }>();

		if (products?.length) {
			for (const { product } of products) {
				newMap.set(product.id, {
					eventCount: getEventCount(product.id),
					pointsCost: product.product_price?.[0]?.cost_units ?? 0
				});
			}
		}

		productDetailsMap = newMap;
	});

	// Helper function to get event count for a product
	function getEventCount(productId: string): number {
		// Find the product first
		const product = products?.find(
			(p: { product: { id: string } }) => p.product.id === productId
		)?.product;

		// If product has event_product array, count the primary events
		if (product?.event_product && Array.isArray(product.event_product)) {
			return product.event_product.filter((ep: { kind: string }) => ep.kind === 'product_primary')
				.length;
		}

		// Fallback to the old method if event_product not available
		return (
			products?.filter(
				(ep: { product: { id: string }; kind: string }) =>
					ep.product.id === productId && ep.kind === 'product_primary'
			).length ?? 0
		);
	}

	function formatPackageName(eventCount: number): string {
		return eventCount === 1 ? 'Single Session' : `${eventCount} Sessions`;
	}

	function handleSelectOption(productId: string): void {
		onProductSelect(productId);
	}

	// Helper to get the selected product display name
	let selectedProductDisplay = $derived(() => {
		if (!selectedProductId) return 'Select Package';
		const details = productDetailsMap.get(selectedProductId);
		return details ? formatPackageName(details.eventCount) : 'Select Package';
	});
</script>

<div class="flex items-center justify-between">
	<h4 class="text-sm font-medium">Available Options</h4>
	<DropdownMenu.Root>
		<DropdownMenu.Trigger class={buttonVariants({ variant: 'outline' })}>
			<span class="truncate">
				{selectedProductDisplay}
			</span>
			<ChevronDown class="ml-2 size-4 shrink-0" />
		</DropdownMenu.Trigger>
		<DropdownMenu.Content class="w-56">
			<DropdownMenu.Group>
				<DropdownMenu.RadioGroup bind:value={selectedProductId}>
					{#each products ?? [] as { product }}
						{@const details = productDetailsMap.get(product.id)}
						<DropdownMenu.RadioItem
							value={product.id}
							onclick={() => handleSelectOption(product.id)}
						>
							<div class="flex justify-between gap-2">
								<span>{formatPackageName(details?.eventCount ?? 0)}</span>
								<span class="text-muted-foreground">
									{details?.pointsCost ?? 0} pts
								</span>
							</div>
						</DropdownMenu.RadioItem>
					{/each}
				</DropdownMenu.RadioGroup>
			</DropdownMenu.Group>
		</DropdownMenu.Content>
	</DropdownMenu.Root>
</div>
