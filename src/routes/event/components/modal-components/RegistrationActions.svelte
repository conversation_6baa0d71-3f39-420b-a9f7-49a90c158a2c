<!-- Registration Actions Component -->
<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { CheckCircle, Loader2 } from '@lucide/svelte';
	import type {
		ProductDetails,
		LocalizedText,
		OrderRequirement,
		FindOptionsResponse
	} from '../../types';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { getLocalizedText } from '$lib/utils/localization';
	import type { SupabaseClient } from '@supabase/supabase-js';

	interface Props {
		selectedProduct: ProductDetails | null;
		optionsData: FindOptionsResponse | null;
		buyingOptionSelections: Map<string, string[]>;
		isWalkinMode: boolean;
		supabase?: SupabaseClient;
		userId?: string;
		onSuccess?: () => void;
	}

	let {
		selectedProduct = $bindable(),
		optionsData = $bindable(),
		buyingOptionSelections = $bindable(),
		isWalkinMode = $bindable(),
		supabase = $bindable(),
		userId = $bindable(''),
		onSuccess = $bindable()
	}: Props = $props();

	// Internal state
	let isLoading = $state(false);
	let error = $state<string | null>(null);
	let paymentStatus = $state<'idle' | 'processing' | 'success' | 'error'>('idle');

	// Consolidated waitlist loading state
	let isLoadingWaitlist = $state(false);

	// Derived calculations
	const totals = $derived.by(() => {
		// Explicitly reference buyingOptionSelections to ensure reactivity
		const selectedEntries = Array.from(buyingOptionSelections.entries());

		const { totalUnits, needsPayment } = calculateTotals();
		const moneyPrice = getSelectedMoneyOptionPrice();
		const fillUpNeeded = needsFillUp();

		console.log('Recalculated totals:', {
			totalUnits,
			needsPayment,
			moneyPrice,
			fillUpNeeded,
			selectionCount: selectedEntries.length
		});

		return { totalUnits, needsPayment, moneyPrice, fillUpNeeded };
	});

	// Check if any options are selected
	const hasAnySelectedOptions = $derived(
		buyingOptionSelections.size > 0 &&
			Array.from(buyingOptionSelections.entries()).some(
				([_, selections]) =>
					selections && selections.length > 0 && selections.some((id) => id && id.length > 0)
			)
	);

	// Check if user is already on the waitlist
	const isUserOnWaitlist = $derived(() => {
		if (!userId || !selectedProduct?.waiting_group?.group_member) return false;

		return selectedProduct.waiting_group.group_member.some(
			(member: any) => member.user_id === userId
		);
	});

	// Helper function to find a product price with passcode requirement
	function findProductPriceWithPasscode(): {
		productPriceId: string;
		orderRequirement: OrderRequirement | null;
	} | null {
		if (!selectedProduct?.product_price?.length) return null;

		// Find product price with passcode requirement - no need for consumer item
		for (const productPrice of selectedProduct.product_price) {
			// Safely cast the order_requirement to ensure TypeScript is happy
			const orderRequirement = productPrice.order_requirement as OrderRequirement | null;

			if (orderRequirement?.require_passcode && orderRequirement.require_passcode.length > 0) {
				console.log('Found product price with passcode requirement:', productPrice.id);
				return {
					productPriceId: productPrice.id ?? '', // This is the product_price.id
					orderRequirement
				};
			}
		}

		return null;
	}

	// Calculate total units and monetary value
	function calculateTotals() {
		if (!optionsData?.consumer_items) return { totalUnits: 0, needsPayment: false };

		let totalUnits = 0;
		let needsPayment = false;
		let foundPriceData = false;

		// Look for the selected option in the price_option_combinations to get actual units
		for (const [consumerId, selectedOptions] of buyingOptionSelections.entries()) {
			if (selectedOptions.length === 0) continue;

			const selectedOption = selectedOptions[0];
			console.log('Calculating units for selected option:', selectedOption);

			const consumerItem = optionsData.consumer_items.find(
				(item) => item.consumer_profile?.id === consumerId
			);

			if (!consumerItem || !consumerItem.product_prices) continue;

			// Try to find the units information for this selection
			for (const price of consumerItem.product_prices) {
				// For regular unit options, use the cost from the product price
				totalUnits = price.consumer_total_units_cost ?? 0;
				foundPriceData = true;

				// Check if payment is needed
				if ((price.consumer_total_units_remaining ?? 0) < 0) {
					needsPayment = true;
				}
			}
		}

		// Only use fallback if we truly have no price data from the API
		// Do NOT use fallback when totalUnits is 0, as that might be the correct value (free option)
		if (!foundPriceData && selectedProduct?.product_price?.[0]?.cost_units) {
			totalUnits = selectedProduct.product_price[0].cost_units;
		}

		console.log('Final calculated totalUnits:', totalUnits, 'foundPriceData:', foundPriceData);
		return { totalUnits, needsPayment };
	}

	// Get selected money option price if available - returns null for UI feedback only
	function getSelectedMoneyOptionPrice(): number | null {
		if (!optionsData?.consumer_items) return null;

		// First check if any selections exist
		if (buyingOptionSelections.size === 0) return null;

		// Try to match by selected option ID
		for (const [consumerId, selectedOptions] of buyingOptionSelections.entries()) {
			if (selectedOptions.length === 0) continue;

			const selectedOption = selectedOptions[0];
			console.log('Looking up price for selected option:', selectedOption);

			// Find the consumer item
			const consumerItem = optionsData.consumer_items.find(
				(item) => item.consumer_profile?.id === consumerId
			);
			if (!consumerItem || !consumerItem.product_prices) continue;

			// Direct lookup by option ID
			for (const price of consumerItem.product_prices) {
				if (!price.price_option_combinations) continue;

				for (const combo of price.price_option_combinations) {
					if (!combo.options) continue;

					const comboOptionIds = combo.options
						.map((opt) => opt.price_option?.id ?? '')
						.filter((id) => id);

					// Check if the selected option matches this combo
					let isMatch = false;
					if (selectedOption.includes(',')) {
						const selectedIds = selectedOption.split(',');
						isMatch = selectedIds.some((id) => comboOptionIds.includes(id));
					} else {
						isMatch = comboOptionIds.includes(selectedOption);
					}

					if (isMatch) {
						console.log('Found matching combo for price lookup');
						console.log('Combo structure:', JSON.stringify(combo, null, 2));

						// Sum up all money_int values in the options array
						let totalPrice = 0;
						let foundPriceData = false;
						for (const opt of combo.options || []) {
							if (!opt.price_option) continue;

							const optMoneyInt = (opt.price_option as any).money_int;
							if (optMoneyInt !== undefined && optMoneyInt !== null) {
								console.log(
									`Adding price from option ${opt.price_option.id}: ${optMoneyInt / 100}`
								);
								totalPrice += optMoneyInt / 100; // Convert cents to dollars
								foundPriceData = true;
							}
						}

						if (foundPriceData) {
							console.log('Total calculated price:', totalPrice);
							return totalPrice; // Can be 0 for free items
						}

						// If we couldn't find any prices, return null
						console.log('No valid prices found in the combo options');
						return null;
					}
				}
			}
		}

		// If we got here, we couldn't find a price
		console.log('Could not determine price for the selected option');
		return null;
	}

	// Simplified function to check if fill-up is needed
	function needsFillUp(): boolean {
		if (!optionsData?.consumer_items) return false;
		// Don't allow fill-up for walk-ins
		if (isWalkinMode) return false;

		// Return true if any consumer has negative remaining units
		const hasNegativeUnits = optionsData.consumer_items.some((item) =>
			item.product_prices?.some((price) => (price.consumer_total_units_remaining ?? 0) < 0)
		);

		if (hasNegativeUnits) return true;

		// Return true if any consumer has a money option selected
		return optionsData.consumer_items.some((item) => {
			if (!item.consumer_profile?.id) return false;
			const selectedOptions = buyingOptionSelections.get(item.consumer_profile.id) ?? [];
			return selectedOptions.length > 0 && selectedOptions[0]?.includes(',');
		});
	}

	// Helper to get price option data for Stripe - throws errors for actual payment
	function getPriceOptionDataForStripe(): {
		price_option_ids: string[];
		quantity: number;
		money_int: number;
		currency_code: string;
		title: string;
	}[] {
		if (!optionsData?.consumer_items) return [];

		const priceOptionData: {
			price_option_ids: string[];
			quantity: number;
			money_int: number;
			currency_code: string;
			title: string;
		}[] = [];

		for (const [consumerId, selectedOptionIds] of buyingOptionSelections.entries()) {
			if (selectedOptionIds.length === 0) continue;

			const selectedOption = selectedOptionIds[0];
			console.log('Getting Stripe data for option:', selectedOption);

			// Find the consumer item
			const consumerItem = optionsData.consumer_items.find(
				(item) => item.consumer_profile?.id === consumerId
			);
			if (!consumerItem || !consumerItem.product_prices) continue;

			// Special case for direct option ID match (not comma-separated)
			if (!selectedOption.includes(',')) {
				// Try to find the matching price option in all combinations
				let priceFound = false;

				for (const price of consumerItem.product_prices) {
					if (!price.price_option_combinations) continue;

					for (const combo of price.price_option_combinations) {
						if (!combo.options) continue;

						const comboOptionIds = combo.options
							.map((opt) => opt.price_option?.id ?? '')
							.filter((id) => id);

						// Check if the selected option is in this combo
						if (comboOptionIds.includes(selectedOption)) {
							console.log('Found option in combo for Stripe:', selectedOption);

							// Generate title for this option
							const titles = combo.options
								.map((opt) => getLocalizedText(opt.price_option?.title as LocalizedText, 'en'))
								.filter(Boolean);
							const title = titles.join(', ');

							// Debug the combo structure
							console.log('Pricing combo structure:', JSON.stringify(combo, null, 2));

							// Sum up all money_int values in the options array
							let totalMoneyInt = 0;
							let foundPriceData = false;
							const currencyCode = (combo as any).currency_code || 'USD';

							for (const opt of combo.options || []) {
								if (!opt.price_option) continue;

								const optMoneyInt = (opt.price_option as any).money_int;
								if (optMoneyInt !== undefined && optMoneyInt !== null) {
									console.log(`Adding price from option ${opt.price_option.id}: ${optMoneyInt}`);
									totalMoneyInt += optMoneyInt;
									foundPriceData = true;
								}
							}

							// If no price data found, throw an error
							if (!foundPriceData) {
								console.error('No price data found for option:', selectedOption);
								throw new Error('Price data is missing for this payment option');
							}

							priceOptionData.push({
								price_option_ids: [selectedOption],
								quantity: 1,
								money_int: totalMoneyInt,
								currency_code: currencyCode,
								title: title || 'Selected option'
							});

							priceFound = true;
							break;
						}
					}
					if (priceFound) break;
				}

				// If no price found but we have an option ID, error out
				if (!priceFound) {
					console.error('No price data found for option:', selectedOption);
					throw new Error('Price data is missing for the selected payment option');
				}

				continue;
			}

			// Handle comma-separated option IDs
			if (selectedOption.includes(',')) {
				const optionIds = selectedOption.split(',').filter(Boolean);

				// Find the price option combination
				let priceFound = false;

				for (const price of consumerItem.product_prices) {
					if (!price.price_option_combinations) continue;

					for (const combo of price.price_option_combinations) {
						if (!combo.options) continue;

						const comboOptionIds = combo.options
							.map((opt) => opt.price_option?.id ?? '')
							.filter((id) => id);

						// Check if the combo matches our selected option
						const sortedComboIds = [...comboOptionIds].sort();
						const sortedSelectedIds = [...optionIds].sort();

						// Check exact match
						const isMatch =
							sortedComboIds.length === sortedSelectedIds.length &&
							sortedComboIds.every((id, index) => id === sortedSelectedIds[index]);

						// Also check if the option is included in the combo (more flexible matching)
						const isOptionIncluded =
							optionIds.length === 1 && comboOptionIds.includes(optionIds[0]);

						if (isMatch || isOptionIncluded) {
							// Generate title for this combination
							const titles = combo.options
								.map((opt) => getLocalizedText(opt.price_option?.title as LocalizedText, 'en'))
								.filter(Boolean);
							const title = titles.join(', ');

							// Debug the combo structure
							console.log('Combo pricing structure:', JSON.stringify(combo, null, 2));

							// Sum up all money_int values in the options array
							let totalMoneyInt = 0;
							let foundPriceData = false;
							const currencyCode = (combo as any).currency_code || 'USD';

							for (const opt of combo.options || []) {
								if (!opt.price_option) continue;

								const optMoneyInt = (opt.price_option as any).money_int;
								if (optMoneyInt !== undefined && optMoneyInt !== null) {
									console.log(`Adding price from option ${opt.price_option.id}: ${optMoneyInt}`);
									totalMoneyInt += optMoneyInt;
									foundPriceData = true;
								}
							}

							// If no price data found, throw an error
							if (!foundPriceData) {
								console.error('No price data found for combination:', optionIds);
								throw new Error('Price data is missing for this payment option');
							}

							// Found matching combo
							priceOptionData.push({
								price_option_ids: optionIds,
								quantity: 1,
								money_int: totalMoneyInt,
								currency_code: currencyCode,
								title: title || 'Selected package'
							});

							priceFound = true;
							break;
						}
					}
					if (priceFound) break;
				}

				// If no price found but we have option IDs, error out
				if (!priceFound) {
					console.error('No price data found for combination:', optionIds);
					throw new Error('Price data is missing for the selected payment option');
				}
			}
		}

		return priceOptionData;
	}

	function prepareOrderInput() {
		if (!optionsData?.consumer_items) throw new Error('No options data available');

		// Skip validation that could throw errors
		const result = {
			payer_id: optionsData.payer_id,
			consumer_profiles: optionsData.consumer_items
				.filter((item) => item.consumer_profile && item.product_prices)
				.map((item) => {
					const consumerId = item.consumer_profile?.id ?? '';
					const selectedOptions = buyingOptionSelections.get(consumerId) ?? [];

					return {
						consumer_profile_id: consumerId,
						product_count: item.product_count ?? 1,
						product_prices: (item.product_prices ?? []).map((price) => {
							const productPriceId = price.product_price?.id ?? '';
							const selectedOptionId = selectedOptions[0] ?? '';

							// Prepare price option combinations data - simplified
							const fillUpCombinations = [];
							if (selectedOptionId) {
								// Handle different option formats
								if (selectedOptionId.includes(',')) {
									// For comma-separated options
									selectedOptionId.split(',').forEach((optionId) => {
										if (optionId) {
											fillUpCombinations.push({
												price_option_id: optionId,
												quantity: 1
											});
										}
									});
								} else {
									// For single options
									fillUpCombinations.push({
										price_option_id: selectedOptionId,
										quantity: 1
									});
								}
							}

							// Check if there is an order requirement with passcode
							const productPriceWithPasscode = findProductPriceWithPasscode();
							const orderResult = {
								product_price_id: productPriceId,
								fill_up_with_price_option_combinations: fillUpCombinations
							};

							// Only add the passcode if this is the product price that requires it
							if (
								productPriceWithPasscode &&
								productPriceWithPasscode.productPriceId === productPriceId &&
								productPriceWithPasscode.orderRequirement?.require_passcode &&
								productPriceWithPasscode.orderRequirement.require_passcode.length > 0
							) {
								return {
									...orderResult,
									order_requirement_passcode:
										productPriceWithPasscode.orderRequirement.require_passcode
								};
							}

							return orderResult;
						})
					};
				})
		};

		// Basic validation
		if (!result.consumer_profiles.length) {
			console.warn('No valid consumer profiles found');
		}

		return result;
	}

	// Function to handle payment purchase flow
	async function handlePurchase() {
		// Don't allow money purchase for walk-ins
		if (isWalkinMode) {
			return;
		}

		const selectedOptions = Array.from(buyingOptionSelections.values()).flat();
		if (!selectedOptions.length) return;

		isLoading = true;
		paymentStatus = 'processing';
		error = null;

		try {
			// Create the order
			const orderInput = prepareOrderInput();

			// Check if we have valid price options before proceeding
			const hasValidPriceOptions = orderInput.consumer_profiles.some((profile) =>
				profile.product_prices.some(
					(price) => price.fill_up_with_price_option_combinations.length > 0
				)
			);

			if (!hasValidPriceOptions) {
				throw new Error('No valid price options selected');
			}

			// API call to create order
			const orderResponse = await fetch('/event/private/new-order', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(orderInput)
			});

			// Parse response safely
			let orderResult;
			try {
				orderResult = await orderResponse.json();
			} catch (err) {
				throw new Error('Invalid server response');
			}

			// Handle error response
			if (orderResult.type === 'error') {
				const message = orderResult.error?.message || 'Unknown server error';
				throw new Error(message);
			}

			// Ensure we have an order ID
			if (!orderResult.data?.order_id) {
				throw new Error('No order ID returned from server');
			}

			try {
				// Get price option data for Stripe - this may throw if price data is missing
				const priceOptionData = getPriceOptionDataForStripe();

				// Initiate Stripe checkout
				const stripeResponse = await fetch('/event/private/stripe-checkout', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						optionIds: selectedOptions,
						orderId: orderResult.data.order_id,
						priceOptionData
					})
				});

				const stripeResult = await stripeResponse.json();
				if (stripeResult.type === 'error') {
					throw new Error(stripeResult.error?.message || 'Stripe checkout failed');
				}

				// For Stripe checkout with successful response, redirect user
				// Note: We don't close modal here since we're redirecting to Stripe
				if (stripeResult.type === 'success' && stripeResult.data?.url) {
					window.location.href = stripeResult.data.url;
				} else {
					throw new Error('No checkout URL received');
				}
			} catch (priceError: unknown) {
				// Handle price data missing error specifically
				console.error('Price data error:', priceError);
				const errorMessage = priceError instanceof Error ? priceError.message : 'Unknown error';
				throw new Error(`Payment failed: ${errorMessage}`);
			}
		} catch (err) {
			console.error('Error initiating checkout:', err);
			paymentStatus = 'error';
			error = err instanceof Error ? err.message : 'Checkout failed';
		} finally {
			isLoading = false;
		}
	}

	// Original handleCheckout for unit-only payments (no fill-up needed)
	async function handleCheckout() {
		if (!optionsData?.consumer_items) {
			console.error('Missing consumer items in options data');
			return;
		}

		isLoading = true;
		paymentStatus = 'processing';
		error = null;

		try {
			// Log walk-in mode status
			console.log('Checkout - Walk-in mode:', isWalkinMode);

			// Check for valid selection on selected option
			const hasValidSelection = Array.from(buyingOptionSelections.entries()).some(
				([profileId, selections]) => selections && selections.length > 0
			);

			if (!hasValidSelection) {
				console.warn('No valid selection found in buyingOptionSelections');
			}

			// Prepare the order input
			const orderInput = {
				payer_id: optionsData.payer_id,
				consumer_profiles: optionsData.consumer_items
					.filter((item) => item.consumer_profile && item.product_prices)
					.map((item) => {
						// Use walkin profile ID as the consumer profile ID if in walk-in mode
						const consumerProfileId = isWalkinMode ? userId : (item.consumer_profile?.id ?? '');

						const selectedOptions =
							buyingOptionSelections.get(item.consumer_profile?.id ?? '') || [];

						console.log(`Consumer ${consumerProfileId}: Selected options:`, selectedOptions);

						return {
							consumer_profile_id: consumerProfileId,
							product_count: item.product_count ?? 1,
							product_prices: (item.product_prices ?? []).map((price) => {
								const productPriceId = price.product_price?.id ?? '';

								console.log(
									`Product price ${productPriceId}: Units remaining:`,
									price.consumer_total_units_remaining
								);

								// Check if there is an order requirement with passcode
								const productPriceWithPasscode = findProductPriceWithPasscode();
								const orderResult = {
									product_price_id: productPriceId,
									// For direct registration with sufficient units, we don't need price options
									fill_up_with_price_option_combinations: []
								};

								// Only add the passcode if this is the product price that requires it
								if (
									productPriceWithPasscode &&
									productPriceWithPasscode.productPriceId === productPriceId &&
									productPriceWithPasscode.orderRequirement?.require_passcode &&
									productPriceWithPasscode.orderRequirement.require_passcode.length > 0
								) {
									return {
										...orderResult,
										order_requirement_passcode:
											productPriceWithPasscode.orderRequirement.require_passcode
									};
								}

								return orderResult;
							})
						};
					})
			};

			console.log(
				`Submitting order for ${isWalkinMode ? 'walk-in' : 'regular'} registration`,
				JSON.stringify(orderInput)
			);

			// Send to the API endpoint
			const response = await fetch('/event/private/new-order', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(orderInput)
			});

			const result = await response.json();

			if (result.type === 'error') {
				throw new Error(result.error.message);
			}

			if (result.type === 'success') {
				paymentStatus = 'success';

				// Display success state for a moment before closing
				setTimeout(() => {
					// Call onSuccess callback if provided, otherwise do the goto
					if (onSuccess) {
						onSuccess();
					} else {
						// Update URL and invalidate data
						goto($page.url.pathname, {
							replaceState: true,
							noScroll: true,
							invalidateAll: true
						});
					}
				}, 1000);
			}
		} catch (err) {
			console.error('Error placing order:', err);
			paymentStatus = 'error';
			if (err instanceof Error) {
				error = err.message;
			} else {
				error = 'An unexpected error occurred while placing your order.';
			}
		} finally {
			isLoading = false;
		}
	}

	// Handle joining waitlist
	async function handleJoinWaitlist() {
		if (!supabase || !selectedProduct?.id || !userId) return;

		isLoadingWaitlist = true;

		try {
			const { data, error } = await supabase.rpc('new_product_group_member', {
				input_product_id: selectedProduct.id,
				input_user_id: userId,
				input_group_kind: 'waiting_group'
			});

			if (error) throw error;

			// Refresh page to update UI
			goto($page.url.pathname, {
				replaceState: true,
				noScroll: true,
				invalidateAll: true
			});
		} catch (err) {
			console.error('Error joining waitlist:', err);
			error = err instanceof Error ? err.message : 'Failed to join waitlist';
		} finally {
			isLoadingWaitlist = false;
		}
	}

	// Handle leaving waitlist
	async function handleLeaveWaitlist() {
		if (!supabase || !selectedProduct?.waiting_group?.id || !userId) return;

		isLoadingWaitlist = true;

		try {
			const { data, error } = await supabase.rpc('delete_group_member', {
				input_group_id: selectedProduct.waiting_group.id,
				input_user_id: userId
			});

			if (error) throw error;

			// Refresh page to update UI
			goto($page.url.pathname, {
				replaceState: true,
				noScroll: true,
				invalidateAll: true
			});
		} catch (err) {
			console.error('Error leaving waitlist:', err);
			error = err instanceof Error ? err.message : 'Failed to leave waitlist';
		} finally {
			isLoadingWaitlist = false;
		}
	}

	// Function to determine button text based on payment status
	function getPaymentButtonText(): string {
		if (paymentStatus === 'processing') return 'Processing...';
		if (paymentStatus === 'success') return 'Registered Successfully';
		if (paymentStatus === 'error') return 'Retry Registration';
		if (totals.fillUpNeeded) {
			if (!hasAnySelectedOptions) return 'Select Payment Option';
			return 'Proceed to Payment';
		}
		return 'Register Now';
	}
</script>

<div class="flex items-center justify-between gap-4">
	<div class="text-left">
		{#if error}
			<p class="text-destructive">Error: {error}</p>
		{:else}
			<p class="font-medium">
				{#if totals.fillUpNeeded}
					{#if hasAnySelectedOptions && totals.moneyPrice !== null && totals.moneyPrice > 0}
						<!-- Money buying option - just show dollars -->
						Total: ${totals.moneyPrice.toFixed(2)}
					{:else}
						<span class="text-yellow-600 dark:text-yellow-400">
							{hasAnySelectedOptions
								? 'Select payment option'
								: 'Select payment option to continue'}
						</span>
					{/if}
				{:else}
					<!-- Unit buying option - show points -->
					Using {totals.totalUnits.toFixed(1)} pts from your account
				{/if}
			</p>
		{/if}
	</div>

	<div class="shrink-0">
		{#if !selectedProduct}
			<!-- No product selected -->
			<Button variant="outline" disabled>Please select a package</Button>
		{:else if selectedProduct.auto_stock_available <= 0}
			<!-- Product is sold out - offer waitlist option -->
			{#if isUserOnWaitlist()}
				<Button onclick={handleLeaveWaitlist} disabled={isLoadingWaitlist} variant="outline">
					{#if isLoadingWaitlist}
						<Loader2 class="mr-2 size-4 animate-spin" />
						<span>Loading...</span>
					{:else}
						<span>Leave Waitlist</span>
					{/if}
				</Button>
			{:else}
				<Button onclick={handleJoinWaitlist} disabled={isLoadingWaitlist} variant="outline">
					{#if isLoadingWaitlist}
						<Loader2 class="mr-2 size-4 animate-spin" />
						<span>Loading...</span>
					{:else}
						<span>Join Waitlist</span>
					{/if}
				</Button>
			{/if}
		{:else if totals.fillUpNeeded}
			<!-- Payment button for when options need to be selected -->
			<Button
				onclick={handlePurchase}
				disabled={isLoading || paymentStatus === 'success' || !hasAnySelectedOptions}
				variant="default"
			>
				{#if paymentStatus === 'success'}
					<CheckCircle class="mr-2 size-4" />
				{/if}
				<span>{getPaymentButtonText()}</span>
			</Button>
		{:else}
			<!-- Direct registration button with existing points -->
			<Button
				onclick={handleCheckout}
				disabled={isLoading || paymentStatus === 'success'}
				variant="default"
			>
				{#if paymentStatus === 'success'}
					<CheckCircle class="mr-2 size-4" />
				{/if}
				<span>{getPaymentButtonText()}</span>
			</Button>
		{/if}
	</div>
</div>
