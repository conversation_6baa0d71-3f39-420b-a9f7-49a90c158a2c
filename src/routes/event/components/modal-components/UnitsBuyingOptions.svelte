<script lang="ts">
	import * as RadioGroup from '$lib/components/ui/radio-group/index.js';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { Clock } from '@lucide/svelte';

	// Updated type to match the actual server response structure
	interface OrderPriceRecord {
		id: string;
		units_available: number;
		units_cost: number;
		deal_units: number;
		auto_price_option_expire_at: string | null;
		price_option_purchase_count: number;
		price_option_title: LocalizedText;
		price_color_primary_semantic: string;
		price_color_primary_hex: string;
	}

	interface Props {
		options: Array<{
			id: string;
			order_price_record: OrderPriceRecord;
		}>;
		selectedOptionId: string | null;
		onSelect: (id: string) => void;
	}

	let { options, selectedOptionId, onSelect }: Props = $props();

	function formatUnits(units: number): string {
		return `${units.toFixed(1)} ${units === 1 ? 'pt' : 'pts'}`;
	}

	function formatExpirationDate(dateStr: string | null): string {
		if (!dateStr) return '';

		const date = new Date(dateStr);
		return date.toLocaleDateString(undefined, {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}
</script>

<div class="w-full overflow-x-hidden">
	<RadioGroup.Root value={selectedOptionId ?? ''} onValueChange={(value) => onSelect(value)}>
		<div class="space-y-4">
			{#each options as option}
				{@const isSelected = selectedOptionId === option.id}
				{@const orderPrice = option.order_price_record}
				<div class="w-full">
					<button
						type="button"
						class="group flex w-full flex-col gap-3 rounded-lg border bg-card p-4 text-left text-card-foreground transition-all hover:border-primary/50 hover:shadow-sm {isSelected
							? 'border-primary/70 bg-primary/5 dark:bg-primary/10'
							: ''}"
						onclick={() => onSelect(option.id)}
					>
						<div class="flex w-full items-start justify-between">
							<div class="min-w-0 flex-1">
								<h3 class="truncate text-base font-medium {isSelected ? 'text-primary' : ''}">
									{getLocalizedText(orderPrice.price_option_title)}
								</h3>
							</div>

							<div class="ml-2 shrink-0">
								<span
									class="whitespace-nowrap text-sm font-medium {isSelected ? 'text-primary' : ''}"
								>
									× {orderPrice.price_option_purchase_count}
								</span>
							</div>
						</div>

						<div class="flex w-full items-end justify-between">
							<div class="flex flex-col">
								<span class="text-lg font-medium {isSelected ? 'text-primary' : ''}">
									{formatUnits(orderPrice.units_available)}
								</span>
								<span class="text-xs text-muted-foreground">remaining</span>
							</div>

							{#if orderPrice.auto_price_option_expire_at}
								<div class="flex items-center gap-1 text-xs text-muted-foreground">
									<Clock size={12} class="shrink-0 opacity-70" />
									<span class="truncate"
										>Expires {formatExpirationDate(orderPrice.auto_price_option_expire_at)}</span
									>
								</div>
							{/if}
						</div>
					</button>
				</div>
			{/each}
		</div>
	</RadioGroup.Root>
</div>
