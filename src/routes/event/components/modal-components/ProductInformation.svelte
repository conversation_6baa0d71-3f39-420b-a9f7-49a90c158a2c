<!-- Product Information Component -->
<script lang="ts">
	import type { ProductDetails } from '../../types';
	import AddToCalendar from './AddToCalendar.svelte';
	import EventList from './EventList.svelte';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { format, parseISO } from 'date-fns';

	interface Props {
		product: ProductDetails;
	}

	let { product = $bindable() }: Props = $props();

	// Calculate event count directly from product
	let eventCount = $derived(
		product.event_product
			? product.event_product.filter((ep: any) => ep.kind === 'product_primary').length
			: 1
	);

	// Get session schedule summary
	function getSessionScheduleSummary(): string {
		if (!product.event_product || product.event_product.length <= 1) return '';

		const events = product.event_product.map((ep: any) => ep.event);
		if (events.length <= 1) return '';

		// Check if all events have consistent day of week, time, and venue
		let commonDayOfWeek: number | null = null;
		let commonStartTime: string | null = null;
		let commonDuration: number | null = null;
		let commonVenue: string | null = null;

		for (const event of events) {
			const eventDate = parseISO(event.start_at);
			const dayOfWeek = eventDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
			const startTime = format(eventDate, 'h:mm a');
			const duration = event.duration_minute;

			// Get venue information
			const landmark = event.landmark || event.space?.landmark;
			const spaceName = event.space?.name_short
				? getLocalizedText(event.space.name_short as LocalizedText)
				: '';
			const landmarkTitle = landmark?.title_short
				? getLocalizedText(landmark.title_short as LocalizedText)
				: '';
			const venueParts = [spaceName, landmarkTitle].filter(Boolean);
			const venue = venueParts.length > 0 ? venueParts.join(', ') : '';

			if (commonDayOfWeek === null) {
				commonDayOfWeek = dayOfWeek;
				commonStartTime = startTime;
				commonDuration = duration;
				commonVenue = venue;
			} else if (
				commonDayOfWeek !== dayOfWeek ||
				commonStartTime !== startTime ||
				commonDuration !== duration ||
				commonVenue !== venue
			) {
				// Not consistent, return empty
				return '';
			}
		}

		if (commonDayOfWeek !== null && commonStartTime && commonDuration) {
			const dayNames = [
				'Sunday',
				'Monday',
				'Tuesday',
				'Wednesday',
				'Thursday',
				'Friday',
				'Saturday'
			];
			const dayName = dayNames[commonDayOfWeek];

			// Calculate end time
			const sampleDate = parseISO(events[0].start_at);
			const endDate = new Date(sampleDate.getTime() + commonDuration * 60 * 1000);
			const endTime = format(endDate, 'h:mm a');

			let summary = `${commonStartTime} ~ ${endTime} every ${dayName}`;
			if (commonVenue) {
				summary += ` at ${commonVenue}`;
			}

			return summary;
		}

		return '';
	}

	const scheduleSummary = $derived(getSessionScheduleSummary());
</script>

<div>
	<div class="flex items-center justify-between">
		<div class="flex items-center">
			<h4 class="text-lg font-semibold">
				{#if eventCount === 1}
					Standalone session
				{:else}
					{eventCount} sessions
				{/if}
			</h4>
			<AddToCalendar {product} />
		</div>
		{#if scheduleSummary}
			<p class="text-muted-foreground text-sm">{scheduleSummary}</p>
		{/if}
	</div>

	{#if product.auto_stock_available !== null}
		<p class="text-muted-foreground text-sm">
			{#if product.auto_stock_available <= 0}
				Sold out
			{:else if product.auto_stock_available <= 3}
				Only a few spots left
			{:else if product.auto_stock_available <= 10}
				Limited spots available
			{/if}
		</p>
	{/if}

	<div class="my-3 border-t"></div>

	<EventList {product} showSessionNumbers={true} />
</div>
