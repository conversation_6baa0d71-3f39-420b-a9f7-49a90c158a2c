<script lang="ts">
	import EventRow from './EventRow.svelte';
	import type { ProductDetails } from '../../types';

	interface Props {
		product: ProductDetails;
		currentEventId?: string;
		showSessionNumbers?: boolean;
	}

	let { product, currentEventId, showSessionNumbers = false }: Props = $props();

	// Sort events by start_at date
	const sortedEvents = $derived(() => {
		if (!product?.event_product) return [];

		return [...product.event_product]
			.filter((ep) => ep.kind === 'product_primary')
			.sort((a, b) => {
				const dateA = new Date(a.event.start_at).getTime();
				const dateB = new Date(b.event.start_at).getTime();
				return dateA - dateB;
			});
	});
</script>

<div class="space-y-3">
	{#if sortedEvents().length > 0}
		{#each sortedEvents() as eventProduct, index}
			<EventRow
				event={eventProduct.event}
				showBorder={index > 0}
				sessionNumber={showSessionNumbers ? index + 1 : undefined}
				isCurrentEvent={currentEventId ? eventProduct.event.id === currentEventId : false}
			/>
		{:else}
			<p class="text-sm text-muted-foreground text-center py-4">
				No sessions available for this product.
			</p>
		{/each}
	{:else}
		<p class="py-4 text-center text-sm text-muted-foreground">
			No sessions available for this product.
		</p>
	{/if}
</div>
