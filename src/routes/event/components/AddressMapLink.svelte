<script lang="ts">
	import * as Popover from '$lib/components/ui/popover';
	import { Button } from '$lib/components/ui/button';

	interface Props {
		event: any;
		children: () => any;
	}

	let { event, children }: Props = $props();

	// Get normalized address from landmark or space's landmark
	function getAddress(): string | null {
		const normalizedAddress =
			event.landmark?.address?.auto_normalized_address_local ||
			event.space?.landmark?.address?.auto_normalized_address_local;

		// Fall back to the event.location if no normalized address available
		return normalizedAddress || event.location || null;
	}

	// Open Google Maps with the address
	function openGoogleMaps() {
		const address = getAddress();
		if (!address) return;

		const encodedAddress = encodeURIComponent(address);
		window.open(`https://www.google.com/maps/search/?api=1&query=${encodedAddress}`, '_blank');
	}

	// Open Apple Maps with the address
	function openAppleMaps() {
		const address = getAddress();
		if (!address) return;

		const encodedAddress = encodeURIComponent(address);
		// Apple Maps URL scheme
		window.open(`maps://?q=${encodedAddress}`, '_blank');

		// Fallback for web - opens Apple Maps on the web
		setTimeout(() => {
			window.open(`https://maps.apple.com/?q=${encodedAddress}`, '_blank');
		}, 100);
	}
</script>

<Popover.Root>
	<Popover.Trigger>
		{@render children()}
	</Popover.Trigger>
	<Popover.Content class="w-56 p-2">
		<div class="space-y-2">
			<h4 class="text-sm font-medium">Open in Maps</h4>
			<div class="flex flex-col gap-2">
				<Button variant="outline" class="w-full justify-start" onclick={openGoogleMaps}>
					Google Maps
				</Button>
				<Button variant="outline" class="w-full justify-start" onclick={openAppleMaps}>
					Apple Maps
				</Button>
			</div>
		</div>
	</Popover.Content>
</Popover.Root>
