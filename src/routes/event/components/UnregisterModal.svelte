<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import ResponsiveModal from '$lib/components/shared/ResponsiveModal.svelte';
	import { AlertCircle, Loader2 } from '@lucide/svelte';
	import { toast } from 'svelte-sonner';
	import type { Snippet } from 'svelte';
	import CancellationPolicy from './modal-components/CancellationPolicy.svelte';

	interface Props {
		orderProductId: string;
		eventTitle: string;
		onClose: () => void;
		onSuccess: () => void;
		cancellationData: {
			should_return_units: number;
			is_cancel_allowed: boolean;
			policy?: {
				firstEventStartAt?: string;
				auto_cancel_at_far?: string;
				auto_cancel_at_far_return_units?: number;
				auto_cancel_at_near?: string;
				auto_cancel_at_near_return_units?: number;
			};
		};
	}

	let { orderProductId, eventTitle, onClose, onSuccess, cancellationData }: Props = $props();

	let modalOpen = $state(true);
	let isCancelling = $state(false);
	let error = $state<string | null>(null);
	let canCancel = $state(cancellationData.is_cancel_allowed);
	let returnUnits = $state(cancellationData.should_return_units || 0);
	let cancellationPolicy = $state<{
		firstEventStartAt?: Date;
		autoCancelAtFar?: Date;
		autoCancelAtFarReturnUnits?: number;
		autoCancelAtNear?: Date;
		autoCancelAtNearReturnUnits?: number;
	} | null>(null);

	// Convert the policy data if available
	if (cancellationData.policy) {
		cancellationPolicy = {
			firstEventStartAt: cancellationData.policy.firstEventStartAt
				? new Date(cancellationData.policy.firstEventStartAt)
				: undefined,
			autoCancelAtFar: cancellationData.policy.auto_cancel_at_far
				? new Date(cancellationData.policy.auto_cancel_at_far)
				: undefined,
			autoCancelAtFarReturnUnits: cancellationData.policy.auto_cancel_at_far_return_units,
			autoCancelAtNear: cancellationData.policy.auto_cancel_at_near
				? new Date(cancellationData.policy.auto_cancel_at_near)
				: undefined,
			autoCancelAtNearReturnUnits: cancellationData.policy.auto_cancel_at_near_return_units
		};
	}

	let currentStage = $state<'far' | 'near' | 'none'>(determineCancellationStage());

	// Format number with commas (e.g. 1000 -> 1,000)
	function formatNumberWithCommas(num: number): string {
		return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
	}

	// Determine the current cancellation stage
	function determineCancellationStage() {
		if (!cancellationPolicy?.firstEventStartAt) return 'none';

		const now = new Date();
		const eventStartDate = new Date(cancellationPolicy.firstEventStartAt);

		// Cancel stage is determined by how far in advance we are based on auto_cancel timestamps
		// Far = earliest stage, most lenient policy
		// Near = closer to event, less lenient policy
		// None = too close to event, no cancellation

		if (cancellationPolicy.autoCancelAtFar && now < cancellationPolicy.autoCancelAtFar) {
			return 'far';
		} else if (cancellationPolicy.autoCancelAtNear && now < cancellationPolicy.autoCancelAtNear) {
			return 'near';
		} else {
			return 'none';
		}
	}

	async function cancelRegistration() {
		if (!canCancel) return;

		isCancelling = true;
		error = null;

		try {
			const response = await fetch('/event/private/cancel-order', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ orderProductId })
			});

			const result = await response.json();

			if (result.type === 'error') {
				throw new Error(result.error.message);
			}

			if (result.type === 'success' && result.data?.performed_update) {
				toast.success('Successfully unregistered from event');
				onSuccess();
				onClose();
			} else {
				throw new Error('Failed to process unregistration');
			}
		} catch (err) {
			console.error('Error cancelling registration:', err);
			error = err instanceof Error ? err.message : 'Failed to unregister from event';
		} finally {
			isCancelling = false;
		}
	}

	function handleOpenChange(open: boolean) {
		modalOpen = open;
		if (!open) onClose();
	}
</script>

{#snippet content()}
	{#if error}
		<div class="py-4">
			<div class="flex items-start gap-3 rounded-md bg-destructive/10 p-4 text-destructive">
				<AlertCircle class="h-5 w-5 shrink-0" />
				<div>
					<p class="font-medium">Unable to process cancellation request</p>
					<p class="text-sm">{error}</p>
					<p class="mt-2 text-xs">Please try again or contact support if this issue persists.</p>
				</div>
			</div>
		</div>
	{:else}
		<div class="py-4">
			<!-- Warning banner with dark mode friendly colors - only show when cancellation is possible -->
			{#if canCancel}
				<div
					class="mb-5 flex items-start gap-3 rounded-md border border-amber-200/70 bg-amber-50/70 p-3 dark:border-amber-900/50 dark:bg-amber-950/20 dark:text-amber-300"
				>
					<AlertCircle class="h-5 w-5 shrink-0 text-amber-600 dark:text-amber-400" />
					<div>
						<p class="text-sm font-medium text-amber-800 dark:text-amber-300">
							You are about to cancel your registration
						</p>
						<p class="text-xs text-amber-700 dark:text-amber-400">
							This action cannot be undone and may affect your ability to rejoin if the class fills
							up.
						</p>
					</div>
				</div>
			{/if}

			{#if cancellationPolicy?.firstEventStartAt}
				{#if canCancel}
					<!-- Refund information with consistent spacing -->
					<div class="mb-4">
						<h3 class="mb-2 text-sm font-medium">Refund Information</h3>
						{#if returnUnits > 0}
							<p class="text-sm">
								<span class="font-medium text-primary"
									>{formatNumberWithCommas(returnUnits)} points</span
								> will be refunded to your account.
							</p>
						{:else}
							<p class="text-sm text-muted-foreground">
								No points will be refunded for this cancellation based on the current policy.
							</p>
						{/if}
					</div>

					{#if cancellationPolicy.firstEventStartAt > new Date()}
						<div class="mb-4">
							<CancellationPolicy
								firstEventStartDate={cancellationPolicy.firstEventStartAt}
								autoCancelAtFar={cancellationPolicy.autoCancelAtFar}
								autoCancelAtFarReturnUnits={cancellationPolicy.autoCancelAtFarReturnUnits}
								autoCancelAtNear={cancellationPolicy.autoCancelAtNear}
								autoCancelAtNearReturnUnits={cancellationPolicy.autoCancelAtNearReturnUnits}
							/>
						</div>
					{/if}
				{:else}
					<div
						class="flex items-start gap-3 rounded-md border border-destructive/50 bg-destructive/10 p-4 text-destructive"
					>
						<AlertCircle class="h-5 w-5 shrink-0" />
						<div>
							<p class="mb-2 font-medium">Cancellation Period Expired</p>
							<p class="text-sm">
								This class can no longer be cancelled as it is scheduled to begin soon or has
								already started.
							</p>
						</div>
					</div>
				{/if}
			{/if}

			{#if import.meta.env.DEV}
				<div class="mt-4 border-t border-muted pt-2 text-xs text-muted-foreground">
					<p>Debug info:</p>
					<ul class="list-disc pl-4">
						<li>Current stage: {currentStage}</li>
						<li>canCancel: {canCancel.toString()}</li>
						<li>returnUnits: {returnUnits}</li>
						{#if cancellationPolicy?.firstEventStartAt}
							<li>
								Event in: {(
									(new Date(cancellationPolicy.firstEventStartAt).getTime() -
										new Date().getTime()) /
									(1000 * 60)
								).toFixed(0)} minutes
							</li>
							{#if cancellationPolicy?.autoCancelAtFar}
								<li>Far threshold: {cancellationPolicy.autoCancelAtFar.toLocaleString()}</li>
							{/if}
							{#if cancellationPolicy?.autoCancelAtNear}
								<li>Near threshold: {cancellationPolicy.autoCancelAtNear.toLocaleString()}</li>
							{/if}
						{/if}
					</ul>
				</div>
			{/if}
		</div>
	{/if}
{/snippet}

{#snippet footer()}
	{#if !error}
		<div class="flex justify-between gap-2">
			<Button variant="ghost" disabled={isCancelling} onclick={onClose}>Keep Registration</Button>
			{#if canCancel}
				<Button onclick={cancelRegistration} variant="destructive" disabled={isCancelling}>
					{#if isCancelling}
						<div class="mr-2 animate-spin">
							<Loader2 class="h-4 w-4" />
						</div>
						Processing...
					{:else}
						Confirm Cancellation
					{/if}
				</Button>
			{/if}
		</div>
	{/if}
{/snippet}

<ResponsiveModal
	open={modalOpen}
	onOpenChange={handleOpenChange}
	title="Cancel Registration: {eventTitle}"
	description="Please review your cancellation options"
>
	{@render content()}
	{@render footer()}
</ResponsiveModal>
