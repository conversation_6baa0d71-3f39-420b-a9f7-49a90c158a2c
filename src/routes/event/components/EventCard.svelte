<!-- Event Card Component -->
<script lang="ts">
	import { parseISO, formatDistanceStrict, startOfWeek } from 'date-fns';
	import type { PageData } from '../$types';
	import { Button } from '$lib/components/ui/button';
	import {
		Clock,
		MapPin,
		Users,
		Link2,
		Ticket,
		Check,
		Loader2,
		X,
		Play,
		Pause
	} from '@lucide/svelte';
	import EventPlaceholder from './EventPlaceholder.svelte';
	import { buttonVariants } from '$lib/components/ui/button';
	import { dev } from '$app/environment';
	import UnregisterModal from './UnregisterModal.svelte';
	import CancelProductModal from './CancelProductModal.svelte';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import * as Popover from '$lib/components/ui/popover';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { goto, invalidate } from '$app/navigation';
	import type { FindOptionsResponse } from '../types';
	import EventRegistrationModal from './EventRegistrationModal.svelte';
	import AddressMapLink from './AddressMapLink.svelte';
	import EventStatusBanner from './EventStatusBanner.svelte';
	import { fade, scale, slide } from 'svelte/transition';
	import { cubicOut } from 'svelte/easing';
	import { formatTime, formatDateTime, formatDateTimeRange } from '$lib/utils/datetime';
	import { getEventTimezone } from '$lib/utils/event-timezone';
	import {
		getEventSemanticColors,
		getEventGradientClasses,
		getEventTextClasses,
		getEventGlowClasses
	} from '$lib/utils/event-colors';
	import TimezoneInfo from '$lib/components/shared/TimezoneInfo.svelte';

	interface Props {
		event: PageData['events'][number];
		supabase: any;
		userId?: string;
		brand?: any;
		currentlyPlayingEventId?: string;
		onVideoPlay?: (eventId: string) => void;
		onVideoPause?: (eventId: string) => void;
	}

	let {
		event,
		supabase,
		userId,
		brand,
		currentlyPlayingEventId,
		onVideoPlay,
		onVideoPause
	}: Props = $props();
	let showUnregisterModal = $state(false);
	let selectedOrderProductId = $state('');
	let isLoadingUnregister = $state(false);
	let cancellationData = $state<any>(null);

	// Single loading state for all card actions
	let isLoading = $state(false);

	// State for registration data and modal
	let optionsData = $state<FindOptionsResponse | null>(null);
	let showRegistrationModal = $state(false);
	let selectedProductId = $state<string>('');

	// Cancel product states
	let showCancelProductModal = $state(false);
	let selectedCancelProductId = $state('');
	let cancelDryRunData = $state<any>(null);
	let isLoadingCancelDryRun = $state(false);

	// Video player states
	let showFullWidthVideo = $state(false);
	let isVideoPlaying = $state(false);

	// Timezone difference state
	let showTimezoneInfo = $state(false);

	// Derived state - this card is playing if it matches the currently playing event
	let isThisCardPlaying = $derived(currentlyPlayingEventId === event.id);

	// Get event timezone
	const eventTimezone = $derived(
		getEventTimezone(event) || Intl.DateTimeFormat().resolvedOptions().timeZone
	) as string;

	// Get event colors and gradient classes
	const eventColors = $derived(getEventSemanticColors(event));
	const gradientClasses = $derived(getEventGradientClasses(eventColors));
	const textClasses = $derived(getEventTextClasses(eventColors));
	const glowClasses = $derived(getEventGlowClasses(eventColors));

	// Effect to sync video state with global playing state
	$effect(() => {
		if (currentlyPlayingEventId === event.id) {
			// This card should be playing
			if (!showFullWidthVideo) {
				showFullWidthVideo = true;
				isVideoPlaying = true;
			}
		} else if (
			currentlyPlayingEventId &&
			currentlyPlayingEventId !== event.id &&
			showFullWidthVideo
		) {
			// Another card is playing, pause this one
			showFullWidthVideo = false;
			isVideoPlaying = false;
		}
	});

	// Simple function to refresh event data
	async function refreshEventData() {
		await invalidate('events');
	}

	// Video player functions
	function handlePlayVideo() {
		showFullWidthVideo = true;
		isVideoPlaying = true;
		onVideoPlay?.(event.id);
	}

	function handlePauseVideo() {
		isVideoPlaying = false;
		onVideoPause?.(event.id);
	}

	function handleCloseVideo() {
		showFullWidthVideo = false;
		isVideoPlaying = false;
		onVideoPause?.(event.id);
	}

	// Fetch buying options and handle registration
	async function fetchBuyingOptions(productId: string) {
		if (!productId) return;

		isLoading = true;
		optionsData = null;

		try {
			const response = await fetch(`/event/private/find-options?productId=${productId}`, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json'
				}
			});

			const result = await response.json();

			if (result.type === 'error') {
				throw new Error(result.error.message);
			}

			if (result.type === 'success' && result.data) {
				optionsData = result.data;
				selectedProductId = productId;
				showRegistrationModal = true;
			} else {
				throw new Error('Invalid response structure');
			}
		} catch (err) {
			console.error('Error in fetchBuyingOptions:', err);
		} finally {
			isLoading = false;
		}
	}

	// Add function to handle registration click
	function handleRegistration() {
		if (!userId) {
			// Redirect to sign-in with event ID
			goto(`/auth/sign-in?eid=${event.id}`);
			return;
		}

		if (event.products.length === 0) return;

		const primaryProduct = event.products[0].product;
		if (!primaryProduct?.id) return;

		fetchBuyingOptions(primaryProduct.id);
	}

	// Handle modal close
	function handleModalClose() {
		showRegistrationModal = false;

		// Always refresh data when modal is closed (registration may have happened)
		refreshEventData();

		// Reset state
		optionsData = null;
		selectedProductId = '';
	}

	function formatEventDateTime(dateStr: string | null): string {
		if (!dateStr) return 'TBA';
		return formatDateTime(dateStr, { timeZone: eventTimezone });
	}

	function formatEventTime(dateStr: string | null): string {
		if (!dateStr) return 'TBA';
		return formatTime(dateStr, { timeZone: eventTimezone });
	}

	function formatEventDate(dateStr: string | null): string {
		if (!dateStr) return 'TBA';
		const date = parseISO(dateStr);
		const endDate = new Date(date.getTime() + event.duration_minute * 60 * 1000);
		return formatDateTimeRange(date, endDate, { timeZone: eventTimezone });
	}

	function formatDuration(minutes: number): string {
		return `${minutes} min`;
	}

	function isProductAvailable(
		product: PageData['events'][number]['products'][number]['product']
	): boolean {
		return (
			(product as any).auto_stock_available > 0 &&
			(!(product as any).open_to_buy_at || new Date((product as any).open_to_buy_at) <= new Date())
		);
	}

	// Check if the event is sold out (all products have zero or negative stock)
	function isEventSoldOut(event: PageData['events'][number]): boolean {
		return event.products.every((p: any) => !isProductAvailable(p.product));
	}

	// Check if the user is already registered for this event
	function isUserRegistered(event: PageData['events'][number]): boolean {
		return event.products.some((p: any) => (p.product as any).user_registered === true);
	}

	// Get the first order product ID for the registered product
	function getRegisteredOrderProductId(event: PageData['events'][number]): string | null {
		if (!isUserRegistered(event)) return null;

		for (const eventProduct of event.products) {
			const product = eventProduct.product as any;
			if (product.user_registered && product.order_product) {
				for (const orderProduct of product.order_product) {
					if (orderProduct.canceled_at === null && orderProduct.auto_units_owed <= 0) {
						return orderProduct.id;
					}
				}
			}
		}
		return null;
	}

	// New function to check if user is on waitlist for any product
	function isUserOnWaitlist(event: PageData['events'][number]): boolean {
		if (!userId) return false;

		return event.products.some((p: any) => {
			const product = p.product;
			return (
				product.waiting_group?.group_member &&
				product.waiting_group.group_member.some((member: any) => member.user_id === userId)
			);
		});
	}

	// New function to get the product to use for waitlist actions
	function getPrimaryWaitlistProduct(event: PageData['events'][number]): any {
		// Return the first product that has a waiting_group
		return event.products.find((p: any) => p.product.waiting_group?.id)?.product;
	}

	async function handleUnregister() {
		const orderProductId = getRegisteredOrderProductId(event);
		if (orderProductId) {
			selectedOrderProductId = orderProductId;
			isLoadingUnregister = true;

			try {
				// Fetch cancellation info before showing modal
				const response = await fetch(
					`/event/private/cancel-order?orderProductId=${orderProductId}`,
					{
						method: 'GET',
						headers: {
							'Content-Type': 'application/json'
						}
					}
				);

				const result = await response.json();

				if (result.type === 'error') {
					throw new Error(result.error.message);
				}

				// Store the fetched data
				cancellationData = result.data;

				// Only show modal after data is loaded
				showUnregisterModal = true;
			} catch (err) {
				console.error('Error checking cancellation eligibility:', err);
				// Handle error (could show toast here)
			} finally {
				isLoadingUnregister = false;
			}
		}
	}

	async function handleCancelProduct(productId: string) {
		if (!supabase) {
			console.error('Supabase client not provided');
			return;
		}

		selectedCancelProductId = productId;
		isLoadingCancelDryRun = true;

		try {
			// Use the admin function to perform a dry run (input_perform_update = false)
			const { data, error } = await supabase.rpc('admin_cancel_order_product_on_product', {
				input_product_id: productId,
				input_add_to_product_id: null,
				input_hide_product_events: true,
				input_perform_update: false
			});

			if (error) {
				throw error;
			}

			// Store the dry run data
			cancelDryRunData = data;

			// Show the cancellation modal
			showCancelProductModal = true;
		} catch (err) {
			console.error('Error performing cancel product dry run:', err);
		} finally {
			isLoadingCancelDryRun = false;
		}
	}

	// Function to handle unregister success
	async function handleUnregisterSuccess() {
		showUnregisterModal = false;
		await refreshEventData();
	}

	function getEventNumber(
		product: PageData['events'][number]['products'][number]['product'],
		eventStartAt: string
	): number {
		if (!product.first_event_start_at || !product.last_event_end_at) return 0;
		const eventDate = parseISO(eventStartAt);
		const firstEventDate = parseISO(product.first_event_start_at);
		const lastEventDate = parseISO(product.last_event_end_at);

		return (
			Math.floor(
				((eventDate.getTime() - firstEventDate.getTime()) /
					(lastEventDate.getTime() - firstEventDate.getTime())) *
					(product.event_count - 1)
			) + 1
		);
	}

	// Get the landmark name from the event (for section headers)
	function getLandmarkName(): string {
		if (event.landmark?.title_short) {
			return getLocalizedText(event.landmark.title_short as LocalizedText);
		}
		return event.location.split(', ')[1] || event.location; // Fallback to old method
	}

	// New function to handle joining waitlist
	async function handleJoinWaitlist() {
		if (!userId) {
			// Redirect to sign-in with event ID
			goto(`/auth/sign-in?eid=${event.id}`);
			return;
		}

		if (!supabase) return;

		const product = getPrimaryWaitlistProduct(event);
		if (!product) return;

		isLoading = true;

		try {
			const { data, error } = await supabase.rpc('new_product_group_member', {
				input_product_id: product.id,
				input_user_id: userId,
				input_group_kind: 'waiting_group'
			});

			if (error) throw error;

			// Refresh event data directly
			await refreshEventData();
		} catch (err) {
			console.error('Error joining waitlist:', err);
		} finally {
			isLoading = false;
		}
	}

	// New function to handle leaving waitlist
	async function handleLeaveWaitlist() {
		if (!supabase || !userId) return;

		const product = getPrimaryWaitlistProduct(event);
		if (!product || !product.waiting_group?.id) return;

		isLoading = true;

		try {
			const { data, error } = await supabase.rpc('delete_group_member', {
				input_group_id: product.waiting_group.id,
				input_user_id: userId
			});

			if (error) throw error;

			// Refresh event data directly
			await refreshEventData();
		} catch (err) {
			console.error('Error leaving waitlist:', err);
		} finally {
			isLoading = false;
		}
	}
</script>

<div class="space-y-2 py-2">
	<!-- Inline Video Player -->
	{#if showFullWidthVideo && (event.metadata as any).promo_video_url}
		<div
			class="relative w-full overflow-hidden rounded-lg bg-black"
			style="aspect-ratio: 16/9;"
			transition:slide={{ duration: 400, easing: cubicOut }}
		>
			<button
				onclick={handleCloseVideo}
				class="absolute top-2 right-4 z-10 rounded-full bg-gray-900/60 p-1.5 text-white backdrop-blur-sm transition-all duration-200 hover:bg-gray-900/80"
			>
				<X
					class="h-8 w-8 text-white/80 transition-all duration-200 hover:scale-110 hover:text-white"
				/>
			</button>

			<!-- svelte-ignore a11y_media_has_caption -->
			<video
				src={(event.metadata as any).promo_video_url}
				class="h-full w-full"
				controls
				autoplay
				onpause={handlePauseVideo}
				onplay={() => {
					isVideoPlaying = true;
				}}
			>
				<source src={(event.metadata as any).promo_video_url} type="video/mp4" />
				Your browser does not support the video tag.
			</video>
		</div>
	{/if}

	<!-- Regular Event Card Content -->
	<!-- Status Banner - Full Width -->
	<EventStatusBanner
		startAt={event.start_at}
		durationMinute={event.duration_minute}
		{event}
		className=""
	/>

	<div class="flex gap-2">
		<!-- Left Column - Responsive Width (Time and Preview) -->
		<div class="flex w-[88px] flex-col gap-2 sm:w-[140px] md:w-[160px]">
			<!-- Time with background (full width) -->
			<div
				class="group flex w-full items-center justify-center rounded-lg py-1.5 text-center transition-all duration-200 sm:py-2 {gradientClasses} {glowClasses}"
			>
				<TimezoneInfo
					eventDate={event.start_at}
					{eventTimezone}
					buttonClass="w-full cursor-pointer"
					bind:showInfo={showTimezoneInfo}
				>
					<div class="relative flex h-5 items-center justify-center leading-none sm:h-5">
						<div
							class="font-mono text-base leading-none font-semibold sm:text-lg md:text-xl {textClasses}"
						>
							{formatEventTime(event.start_at)}
						</div>
						{#if showTimezoneInfo}
							<div
								class="absolute top-1/2 left-[calc(100%+0.25rem)] h-1 w-1 -translate-y-1/2 rounded-full opacity-60 shadow-sm group-hover:opacity-80 {textClasses ===
								'text-white'
									? 'bg-white'
									: 'bg-gray-800'}"
								style="box-shadow: 0 0 6px rgba(255, 255, 255, 0.4);"
							></div>
						{/if}
					</div>
				</TimezoneInfo>
			</div>

			<!-- Preview Image with rounded corners -->
			<div class="aspect-square w-full overflow-hidden rounded-lg">
				{#if showFullWidthVideo}
					<!-- Close button when video is playing -->
					<button
						onclick={handleCloseVideo}
						class="flex h-full w-full items-center justify-center bg-gray-900/60 transition-all duration-200 hover:bg-gray-900/80"
					>
						<X
							class="h-8 w-8 text-white/80 transition-all duration-200 hover:scale-110 hover:text-white"
						/>
					</button>
				{:else if (event.metadata as any).promo_video_thumbnail_url}
					<!-- Priority 1: Video thumbnail -->
					<div class="relative h-full w-full">
						<img
							src={(event.metadata as any).promo_video_thumbnail_url}
							alt={event.title}
							class="h-full w-full object-cover"
						/>
						{#if (event.metadata as any).promo_video_url}
							<button
								onclick={handlePlayVideo}
								class="absolute inset-0 flex items-center justify-center bg-black/20 transition-all duration-300 hover:bg-black/30"
							>
								<Play
									class="h-8 w-8 text-white/80 transition-all duration-200 hover:scale-110 hover:text-white"
								/>
							</button>
						{/if}
					</div>
				{:else if (event.metadata as any).promo_image_url}
					<!-- Priority 2: Static image -->
					<div class="relative h-full w-full">
						<img
							src={(event.metadata as any).promo_image_url}
							alt={event.title}
							class="h-full w-full object-cover"
						/>
						{#if (event.metadata as any).promo_video_url}
							<button
								onclick={handlePlayVideo}
								class="absolute inset-0 flex items-center justify-center bg-black/20 transition-all duration-300 hover:bg-black/30"
							>
								<Play
									class="h-8 w-8 text-white/80 transition-all duration-200 hover:scale-110 hover:text-white"
								/>
							</button>
						{/if}
					</div>
				{:else if (event.metadata as any).promo_video_url}
					<!-- Priority 3: Video without controls (browser thumbnail) -->
					<div class="relative h-full w-full">
						<video
							src={(event.metadata as any).promo_video_url}
							class="h-full w-full object-cover"
							muted
							preload="metadata"
						></video>
						<button
							onclick={handlePlayVideo}
							class="absolute inset-0 flex items-center justify-center bg-black/20 transition-all duration-300 hover:bg-black/30"
						>
							<Play
								class="h-8 w-8 text-white/80 transition-all duration-200 hover:scale-110 hover:text-white"
							/>
						</button>
					</div>
				{:else}
					<!-- Priority 4: Placeholder -->
					<EventPlaceholder
						title={typeof event.title === 'string' ? event.title : 'Event'}
						height="100%"
					/>
				{/if}
			</div>
		</div>

		<!-- Right Column - Flexible Width (All Content) -->
		<div class="flex min-w-0 flex-1 flex-col justify-between">
			<!-- Top content area (title, subtitle) -->
			<div>
				<div class="flex items-start justify-between gap-2 py-0.5">
					<!-- Title -->
					<div class="min-w-0 flex-1">
						<a href="/event/{event.id}" class="block">
							<h3 class="line-clamp-2 text-base font-medium hover:underline">{event.title}</h3>
						</a>

						<!-- Subtitle (if available) -->
						{#if event.subtitle}
							{#if dev}
								<Popover.Root>
									<Popover.Trigger class="w-full text-left">
										<p class="text-muted-foreground mt-1 line-clamp-2 cursor-pointer text-sm">
											{event.subtitle}
										</p>
									</Popover.Trigger>
									<Popover.Content class="w-80">
										<div class="space-y-2">
											<h4 class="leading-none font-medium">Debug Information</h4>
											<div class="text-muted-foreground/60 mt-0.5 text-xs break-all">
												<div>Event: {event.id}</div>
												<div>Meta: {(event.metadata as any).id ?? 'N/A'}</div>
												{#each event.products as { product }, index}
													<div class="mt-2 flex items-center">
														<div class="flex-1">
															Product {index + 1}: {(product as any).id} ({product.event_count} primary
															events)
														</div>
														<Button
															variant="destructive"
															size="sm"
															class="h-6 text-xs"
															onclick={() => handleCancelProduct((product as any).id)}
															disabled={isLoadingCancelDryRun}
														>
															{#if isLoadingCancelDryRun && selectedCancelProductId === (product as any).id}
																<Loader2 class="mr-1 h-3 w-3 animate-spin" />
																Loading...
															{:else}
																Cancel Product
															{/if}
														</Button>
													</div>
												{/each}
											</div>
										</div>
									</Popover.Content>
								</Popover.Root>
							{:else}
								<p class="text-muted-foreground mt-1 line-clamp-2 text-sm">{event.subtitle}</p>
							{/if}
						{/if}

						<!-- Instructor -->
						<div class="mt-2 space-y-1">
							{#if event.host}
								<div class="flex items-center gap-1 text-sm">
									<Users class="text-muted-foreground h-3.5 w-3.5 shrink-0" />
									<span class="text-muted-foreground truncate">{event.host}</span>
								</div>
							{/if}

							<!-- Location -->
							<div class="flex items-start gap-1 text-sm">
								<MapPin class="text-muted-foreground mt-0.5 h-3.5 w-3.5 shrink-0" />
								<AddressMapLink {event}>
									<span
										class="text-muted-foreground line-clamp-2 cursor-pointer text-left hover:underline"
									>
										{event.location}
									</span>
								</AddressMapLink>
							</div>
						</div>
					</div>

					<!-- Duration Badge -->
					<div class="bg-muted/50 text-muted-foreground shrink-0 rounded-full px-2.5 py-1 text-sm">
						{formatDuration(event.duration_minute)}
					</div>
				</div>
			</div>

			<!-- Bottom area with register button only -->
			<div class="mt-2 flex justify-end">
				<!-- Registration Button -->
				{#if event.products.length > 0}
					<div class="shrink-0">
						{#if isUserRegistered(event)}
							<div class="flex gap-1">
								<Button variant="ghost" class="h-8 px-2 py-0 text-sm" disabled>
									<Check class="mr-1 h-3.5 w-3.5" />
									Registered
								</Button>
								<Button
									variant="destructive"
									class="h-8 px-2 py-0 text-sm"
									onclick={handleUnregister}
									disabled={isLoadingUnregister}
								>
									{#if isLoadingUnregister}
										<Loader2 class="h-3.5 w-3.5 animate-spin" />
									{:else}
										Unregister
									{/if}
								</Button>
							</div>
						{:else if isEventSoldOut(event)}
							{#if isUserOnWaitlist(event)}
								<Button
									variant="outline"
									class="h-8 px-3 text-sm"
									onclick={handleLeaveWaitlist}
									disabled={isLoading}
								>
									{#if isLoading}
										<Loader2 class="mr-1 h-3.5 w-3.5 animate-spin" />
										Loading...
									{:else}
										Leave Waitlist
									{/if}
								</Button>
							{:else}
								<Button
									variant="outline"
									class="h-8 px-3 text-sm"
									onclick={handleJoinWaitlist}
									disabled={isLoading}
								>
									{#if isLoading}
										<Loader2 class="mr-1 h-3.5 w-3.5 animate-spin" />
										Loading...
									{:else}
										Join Waitlist
									{/if}
								</Button>
							{/if}
						{:else if isLoading}
							<Button variant="default" class="h-8 px-3 text-sm" disabled>
								<Loader2 class="mr-1 h-3.5 w-3.5 animate-spin" />
								Loading
							</Button>
						{:else}
							<Button variant="default" class="h-8 px-3 text-sm" onclick={handleRegistration}>
								{userId
									? event.products.length > 1
										? 'View Options'
										: 'Register'
									: 'Login & Register'}
							</Button>
						{/if}
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>

{#if showUnregisterModal && selectedOrderProductId && cancellationData}
	<UnregisterModal
		orderProductId={selectedOrderProductId}
		eventTitle={event.title}
		onClose={() => (showUnregisterModal = false)}
		onSuccess={handleUnregisterSuccess}
		{cancellationData}
	/>
{/if}

{#if showCancelProductModal && selectedCancelProductId && cancelDryRunData && supabase}
	<CancelProductModal
		productId={selectedCancelProductId}
		eventTitle={event.title}
		eventTime={formatEventDateTime(event.start_at)}
		eventLocation={event.location}
		eventHost={event.host || ''}
		onClose={() => (showCancelProductModal = false)}
		onSuccess={handleUnregisterSuccess}
		dryRunData={cancelDryRunData}
		{supabase}
	/>
{/if}

<!-- Registration Modal -->
{#if showRegistrationModal && optionsData && userId}
	<EventRegistrationModal
		selectedEvent={event}
		{selectedProductId}
		{optionsData}
		{userId}
		{brand}
		onOpenChange={handleModalClose}
	/>
{/if}
