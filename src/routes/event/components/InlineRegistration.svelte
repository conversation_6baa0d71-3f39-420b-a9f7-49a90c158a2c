<!-- Inline Registration Component -->
<script lang="ts">
	import type { PageData } from '../$types';
	import type { Brand, FindOptionsResponse, ProductDetails } from '../types';
	import { invalidate, invalidateAll } from '$app/navigation';
	import PasscodeForm from './modal-components/PasscodeForm.svelte';
	import ProductSelector from './modal-components/ProductSelector.svelte';
	import ProductInformation from './modal-components/ProductInformation.svelte';
	import RegistrationActions from './modal-components/RegistrationActions.svelte';
	import PaymentOptionSelector from './modal-components/PaymentOptionSelector.svelte';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { Button } from '$lib/components/ui/button';
	import { Loader2, ChevronDown, ChevronUp, Tag } from '@lucide/svelte';
	import { slide } from 'svelte/transition';

	interface Props {
		product: ProductDetails;
		userId: string;
		brand: App.Locals['brand'];
		onRegistrationSuccess?: () => void;
		isRegistered?: boolean;
		onUnregister?: () => void;
		// New props for modal usage
		preloadedOptionsData?: FindOptionsResponse | null;
		isWalkinMode?: boolean;
		hideRegistrationActions?: boolean;
		buyingOptionSelections?: Map<string, string[]>;
		optionsData?: FindOptionsResponse | null;
	}

	let {
		product = $bindable(),
		userId,
		brand,
		onRegistrationSuccess = $bindable(),
		isRegistered = false,
		onUnregister,
		// New props with defaults
		preloadedOptionsData = null,
		isWalkinMode = false,
		hideRegistrationActions = false,
		buyingOptionSelections = $bindable(new Map<string, string[]>()),
		optionsData = $bindable<FindOptionsResponse | null>(null)
	}: Props = $props();

	// State management
	let isLoading = $state(false);
	let initializedProducts = new Set<string>();
	let showMoreOptions = $state(false);
	let isUnregistering = $state(false);

	// Initialize optionsData with preloaded data
	$effect(() => {
		if (preloadedOptionsData && !optionsData) {
			optionsData = preloadedOptionsData;
		}
	});

	// Auto-select when options data changes
	$effect(() => {
		if (optionsData && product?.id && !initializedProducts.has(product.id)) {
			setTimeout(autoSelectOptions, 0);
		}
	});

	// Check if ANY product_price requires a passcode
	let hasPasscodeProtectedPrices = $derived(
		product.product_price?.some(
			(price) =>
				price.order_requirement?.require_passcode &&
				price.order_requirement.require_passcode.length > 0
		)
	);

	// Check if we should show the registration content
	let showRegistrationContent = $derived(!!optionsData?.consumer_items?.length);

	// Auto-expand "More options" only on initial load for passcode-only classes
	let hasInitialized = false;
	$effect(() => {
		// Only check once on initial load
		if (!hasInitialized && optionsData !== null) {
			hasInitialized = true;
			// If no options but has passcode requirements, expand
			if (!optionsData.consumer_items?.length && hasPasscodeProtectedPrices) {
				showMoreOptions = true;
			}
		}
	});

	// Fetch buying options
	async function fetchBuyingOptions(productId: string) {
		if (!productId) return;

		// Skip if we already have preloaded options data
		if (preloadedOptionsData) return;

		isLoading = true;
		optionsData = null;

		try {
			// For walk-in mode, pass the userId as profileId
			const url = isWalkinMode
				? `/event/private/find-options?productId=${productId}&profileId=${userId}`
				: `/event/private/find-options?productId=${productId}`;

			const response = await fetch(url, {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json'
				}
			});

			const result = await response.json();

			if (result.type === 'error') {
				throw new Error(result.error.message);
			}

			if (result.type === 'success' && result.data) {
				optionsData = result.data;
				// Auto-select options for the new product
				if (!initializedProducts.has(productId)) {
					setTimeout(autoSelectOptions, 0);
				}
			}
		} catch (err) {
			console.error('Error fetching buying options:', err);
		} finally {
			isLoading = false;
		}
	}

	// Auto-select options logic (similar to EventRegistrationModal)
	function autoSelectOptions() {
		if (!optionsData?.consumer_items || !product.id) return;

		if (initializedProducts.has(product.id)) return;

		console.log('Auto-selecting options for product:', product.id, 'in walkin mode:', isWalkinMode);

		const newSelections = new Map<string, string[]>();

		for (const item of optionsData.consumer_items) {
			if (!item.consumer_profile || !item.product_prices || item.product_prices.length === 0)
				continue;

			const consumerProfileId = item.consumer_profile.id;
			if (!consumerProfileId) continue;

			const firstPrice = item.product_prices[0];
			const hasAvailableUnits = (firstPrice.consumer_total_units_available ?? 0) > 0;
			const hasSufficientUnits = (firstPrice.consumer_total_units_remaining ?? 0) >= 0;

			// For walk-in mode, we should only show unit options
			if (isWalkinMode) {
				// Only select unit options when there are sufficient units
				if (hasSufficientUnits && firstPrice.order_prices && firstPrice.order_prices.length > 0) {
					const firstOrderPrice = firstPrice.order_prices[0];
					if (firstOrderPrice.order_price?.id) {
						newSelections.set(consumerProfileId, [firstOrderPrice.order_price.id]);
					}
				}
				continue;
			}

			// Normal mode - try money options first (price_option_combinations)
			if (firstPrice.price_option_combinations && firstPrice.price_option_combinations.length > 0) {
				const firstOption = firstPrice.price_option_combinations[0];
				if (firstOption?.options) {
					const optionIds = firstOption.options
						.map((opt) => opt.price_option?.id ?? '')
						.filter((id) => id);

					if (optionIds.length > 0) {
						const optionIdString = optionIds.join(',');
						newSelections.set(consumerProfileId, [optionIdString]);
						continue;
					}
				}
			}

			// Fall back to unit options if sufficient units
			if (hasSufficientUnits && firstPrice.order_prices && firstPrice.order_prices.length > 0) {
				const firstOrderPrice = firstPrice.order_prices[0];
				if (firstOrderPrice.order_price?.id) {
					newSelections.set(consumerProfileId, [firstOrderPrice.order_price.id]);
				}
			}
		}

		// Always assign a new Map instance to trigger reactivity
		buyingOptionSelections = newSelections;

		initializedProducts.add(product.id);
	}

	// Handle option selection
	function handleOptionSelect(consumerId: string, optionId: string) {
		buyingOptionSelections = new Map(buyingOptionSelections);
		buyingOptionSelections.set(consumerId, [optionId]);
	}

	// Handle passcode submission
	async function handlePasscodeSubmission(passcode: string) {
		if (!product?.id) {
			throw new Error('Missing product ID for passcode submission');
		}

		try {
			// For walk-in mode, use userId as profileId
			const profileId = isWalkinMode ? userId : optionsData?.payer_id || '';
			const response = await fetch(
				`/event/private/find-options?productId=${product.id}&profileId=${profileId}&passcode=${encodeURIComponent(passcode)}`,
				{
					method: 'GET',
					headers: {
						'Content-Type': 'application/json'
					}
				}
			);

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Failed to verify passcode');
			}

			const result = await response.json();

			if (result.type === 'error') {
				throw new Error(result.error.message || 'Invalid passcode');
			}

			// Update options data with passcode-protected options
			optionsData = result.data;

			// Reset initialization and clear selections
			if (initializedProducts.has(product.id)) {
				initializedProducts.delete(product.id);
			}

			// Clear existing selections before auto-selecting new options
			buyingOptionSelections = new Map();

			// Auto-select new options after a tick to ensure state updates
			setTimeout(() => {
				autoSelectOptions();
			}, 0);

			return result.data;
		} catch (err) {
			console.error('Error submitting passcode:', err);
			throw err;
		}
	}

	// Handle registration success
	function handleRegistrationSuccess() {
		if (onRegistrationSuccess) {
			onRegistrationSuccess();
		}
	}

	// Handle unregister
	async function handleUnregister() {
		if (!onUnregister) return;

		isUnregistering = true;
		try {
			await onUnregister();
			// Also refresh the page after successful unregistration
			if (onRegistrationSuccess) {
				onRegistrationSuccess();
			}
		} finally {
			isUnregistering = false;
		}
	}

	// Initialize - fetch options when component loads
	$effect(() => {
		if (product?.id && product.auto_stock_available > 0 && !isRegistered && !preloadedOptionsData) {
			// Only fetch if we don't have options data yet
			if (!optionsData) {
				fetchBuyingOptions(product.id);
			}
		}
	});
</script>

{#if isRegistered}
	<!-- User is already registered -->
	<div class="space-y-6">
		<!-- Always show product information -->
		<ProductInformation {product} />

		<!-- Unregister button -->
		<div class="border-t pt-4">
			<div class="flex items-center justify-between">
				<p class="text-sm text-muted-foreground">You're registered for this event</p>
				{#if onUnregister}
					<Button variant="outline" onclick={handleUnregister} disabled={isUnregistering}>
						{#if isUnregistering}
							<Loader2 class="mr-2 h-4 w-4 animate-spin" />
							Unregistering...
						{:else}
							Unregister
						{/if}
					</Button>
				{/if}
			</div>
		</div>
	</div>
{:else}
	<!-- Registration flow -->
	<div class="space-y-6">
		{#if isWalkinMode}
			<div
				class="rounded-lg border border-amber-200 bg-amber-50 p-3 dark:border-amber-800 dark:bg-amber-950/30"
			>
				<p class="text-sm text-amber-800 dark:text-amber-300">
					<strong>Walk-in Mode:</strong> Registration uses existing points only. Payment options are
					not available for walk-in registration.
				</p>
			</div>
		{/if}

		{#if isLoading}
			<!-- Loading State -->
			<div class="flex items-center justify-center p-8">
				<div class="flex items-center gap-2">
					<Loader2 class="h-4 w-4 animate-spin" />
					<span class="text-sm text-muted-foreground">Loading registration options...</span>
				</div>
			</div>
		{:else if product.auto_stock_available <= 0}
			<!-- Sold out message -->
			<div class="p-4 text-center">
				<p class="text-sm text-muted-foreground">
					This event is currently sold out. You can join the waitlist using the button above.
				</p>
			</div>
		{:else}
			<!-- Show available registration options if we have them -->
			{#if showRegistrationContent}
				<!-- Registration Content -->
				<div class="space-y-6">
					<!-- Product Information -->
					<ProductInformation {product} />

					<!-- Payment Options -->
					{#if optionsData?.consumer_items}
						{#each optionsData.consumer_items as item (item.consumer_profile?.id)}
							{#if item.consumer_profile?.id && item.product_prices?.length}
								<PaymentOptionSelector
									consumerItem={item}
									{isWalkinMode}
									selectedOptionId={buyingOptionSelections.get(item.consumer_profile.id)?.[0] ??
										null}
									onOptionSelect={(optionId) =>
										item.consumer_profile?.id &&
										handleOptionSelect(item.consumer_profile.id, optionId)}
									selectedProduct={product}
									{brand}
								/>
							{/if}
						{/each}
					{/if}

					<!-- More options toggle (for passcode form) -->
					{#if hasPasscodeProtectedPrices}
						<div class="space-y-2">
							<Button
								variant="ghost"
								size="sm"
								onclick={() => (showMoreOptions = !showMoreOptions)}
								class="flex items-center gap-1 text-muted-foreground hover:text-foreground"
							>
								<Tag class="h-3 w-3" />
								<span>More options</span>
								{#if showMoreOptions}
									<ChevronUp class="h-3 w-3" />
								{:else}
									<ChevronDown class="h-3 w-3" />
								{/if}
							</Button>

							<!-- Passcode form right below the button -->
							{#if showMoreOptions}
								<div transition:slide={{ duration: 300 }} class="rounded-md bg-muted/50 p-4">
									<PasscodeForm onSubmit={handlePasscodeSubmission} />
								</div>
							{/if}
						</div>
					{/if}

					<!-- Registration Actions -->
					{#if !hideRegistrationActions}
						<RegistrationActions
							selectedProduct={product}
							{optionsData}
							{buyingOptionSelections}
							{isWalkinMode}
							{userId}
							onSuccess={handleRegistrationSuccess}
						/>
					{/if}
				</div>
			{/if}

			<!-- Initial state or no options state -->
			{#if !showRegistrationContent}
				<div class="space-y-6">
					<!-- Product Information - always show -->
					<ProductInformation {product} />

					<!-- Passcode form if product has passcode requirements -->
					{#if hasPasscodeProtectedPrices}
						<div class="space-y-2">
							<Button
								variant="ghost"
								size="sm"
								onclick={() => (showMoreOptions = !showMoreOptions)}
								class="flex items-center gap-1 text-muted-foreground hover:text-foreground"
							>
								<Tag class="h-3 w-3" />
								<span>More options</span>
								{#if showMoreOptions}
									<ChevronUp class="h-3 w-3" />
								{:else}
									<ChevronDown class="h-3 w-3" />
								{/if}
							</Button>

							<!-- Passcode form right below the button -->
							{#if showMoreOptions}
								<div transition:slide={{ duration: 300 }} class="rounded-md bg-muted/50 p-4">
									<PasscodeForm onSubmit={handlePasscodeSubmission} />
								</div>
							{/if}
						</div>
					{/if}

					<!-- Messages -->
					{#if optionsData && !optionsData.consumer_items?.length}
						<!-- No options available -->
						<div class="p-4 text-center">
							<p class="text-sm text-muted-foreground">
								No registration options are currently available for this event.
							</p>
						</div>
					{:else if !preloadedOptionsData}
						<!-- Initial state - show a simple message -->
						<div class="p-4 text-center">
							<p class="text-sm text-muted-foreground">
								Registration options will appear here once loaded.
							</p>
						</div>
					{/if}
				</div>
			{/if}
		{/if}
	</div>
{/if}
