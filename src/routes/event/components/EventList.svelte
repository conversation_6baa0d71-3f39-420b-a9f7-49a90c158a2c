<!-- Event list with cards -->
<script lang="ts">
	import { format, isSameDay, parseISO, getDay, isToday, isTomorrow, isYesterday } from 'date-fns';
	import type { PageData } from '../$types';
	import EventCard from './EventCard.svelte';
	import { dev } from '$app/environment';
	import type { SupabaseClient } from '@supabase/supabase-js';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
	import { getEventTimezone } from '$lib/utils/event-timezone';
	import { formatDateShort } from '$lib/utils/datetime';

	interface Props {
		events: PageData['events'];
		selectedDate: Date | null;
		supabase?: SupabaseClient;
		userId?: string;
		brand?: any;
	}

	let { events, selectedDate, supabase, userId, brand }: Props = $props();

	// Debug the incoming data
	console.log('Events count:', events.length);
	console.log('Selected date:', selectedDate);

	// Filter events based on selected date's day of week
	let filteredEvents = $derived(() => {
		console.log('Filtering events for day:', selectedDate ? getDay(selectedDate) : 'All');

		if (selectedDate === null) {
			console.log('Showing all events:', events.length);
			return events;
		}

		const selectedDay = getDay(selectedDate);
		const filtered = events.filter((event) => {
			// Get event timezone
			const eventTimezone =
				getEventTimezone(event) || Intl.DateTimeFormat().resolvedOptions().timeZone;

			// Get the day of week in the event's timezone
			const eventDate = new Date(event.start_at);
			const formatter = new Intl.DateTimeFormat('en-US', {
				timeZone: eventTimezone,
				weekday: 'long'
			});
			const eventDayName = formatter.format(eventDate);

			// Map day name to day number (0 = Sunday, 1 = Monday, etc.)
			const dayMap: Record<string, number> = {
				Sunday: 0,
				Monday: 1,
				Tuesday: 2,
				Wednesday: 3,
				Thursday: 4,
				Friday: 5,
				Saturday: 6
			};
			const eventDay = dayMap[eventDayName];

			const matches = eventDay === selectedDay;
			return matches;
		});

		console.log(`Found ${filtered.length} events for day ${selectedDay}`);
		return filtered;
	});

	interface EventsByLocation {
		[location: string]: PageData['events'][number][];
	}

	interface DayGroup {
		__label: string;
		__timestamp: number;
		__dateStr: string;
		__shortDateStr: string;
		[location: string]: string | number | PageData['events'][number][];
	}

	interface GroupsObject {
		[day: string]: DayGroup;
	}

	interface GroupedLocation {
		location: string;
		landmarkTitle: LocalizedText;
		events: PageData['events'][number][];
	}

	interface DayGroupResult {
		day: string;
		label: string;
		dateStr: string;
		shortDateStr: string;
		locations: GroupedLocation[];
	}

	// Group events by day and location
	let groupedEvents = $derived(() => {
		const groups: GroupsObject = {};

		filteredEvents().forEach((event) => {
			// Get event timezone
			const eventTimezone =
				getEventTimezone(event) || Intl.DateTimeFormat().resolvedOptions().timeZone;

			// Parse and format date in event timezone
			const eventDate = new Date(event.start_at);

			// Create a formatter for the event timezone to get consistent day grouping
			const dayFormatter = new Intl.DateTimeFormat('en-US', {
				timeZone: eventTimezone,
				year: 'numeric',
				month: '2-digit',
				day: '2-digit'
			});
			const dayKey = dayFormatter.format(eventDate).replace(/\//g, '-');

			const dayLabel = formatDateLabel(eventDate);
			const dateStr = new Intl.DateTimeFormat('en-US', {
				timeZone: eventTimezone,
				weekday: 'long',
				month: 'long',
				day: 'numeric'
			}).format(eventDate);

			const shortDateStr = new Intl.DateTimeFormat('en-US', {
				timeZone: eventTimezone,
				weekday: 'short',
				month: 'numeric',
				day: 'numeric'
			}).format(eventDate);

			// Use the landmark ID as the grouping key
			const locationKey = event.landmark?.id || 'unknown';
			const landmarkTitle = (event.landmark?.title_short as LocalizedText) || {
				en: 'Unknown Location'
			};

			if (!groups[dayKey]) {
				groups[dayKey] = {
					__label: dayLabel,
					__timestamp: eventDate.getTime(),
					__dateStr: dateStr,
					__shortDateStr: shortDateStr
				};
			}

			if (!groups[dayKey][locationKey]) {
				groups[dayKey][locationKey] = [];
			}

			(groups[dayKey][locationKey] as PageData['events'][number][]).push(event);
		});

		// Sort the day groups by timestamp
		return Object.entries(groups)
			.sort(([, a], [, b]) => a.__timestamp - b.__timestamp)
			.map(([day, locations]): DayGroupResult => {
				// Sort locations alphabetically within each day
				const sortedLocations = Object.entries(locations)
					.filter(([key]) => !key.startsWith('__'))
					.sort(([, locEventsA], [, locEventsB]) => {
						// Sort by the first event's landmark name in each location group
						const eventsA = locEventsA as PageData['events'][number][];
						const eventsB = locEventsB as PageData['events'][number][];

						const landmarkA = eventsA[0]?.landmark?.title_short as LocalizedText;
						const landmarkB = eventsB[0]?.landmark?.title_short as LocalizedText;

						const nameA = getLocalizedText(landmarkA);
						const nameB = getLocalizedText(landmarkB);

						return nameA.localeCompare(nameB);
					});

				return {
					day,
					label: locations.__label,
					dateStr: locations.__dateStr,
					shortDateStr: locations.__shortDateStr,
					locations: sortedLocations.map(([locationKey, locEvents]): GroupedLocation => {
						const events = locEvents as PageData['events'][number][];
						return {
							location: locationKey,
							landmarkTitle: (events[0]?.landmark?.title_short as LocalizedText) || {
								en: 'Unknown Location'
							},
							events: events.sort((a, b) => {
								return new Date(a.start_at).getTime() - new Date(b.start_at).getTime();
							})
						};
					})
				};
			});
	});

	// Format relative date label (Today, Tomorrow, etc.)
	function formatDateLabel(date: Date): string {
		// Always return the full date format
		return format(date, 'EEEE, MMMM d');
	}

	// Video coordination state
	let currentlyPlayingEventId = $state<string | undefined>(undefined);

	function handleVideoPlay(eventId: string) {
		currentlyPlayingEventId = eventId;
	}

	function handleVideoPause(eventId: string) {
		if (currentlyPlayingEventId === eventId) {
			currentlyPlayingEventId = undefined;
		}
	}
</script>

<div class="space-y-4">
	{#if dev}
		<!-- Debug info - only shown in development mode -->
		<div class="bg-muted/30 mb-4 rounded-md p-2 text-xs">
			<div>
				Debug: Selected date: {selectedDate ? format(selectedDate, 'EEEE, MMMM d') : 'All events'}
			</div>
			<div>Total events: {events.length}</div>
			<div>Filtered events: {filteredEvents().length}</div>
			<div>Selected day of week: {selectedDate ? getDay(selectedDate) : 'All'}</div>
		</div>
	{/if}

	{#if filteredEvents().length === 0}
		<div class="text-muted-foreground flex h-32 items-center justify-center">
			{#if selectedDate}
				No events scheduled on {format(selectedDate, 'EEEE')}
			{:else}
				No events available
			{/if}
		</div>
	{:else}
		{#each groupedEvents() as dayGroup}
			{#each dayGroup.locations as locationGroup}
				<!-- Section header with day and landmark only -->
				<div class="bg-background/90 sticky top-[64px] z-10 mb-2 border-b py-2 backdrop-blur-sm">
					<h2 class="flex items-center justify-between text-lg font-medium">
						<span>{dayGroup.shortDateStr} • {getLocalizedText(locationGroup.landmarkTitle)}</span>
						<span class="bg-primary/10 text-primary rounded-full px-2 py-0.5 text-xs">
							{locationGroup.events.length}
						</span>
					</h2>
				</div>

				<!-- Events for this location group -->
				<div class="mb-6 space-y-3">
					{#each locationGroup.events as event (event.id)}
						<EventCard
							{event}
							{supabase}
							{userId}
							{brand}
							{currentlyPlayingEventId}
							onVideoPlay={handleVideoPlay}
							onVideoPause={handleVideoPause}
						/>
					{/each}
				</div>
			{/each}
		{/each}
	{/if}
</div>
