<!-- Event Registration Modal -->
<script lang="ts">
	import ResponsiveModal from '$lib/components/shared/ResponsiveModal.svelte';
	import type { PageData } from '../$types';
	import type { FindOptionsResponse, ProductDetails } from '../types';
	import { invalidate } from '$app/navigation';
	import ProductSelector from './modal-components/ProductSelector.svelte';
	import InlineRegistration from './InlineRegistration.svelte';
	import RegistrationActions from './modal-components/RegistrationActions.svelte';

	interface Props {
		selectedEvent: PageData['events'][number] | null;
		selectedProductId?: string | null;
		optionsData: FindOptionsResponse | null;
		// Walk-in profile ID for check-in flow
		walkinProfileId?: string;
		userId: string;
		brand: App.Locals['brand'];
		onOpenChange?: (isOpen: boolean) => void;
	}

	let {
		selectedEvent = $bindable(),
		selectedProductId,
		optionsData,
		walkinProfileId = '',
		userId,
		brand,
		onOpenChange
	}: Props = $props();
	let modalOpen = $state(false);

	// Internal state for managing selected product
	let internalSelectedProductId = $state<string>('');

	// State from InlineRegistration that we need to manage here
	let buyingOptionSelections = $state(new Map<string, string[]>());
	let inlineOptionsData = $state<FindOptionsResponse | null>(null);

	// Derive whether we're in walk-in mode
	let isWalkinMode = $derived(!!walkinProfileId);

	// Update modal visibility based on selectedEvent and optionsData
	$effect(() => {
		modalOpen = !!selectedEvent && !!optionsData;
	});

	// Initialize inline options data
	$effect(() => {
		if (optionsData && !inlineOptionsData) {
			inlineOptionsData = optionsData;
		}
	});

	// Auto-select first product if selectedProductId is null
	$effect(() => {
		if (selectedEvent?.products && selectedEvent.products.length > 0) {
			if (!selectedProductId && selectedEvent.products[0].product.id) {
				internalSelectedProductId = selectedEvent.products[0].product.id;
			} else if (selectedProductId) {
				internalSelectedProductId = selectedProductId;
			}
		}
	});

	// Handle modal close - update the parent's selectedEvent binding
	function handleModalClose(isOpen: boolean) {
		if (!isOpen) {
			if (onOpenChange) {
				onOpenChange(false);
			}
		}
	}

	let selectedProduct = $state<ProductDetails | null>(null);

	$effect(() => {
		if (!selectedEvent) {
			selectedProduct = null;
			return;
		}

		const productData = selectedEvent?.products?.find(
			(p: { product: { id: string } }) => p.product.id === internalSelectedProductId
		)?.product;

		selectedProduct = (productData as unknown as ProductDetails) || null;
	});

	// Product selection handler
	function handleProductSelection(productId: string) {
		internalSelectedProductId = productId;
	}

	// Handle registration success
	function handleRegistrationSuccess() {
		// Close the modal
		if (onOpenChange) {
			onOpenChange(false);
		}

		// Invalidate events data to refresh the list
		invalidate('events');
	}
</script>

{#snippet modalFooter()}
	{#if selectedProduct}
		<RegistrationActions
			{selectedProduct}
			optionsData={inlineOptionsData}
			{buyingOptionSelections}
			{isWalkinMode}
			userId={walkinProfileId || userId}
			onSuccess={handleRegistrationSuccess}
		/>
	{/if}
{/snippet}

<ResponsiveModal
	open={modalOpen}
	onOpenChange={handleModalClose}
	title={isWalkinMode ? `Register Walk-in` : 'Register for Events'}
	description={isWalkinMode
		? "Registering using existing points in this profile's account"
		: 'Select your preferred registration option'}
	footer={modalFooter}
>
	<div class="space-y-6">
		<!-- Product Selection Component - Only render when multiple products exist -->
		{#if selectedEvent?.products && selectedEvent.products.length > 1}
			<ProductSelector
				products={selectedEvent.products}
				selectedProductId={internalSelectedProductId}
				onProductSelect={handleProductSelection}
			/>
		{:else if selectedEvent?.products && selectedEvent.products.length === 1 && !internalSelectedProductId && selectedEvent.products[0].product.id}
			<!-- Auto-select the only available product silently -->
			{((internalSelectedProductId = selectedEvent.products[0].product.id), '')}
		{/if}

		<!-- Use InlineRegistration for the actual registration flow (without the actions) -->
		{#if selectedProduct}
			<InlineRegistration
				product={selectedProduct}
				preloadedOptionsData={optionsData}
				{isWalkinMode}
				userId={walkinProfileId || userId}
				{brand}
				onRegistrationSuccess={handleRegistrationSuccess}
				hideRegistrationActions={true}
				bind:buyingOptionSelections
				bind:optionsData={inlineOptionsData}
			/>
		{/if}
	</div>
</ResponsiveModal>
