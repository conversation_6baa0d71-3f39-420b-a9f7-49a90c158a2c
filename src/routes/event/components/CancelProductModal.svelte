<!-- Cancel Product Modal Component -->
<script lang="ts">
	import {
		Dialog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle
	} from '$lib/components/ui/dialog';
	import { Button } from '$lib/components/ui/button';
	import { Label } from '$lib/components/ui/label';
	import { Loader2 } from '@lucide/svelte';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { Textarea } from '$lib/components/ui/textarea';
	import type { SupabaseClient } from '@supabase/supabase-js';

	interface Props {
		productId: string;
		eventTitle: string;
		eventTime?: string;
		eventLocation?: string;
		eventHost?: string;
		dryRunData: any;
		supabase: SupabaseClient;
		onClose: () => void;
		onSuccess?: () => Promise<void>;
	}

	let {
		productId,
		eventTitle,
		eventTime = '',
		eventLocation = '',
		eventHost = '',
		dryRunData,
		supabase,
		onClose,
		onSuccess
	}: Props = $props();

	let isLoading = $state(false);
	let affectedUsers = $state<{ id: string; email: string }[]>([]);
	let errorMessage = $state('');
	let success = $state(false);
	let emailTemplate = $state('');

	// Get affected users and generate email template on component mount
	$effect(() => {
		extractAffectedUsers();
		generateEmailTemplate();
	});

	function extractAffectedUsers() {
		// Extract user info from the dryRunData
		const users: { id: string; email: string }[] = [];

		if (dryRunData?.canceled_product?.affected_consumers) {
			const ids = dryRunData.canceled_product.affected_consumers.ids || [];
			const emails = dryRunData.canceled_product.affected_consumers.emails || [];

			for (let i = 0; i < ids.length; i++) {
				if (ids[i] && emails[i]) {
					users.push({
						id: ids[i],
						email: emails[i]
					});
				}
			}
		}

		affectedUsers = users;
	}

	function generateEmailTemplate() {
		emailTemplate = `Subject: [EDS] Cancellation of "${eventTitle}" ${eventTime}

Dear Participant,

We regret to inform you that the following session has been canceleddue to the instructor's personal scheduling change:

Session Name: ${eventTitle}
${eventTime ? `Date/Time: ${eventTime}` : ''}
${eventLocation ? `Location: ${eventLocation}` : ''}
${eventHost ? `Instructor: ${eventHost}` : ''}

We understand this may cause inconvenience, and we sincerely apologize. Your credits for this session have been refunded to your account.

If you have any questions or concerns, please don't hesitate to contact us.

Thank you for your understanding.

Best regards,
The EDS Team`;
	}

	async function handleConfirmCancel() {
		if (!productId || !supabase) return;

		isLoading = true;
		errorMessage = '';

		try {
			// Execute the admin cancel function with perform_update = true
			const { data, error } = await supabase.rpc('admin_cancel_order_product_on_product', {
				input_product_id: productId,
				input_add_to_product_id: null,
				input_hide_product_events: true,
				input_perform_update: true
			});

			if (error) throw error;

			// Set success state
			success = true;

			// Call onSuccess callback if provided
			if (onSuccess) {
				await onSuccess();
			}
		} catch (err) {
			console.error('Error cancelling product:', err);
			errorMessage = err instanceof Error ? err.message : 'Failed to cancel product';
		} finally {
			isLoading = false;
		}
	}

	function copyEmailTemplate() {
		navigator.clipboard.writeText(emailTemplate).catch((err) => {
			console.error('Failed to copy email template:', err);
		});
	}

	// Get the composed emails string directly from dryRunData
	const affectedEmails = $derived(
		dryRunData?.canceled_product?.affected_consumers?.composed_emails || ''
	);

	// Get is_cancel_allowed from dryRunData - admin function will always allow it for admins
	const isCancelAllowed = $derived(true);

	// Get total_returned_units from dryRunData
	const totalReturnedUnits = $derived(dryRunData?.canceled_product?.total_returned_units || 0);

	// Get total_canceled_orders from dryRunData
	const totalCanceledOrders = $derived(dryRunData?.canceled_product?.total_canceled_orders || 0);
</script>

<Dialog open={true} onOpenChange={onClose}>
	<DialogContent class="sm:max-w-[500px]">
		<DialogHeader>
			<DialogTitle>Cancel Product</DialogTitle>
			<DialogDescription>
				{#if success}
					Product successfully canceled and credits returned to users.
				{:else}
					This will cancel the product for all users and return credits according to the
					cancellation policy.
				{/if}
			</DialogDescription>
		</DialogHeader>

		{#if errorMessage}
			<Alert variant="destructive">
				<AlertDescription>{errorMessage}</AlertDescription>
			</Alert>
		{/if}

		{#if !success}
			<div class="grid gap-4 py-4">
				<div>
					<Label>Cancellation Details</Label>
					<div class="mt-2 rounded-md bg-muted p-3 text-sm">
						<p>Total affected orders: <strong>{totalCanceledOrders}</strong></p>
						<p>Credits to return: <strong>{totalReturnedUnits}</strong></p>
					</div>
				</div>

				<div>
					<Label>Affected Users ({affectedUsers.length})</Label>
					<div class="relative mt-2">
						<Textarea readonly value={affectedEmails} class="h-24 resize-none pr-16 text-sm" />
						<Button
							variant="secondary"
							size="sm"
							class="absolute right-2 top-2"
							onclick={() => navigator.clipboard.writeText(affectedEmails)}
						>
							Copy
						</Button>
					</div>
				</div>

				<div>
					<Label>Email Template</Label>
					<div class="relative mt-2">
						<Textarea rows={8} bind:value={emailTemplate} class="resize-none pr-16" />
						<Button
							variant="secondary"
							size="sm"
							class="absolute right-2 top-2"
							onclick={copyEmailTemplate}
						>
							Copy
						</Button>
					</div>
				</div>
			</div>

			<DialogFooter>
				<Button variant="outline" onclick={onClose} disabled={isLoading}>Cancel</Button>
				<Button
					variant="destructive"
					onclick={handleConfirmCancel}
					disabled={isLoading || !isCancelAllowed}
				>
					{#if isLoading}
						<Loader2 class="mr-2 h-4 w-4 animate-spin" />
						Processing...
					{:else}
						Confirm Cancellation
					{/if}
				</Button>
			</DialogFooter>
		{:else}
			<DialogFooter>
				<Button onclick={onClose}>Close</Button>
			</DialogFooter>
		{/if}
	</DialogContent>
</Dialog>
