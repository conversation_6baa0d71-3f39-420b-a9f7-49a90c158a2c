<!-- Calendar header with month navigation -->
<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Calendar } from '$lib/components/ui/calendar';
	import * as Popover from '$lib/components/ui/popover';
	import { CalendarIcon } from '@lucide/svelte';
	import { format, isToday } from 'date-fns';
	import { type DateValue, parseDate, getLocalTimeZone } from '@internationalized/date';
	import EventFilter from './EventFilter.svelte';

	interface Props {
		currentMonth: Date;
		selectedDate: Date | null;
		onDateSelect: (date: Date | null) => void;
	}

	let { currentMonth, selectedDate, onDateSelect }: Props = $props();
	let selectedFilter = $state('all');

	function handleFilterChange(filter: string) {
		// Always maintain a selected filter, default to 'all' if somehow undefined
		selectedFilter = filter || 'all';
		// TODO: Apply filter logic
	}

	// Convert selectedDate to a DateValue for the Calendar component
	let value = $derived(
		selectedDate ? (parseDate(format(selectedDate, 'yyyy-MM-dd')) as DateValue) : undefined
	);

	function handleCalendarSelect(newValue: DateValue | undefined) {
		if (newValue) {
			onDateSelect(newValue.toDate(getLocalTimeZone()));
		}
	}
</script>

<div class="flex flex-col gap-2 p-1 sm:flex-row sm:items-center sm:justify-between">
	<!-- <div class="order-2 sm:order-1">
		<EventFilter {selectedFilter} onFilterChange={handleFilterChange} />
	</div> -->

	<div class="order-1 flex items-center justify-end sm:order-2">
		<Popover.Root>
			<Popover.Trigger>
				{#snippet child({ props })}
					<Button
						variant="ghost"
						class="text-lg font-semibold {selectedDate && isToday(selectedDate)
							? 'text-primary'
							: ''}"
						{...props}
					>
						<CalendarIcon class="mr-2 h-4 w-4" />
						{format(currentMonth, 'MMMM yyyy')}
					</Button>
				{/snippet}
			</Popover.Trigger>
			<Popover.Content class="w-auto p-0" align="start">
				<Calendar type="single" {value} onValueChange={handleCalendarSelect} />
			</Popover.Content>
		</Popover.Root>
	</div>
</div>
