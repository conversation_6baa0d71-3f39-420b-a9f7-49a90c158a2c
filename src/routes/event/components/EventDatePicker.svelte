<!-- Horizontal date picker -->
<script lang="ts">
	import * as Carousel from '$lib/components/ui/carousel';
	import { addDays, format, isSameDay, isToday, isTomorrow, startOfDay, isMonday } from 'date-fns';
	import type { CarouselAPI } from '$lib/components/ui/carousel/context';
	import * as Card from '$lib/components/ui/card/index.js';
	import { ChevronLeft, ChevronRight } from '@lucide/svelte';

	interface Props {
		selectedDate: Date;
		onDateSelect: (date: Date) => void;
		containerWidth: number;
	}

	let { selectedDate, onDateSelect, containerWidth }: Props = $props();
	let dates = $state<Date[]>([]);
	let api = $state<CarouselAPI>();
	let current = $state(0);
	let numVisibleDays = $derived(
		containerWidth < 400
			? 3
			: containerWidth < 600
				? 4
				: containerWidth < 800
					? 5
					: containerWidth < 1000
						? 6
						: containerWidth < 1200
							? 7
							: 8
	);

	// Calculate dates array
	$effect(() => {
		const today = startOfDay(new Date());
		dates = Array.from({ length: numVisibleDays * 3 }, (_, i) => addDays(today, i));
	});

	function formatDayName(date: Date): string {
		if (isToday(date)) return 'Today';
		if (isTomorrow(date)) return 'Tomorrow';
		return format(date, 'EEE');
	}

	function handleDateClick(date: Date) {
		if (!isSameDay(date, selectedDate)) {
			onDateSelect(startOfDay(date));
		}
	}

	$effect(() => {
		if (api) {
			api.scrollTo(0);
			api.on('select', () => {
				current = api!.selectedScrollSnap();
			});
		}
	});
</script>

<div class="flex justify-center py-4">
	<Carousel.Root
		opts={{
			align: 'start',
			dragFree: true,
			slidesToScroll: numVisibleDays
		}}
		style="width: {containerWidth - 160}px"
		setApi={(emblaApi) => (api = emblaApi)}
	>
		<Carousel.Content class="-ml-4">
			{#each dates as date}
				<Carousel.Item class="pl-4" style="flex: 0 0 {100 / numVisibleDays}%">
					<Card.Root
						class="h-24 cursor-pointer border-none hover:bg-muted/70 {isSameDay(date, selectedDate)
							? 'bg-primary/15 text-primary'
							: 'bg-muted/50'} {isToday(date) || isTomorrow(date) ? 'text-primary' : ''}"
						onclick={() => handleDateClick(date)}
					>
						<Card.Content class="flex h-full flex-col items-center justify-between p-2">
							<span class="{isMonday(date) ? 'font-bold text-destructive' : ''} text-sm"
								>{formatDayName(date)}</span
							>
							<span class="text-2xl">{format(date, 'd')}</span>
							<span class="text-xs text-muted-foreground">{format(date, 'MMM')}</span>
						</Card.Content>
					</Card.Root>
				</Carousel.Item>
			{/each}
		</Carousel.Content>
		<Carousel.Previous />
		<Carousel.Next />
	</Carousel.Root>
</div>
