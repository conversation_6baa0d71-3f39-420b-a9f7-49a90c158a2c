import { error, json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import {
	STRIPE_SECRET_KEY,
	EDS_STRIPE_SECRET_KEY,
	EDS_STRIPE_SECRET_KEY_TEST,
	STRIPE_SECRET_KEY_TEST
} from '$env/static/private';
import Stripe from 'stripe';

export const POST: RequestHandler = async ({ request, locals: { supabase, session, brand } }) => {
	if (!session) {
		throw error(401, 'Unauthorized');
	}

	try {
		const { sessionId } = await request.json();

		if (!sessionId) {
			throw error(400, 'Session ID is required');
		}

		// Look up the payment session in our database
		const { data: paymentData, error: paymentError } = await supabase
			.from('order_payment')
			.select('order_id, stripe_checkout_session_id')
			.eq('stripe_checkout_session_id', sessionId)
			.single();

		if (paymentError) {
			console.error('Error fetching payment record:', paymentError);
			throw error(404, 'Payment record not found');
		}

		// Determine if we're in development mode
		const isDev = process.env.NODE_ENV !== 'production';

		// EDS brand ID
		const EDS_BRAND_ID = '073b8001-d1ff-19b2-9fbf-cfd5e80f2c0a';
		const isEdsBrand = brand?.id === EDS_BRAND_ID;

		// Select the appropriate Stripe key based on environment and brand
		let stripeKey: string;
		if (isDev) {
			stripeKey = isEdsBrand ? EDS_STRIPE_SECRET_KEY_TEST : STRIPE_SECRET_KEY_TEST;
		} else {
			stripeKey = isEdsBrand ? EDS_STRIPE_SECRET_KEY : STRIPE_SECRET_KEY;
		}

		// Initialize Stripe and verify the session
		const stripe = new Stripe(stripeKey);

		const checkoutSession = await stripe.checkout.sessions.retrieve(sessionId);

		if (checkoutSession.payment_status !== 'paid') {
			throw error(400, 'Payment has not been completed');
		}

		// Check if this was a connected account payment
		const isConnectedPayment =
			checkoutSession.payment_intent &&
			brand?.stripe_account_id &&
			(checkoutSession.payment_intent as any).transfer_data?.destination ===
				brand.stripe_account_id;

		console.log('Payment verification: Connected account payment:', isConnectedPayment);

		// Update the order status to paid
		const { error: updateError } = await supabase.rpc('client_confirm_order_payment', {
			input_order_id: paymentData.order_id,
			input_session_id: sessionId
		});

		if (updateError) {
			console.error('Error confirming payment:', updateError);
			throw error(500, 'Failed to confirm payment');
		}

		return json({
			type: 'success',
			status: 200,
			data: {
				orderId: paymentData.order_id,
				status: 'paid'
			}
		});
	} catch (err) {
		console.error('Error verifying payment:', err);
		if (err instanceof Response) throw err;
		throw error(500, err instanceof Error ? err.message : 'Failed to verify payment');
	}
};
