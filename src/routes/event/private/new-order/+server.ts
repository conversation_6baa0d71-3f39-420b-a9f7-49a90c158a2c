import { error, json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import type { Database } from '$lib/supabase/database.types';

// Interface for validating order input
interface OrderInput {
	payer_id: string;
	consumer_profiles: Array<{
		consumer_profile_id: string;
		product_count: number;
		product_prices: Array<{
			product_price_id: string;
			fill_up_with_price_option_combinations?: Array<{
				price_option_id: string;
				quantity: number;
			}>;
			order_requirement_passcode?: string;
		}>;
	}>;
}

export const POST: RequestHandler = async ({ request, url, locals: { supabase, session } }) => {
	if (!session) {
		throw error(401, 'Unauthorized');
	}

	try {
		// Get order input from request body
		const orderInput = (await request.json()) as OrderInput;

		// Log the incoming order input for debugging
		console.log('Received order input:', JSON.stringify(orderInput, null, 2));

		// Validate required fields and data structure
		if (!orderInput.payer_id) {
			return json(
				{
					type: 'error',
					status: 400,
					error: { message: 'Missing payer_id in order input' }
				},
				{ status: 400 }
			);
		}

		// Validate that user ID in the order matches the logged-in user
		if (orderInput.payer_id !== session.user.id) {
			throw error(403, 'Payer ID must match the authenticated user');
		}

		// Validate consumer profiles
		if (
			!orderInput.consumer_profiles ||
			!Array.isArray(orderInput.consumer_profiles) ||
			orderInput.consumer_profiles.length === 0
		) {
			return json(
				{
					type: 'error',
					status: 400,
					error: { message: 'Missing or invalid consumer_profiles array' }
				},
				{ status: 400 }
			);
		}

		// Validate each consumer profile
		for (const profile of orderInput.consumer_profiles) {
			if (!profile.consumer_profile_id) {
				return json(
					{
						type: 'error',
						status: 400,
						error: { message: 'Missing consumer_profile_id in consumer profile' }
					},
					{ status: 400 }
				);
			}

			if (
				!profile.product_prices ||
				!Array.isArray(profile.product_prices) ||
				profile.product_prices.length === 0
			) {
				return json(
					{
						type: 'error',
						status: 400,
						error: {
							message: 'Missing or invalid product_prices array',
							details: `Consumer profile ${profile.consumer_profile_id} has no product prices`
						}
					},
					{ status: 400 }
				);
			}

			// Validate each product price
			for (const price of profile.product_prices) {
				if (!price.product_price_id) {
					return json(
						{
							type: 'error',
							status: 400,
							error: {
								message: 'Missing product_price_id in product price',
								details: `Consumer profile ${profile.consumer_profile_id} has a product price without an ID`
							}
						},
						{ status: 400 }
					);
				}

				// Validate fill_up_with_price_option_combinations if present
				if (
					price.fill_up_with_price_option_combinations &&
					Array.isArray(price.fill_up_with_price_option_combinations) &&
					price.fill_up_with_price_option_combinations.length > 0
				) {
					for (const combo of price.fill_up_with_price_option_combinations) {
						if (!combo.price_option_id) {
							return json(
								{
									type: 'error',
									status: 400,
									error: {
										message: 'Missing price_option_id in price option combination',
										details: `Product price ${price.product_price_id} has a price option combination without an ID`
									}
								},
								{ status: 400 }
							);
						}
					}
				}
			}
		}

		// Prepare parameters according to the function signature
		let params = {
			payer_id: orderInput.payer_id,
			consumer_profiles: orderInput.consumer_profiles
		};

		// Log parameters that will be sent to the function
		console.log('Calling client_new_order with params:', JSON.stringify(params, null, 2));

		// Call the PostgreSQL function with the parameters
		let { data: orderData, error: orderError } = await supabase.rpc('client_new_order', params);

		if (orderError) {
			console.error('Error calling client_new_order:', orderError);

			// Log detailed error information for other errors
			console.error('Supabase RPC error creating order:', {
				message: orderError.message,
				details: orderError.details,
				hint: orderError.hint,
				code: orderError.code
			});

			// Add specific debugging for P0001 errors
			if (orderError.code === 'P0001') {
				console.error('Database function implementation error detected:');
				console.error(
					"This is likely an issue with the PostgreSQL function itself, not with how it's being called."
				);
				console.error('Error message details:', orderError.message);

				// Try to extract variable name from error message using regex
				const varMatch = orderError.message.match(/record "([^"]+)" has no field/);
				if (varMatch && varMatch[1]) {
					console.error(
						`The PostgreSQL function is trying to access a field on variable "${varMatch[1]}" that doesn't exist.`
					);
				}

				// Try to extract field name from error message
				const fieldMatch = orderError.message.match(/has no field "([^"]+)"/);
				if (fieldMatch && fieldMatch[1]) {
					console.error(
						`The PostgreSQL function is trying to access a non-existent field "${fieldMatch[1]}".`
					);
				}
			}

			// Return detailed error message to client
			return json(
				{
					type: 'error',
					status: 500,
					error: {
						message: orderError.message || 'Failed to create order',
						details: orderError.details,
						code: orderError.code,
						hint:
							orderError.code === 'P0001'
								? "This appears to be an error in the database function implementation. The database function is trying to access fields that don't exist."
								: 'The function signature may have changed. It should accept (payer_id UUID, consumer_profiles array)'
					}
				},
				{ status: 500 }
			);
		}

		if (!orderData || !orderData.order_id) {
			console.error('No order data returned from RPC call', { orderData });
			return json(
				{
					type: 'error',
					status: 500,
					error: {
						message: 'Order created but no order ID returned',
						details: 'The database operation completed but did not return the expected data'
					}
				},
				{ status: 500 }
			);
		}

		console.log('Order created successfully:', orderData);
		return json({
			type: 'success',
			status: 200,
			data: orderData
		});
	} catch (err) {
		// Enhanced error logging
		console.error('Unexpected error processing order:', err);

		// If it's a Response object (like from a throw error() call), just throw it
		if (err instanceof Response) throw err;

		// Create a more informative error response
		return json(
			{
				type: 'error',
				status: 500,
				error: {
					message: err instanceof Error ? err.message : 'Failed to create order',
					stack:
						process.env.NODE_ENV === 'development'
							? err instanceof Error
								? err.stack
								: undefined
							: undefined
				}
			},
			{ status: 500 }
		);
	}
};
