import { error, json } from '@sveltejs/kit';
import type { <PERSON>questHand<PERSON> } from './$types';
import type { Database } from '$lib/supabase/database.types';

export const GET: RequestHandler = async ({ url, locals: { supabase, user, session } }) => {
	// Check if user is authenticated
	if (!user || !session) {
		throw error(401, 'Unauthorized');
	}

	const productId = url.searchParams.get('productId');
	const profileId = url.searchParams.get('profileId');
	const passcode = url.searchParams.get('passcode');

	if (!productId) {
		throw error(400, 'Product ID is required');
	}

	// Use the profileId if provided, otherwise use the current user's ID
	const consumerProfileId = profileId || user.id;

	console.log(
		`Fetching buying options for productId=${productId}, profileId=${consumerProfileId}, passcode=${passcode ? 'provided' : 'not provided'}`
	);

	// Call the client_find_options_to_order_products RPC function with the correct structure
	const { data, error: rpcError } = await supabase.rpc('client_find_options_to_order_products', {
		input_payer_id: session.user.id,
		input: {
			items: [
				{
					consumer_profile_id: consumerProfileId,
					product_id: productId,
					product_count: 1,
					product_price_passcode: passcode
				}
			]
		}
	});

	if (rpcError) {
		console.error('Error calling client_find_options_to_order_products:', rpcError);
		throw error(500, rpcError.message || 'Failed to fetch options');
	}

	return json({
		type: 'success',
		data
	});
};
