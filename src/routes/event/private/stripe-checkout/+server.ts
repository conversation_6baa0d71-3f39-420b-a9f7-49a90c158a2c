import { error, json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import {
	STRIPE_SECRET_KEY,
	STRIPE_SECRET_KEY_TEST,
	EDS_STRIPE_SECRET_KEY,
	EDS_STRIPE_SECRET_KEY_TEST
} from '$env/static/private';
import Stripe from 'stripe';

export const POST: RequestHandler = async ({
	request,
	locals: { supabase, user, session, brand }
}) => {
	if (!user) {
		throw error(401, 'Unauthorized');
	}

	try {
		const reqBody = await request.json();
		const { optionIds, orderId } = reqBody;

		if (!optionIds || !optionIds.length) {
			throw error(400, 'No options selected');
		}

		// Validate the options exist
		const { data: optionsData, error: optionsError } = await supabase.rpc(
			'client_helper_find_price_option_details',
			{
				price_option_ids: optionIds
			}
		);

		if (optionsError) {
			throw error(500, optionsError.message);
		}

		if (!optionsData || optionsData.length === 0) {
			throw error(404, 'Selected price options not found');
		}

		// Calculate the total amount to charge
		const totalAmount = optionsData.reduce(
			(sum: number, option: any) => sum + (option.money_int || 0),
			0
		);
		const currency = optionsData[0]?.currency_code?.toLowerCase() || 'usd';

		if (totalAmount <= 0) {
			throw error(400, 'Invalid payment amount');
		}

		// Calculate Stripe fee only if brand configured to have payer pay the fee
		const shouldAddStripeFee = brand?.stripe_fee_paid_by === 'payer';

		// Calculate Stripe fee (typically 2.9% + $0.30 for US)
		const stripeFeePercentage = 0.029; // 2.9%
		const stripeFeeFixed = 30; // $0.30 in cents
		const stripeFeeAmount = shouldAddStripeFee
			? Math.round(totalAmount * stripeFeePercentage + stripeFeeFixed)
			: 0;

		console.log(
			`Original amount: ${totalAmount}, Stripe fee: ${stripeFeeAmount}, Payer pays fee: ${shouldAddStripeFee}`
		);

		// Determine if we're in development mode
		const isDev = process.env.NODE_ENV !== 'production';

		// Set base URL based on environment
		const baseUrl = isDev ? 'http://localhost:8888' : `https://${brand?.portal_url}`;

		// EDS brand ID
		const EDS_BRAND_ID = '073b8001-d1ff-19b2-9fbf-cfd5e80f2c0a';
		const isEdsBrand = brand?.id === EDS_BRAND_ID;

		// Select the appropriate Stripe key based on environment and brand
		let stripeKey: string;
		if (isDev) {
			stripeKey = isEdsBrand ? EDS_STRIPE_SECRET_KEY_TEST : STRIPE_SECRET_KEY_TEST;
		} else {
			stripeKey = isEdsBrand ? EDS_STRIPE_SECRET_KEY : STRIPE_SECRET_KEY;
		}

		if (!stripeKey) {
			throw error(500, 'Stripe Secret Key is not configured');
		}

		const stripe = new Stripe(stripeKey);

		// Create checkout session
		const checkoutSession = await stripe.checkout.sessions.create({
			payment_method_types: ['card'],
			line_items: [
				{
					price_data: {
						currency,
						product_data: {
							name: 'Class Pass Purchase',
							description: `Purchase of ${optionsData.length} class pass options`
						},
						unit_amount: totalAmount,
						tax_behavior: 'exclusive' as const
					},
					quantity: 1,
					adjustable_quantity: {
						enabled: false
					}
				},
				// Add processing fee as separate line item if configured
				...(shouldAddStripeFee
					? [
							{
								price_data: {
									currency,
									product_data: {
										name: 'Payment Processing Fee',
										description: 'Non-refundable bank processing fee'
									},
									unit_amount: stripeFeeAmount,
									tax_behavior: 'exclusive' as const
								},
								quantity: 1
							}
						]
					: [])
			],
			payment_intent_data: {
				metadata: {
					order_id: orderId,
					user_id: user.id,
					price_option_ids: optionIds.join(',')
				},
				// Add transfer data if the brand has a connected Stripe account and it's not EDS brand
				...(brand.stripe_account_id &&
					!isEdsBrand && {
						transfer_data: {
							destination: brand.stripe_account_id
						},
						application_fee_amount: 0 // Set your platform fee here if needed - calculated from original amount
					})
			},
			automatic_tax: { enabled: true },
			mode: 'payment',
			success_url: `${baseUrl}/event/payment-success?session_id={CHECKOUT_SESSION_ID}`,
			cancel_url: `${baseUrl}/event`,
			metadata: {
				brand_id: brand.id,
				order_id: orderId,
				user_id: user.id,
				price_option_ids: optionIds.join(',')
			}
		});

		if (!checkoutSession.url) {
			throw error(500, 'Failed to create checkout session URL');
		}

		// Store the checkout session ID in the database for verification later
		const { error: dbError } = await supabase.from('order_payment').insert({
			order_id: orderId,
			stripe_checkout_session_id: checkoutSession.id,
			stripe_checkout_session_url: checkoutSession.url
		});

		if (dbError) {
			console.error('Error saving checkout session:', dbError);
			// Continue anyway as we might be able to process the payment
		}

		return json({
			type: 'success',
			status: 200,
			data: {
				url: checkoutSession.url
			}
		});
	} catch (err) {
		console.error('Error creating checkout session:', err);
		if (err instanceof Response) throw err;
		throw error(500, err instanceof Error ? err.message : 'Failed to create checkout session');
	}
};
