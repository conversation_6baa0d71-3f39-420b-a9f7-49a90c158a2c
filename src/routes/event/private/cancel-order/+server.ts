import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import type { Database } from '$lib/supabase/database.types';

// GET handler to check if cancellation is possible (dry run)
export const GET: RequestHandler = async ({ url, fetch, locals: { user, supabase } }) => {
	if (!user) {
		return json(
			{
				type: 'error',
				error: { message: 'Unauthorized' }
			},
			{ status: 401 }
		);
	}

	const orderProductId = url.searchParams.get('orderProductId');
	if (!orderProductId) {
		return json(
			{
				type: 'error',
				error: { message: 'Order product ID is required' }
			},
			{ status: 400 }
		);
	}

	try {
		// Check if cancellation is possible (dry run)
		const { data, error } = await supabase.rpc('cancel_order_product', {
			input_order_product_id: orderProductId,
			input_perform_update: false
		});

		if (error) throw error;

		// Get product cancellation policy information
		const { data: orderProductData, error: orderProductError } = await supabase
			.from('order_product')
			.select(
				`
				id,
				product_purchased_count,
				product_price (
					id,
					cost_units,
					auto_cancel_at_far,
					auto_cancel_at_far_return_units,
					auto_cancel_at_near,
					auto_cancel_at_near_return_units,
					product: product_id (
						id,
						auto_first_event_start_at
					)
				)
			`
			)
			.eq('id', orderProductId)
			.single();

		if (orderProductError) throw orderProductError;

		// Extract policy data
		type ProductPriceWithRelations = {
			id: string;
			cost_units: number;
			auto_cancel_at_far: string | null;
			auto_cancel_at_far_return_units: number | null;
			auto_cancel_at_near: string | null;
			auto_cancel_at_near_return_units: number | null;
			product: {
				id: string;
				auto_first_event_start_at: string | null;
			};
		};

		const productPriceData =
			orderProductData?.product_price as unknown as ProductPriceWithRelations;

		const firstEventStartAt = productPriceData?.product?.auto_first_event_start_at;

		return json({
			type: 'success',
			data: {
				...data,
				policy: {
					firstEventStartAt,
					auto_cancel_at_far: productPriceData?.auto_cancel_at_far,
					auto_cancel_at_far_return_units: productPriceData?.auto_cancel_at_far_return_units,
					auto_cancel_at_near: productPriceData?.auto_cancel_at_near,
					auto_cancel_at_near_return_units: productPriceData?.auto_cancel_at_near_return_units
				}
			}
		});
	} catch (err) {
		console.error('Error checking cancellation eligibility:', err);

		// Return a more specific error message if available
		const errorMessage =
			err instanceof Error ? err.message : 'Failed to check cancellation eligibility';

		return json(
			{
				type: 'error',
				error: { message: errorMessage }
			},
			{ status: 500 }
		);
	}
};

// POST handler to actually perform the cancellation
export const POST: RequestHandler = async ({ request, locals: { user, supabase } }) => {
	if (!user) {
		return json(
			{
				type: 'error',
				error: { message: 'Unauthorized' }
			},
			{ status: 401 }
		);
	}

	try {
		const body = await request.json();
		const { orderProductId } = body;

		if (!orderProductId) {
			return json(
				{
					type: 'error',
					error: { message: 'Order product ID is required' }
				},
				{ status: 400 }
			);
		}

		// Perform the actual cancellation
		const { data, error } = await supabase.rpc('cancel_order_product', {
			input_order_product_id: orderProductId,
			input_perform_update: true
		});

		if (error) throw error;

		return json({
			type: 'success',
			data
		});
	} catch (err) {
		console.error('Error cancelling order product:', err);

		// Return a more specific error message if available
		const errorMessage = err instanceof Error ? err.message : 'Failed to process cancellation';

		return json(
			{
				type: 'error',
				error: { message: errorMessage }
			},
			{ status: 500 }
		);
	}
};
