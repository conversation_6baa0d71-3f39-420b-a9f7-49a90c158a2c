import type { Database } from '$lib/supabase/database.types';

// Types for event dialog
export interface LocalizedText {
	[key: string]: string;
}

// Using database types directly
export type Brand = Database['public']['CompositeTypes']['find_options_output_brand'];
export type Price = Database['public']['CompositeTypes']['find_options_output_price'];
export type OrderPrice = Database['public']['CompositeTypes']['find_options_output_order_price'];
export type ProductPrice =
	Database['public']['CompositeTypes']['find_options_output_product_price'];
export type ConsumerProfile =
	Database['public']['CompositeTypes']['find_options_output_consumer_profile'];
export type PriceOption = Database['public']['CompositeTypes']['find_options_output_price_option'];
export type PriceOptionItem =
	Database['public']['CompositeTypes']['find_options_output_price_option_item'];
export type PriceOptionCombination =
	Database['public']['CompositeTypes']['find_options_output_price_option_combination'];
export type OrderPriceItem =
	Database['public']['CompositeTypes']['find_options_output_order_price_item'];
export type ProductPriceItem =
	Database['public']['CompositeTypes']['find_options_output_product_price_item'];
export type ConsumerItem =
	Database['public']['CompositeTypes']['find_options_output_consumer_item'];
export type FindOptionsResponse =
	Database['public']['CompositeTypes']['find_options_output_result'];
export type OrderRequirement = Database['public']['Tables']['order_requirement']['Row'];

export interface EventDialogProps {
	eventId: string;
	payerId: string;
	onClose: () => void;
	onSuccess: () => void;
}

// Define landmark type
export interface Landmark {
	id: string;
	title_short: LocalizedText;
	address?: {
		auto_normalized_address_local: string | null;
	} | null;
}

// UI Types for transformed data
export interface ConsumerProfileUI {
	id: string;
	productCount: number;
	name: string;
	productPrices: ConsumerProductPriceUI[];
}

export interface ConsumerProductPriceUI {
	id: string;
	priceId: string;
	title: string;
	colorSemantic: string;
	unitsBuyingOptions: UnitsBuyingOptionUI[];
	moneyBuyingOptions: MoneyBuyingOptionUI[];
	consumerTotalUnitsAvailable: number;
	consumerTotalUnitsCost: number;
	consumerTotalUnitsRemaining: number;
	payerTotalUnitsAvailable: number;
	autoCancelAtFar?: string | null;
	autoCancelAtFarReturnUnits?: number | null;
	autoCancelAtNear?: string | null;
	autoCancelAtNearReturnUnits?: number | null;
}

export interface UnitsBuyingOptionUI {
	id: string;
	orderPrices: OrderPriceUI[];
	colorPrimarySemantic: string;
	priceBrandName: string;
	priceTitle: string;
	consumerNameComponents: {
		givenName?: string;
		familyName?: string;
	};
	totalUnitsCost: number;
}

export interface MoneyBuyingOptionUI {
	id: string;
	priceOptionCombination: PriceOptionItem[];
	title: string;
	colorPrimarySemantic: string;
	totalMoneyInt: number;
	totalUnits: number;
	originalTotalMoneyInt: number;
	originalTotalUnits: number;
	currency: string;
	priceBrandName: string;
	priceTitle: string;
}

export interface OrderPriceUI {
	id: string;
	unitsAvailable: number;
	unitsCost: number;
	dealUnits: number;
	expireAt: string;
	purchaseCount: number;
	formattedUnits: string;
}

// Updated type to match the actual server response structure used in UnitsBuyingOptions
export interface OrderPriceRecord {
	id: string;
	units_available: number;
	units_cost: number;
	deal_units: number;
	auto_price_option_expire_at: string | null;
	price_option_purchase_count: number;
	price_option_title: LocalizedText;
	price_color_primary_semantic: string;
	price_color_primary_hex: string;
}

// Add to your existing types or create a new one if needed
export interface ProductWithRegistrationStatus {
	id: string;
	auto_stock_available: number;
	open_to_buy_at: string | null;
	title: string | null;
	product_prices: any[];
	event_count: number;
	first_event_start_at: string | null;
	last_event_end_at: string | null;
	user_registered: boolean; // Indicates if the user has already registered for this product
}

// Update ProductDetails type to include landmark
export interface ProductDetails {
	id: string;
	title: string | null;
	event_count: number;
	first_event_start_at: string | null;
	last_event_end_at: string | null;
	auto_stock_available: number;
	auto_first_event_start_at: string | null;
	open_to_buy_at: string | null;
	user_registered: boolean;
	waiting_group?: {
		id: string;
		group_member?: Array<{
			id: string;
			user_id: string;
		}>;
	};
	product_price: Array<{
		id: string;
		cost_units: number;
		title: string;
		title_short: string;
		product_classification: string;
		auto_cancel_at_far: string | null;
		auto_cancel_at_far_return_units: number | null;
		auto_cancel_at_near: string | null;
		auto_cancel_at_near_return_units: number | null;
		order_requirement?: OrderRequirement | null;
	}>;
	event_product?: Array<{
		id: string;
		kind: string | null;
		event: {
			id: string;
			start_at: string;
			duration_minute: number;
			metadata: {
				auto_final_title: any;
				auto_final_subtitle: any;
			};
			landmark: {
				id: string;
				title_short: LocalizedText;
			};
		};
	}>;
}

// Event type with landmark
export interface EventWithLandmark {
	id: string;
	start_at: string;
	duration_minute: number;
	title: string;
	subtitle: string;
	location: string;
	host: string;
	metadata: any;
	landmark?: Landmark;
	products: Array<{
		id: string;
		kind: string | null;
		product: ProductDetails;
	}>;
}
