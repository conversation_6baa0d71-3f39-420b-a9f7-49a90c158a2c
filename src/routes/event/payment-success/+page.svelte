<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { Button } from '$lib/components/ui/button/index.js';
	import { CheckCircle, XCircle } from '@lucide/svelte';
	import { page } from '$app/state';

	let isLoading = $state(true);
	let success = $state(false);
	let error = $state<string | null>(null);
	let redirectTimeout = $state<number | null>(null);

	onMount(async () => {
		const sessionId = page.url.searchParams.get('session_id');

		if (!sessionId) {
			error = 'Invalid checkout session';
			isLoading = false;
			return;
		}

		try {
			const response = await fetch('/event/private/verify-payment', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ sessionId })
			});

			const result = await response.json();

			if (result.type === 'error') {
				throw new Error(result.error.message);
			}

			success = true;

			// Redirect back to events page after 5 seconds
			redirectTimeout = setTimeout(() => {
				goto('/event');
			}, 5000) as unknown as number;
		} catch (err) {
			console.error('Error verifying payment:', err);
			if (err instanceof Error) {
				error = err.message;
			} else {
				error = 'An unexpected error occurred while verifying your payment.';
			}
		} finally {
			isLoading = false;
		}
	});

	function clearRedirectAndNavigate(path: string): void {
		if (redirectTimeout !== null) {
			clearTimeout(redirectTimeout);
			redirectTimeout = null;
		}
		goto(path);
	}
</script>

<div class="container mx-auto flex max-w-lg flex-col items-center justify-center py-12">
	<div class="w-full space-y-6 rounded-xl bg-card p-6 shadow-sm">
		<div class="flex flex-col items-center justify-center text-center">
			{#if isLoading}
				<div
					class="inline-block h-16 w-16 animate-spin rounded-full border-4 border-primary/30 border-t-primary"
				></div>
				<h2 class="mt-4 text-xl font-semibold">Verifying Payment</h2>
				<p class="mt-2 text-muted-foreground">Please wait while we verify your payment...</p>
			{:else if success}
				<CheckCircle class="h-16 w-16 text-green-500" />
				<h2 class="mt-4 text-xl font-semibold">Payment Successful!</h2>
				<p class="mt-2 text-muted-foreground">Your registration has been completed successfully.</p>
				<p class="mt-2 text-muted-foreground">
					You can view your order details or register for more sessions.
				</p>
			{:else}
				<XCircle class="h-16 w-16 text-destructive" />
				<h2 class="mt-4 text-xl font-semibold">Payment Verification Failed</h2>
				<p class="mt-2 text-muted-foreground">
					{error || 'There was an error processing your payment. Please try again.'}
				</p>
			{/if}
		</div>

		<div class="flex flex-col justify-center gap-3 pt-4 sm:flex-row">
			{#if !isLoading && success}
				<Button variant="default" onclick={() => clearRedirectAndNavigate('/private/my-order')}
					>View My Order</Button
				>
				<Button variant="outline" onclick={() => clearRedirectAndNavigate('/event')}
					>Register More Sessions</Button
				>
			{:else if !isLoading}
				<Button onclick={() => clearRedirectAndNavigate('/event')}>Return to Events</Button>
			{/if}
		</div>
	</div>
</div>
