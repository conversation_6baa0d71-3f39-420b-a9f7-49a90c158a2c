import { create<PERSON>rowserC<PERSON>, createServer<PERSON><PERSON>, isBrowser } from '@supabase/ssr';
import { PUBLIC_SUPABASE_ANON_KEY, PUBLIC_SUPABASE_URL } from '$env/static/public';
import type { LayoutLoad } from './$types';

export const load: LayoutLoad = async ({ data, depends, fetch }) => {
	depends('supabase:auth');

	// Use server-side data if available, otherwise create browser client
	const supabase = isBrowser()
		? createBrowserClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
				global: { fetch }
			})
		: createServerClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
				global: {
					fetch
				},
				cookies: {
					getAll() {
						return data.cookies;
					}
				}
			});

	console.log('[root/layout] Starting layout load');

	// Get the user first to validate the JWT
	// This is remove for performance reasons, double check if security is compromised
	// const {
	// 	data: { user }
	// } = await supabase.auth.getUser();

	return {
		...data,
		supabase
	};
};
