import type { LayoutServerLoad } from './$types';
import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';
import type {
	NavigationItem,
	BrandFeatureGroup,
	IconType,
	GroupNavigationItem
} from '$lib/types/navigation';
import type { Database } from '$lib/supabase/database.types';

function getFeatureItems(
	featureId: string,
	settings: Record<string, unknown>,
	brandId: string,
	isOwner: boolean
): NavigationItem[] {
	const baseUrl = '/private';
	const items: NavigationItem[] = [];

	if (featureId.startsWith('brand_ai_')) {
		items.push({
			title: 'AI',
			url: `${baseUrl}/agent`,
			icon: 'Bot' as const,
			features: ['brand_ai_write', 'brand_ai_read'],
			settings
		});
	} else if (featureId.startsWith('brand_payroll_')) {
		items.push({
			title: 'Payroll',
			url: `${baseUrl}/payroll`,
			icon: 'Receipt' as const,
			features: ['brand_payroll_read', 'brand_payroll_write'],
			settings
		});
	} else if (featureId.startsWith('brand_order_')) {
		// Only show Dashboard and Customers for specific brand if user is the owner
		if (brandId !== '073b8001-d1ff-19b2-9fbf-cfd5e80f2c0a' || isOwner) {
			items.push({
				title: 'Dashboard',
				url: `${baseUrl}/dashboard`,
				icon: 'LayoutDashboard' as const,
				features: ['brand_order_read'],
				settings
			});
		}
		items.push({
			title: 'Customers',
			url: `${baseUrl}/user`,
			icon: 'Users' as const,
			features: ['brand_order_write', 'brand_order_read'],
			settings
		});
	} else if (featureId.startsWith('brand_landmark_')) {
		items.push({
			title: 'Locations',
			url: `${baseUrl}/location`,
			icon: 'MapPin' as const,
			features: ['brand_landmark_write', 'brand_landmark_read'],
			settings
		});
	} else if (featureId.startsWith('brand_product_')) {
		items.push({
			title: 'Products',
			url: `${baseUrl}/product`,
			icon: 'Package' as const,
			features: ['brand_product_write', 'brand_product_read'],
			settings
		});
		items.push({
			title: 'Wiki Pages',
			url: `${baseUrl}/wikipage`,
			icon: 'Package' as const,
			features: ['brand_product_write', 'brand_product_read'],
			settings
		});
		items.push({
			title: 'Check-In',
			url: `${baseUrl}/check-in`,
			icon: 'Package' as const,
			features: ['brand_product_write', 'brand_product_read'],
			settings
		});
		items.push({
			title: 'Change Requests',
			url: `${baseUrl}/request`,
			icon: 'GitPullRequest' as const,
			features: ['brand_product_write', 'brand_product_read'],
			settings
		});
	} else if (featureId.startsWith('brand_time_slot_')) {
		items.push({
			title: 'Time Slots',
			url: `${baseUrl}/timeslot`,
			icon: 'Calendar' as const,
			features: ['brand_time_slot_write', 'brand_time_slot_read'],
			settings
		});
	}

	return items;
}

type FeatureJoin = Database['public']['Tables']['profile_feature_access']['Row'] & {
	brand: Pick<Database['public']['Tables']['brand']['Row'], 'id' | 'name_short'>;
	brand_id: string;
};

// +layout.server.ts
export const load = async ({ locals: { brand, supabase, user, session }, cookies }) => {
	console.log('[layout.server] Starting load');
	console.log('[layout.server] Brand:', brand);
	console.log('[layout.server] Is global marketplace:', brand === null);

	// In global marketplace mode, skip brand-specific features
	if (!brand) {
		return {
			cookies: cookies.getAll(),
			brand: null,
			user,
			session,
			groupsPromise: Promise.resolve([]),
			profileFeaturesPromise: Promise.resolve([])
		};
	}

	const groupsPromise = Promise.resolve([]);
	// Load groups without blocking
	// const groupsPromise = supabase
	// 	.from('group')
	// 	.select('id,name')
	// 	.order('created_at', { ascending: false })
	// 	.eq('brand_id', brand.id)
	// 	.limit(3)
	// 	.then(({ data: groups, error }) => {
	// 		if (error) {
	// 			console.error('Error loading groups:', error);
	// 			return [];
	// 		}
	// 		// console.log('[layout.server] Groups:', groups);

	// 		return groups.map(
	// 			(group): GroupNavigationItem => ({
	// 				title: group.name,
	// 				url: `/private/group/${group.id}`,
	// 				icon: 'Users' as const,
	// 				id: group.id
	// 			})
	// 		);
	// 	});

	// Load profile features without blocking
	const profileFeaturesPromise = async () => {
		if (!user?.id || !brand?.id) return [];

		const { data: features, error } = await supabase
			.from('profile_feature_access')
			.select(
				`
				feature_access_id,
				feature_access_settings,
				brand_id:auto_feature_access_brand_id,
				brand:auto_feature_access_brand_id!inner (
					id,
					name_short,
					owner_profile_id
				)
			`
			)
			.eq('profile_id', user.id)
			.eq('auto_feature_access_brand_id', brand.id);

		if (error) {
			console.error('Error loading profile features:', error);
			return [];
		}

		// Group features by brand
		const brandGroups = (features || []).reduce(
			(groups, feature: any) => {
				const brandId = feature.brand_id;
				if (!groups[brandId]) {
					groups[brandId] = {
						brand_id: brandId,
						brand_name: getLocalizedText((feature.brand?.name_short as LocalizedText) || {}),
						features: new Map()
					};
				}
				const navigationItems = getFeatureItems(
					feature.feature_access_id,
					feature.feature_access_settings as Record<string, unknown>,
					feature.brand_id,
					user.id === feature.brand?.owner_profile_id
				);
				navigationItems.forEach((item) => {
					if (!groups[brandId].features.has(item.title)) {
						groups[brandId].features.set(item.title, item);
					}
				});
				return groups;
			},
			{} as Record<
				string,
				Omit<BrandFeatureGroup, 'features'> & { features: Map<string, NavigationItem> }
			>
		);

		// Convert Map back to array before returning
		const result = Object.values(brandGroups).map((group) => ({
			...group,
			features: Array.from(group.features.values())
		}));

		// Add a completely separate Owner section if user is the brand owner
		if (user.id === brand.owner_profile_id) {
			result.push({
				brand_id: 'owner',
				brand_name: 'Owner',
				features: [
					{
						title: 'Brand Permissions',
						url: '/private/brand_permission',
						icon: 'Shield' as IconType
					},
					{
						title: 'Payroll',
						url: '/private/payroll',
						icon: 'Receipt' as IconType
					},
					{
						title: 'Programs',
						url: '/private/program',
						icon: 'Package' as IconType
					}
				]
			});
		}

		return result;
	};

	return {
		cookies: cookies.getAll(),
		brand,
		user,
		session,
		groupsPromise,
		profileFeaturesPromise: profileFeaturesPromise()
	};
};
