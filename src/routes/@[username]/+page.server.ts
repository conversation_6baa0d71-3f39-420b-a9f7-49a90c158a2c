import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';

export const load: PageServerLoad = async ({ params, locals: { supabase, user } }) => {
	const { username } = params;

	// Fetch profile data
	const { data: profile, error: profileError } = await supabase
		.from('profile')
		.select(
			`
			id,
			username,
			nickname,
			given_name,
			family_name,
			about,
			avatar_url,
			website,
			postal_code,
			created_at,
			instagram_id,
			twitter_id,
			facebook_id
		`
		)
		.eq('username', username)
		.single();

	if (profileError || !profile) {
		error(404, 'Profile not found');
	}

	// Check if viewing own profile
	const isOwnProfile = user?.id === profile.id;

	// Fetch profile wikipages (user's content)
	const { data: wikipages } = await supabase
		.from('profile_wikipage')
		.select(
			`
			id,
			kind,
			created_at,
			wikipage:wikipage_id (
				id,
				title,
				brief,
				desc,
				avatar,
				created_at,
				updated_at
			)
		`
		)
		.eq('profile_id', profile.id)
		.order('created_at', { ascending: false })
		.limit(12);

	// Fetch events with landmarks for map
	const { data: events } = await supabase
		.from('event')
		.select(
			`
			id,
			title,
			auto_final_title,
			auto_final_subtitle,
			start_at,
			duration_minute,
			poster_image_url,
			created_at,
			publishing_state,
			landmark_id,
			landmark:landmark_id (
				id,
				title_short,
				title_full,
				address:address_id (
					street,
					city,
					state,
					country,
					postal_code,
					auto_normalized_address
				)
			),
			metadata:metadata_id (
				title,
				subtitle,
				desc
			)
		`
		)
		.eq('creator_id', profile.id)
		.eq('publishing_state', 'published')
		.order('start_at', { ascending: false })
		.limit(50); // More for map view

	// Check if current user follows this profile
	let isFollowing = false;
	if (user && user.id !== profile.id) {
		const { data: followData } = await supabase
			.from('profile_connection')
			.select('id')
			.eq('from_profile_id', user.id)
			.eq('to_profile_id', profile.id)
			.eq('connection_type', 'follow')
			.eq('status', 'active')
			.single();

		isFollowing = !!followData;
	}

	// Check mutual groups for implicit connection
	let mutualGroupCount = 0;
	if (user && user.id !== profile.id) {
		// Get groups for current user
		const { data: userGroups } = await supabase
			.from('group_member')
			.select('group_id')
			.eq('user_id', user.id)
			.is('deleted_at', null)
			.not('accepted_at', 'is', null);

		if (userGroups && userGroups.length > 0) {
			// Count how many of these groups the profile is also in
			const groupIds = userGroups.map((g) => g.group_id);
			const { count } = await supabase
				.from('group_member')
				.select('*', { count: 'exact', head: true })
				.eq('user_id', profile.id)
				.in('group_id', groupIds)
				.is('deleted_at', null)
				.not('accepted_at', 'is', null);

			mutualGroupCount = count || 0;
		}
	}

	// Get profile stats using the database function
	const { data: statsData } = await supabase.rpc('get_profile_stats', { profile_id: profile.id });

	const stats = statsData?.[0] || {
		posts_count: 0,
		events_count: 0,
		followers_count: 0,
		following_count: 0
	};

	// Construct display name from given_name and family_name
	const displayName =
		[
			getLocalizedText((profile.given_name as LocalizedText) || {}),
			getLocalizedText((profile.family_name as LocalizedText) || {})
		]
			.filter(Boolean)
			.join(' ') ||
		profile.nickname ||
		profile.username;

	return {
		profile: {
			...profile,
			display_name: displayName
		},
		stats,
		wikipages: wikipages || [],
		events: events || [],
		isOwnProfile,
		isFollowing,
		mutualGroupCount,
		currentUserId: user?.id
	};
};
