-- Profile Connection Schema
-- This table handles both implicit (group-based) and explicit (follow) connections

-- Main connection table
CREATE TABLE IF NOT EXISTS profile_connection (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- The profile initiating the connection (follower)
    from_profile_id UUID NOT NULL REFERENCES profile(id) ON DELETE CASCADE,
    
    -- The profile receiving the connection (following)
    to_profile_id UUID NOT NULL REFERENCES profile(id) ON DELETE CASCADE,
    
    -- Connection type: 'follow' for explicit, 'group' for implicit
    connection_type TEXT NOT NULL CHECK (connection_type IN ('follow', 'group')),
    
    -- Connection strength (for ranking timeline activities)
    -- Higher values = stronger connection
    -- follow: base 100, group: base 50 + 10 per shared group
    connection_strength INTEGER NOT NULL DEFAULT 50,
    
    -- For group connections: array of shared group IDs
    shared_group_ids UUID[] DEFAULT '{}',
    
    -- Connection status
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'pending', 'blocked', 'muted')),
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    accepted_at TIMESTAMPTZ, -- For future use if you want request/accept flow
    
    -- Ensure no duplicate connections
    CONSTRAINT unique_profile_connection UNIQUE (from_profile_id, to_profile_id, connection_type)
);

-- Indexes for performance
CREATE INDEX idx_profile_connection_from ON profile_connection(from_profile_id, status);
CREATE INDEX idx_profile_connection_to ON profile_connection(to_profile_id, status);
CREATE INDEX idx_profile_connection_type ON profile_connection(connection_type, status);
CREATE INDEX idx_profile_connection_strength ON profile_connection(connection_strength DESC);
CREATE INDEX idx_profile_connection_groups ON profile_connection USING GIN(shared_group_ids);

-- View for bidirectional connections (mutual follows)
CREATE VIEW profile_mutual_connection AS
SELECT 
    pc1.from_profile_id as profile_id_a,
    pc1.to_profile_id as profile_id_b,
    pc1.connection_type,
    GREATEST(pc1.connection_strength, pc2.connection_strength) as mutual_strength,
    pc1.created_at
FROM profile_connection pc1
INNER JOIN profile_connection pc2 
    ON pc1.from_profile_id = pc2.to_profile_id 
    AND pc1.to_profile_id = pc2.from_profile_id
    AND pc1.connection_type = pc2.connection_type
WHERE pc1.status = 'active' 
    AND pc2.status = 'active'
    AND pc1.from_profile_id < pc1.to_profile_id; -- Avoid duplicates

-- Function to update implicit connections based on group membership
CREATE OR REPLACE FUNCTION update_implicit_connections()
RETURNS TRIGGER AS $$
BEGIN
    -- When someone joins a group, create implicit connections with other members
    IF TG_OP = 'INSERT' AND NEW.accepted_at IS NOT NULL THEN
        -- Insert implicit connections with all other active group members
        INSERT INTO profile_connection (
            from_profile_id,
            to_profile_id,
            connection_type,
            connection_strength,
            shared_group_ids
        )
        SELECT 
            NEW.user_id,
            gm.user_id,
            'group',
            50 + (10 * COUNT(DISTINCT gm2.group_id)),
            ARRAY_AGG(DISTINCT gm2.group_id)
        FROM group_member gm
        INNER JOIN group_member gm2 
            ON gm.user_id = gm2.user_id 
            AND gm2.user_id = NEW.user_id
            AND gm2.accepted_at IS NOT NULL
        WHERE gm.group_id = NEW.group_id
            AND gm.user_id != NEW.user_id
            AND gm.accepted_at IS NOT NULL
            AND gm.deleted_at IS NULL
        GROUP BY gm.user_id
        ON CONFLICT (from_profile_id, to_profile_id, connection_type) 
        DO UPDATE SET
            connection_strength = EXCLUDED.connection_strength,
            shared_group_ids = EXCLUDED.shared_group_ids,
            updated_at = now();
            
        -- Also create reverse connections
        INSERT INTO profile_connection (
            from_profile_id,
            to_profile_id,
            connection_type,
            connection_strength,
            shared_group_ids
        )
        SELECT 
            gm.user_id,
            NEW.user_id,
            'group',
            50 + (10 * COUNT(DISTINCT gm2.group_id)),
            ARRAY_AGG(DISTINCT gm2.group_id)
        FROM group_member gm
        INNER JOIN group_member gm2 
            ON gm.user_id = gm2.user_id 
            AND gm2.user_id = NEW.user_id
            AND gm2.accepted_at IS NOT NULL
        WHERE gm.group_id = NEW.group_id
            AND gm.user_id != NEW.user_id
            AND gm.accepted_at IS NOT NULL
            AND gm.deleted_at IS NULL
        GROUP BY gm.user_id
        ON CONFLICT (from_profile_id, to_profile_id, connection_type) 
        DO UPDATE SET
            connection_strength = EXCLUDED.connection_strength,
            shared_group_ids = EXCLUDED.shared_group_ids,
            updated_at = now();
    END IF;
    
    -- When someone leaves a group, update implicit connections
    IF TG_OP = 'UPDATE' AND NEW.deleted_at IS NOT NULL AND OLD.deleted_at IS NULL THEN
        -- Recalculate shared groups for all connections
        WITH shared_groups AS (
            SELECT 
                gm1.user_id as user_a,
                gm2.user_id as user_b,
                COUNT(DISTINCT gm1.group_id) as group_count,
                ARRAY_AGG(DISTINCT gm1.group_id) as groups
            FROM group_member gm1
            INNER JOIN group_member gm2 
                ON gm1.group_id = gm2.group_id 
                AND gm1.user_id != gm2.user_id
            WHERE (gm1.user_id = NEW.user_id OR gm2.user_id = NEW.user_id)
                AND gm1.accepted_at IS NOT NULL
                AND gm2.accepted_at IS NOT NULL
                AND gm1.deleted_at IS NULL
                AND gm2.deleted_at IS NULL
                AND gm1.group_id != NEW.group_id
            GROUP BY gm1.user_id, gm2.user_id
        )
        UPDATE profile_connection pc
        SET 
            connection_strength = 50 + (10 * sg.group_count),
            shared_group_ids = sg.groups,
            updated_at = now()
        FROM shared_groups sg
        WHERE pc.from_profile_id = sg.user_a
            AND pc.to_profile_id = sg.user_b
            AND pc.connection_type = 'group';
            
        -- Delete connections with no shared groups
        DELETE FROM profile_connection
        WHERE connection_type = 'group'
            AND (from_profile_id = NEW.user_id OR to_profile_id = NEW.user_id)
            AND array_length(shared_group_ids, 1) IS NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for group member changes
CREATE TRIGGER trigger_update_implicit_connections
AFTER INSERT OR UPDATE ON group_member
FOR EACH ROW
EXECUTE FUNCTION update_implicit_connections();

-- Function to get mutual groups between two users
CREATE OR REPLACE FUNCTION get_mutual_groups(user_id_a UUID, user_id_b UUID)
RETURNS TABLE(group_id UUID, group_name TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT 
        g.id,
        g.name
    FROM group_member gm1
    INNER JOIN group_member gm2 
        ON gm1.group_id = gm2.group_id
    INNER JOIN "group" g 
        ON g.id = gm1.group_id
    WHERE gm1.user_id = user_id_a
        AND gm2.user_id = user_id_b
        AND gm1.accepted_at IS NOT NULL
        AND gm2.accepted_at IS NOT NULL
        AND gm1.deleted_at IS NULL
        AND gm2.deleted_at IS NULL
    ORDER BY g.name;
END;
$$ LANGUAGE plpgsql;

-- Function to get timeline activities (optimized for fast retrieval)
CREATE OR REPLACE FUNCTION get_timeline_activities(
    for_user_id UUID,
    limit_count INTEGER DEFAULT 50,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE(
    activity_type TEXT,
    activity_id UUID,
    actor_profile_id UUID,
    created_at TIMESTAMPTZ,
    connection_strength INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH connected_profiles AS (
        -- Get all connected profiles with their connection strength
        SELECT DISTINCT
            to_profile_id as profile_id,
            MAX(connection_strength) as strength
        FROM profile_connection
        WHERE from_profile_id = for_user_id
            AND status = 'active'
        GROUP BY to_profile_id
    )
    -- Get activities from connected profiles
    SELECT * FROM (
        -- Events
        SELECT 
            'event'::TEXT as activity_type,
            e.id as activity_id,
            e.creator_id as actor_profile_id,
            e.created_at,
            cp.strength as connection_strength
        FROM event e
        INNER JOIN connected_profiles cp ON e.creator_id = cp.profile_id
        WHERE e.publishing_state = 'published'
            AND e.created_at > now() - interval '30 days'
        
        UNION ALL
        
        -- Wikipages (as posts)
        SELECT 
            'wikipage'::TEXT as activity_type,
            pw.wikipage_id as activity_id,
            pw.profile_id as actor_profile_id,
            pw.created_at,
            cp.strength as connection_strength
        FROM profile_wikipage pw
        INNER JOIN connected_profiles cp ON pw.profile_id = cp.profile_id
        INNER JOIN wikipage w ON w.id = pw.wikipage_id
        WHERE pw.created_at > now() - interval '30 days'
    ) activities
    ORDER BY 
        created_at DESC,
        connection_strength DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- Helper function to follow/unfollow
CREATE OR REPLACE FUNCTION toggle_follow(
    follower_id UUID,
    following_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    existing_follow UUID;
    is_following BOOLEAN;
BEGIN
    -- Check if already following
    SELECT id INTO existing_follow
    FROM profile_connection
    WHERE from_profile_id = follower_id
        AND to_profile_id = following_id
        AND connection_type = 'follow'
        AND status = 'active';
    
    IF existing_follow IS NOT NULL THEN
        -- Unfollow
        DELETE FROM profile_connection WHERE id = existing_follow;
        is_following := false;
    ELSE
        -- Follow
        INSERT INTO profile_connection (
            from_profile_id,
            to_profile_id,
            connection_type,
            connection_strength,
            status
        ) VALUES (
            follower_id,
            following_id,
            'follow',
            100,
            'active'
        );
        is_following := true;
    END IF;
    
    RETURN is_following;
END;
$$ LANGUAGE plpgsql;

-- Stats functions
CREATE OR REPLACE FUNCTION get_profile_stats(profile_id UUID)
RETURNS TABLE(
    posts_count BIGINT,
    events_count BIGINT,
    followers_count BIGINT,
    following_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM profile_wikipage WHERE profile_id = $1) as posts_count,
        (SELECT COUNT(*) FROM event WHERE creator_id = $1 AND publishing_state = 'published') as events_count,
        (SELECT COUNT(*) FROM profile_connection WHERE to_profile_id = $1 AND connection_type = 'follow' AND status = 'active') as followers_count,
        (SELECT COUNT(*) FROM profile_connection WHERE from_profile_id = $1 AND connection_type = 'follow' AND status = 'active') as following_count;
END;
$$ LANGUAGE plpgsql;

-- RLS Policies
ALTER TABLE profile_connection ENABLE ROW LEVEL SECURITY;

-- Users can see their own connections
CREATE POLICY "Users can view own connections" ON profile_connection
    FOR SELECT
    USING (auth.uid() = from_profile_id OR auth.uid() = to_profile_id);

-- Users can create follow connections
CREATE POLICY "Users can follow others" ON profile_connection
    FOR INSERT
    WITH CHECK (auth.uid() = from_profile_id AND connection_type = 'follow');

-- Users can delete their own follow connections
CREATE POLICY "Users can unfollow" ON profile_connection
    FOR DELETE
    USING (auth.uid() = from_profile_id AND connection_type = 'follow');

-- System can manage all connections (for triggers)
CREATE POLICY "System can manage connections" ON profile_connection
    FOR ALL
    USING (current_setting('role') = 'service_role'); 