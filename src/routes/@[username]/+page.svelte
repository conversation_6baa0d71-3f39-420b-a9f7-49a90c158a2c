<script lang="ts">
	import { <PERSON>Container } from '$lib/components/layout';
	import { Button } from '$lib/components/ui/button';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '$lib/components/ui/tabs';
	import { Avatar, AvatarImage, AvatarFallback } from '$lib/components/ui/avatar';
	import { Badge } from '$lib/components/ui/badge';
	import { Separator } from '$lib/components/ui/separator';
	import {
		MapPin,
		Link,
		Calendar,
		MoreHorizontal,
		Settings,
		Share2,
		UserPlus,
		UserCheck,
		CalendarDays,
		FileText,
		Instagram,
		Twitter,
		Facebook,
		Map,
		Users
	} from '@lucide/svelte';
	import { formatDistanceToNow } from 'date-fns';
	import { goto, invalidateAll } from '$app/navigation';
	import { getLocalizedText, type LocalizedText } from '$lib/utils/localization';

	interface Props {
		data: any;
	}

	let { data }: Props = $props();
	let activeTab = $state('wikipages');
	let isFollowing = $state(data.isFollowing);

	function formatNumber(num: number): string {
		if (num >= 1000000) {
			return `${(num / 1000000).toFixed(1)}M`;
		} else if (num >= 1000) {
			return `${(num / 1000).toFixed(1)}K`;
		}
		return num.toString();
	}

	function getInitials(name: string): string {
		return name
			.split(' ')
			.map((n) => n[0])
			.join('')
			.toUpperCase()
			.slice(0, 2);
	}

	async function handleFollow() {
		if (!data.currentUserId) {
			goto('/auth/sign-in');
			return;
		}

		// Toggle follow state optimistically
		isFollowing = !isFollowing;

		try {
			const response = await fetch('/api/profile/follow', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ profileId: data.profile.id })
			});

			const result = await response.json();

			if (!response.ok) {
				// Revert on error
				isFollowing = !isFollowing;
				console.error('Failed to toggle follow:', result.error);
				return;
			}

			// Update stats if provided
			if (result.stats) {
				data.stats = result.stats;
			}
		} catch (error) {
			// Revert on error
			isFollowing = !isFollowing;
			console.error('Error toggling follow:', error);
		}
	}
</script>

{#snippet contentSnippet()}
	<!-- Profile Header Section -->
	<div class="space-y-6">
		<!-- Profile Info -->
		<div class="flex flex-col gap-6 md:flex-row md:items-start md:justify-between">
			<div class="flex flex-col gap-4 sm:flex-row sm:items-start">
				<!-- Avatar -->
				<Avatar class="h-24 w-24 sm:h-32 sm:w-32">
					<AvatarImage src={data.profile.avatar_url} alt={data.profile.display_name} />
					<AvatarFallback class="text-2xl">
						{getInitials(data.profile.display_name)}
					</AvatarFallback>
				</Avatar>

				<!-- Name and Info -->
				<div class="space-y-3">
					<div>
						<h1 class="text-2xl font-semibold">
							{data.profile.display_name}
						</h1>
						<p class="text-muted-foreground">@{data.profile.username}</p>
					</div>

					<!-- About section inline like Instagram -->
					{#if data.profile.about}
						<p class="max-w-2xl text-sm">
							{getLocalizedText((data.profile.about as LocalizedText) || data.profile.about)}
						</p>
					{/if}

					<!-- Connection info -->
					{#if data.mutualGroupCount > 0 && !data.isOwnProfile}
						<div class="flex items-center gap-1 text-sm text-muted-foreground">
							<Users class="h-4 w-4" />
							<span>{data.mutualGroupCount} mutual group{data.mutualGroupCount > 1 ? 's' : ''}</span
							>
						</div>
					{/if}

					<!-- Meta Info -->
					<div class="flex flex-wrap gap-4 text-sm text-muted-foreground">
						{#if data.profile.postal_code}
							<div class="flex items-center gap-1">
								<MapPin class="h-4 w-4" />
								<span>{data.profile.postal_code}</span>
							</div>
						{/if}
						{#if data.profile.website}
							<a
								href={data.profile.website.startsWith('http')
									? data.profile.website
									: `https://${data.profile.website}`}
								target="_blank"
								rel="noopener noreferrer"
								class="flex items-center gap-1 hover:text-foreground"
							>
								<Link class="h-4 w-4" />
								<span>{data.profile.website.replace(/^https?:\/\//, '')}</span>
							</a>
						{/if}
						<div class="flex items-center gap-1">
							<Calendar class="h-4 w-4" />
							<span
								>Joined {formatDistanceToNow(new Date(data.profile.created_at), {
									addSuffix: true
								})}</span
							>
						</div>
					</div>

					<!-- Social Links -->
					{#if data.profile.instagram_id || data.profile.twitter_id || data.profile.facebook_id}
						<div class="flex gap-2">
							{#if data.profile.instagram_id}
								<Button
									href={`https://instagram.com/${data.profile.instagram_id}`}
									target="_blank"
									variant="outline"
									size="sm"
									class="gap-2"
								>
									<Instagram class="h-4 w-4" />
								</Button>
							{/if}
							{#if data.profile.twitter_id}
								<Button
									href={`https://twitter.com/${data.profile.twitter_id}`}
									target="_blank"
									variant="outline"
									size="sm"
									class="gap-2"
								>
									<Twitter class="h-4 w-4" />
								</Button>
							{/if}
							{#if data.profile.facebook_id}
								<Button
									href={`https://facebook.com/${data.profile.facebook_id}`}
									target="_blank"
									variant="outline"
									size="sm"
									class="gap-2"
								>
									<Facebook class="h-4 w-4" />
								</Button>
							{/if}
						</div>
					{/if}
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex gap-2">
				{#if data.isOwnProfile}
					<Button href="/settings/profile" variant="outline" class="gap-2">
						<Settings class="h-4 w-4" />
						Edit Profile
					</Button>
				{:else}
					<Button
						onclick={handleFollow}
						variant={isFollowing ? 'outline' : 'default'}
						class="gap-2"
					>
						{#if isFollowing}
							<UserCheck class="h-4 w-4" />
							Following
						{:else}
							<UserPlus class="h-4 w-4" />
							Follow
						{/if}
					</Button>
				{/if}
				<Button variant="outline" size="icon">
					<Share2 class="h-4 w-4" />
				</Button>
				<Button variant="outline" size="icon">
					<MoreHorizontal class="h-4 w-4" />
				</Button>
			</div>
		</div>

		<!-- Stats -->
		<div class="flex gap-8">
			<div class="text-center">
				<div class="text-2xl font-semibold">{formatNumber(data.stats.posts_count)}</div>
				<div class="text-sm text-muted-foreground">Pages</div>
			</div>
			<div class="text-center">
				<div class="text-2xl font-semibold">{formatNumber(data.stats.events_count)}</div>
				<div class="text-sm text-muted-foreground">Events</div>
			</div>
			<button class="text-center">
				<div class="text-2xl font-semibold">{formatNumber(data.stats.followers_count)}</div>
				<div class="text-sm text-muted-foreground">Followers</div>
			</button>
			<button class="text-center">
				<div class="text-2xl font-semibold">{formatNumber(data.stats.following_count)}</div>
				<div class="text-sm text-muted-foreground">Following</div>
			</button>
		</div>

		<Separator />

		<!-- Content Tabs -->
		<Tabs bind:value={activeTab} class="w-full">
			<TabsList class="w-full justify-start">
				<TabsTrigger value="wikipages" class="gap-2">
					<FileText class="h-4 w-4" />
					Pages
				</TabsTrigger>
				<TabsTrigger value="events" class="gap-2">
					<CalendarDays class="h-4 w-4" />
					Events
				</TabsTrigger>
				<TabsTrigger value="map" class="gap-2">
					<Map class="h-4 w-4" />
					Map
				</TabsTrigger>
			</TabsList>

			<TabsContent value="wikipages" class="mt-6">
				{#if data.wikipages.length > 0}
					<div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
						{#each data.wikipages as item}
							{#if item.wikipage}
								<Card class="group cursor-pointer overflow-hidden transition-all hover:shadow-lg">
									<a href="/wikipage/{item.wikipage.id}" class="block">
										{#if item.wikipage.avatar}
											<div class="aspect-video overflow-hidden bg-muted">
												<img
													src={getLocalizedText(item.wikipage.avatar as LocalizedText)}
													alt={getLocalizedText((item.wikipage.title as LocalizedText) || {})}
													class="h-full w-full object-cover transition-transform group-hover:scale-105"
												/>
											</div>
										{/if}
										<CardContent class="p-4">
											<h3 class="line-clamp-2 font-semibold">
												{getLocalizedText((item.wikipage.title as LocalizedText) || {})}
											</h3>
											{#if item.wikipage.brief}
												<p class="mt-2 line-clamp-3 text-sm text-muted-foreground">
													{getLocalizedText((item.wikipage.brief as LocalizedText) || {})}
												</p>
											{/if}
											<div class="mt-4 flex items-center gap-2 text-xs text-muted-foreground">
												<Badge variant="secondary">{item.kind}</Badge>
												<span
													>{formatDistanceToNow(new Date(item.created_at), {
														addSuffix: true
													})}</span
												>
											</div>
										</CardContent>
									</a>
								</Card>
							{/if}
						{/each}
					</div>
				{:else}
					<div class="py-12 text-center">
						<p class="text-muted-foreground">No pages yet</p>
					</div>
				{/if}
			</TabsContent>

			<TabsContent value="events" class="mt-6">
				{#if data.events.length > 0}
					<div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
						{#each data.events.slice(0, 12) as event}
							<Card class="group cursor-pointer overflow-hidden transition-all hover:shadow-lg">
								<a href="/event/{event.id}" class="block">
									{#if event.poster_image_url}
										<div class="aspect-video overflow-hidden bg-muted">
											<img
												src={event.poster_image_url}
												alt={getLocalizedText(
													(event.auto_final_title as LocalizedText) ||
														(event.title as LocalizedText) ||
														{}
												)}
												class="h-full w-full object-cover transition-transform group-hover:scale-105"
											/>
										</div>
									{/if}
									<CardContent class="p-4">
										<h3 class="line-clamp-2 font-semibold">
											{getLocalizedText(
												(event.auto_final_title as LocalizedText) ||
													(event.title as LocalizedText) ||
													{}
											)}
										</h3>
										{#if event.auto_final_subtitle}
											<p class="mt-1 line-clamp-2 text-sm text-muted-foreground">
												{getLocalizedText((event.auto_final_subtitle as LocalizedText) || {})}
											</p>
										{/if}
										{#if event.landmark?.address}
											<p class="mt-2 flex items-center gap-1 text-xs text-muted-foreground">
												<MapPin class="h-3 w-3" />
												<span class="line-clamp-1">
													{event.landmark.address.city}, {event.landmark.address.state}
												</span>
											</p>
										{/if}
										<div class="mt-3 flex items-center gap-2 text-xs text-muted-foreground">
											<CalendarDays class="h-3 w-3" />
											<span>{new Date(event.start_at).toLocaleDateString()}</span>
											<span>·</span>
											<span>{event.duration_minute} min</span>
										</div>
									</CardContent>
								</a>
							</Card>
						{/each}
					</div>
				{:else}
					<div class="py-12 text-center">
						<p class="text-muted-foreground">No events yet</p>
					</div>
				{/if}
			</TabsContent>

			<TabsContent value="map" class="mt-6">
				<Card>
					<CardContent class="p-6">
						<div class="space-y-4">
							<div class="rounded-lg bg-muted/50 p-12 text-center">
								<Map class="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
								<h3 class="mb-2 text-lg font-semibold">Map View</h3>
								<p class="text-sm text-muted-foreground">
									Interactive map showing events and locations will be implemented here
								</p>
							</div>

							{#if data.events.filter((e: any) => e.landmark).length > 0}
								<div>
									<h4 class="mb-3 font-semibold">Events with Locations</h4>
									<div class="space-y-2">
										{#each data.events.filter((e: any) => e.landmark) as event}
											<div class="flex items-start gap-3 rounded-lg border p-3">
												<MapPin class="mt-0.5 h-4 w-4 text-muted-foreground" />
												<div class="flex-1 space-y-1">
													<p class="text-sm font-medium">
														{getLocalizedText(
															(event.auto_final_title as LocalizedText) ||
																(event.title as LocalizedText) ||
																{}
														)}
													</p>
													<p class="text-xs text-muted-foreground">
														{event.landmark.address.street}, {event.landmark.address.city}, {event
															.landmark.address.state}
													</p>
													<p class="text-xs text-muted-foreground">
														{new Date(event.start_at).toLocaleDateString()} · {event.duration_minute}
														min
													</p>
												</div>
											</div>
										{/each}
									</div>
								</div>
							{/if}
						</div>
					</CardContent>
				</Card>
			</TabsContent>
		</Tabs>
	</div>
{/snippet}

<PageContainer content={contentSnippet} />
