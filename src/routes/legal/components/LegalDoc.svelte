<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { ArrowLeft } from '@lucide/svelte';
	import type { Snippet } from 'svelte';

	interface Props {
		title: string;
		lastUpdated: string;
		children: Snippet;
	}

	let { title, lastUpdated, children }: Props = $props();
</script>

<div class="relative mx-auto max-w-3xl px-4 py-8 md:py-16">
	<a
		href="/legal"
		class="mb-6 inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground md:absolute md:-left-16 md:top-8 md:mb-0"
	>
		<ArrowLeft class="size-4" />
		<span>All Docs</span>
	</a>

	<div class="space-y-6 md:space-y-8">
		<div class="space-y-2">
			<h1 class="text-3xl font-semibold tracking-tight">{title}</h1>
			<p class="text-sm text-muted-foreground">Last updated: {lastUpdated}</p>
		</div>

		<div class="prose prose-sm max-w-none dark:prose-invert">
			{@render children()}
		</div>
	</div>
</div>
