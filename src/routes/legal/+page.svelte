<script lang="ts">
	import { ArrowRight, Scale, Shield } from '@lucide/svelte';

	interface LegalPage {
		title: string;
		description: string;
		href: string;
		Icon: typeof Scale | typeof Shield;
	}

	let pages: LegalPage[] = [
		{
			title: 'Terms of Service',
			description:
				'Our terms and conditions for using HanaWeb services, including your rights and responsibilities.',
			href: '/legal/terms',
			Icon: Scale
		},
		{
			title: 'Privacy Policy',
			description: 'How we collect, use, and protect your personal information and data rights.',
			href: '/legal/privacy',
			Icon: Shield
		}
	];
</script>

<div class="container mx-auto max-w-3xl py-16">
	<div class="mb-12 space-y-2 text-center">
		<h1 class="text-3xl font-semibold tracking-tight">Legal Information</h1>
		<p class="text-muted-foreground">Important documents about your rights and our policies</p>
	</div>

	<div class="grid gap-6">
		{#each pages as { title, description, href, Icon }}
			<a
				{href}
				class="group relative flex items-start gap-6 rounded-lg border bg-card px-8 py-7 transition-all duration-200 hover:border-border/60 hover:shadow-md"
			>
				<div
					class="shrink-0 rounded-lg bg-primary/5 p-2.5 text-primary transition-colors group-hover:bg-primary/10"
				>
					{#key Icon}
						<Icon class="size-5" />
					{/key}
				</div>
				<div class="min-w-0 flex-1">
					<h2 class="text-xl font-semibold tracking-tight">{title}</h2>
					<p class="mt-2 line-clamp-2 text-sm text-muted-foreground">{description}</p>
				</div>
				<div class="shrink-0 self-center pl-6">
					<ArrowRight
						class="size-5 text-muted-foreground/40 transition-transform duration-200 group-hover:translate-x-0.5 group-hover:text-primary"
					/>
				</div>
			</a>
		{/each}
	</div>
</div>
