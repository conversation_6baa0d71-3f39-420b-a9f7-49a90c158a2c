-- =============================================
-- 奶茶店SaaS系统扩展 - 国际化版本
-- 
-- 命名规范：
-- - 使用singular（单数）命名
-- - 使用kind替代type
-- - 统一前缀：
--   * unit_*      - 单位系统
--   * inventory_* - 库存系统  
--   * purchase_*  - 采购系统
--   * supplier_*  - 供应商系统
--   * cost_*      - 成本系统
--   * staff_*     - 人员系统
--   * schedule_*  - 排班系统
--   * revenue_*   - 营收系统
--   * modifier_*  - 产品变体系统
--   * recipe_*    - 配方系统
-- 
-- 与现有系统集成：
-- - brand = tenant (租户/品牌)
-- - landmark = store (门店/仓库位置)
-- - profile = user (用户)
-- - payroll_profile = employee (员工，profile的子集)
-- 
-- 国际化支持：
-- - 所有用户可见的文本字段使用JSONB类型
-- - 格式：{"en": "English", "zh": "中文", "ko": "한국어", "ja": "日本語"}
-- - 使用LocalizedText类型定义
-- =============================================

-- LocalizedText类型定义（用于类型安全）
-- 在应用层使用: type LocalizedText = { [key: string]: string }

-- =============================================
-- 1. 单位系统 (unit_*)
-- 支持语义单位（如"一箱草莓"）到科学单位（如"千克"）的转换
-- =============================================

-- 基础科学单位表（维度标准单位）
CREATE TABLE unit_base (
    id TEXT PRIMARY KEY, -- 'kilogram', 'liter', 'piece', 'meter'
    name JSONB NOT NULL DEFAULT '{}', -- {"en": "Kilogram", "zh": "千克"}
    symbol TEXT NOT NULL, -- 'kg', 'L', 'pcs', 'm' - 符号通常是国际通用的
    dimension TEXT NOT NULL CHECK (dimension IN ('mass', 'volume', 'count', 'length')),
    description JSONB DEFAULT '{}'
);

-- 插入基础单位
INSERT INTO unit_base (id, name, symbol, dimension) VALUES 
('kilogram', '{"en": "Kilogram", "zh": "千克", "ko": "킬로그램", "ja": "キログラム"}', 'kg', 'mass'),
('liter', '{"en": "Liter", "zh": "升", "ko": "리터", "ja": "リットル"}', 'L', 'volume'),  
('piece', '{"en": "Piece", "zh": "个", "ko": "개", "ja": "個"}', 'pcs', 'count'),
('meter', '{"en": "Meter", "zh": "米", "ko": "미터", "ja": "メートル"}', 'm', 'length');

-- 品牌自定义语义单位表
-- 例如：一箱草莓、一袋面粉、标准杯等
CREATE TABLE unit_semantic (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brand_id UUID NOT NULL REFERENCES brand(id),
    name JSONB NOT NULL DEFAULT '{}', -- {"en": "Small Box of Strawberries", "zh": "小箱草莓"}
    abbreviation JSONB DEFAULT '{}', -- {"en": "S.Box", "zh": "小箱"}
    category TEXT CHECK (category IN ('packaging', 'serving', 'custom')),
    description JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(brand_id, name)
);

-- 语义单位到基础单位的转换表
-- 支持一个语义单位转换到多个维度
-- 例如：一箱草莓 = 2.5kg (重量) + 500个 (数量)
CREATE TABLE unit_conversion (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    semantic_unit_id UUID NOT NULL REFERENCES unit_semantic(id) ON DELETE CASCADE,
    base_unit_id TEXT NOT NULL REFERENCES unit_base(id),
    conversion_factor DECIMAL(12,6) NOT NULL, -- semantic_unit * factor = base_unit
    is_primary BOOLEAN DEFAULT false, -- 标记主要转换维度
    notes JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(semantic_unit_id, base_unit_id)
);

-- 创建索引提高查询性能
CREATE INDEX idx_unit_conversion_lookup ON unit_conversion(semantic_unit_id, is_primary);

-- =============================================
-- 2. 修改器/变体系统 (modifier_*)
-- 支持奶茶的各种选项：甜度、温度、加料等
-- =============================================

-- 修改器组表（甜度、温度、杯型、加料等）
CREATE TABLE modifier_group (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brand_id UUID NOT NULL REFERENCES brand(id),
    name JSONB NOT NULL DEFAULT '{}', -- {"en": "Sweetness", "zh": "甜度"}
    code TEXT NOT NULL, -- 'sweetness', 'temperature', 'size', 'topping'
    selection_mode TEXT NOT NULL CHECK (selection_mode IN ('single', 'multiple')),
    is_required BOOLEAN DEFAULT false, -- 是否必选
    min_selections INTEGER DEFAULT 0,
    max_selections INTEGER, -- NULL表示无限制
    instruction JSONB DEFAULT '{}', -- {"en": "Choose your sweetness level", "zh": "请选择甜度"}
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(brand_id, code)
);

-- 修改器选项表（具体的选项）
CREATE TABLE modifier_option (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    modifier_group_id UUID NOT NULL REFERENCES modifier_group(id) ON DELETE CASCADE,
    name JSONB NOT NULL DEFAULT '{}', -- {"en": "Full Sugar", "zh": "全糖"}
    code TEXT NOT NULL, -- 'full-sweet', 'half-sweet', 'no-sweet', 'add-pearl'
    description JSONB DEFAULT '{}', -- {"en": "100% sweetness", "zh": "100%甜度"}
    price_adjustment DECIMAL(10,2) DEFAULT 0, -- 价格调整
    is_default BOOLEAN DEFAULT false, -- 是否默认选项
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(modifier_group_id, code)
);

-- 修改器互斥规则表
-- 例如：选了"零卡糖"就不能选"无糖"
CREATE TABLE modifier_exclusion_rule (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    modifier_option_id UUID NOT NULL REFERENCES modifier_option(id) ON DELETE CASCADE,
    excludes_modifier_option_id UUID NOT NULL REFERENCES modifier_option(id) ON DELETE CASCADE,
    exclusion_kind TEXT DEFAULT 'mutual' CHECK (exclusion_kind IN ('mutual', 'conditional')),
    reason JSONB DEFAULT '{}', -- {"en": "Cannot combine zero-cal with no sugar", "zh": "零卡糖不能与无糖同时选择"}
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(modifier_option_id, excludes_modifier_option_id),
    CHECK (modifier_option_id != excludes_modifier_option_id)
);

-- 产品可用的修改器组
CREATE TABLE product_modifier_group (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES product(id) ON DELETE CASCADE,
    modifier_group_id UUID NOT NULL REFERENCES modifier_group(id),
    is_required BOOLEAN, -- 覆盖modifier_group的默认设置
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(product_id, modifier_group_id)
);

-- =============================================
-- 3. 库存系统 (inventory_*)
-- =============================================

-- 扩展landmark_kind以支持库存位置
INSERT INTO landmark_kind (id, name) VALUES 
('warehouse_main', '{"en": "Main Warehouse", "zh": "主仓库", "ko": "주 창고", "ja": "メイン倉庫"}'),
('warehouse_local', '{"en": "Local Warehouse", "zh": "本地仓库", "ko": "로컬 창고", "ja": "ローカル倉庫"}'), 
('store', '{"en": "Store", "zh": "门店", "ko": "매장", "ja": "店舗"}'),
('supplier', '{"en": "Supplier Location", "zh": "供应商", "ko": "공급업체", "ja": "サプライヤー"}')
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name;

-- 库存物品表
CREATE TABLE inventory_item (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brand_id UUID NOT NULL REFERENCES brand(id),
    name JSONB NOT NULL DEFAULT '{}', -- {"en": "Fresh Strawberries", "zh": "新鲜草莓"}
    sku TEXT,
    category TEXT, -- 'raw_material', 'packaging', 'finished_goods'
    description JSONB DEFAULT '{}',
    
    -- 单位设置
    purchase_unit_id UUID NOT NULL REFERENCES unit_semantic(id), -- 采购单位（如：箱）
    stock_unit_id UUID NOT NULL REFERENCES unit_semantic(id), -- 库存单位（如：千克）
    usage_unit_id UUID NOT NULL REFERENCES unit_semantic(id), -- 使用单位（如：克）
    
    -- 库存阈值
    min_stock_threshold DECIMAL(10,2) DEFAULT 0, -- 最低库存警戒线
    max_stock_threshold DECIMAL(10,2), -- 最高库存限制
    reorder_point DECIMAL(10,2), -- 再订货点
    reorder_quantity DECIMAL(10,2), -- 再订货量
    
    -- 质量控制
    shelf_life_days INTEGER, -- 保质期（天）
    storage_temperature_min DECIMAL(5,2), -- 最低储存温度
    storage_temperature_max DECIMAL(5,2), -- 最高储存温度
    storage_instruction JSONB DEFAULT '{}', -- {"en": "Store in cool dry place", "zh": "存放在阴凉干燥处"}
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 库存移动类型
CREATE TABLE inventory_movement_kind (
    id TEXT PRIMARY KEY,
    name JSONB NOT NULL DEFAULT '{}', -- {"en": "Purchase In", "zh": "采购入库"}
    description JSONB DEFAULT '{}',
    affects_stock INTEGER NOT NULL CHECK (affects_stock IN (-1, 0, 1)), -- -1减少, 0不变, 1增加
    is_active BOOLEAN DEFAULT true
);

INSERT INTO inventory_movement_kind (id, name, affects_stock, description) VALUES 
('purchase_in', '{"en": "Purchase In", "zh": "采购入库", "ko": "구매 입고", "ja": "仕入入庫"}', 1, 
 '{"en": "Goods received from purchase order", "zh": "从采购订单接收货物"}'),
('transfer_out', '{"en": "Transfer Out", "zh": "调拨出库", "ko": "이동 출고", "ja": "移動出庫"}', -1,
 '{"en": "Transfer to another location", "zh": "调拨到其他位置"}'),
('transfer_in', '{"en": "Transfer In", "zh": "调拨入库", "ko": "이동 입고", "ja": "移動入庫"}', 1,
 '{"en": "Receive from another location", "zh": "从其他位置接收"}'),
('consumption', '{"en": "Production Consumption", "zh": "生产消耗", "ko": "생산 소비", "ja": "生産消費"}', -1,
 '{"en": "Consumed in production", "zh": "生产过程中消耗"}'),
('adjustment_increase', '{"en": "Stock Count Surplus", "zh": "盘点盈余", "ko": "재고 조사 잉여", "ja": "棚卸余剰"}', 1,
 '{"en": "Adjustment from stock count", "zh": "盘点调整增加"}'),
('adjustment_decrease', '{"en": "Stock Count Loss", "zh": "盘点亏损", "ko": "재고 조사 손실", "ja": "棚卸損失"}', -1,
 '{"en": "Adjustment from stock count", "zh": "盘点调整减少"}'),
('waste', '{"en": "Waste/Damage", "zh": "损耗报废", "ko": "폐기/손상", "ja": "廃棄/損傷"}', -1,
 '{"en": "Damaged or expired goods", "zh": "损坏或过期商品"}'),
('return_supplier', '{"en": "Return to Supplier", "zh": "退货给供应商", "ko": "공급업체 반품", "ja": "仕入先返品"}', -1,
 '{"en": "Return goods to supplier", "zh": "退货给供应商"}'),
('return_from_customer', '{"en": "Customer Return", "zh": "客户退货", "ko": "고객 반품", "ja": "顧客返品"}', 1,
 '{"en": "Goods returned by customer", "zh": "客户退回的商品"}');

-- 库存移动记录表（所有进出记录）
CREATE TABLE inventory_movement (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    landmark_id UUID NOT NULL REFERENCES landmark(id), -- 发生地点
    item_id UUID NOT NULL REFERENCES inventory_item(id),
    movement_kind_id TEXT NOT NULL REFERENCES inventory_movement_kind(id),
    
    -- 数量信息（正数=入库，负数=出库）
    quantity DECIMAL(10,2) NOT NULL,
    quantity_unit_id UUID NOT NULL REFERENCES unit_semantic(id),
    
    -- 成本信息（用于计算加权平均成本）
    unit_cost DECIMAL(10,4), -- 单位成本
    total_cost DECIMAL(12,2), -- 总成本 = quantity * unit_cost
    
    -- 批次和质量信息
    batch_number TEXT,
    production_date DATE,
    expiry_date DATE,
    
    -- 关联信息
    from_landmark_id UUID REFERENCES landmark(id), -- 来源位置（用于调拨）
    to_landmark_id UUID REFERENCES landmark(id), -- 目标位置（用于调拨）
    consumed_by_order_product_id UUID REFERENCES order_product(id), -- 被哪个订单产品消耗
    purchase_order_id UUID REFERENCES purchase_order(id), -- 关联采购单
    
    -- 其他信息
    reference_number TEXT, -- 单据号
    notes JSONB DEFAULT '{}', -- {"en": "Emergency purchase", "zh": "紧急采购"}
    created_by UUID REFERENCES profile(id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 库存快照表（实时库存状态）
CREATE TABLE inventory_stock (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    landmark_id UUID NOT NULL REFERENCES landmark(id),
    item_id UUID NOT NULL REFERENCES inventory_item(id),
    
    -- 库存数量（由trigger自动计算）
    auto_current_stock DECIMAL(10,2) DEFAULT 0,
    auto_reserved_stock DECIMAL(10,2) DEFAULT 0, -- 预留库存（已下单未出库）
    auto_available_stock DECIMAL(10,2) GENERATED ALWAYS AS (auto_current_stock - auto_reserved_stock) STORED,
    
    -- 批次信息（可选，用于需要批次管理的物品）
    batch_number TEXT,
    production_date DATE,
    expiry_date DATE,
    
    -- 成本信息（由trigger自动计算加权平均成本）
    auto_weighted_avg_cost DECIMAL(10,4), -- 加权平均成本
    auto_total_value DECIMAL(12,2) GENERATED ALWAYS AS (auto_current_stock * COALESCE(auto_weighted_avg_cost, 0)) STORED,
    
    -- 统计信息
    auto_last_movement_at TIMESTAMP,
    auto_stock_days INTEGER, -- 库存天数
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(landmark_id, item_id, COALESCE(batch_number, ''))
);

-- =============================================
-- 4. 配方系统 (recipe_*)
-- BOM (Bill of Materials) 物料清单
-- =============================================

-- 产品配方表（产品需要哪些原料）
CREATE TABLE recipe_material (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES product(id) ON DELETE CASCADE,
    item_id UUID NOT NULL REFERENCES inventory_item(id),
    quantity DECIMAL(10,4) NOT NULL, -- 标准用量
    quantity_unit_id UUID NOT NULL REFERENCES unit_semantic(id),
    is_essential BOOLEAN DEFAULT true, -- 是否必需（缺货时影响产品可售性）
    instruction JSONB DEFAULT '{}', -- {"en": "Add slowly while mixing", "zh": "边搅拌边慢慢加入"}
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(product_id, item_id)
);

-- 修改器对配方的影响
-- 例如：大杯需要1.5倍原料，加珍珠需要额外50g珍珠
CREATE TABLE recipe_modifier_impact (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipe_material_id UUID NOT NULL REFERENCES recipe_material(id) ON DELETE CASCADE,
    modifier_option_id UUID NOT NULL REFERENCES modifier_option(id),
    quantity_multiplier DECIMAL(6,4) DEFAULT 1.0, -- 用量倍数
    additional_quantity DECIMAL(10,4) DEFAULT 0, -- 额外用量
    notes JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(recipe_material_id, modifier_option_id)
);

-- 修改器直接添加的原料
-- 例如：选择"加珍珠"会直接添加珍珠原料
CREATE TABLE recipe_modifier_material (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    modifier_option_id UUID NOT NULL REFERENCES modifier_option(id),
    item_id UUID NOT NULL REFERENCES inventory_item(id),
    quantity DECIMAL(10,4) NOT NULL,
    quantity_unit_id UUID NOT NULL REFERENCES unit_semantic(id),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(modifier_option_id, item_id)
);

-- =============================================
-- 5. 供应商系统 (supplier_*)
-- =============================================

-- 供应商表
CREATE TABLE supplier (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brand_id UUID NOT NULL REFERENCES brand(id),
    name TEXT NOT NULL, -- 供应商名称通常不需要翻译
    code TEXT, -- 供应商编码
    contact_person TEXT,
    phone TEXT,
    email TEXT,
    address_id UUID REFERENCES address(id),
    
    -- 商务条款
    payment_terms JSONB DEFAULT '{}', -- {"en": "Net 30 days", "zh": "30天账期"}
    minimum_order_amount DECIMAL(10,2), -- 最小订货金额
    delivery_lead_days INTEGER, -- 交货周期（天）
    
    -- 评级和状态
    rating INTEGER CHECK (rating BETWEEN 1 AND 5),
    is_preferred BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    
    notes JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 供应商商品目录
CREATE TABLE supplier_catalog (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supplier_id UUID NOT NULL REFERENCES supplier(id),
    item_id UUID NOT NULL REFERENCES inventory_item(id),
    
    -- 供应商商品信息
    supplier_sku TEXT, -- 供应商的商品编码
    supplier_item_name TEXT, -- 供应商的商品名称（可能与我们的不同）
    
    -- 价格信息
    unit_price DECIMAL(10,4) NOT NULL,
    price_unit_id UUID NOT NULL REFERENCES unit_semantic(id), -- 计价单位
    currency_code TEXT DEFAULT 'USD',
    
    -- 订购要求
    min_order_quantity DECIMAL(10,2), -- 最小订购量
    order_unit_id UUID NOT NULL REFERENCES unit_semantic(id), -- 订购单位
    package_quantity DECIMAL(10,2), -- 包装规格（如一箱24瓶）
    package_description JSONB DEFAULT '{}', -- {"en": "24 bottles per case", "zh": "每箱24瓶"}
    
    -- 其他信息
    lead_time_days INTEGER, -- 该商品的交货期
    is_available BOOLEAN DEFAULT true,
    last_purchase_date DATE,
    last_purchase_price DECIMAL(10,4),
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(supplier_id, item_id)
);

-- =============================================
-- 6. 采购系统 (purchase_*)
-- =============================================

-- 采购订单表
CREATE TABLE purchase_order (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brand_id UUID NOT NULL REFERENCES brand(id),
    supplier_id UUID NOT NULL REFERENCES supplier(id),
    
    -- 订单信息
    order_number TEXT NOT NULL UNIQUE,
    order_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expected_delivery_date DATE,
    actual_delivery_date DATE,
    
    -- 金额信息
    subtotal DECIMAL(12,2), -- 商品小计
    tax_amount DECIMAL(12,2) DEFAULT 0,
    shipping_cost DECIMAL(12,2) DEFAULT 0,
    other_cost DECIMAL(12,2) DEFAULT 0, -- 其他费用
    total_amount DECIMAL(12,2), -- 总金额
    
    -- 支付信息
    currency_code TEXT DEFAULT 'USD',
    exchange_rate DECIMAL(10,6) DEFAULT 1,
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'partial', 'paid')),
    payment_due_date DATE,
    
    -- 状态
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'confirmed', 'shipped', 'received', 'cancelled')),
    
    -- 收货信息
    deliver_to_landmark_id UUID REFERENCES landmark(id),
    
    notes JSONB DEFAULT '{}',
    created_by UUID REFERENCES profile(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 采购订单明细
CREATE TABLE purchase_order_item (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    purchase_order_id UUID NOT NULL REFERENCES purchase_order(id) ON DELETE CASCADE,
    item_id UUID NOT NULL REFERENCES inventory_item(id),
    
    -- 订购信息
    quantity DECIMAL(10,2) NOT NULL,
    quantity_unit_id UUID NOT NULL REFERENCES unit_semantic(id),
    unit_price DECIMAL(10,4) NOT NULL,
    line_total DECIMAL(12,2), -- = quantity * unit_price
    
    -- 收货信息
    received_quantity DECIMAL(10,2) DEFAULT 0,
    received_date DATE,
    
    -- 批次信息（用于收货时记录）
    batch_number TEXT,
    production_date DATE,
    expiry_date DATE,
    
    notes JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- =============================================
-- 7. 成本系统 (cost_*)
-- =============================================

-- 成本类别表
CREATE TABLE cost_category (
    id TEXT PRIMARY KEY,
    name JSONB NOT NULL DEFAULT '{}', -- {"en": "Raw Materials", "zh": "原材料成本"}
    category_kind TEXT NOT NULL CHECK (category_kind IN ('direct', 'indirect', 'operating')),
    description JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true
);

INSERT INTO cost_category (id, name, category_kind, description) VALUES 
-- 直接成本
('material', '{"en": "Raw Materials", "zh": "原材料成本", "ko": "원재료 비용", "ja": "原材料コスト"}', 'direct',
 '{"en": "Direct material costs", "zh": "直接材料成本"}'),
('packaging', '{"en": "Packaging", "zh": "包装成本", "ko": "포장 비용", "ja": "包装コスト"}', 'direct',
 '{"en": "Packaging materials cost", "zh": "包装材料成本"}'),
('direct_labor', '{"en": "Direct Labor", "zh": "直接人工", "ko": "직접 인건비", "ja": "直接人件費"}', 'direct',
 '{"en": "Direct labor costs", "zh": "直接人工成本"}'),

-- 间接成本
('rent', '{"en": "Rent", "zh": "租金", "ko": "임대료", "ja": "家賃"}', 'indirect',
 '{"en": "Store and warehouse rent", "zh": "店铺和仓库租金"}'),
('utilities', '{"en": "Utilities", "zh": "水电费", "ko": "공과금", "ja": "光熱費"}', 'indirect',
 '{"en": "Electricity, water, gas", "zh": "电费、水费、燃气费"}'),
('depreciation', '{"en": "Depreciation", "zh": "折旧", "ko": "감가상각", "ja": "減価償却"}', 'indirect',
 '{"en": "Equipment depreciation", "zh": "设备折旧"}'),
('insurance', '{"en": "Insurance", "zh": "保险", "ko": "보험", "ja": "保険"}', 'indirect',
 '{"en": "Business insurance", "zh": "商业保险"}'),

-- 运营成本
('shipping', '{"en": "Shipping", "zh": "运输费用", "ko": "운송비", "ja": "輸送費"}', 'operating',
 '{"en": "Transportation and delivery costs", "zh": "运输和配送成本"}'),
('marketing', '{"en": "Marketing", "zh": "营销费用", "ko": "마케팅 비용", "ja": "マーケティング費用"}', 'operating',
 '{"en": "Marketing and promotion costs", "zh": "营销推广费用"}'),
('waste', '{"en": "Waste/Loss", "zh": "损耗", "ko": "손실", "ja": "廃棄損失"}', 'operating',
 '{"en": "Product waste and shrinkage", "zh": "产品损耗和损失"}');

-- 成本记录表
CREATE TABLE cost_entry (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brand_id UUID NOT NULL REFERENCES brand(id),
    landmark_id UUID REFERENCES landmark(id),
    cost_category_id TEXT NOT NULL REFERENCES cost_category(id),
    
    -- 金额信息
    amount DECIMAL(12,2) NOT NULL,
    currency_code TEXT DEFAULT 'USD',
    
    -- 时间信息
    cost_date DATE NOT NULL,
    period_start DATE, -- 周期性成本的开始日期
    period_end DATE, -- 周期性成本的结束日期
    
    -- 关联信息（使用多个nullable外键）
    purchase_order_id UUID REFERENCES purchase_order(id),
    payroll_calculation_id UUID REFERENCES payroll_calculations(id),
    supplier_id UUID REFERENCES supplier(id),
    
    -- 分摊信息
    is_allocated BOOLEAN DEFAULT false,
    allocation_method TEXT, -- 'by_volume', 'by_value', 'by_time'
    
    description JSONB DEFAULT '{}',
    reference_number TEXT, -- 凭证号
    created_by UUID REFERENCES profile(id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 成本分摊记录表
CREATE TABLE cost_allocation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cost_entry_id UUID NOT NULL REFERENCES cost_entry(id),
    
    -- 分摊目标（使用多个nullable外键）
    allocated_to_product_id UUID REFERENCES product(id),
    allocated_to_order_id UUID REFERENCES "order"(id),
    allocated_to_landmark_id UUID REFERENCES landmark(id),
    
    -- 分摊计算
    allocation_percentage DECIMAL(8,5), -- 分摊比例
    allocated_amount DECIMAL(12,2) NOT NULL, -- 分摊金额
    
    allocation_basis JSONB DEFAULT '{}', -- {"en": "Based on sales volume", "zh": "基于销售量"}
    created_at TIMESTAMP DEFAULT NOW()
);

-- =============================================
-- 8. 员工管理系统 (staff_*)
-- =============================================

-- 职位表
CREATE TABLE staff_position (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brand_id UUID NOT NULL REFERENCES brand(id),
    name JSONB NOT NULL DEFAULT '{}', -- {"en": "Barista", "zh": "调饮师"}
    code TEXT NOT NULL, -- 'barista', 'cashier', 'prep_cook'
    description JSONB DEFAULT '{}',
    responsibilities JSONB DEFAULT '[]', -- [{"en": "Prepare drinks", "zh": "制作饮品"}]
    base_hourly_rate DECIMAL(8,2), -- 基础时薪
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(brand_id, code)
);

-- 员工技能表（员工在各职位的熟练度）
CREATE TABLE staff_skill (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payroll_profile_id UUID NOT NULL REFERENCES payroll_profile(id),
    position_id UUID NOT NULL REFERENCES staff_position(id),
    skill_level INTEGER NOT NULL CHECK (skill_level BETWEEN 1 AND 5), -- 1-5熟练度
    certified_date DATE, -- 认证日期
    certified_by UUID REFERENCES profile(id),
    notes JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(payroll_profile_id, position_id)
);

-- 员工偏好表
CREATE TABLE staff_preference (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payroll_profile_id UUID NOT NULL REFERENCES payroll_profile(id) UNIQUE,
    
    -- 工作时间偏好
    preferred_shift TEXT[], -- ['morning', 'afternoon', 'evening']
    min_hours_per_week DECIMAL(5,2),
    max_hours_per_week DECIMAL(5,2),
    max_consecutive_days INTEGER DEFAULT 4, -- 最多连续工作天数
    
    -- 职位偏好
    preferred_position_ids UUID[], -- 偏好的职位
    avoided_position_ids UUID[], -- 避免的职位
    
    -- 其他偏好
    notes JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =============================================
-- 9. 排班系统 (schedule_*)
-- 通用设计，支持班次、课程、活动等
-- =============================================

-- 排班模板表
CREATE TABLE schedule_template (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brand_id UUID NOT NULL REFERENCES brand(id),
    name JSONB NOT NULL DEFAULT '{}', -- {"en": "Morning Shift", "zh": "早班"}
    template_kind TEXT NOT NULL CHECK (template_kind IN ('shift', 'event', 'course')),
    
    -- 时间设置
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    break_minutes INTEGER DEFAULT 0,
    
    -- 适用日期
    applicable_days INTEGER[], -- 1-7表示周一到周日
    
    description JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 排班表
CREATE TABLE schedule (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    landmark_id UUID NOT NULL REFERENCES landmark(id),
    template_id UUID REFERENCES schedule_template(id),
    
    -- 时间信息
    scheduled_date DATE NOT NULL,
    scheduled_start TIMESTAMP NOT NULL,
    scheduled_end TIMESTAMP NOT NULL,
    
    -- 人员需求（使用JSONB存储复杂需求）
    position_requirements JSONB DEFAULT '[]', -- [{position_id, required_count, skill_level}]
    
    -- 状态
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'completed', 'cancelled')),
    
    notes JSONB DEFAULT '{}',
    created_by UUID REFERENCES profile(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 排班分配表
CREATE TABLE schedule_assignment (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schedule_id UUID NOT NULL REFERENCES schedule(id) ON DELETE CASCADE,
    payroll_profile_id UUID NOT NULL REFERENCES payroll_profile(id),
    position_id UUID NOT NULL REFERENCES staff_position(id),
    
    -- 状态
    status TEXT DEFAULT 'assigned' CHECK (status IN ('assigned', 'confirmed', 'checked_in', 'completed', 'absent')),
    
    -- 实际时间（考勤用）
    actual_start TIMESTAMP,
    actual_end TIMESTAMP,
    break_start TIMESTAMP,
    break_end TIMESTAMP,
    
    -- 工资计算
    hourly_rate DECIMAL(8,2), -- 实际时薪（可能因技能等级调整）
    
    notes JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(schedule_id, payroll_profile_id)
);

-- 员工可用性表
CREATE TABLE schedule_availability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payroll_profile_id UUID NOT NULL REFERENCES payroll_profile(id),
    
    -- 时间范围
    start_datetime TIMESTAMP NOT NULL,
    end_datetime TIMESTAMP NOT NULL,
    
    -- 可用性
    availability_kind TEXT NOT NULL CHECK (availability_kind IN ('available', 'preferred', 'unavailable')),
    
    -- 重复规则（可选）
    recurrence_rule TEXT, -- RFC 5545 RRULE格式
    
    notes JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(payroll_profile_id, start_datetime, end_datetime)
);

-- =============================================
-- 10. 营收系统 (revenue_*)
-- =============================================

-- 营收来源类型
CREATE TABLE revenue_source_kind (
    id TEXT PRIMARY KEY,
    name JSONB NOT NULL DEFAULT '{}', -- {"en": "Sales Revenue", "zh": "销售收入"}
    description JSONB DEFAULT '{}'
);

INSERT INTO revenue_source_kind (id, name, description) VALUES 
('sales', '{"en": "Sales Revenue", "zh": "销售收入", "ko": "판매 수익", "ja": "売上収入"}',
 '{"en": "Revenue from product sales", "zh": "产品销售收入"}'),
('service', '{"en": "Service Revenue", "zh": "服务收入", "ko": "서비스 수익", "ja": "サービス収入"}',
 '{"en": "Revenue from services", "zh": "服务费收入"}'),
('commission', '{"en": "Commission", "zh": "佣金收入", "ko": "수수료 수익", "ja": "手数料収入"}',
 '{"en": "Commission income", "zh": "佣金收入"}'),
('tips', '{"en": "Tips", "zh": "小费收入", "ko": "팁 수익", "ja": "チップ収入"}',
 '{"en": "Customer tips", "zh": "顾客小费"}');

-- 营收记录表
CREATE TABLE revenue_entry (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    brand_id UUID NOT NULL REFERENCES brand(id),
    landmark_id UUID REFERENCES landmark(id),
    revenue_source_kind_id TEXT NOT NULL REFERENCES revenue_source_kind(id),
    
    -- 金额信息
    amount DECIMAL(12,2) NOT NULL,
    currency_code TEXT DEFAULT 'USD',
    
    -- 时间信息
    revenue_date DATE NOT NULL,
    revenue_datetime TIMESTAMP NOT NULL DEFAULT NOW(),
    
    -- 关联信息（使用多个nullable外键）
    order_id UUID REFERENCES "order"(id),
    event_id UUID REFERENCES event(id),
    
    -- 分配信息
    is_allocated BOOLEAN DEFAULT false,
    
    description JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- 营收分配表（分配给员工）
CREATE TABLE revenue_allocation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    revenue_entry_id UUID NOT NULL REFERENCES revenue_entry(id),
    payroll_profile_id UUID NOT NULL REFERENCES payroll_profile(id),
    
    -- 分配信息
    allocation_kind TEXT NOT NULL, -- 'hourly_bonus', 'commission', 'tips'
    allocated_amount DECIMAL(12,2) NOT NULL,
    
    -- 计算依据
    calculation_basis JSONB DEFAULT '{}', -- {"en": "Based on hours worked", "zh": "基于工作时数"}
    
    created_at TIMESTAMP DEFAULT NOW()
);

-- =============================================
-- 11. 扩展现有表
-- =============================================

-- 扩展order表支持奶茶店功能
ALTER TABLE "order" 
ADD COLUMN IF NOT EXISTS order_kind TEXT DEFAULT 'standard' 
    CHECK (order_kind IN ('standard', 'group', 'catering')),
ADD COLUMN IF NOT EXISTS channel TEXT DEFAULT 'pos' 
    CHECK (channel IN ('pos', 'app', 'web', 'third_party')),
ADD COLUMN IF NOT EXISTS delivery_method TEXT DEFAULT 'pickup' 
    CHECK (delivery_method IN ('pickup', 'dine_in', 'delivery')),
ADD COLUMN IF NOT EXISTS scheduled_time TIMESTAMP,
ADD COLUMN IF NOT EXISTS estimated_ready_time TIMESTAMP,
ADD COLUMN IF NOT EXISTS actual_ready_time TIMESTAMP,
ADD COLUMN IF NOT EXISTS group_order_id UUID REFERENCES "order"(id),
ADD COLUMN IF NOT EXISTS preparation_notes JSONB DEFAULT '{}', -- 改为JSONB支持多语言
ADD COLUMN IF NOT EXISTS customer_loyalty_points INTEGER DEFAULT 0;

-- 扩展order_product表
ALTER TABLE order_product
ADD COLUMN IF NOT EXISTS preparation_status TEXT DEFAULT 'pending' 
    CHECK (preparation_status IN ('pending', 'preparing', 'ready', 'served')),
ADD COLUMN IF NOT EXISTS estimated_prep_seconds INTEGER,
ADD COLUMN IF NOT EXISTS actual_prep_seconds INTEGER,
ADD COLUMN IF NOT EXISTS machine_code TEXT, -- 二维码给制作机器
ADD COLUMN IF NOT EXISTS special_instruction JSONB DEFAULT '{}'; -- 特殊要求

-- 订单产品选择的修改器
CREATE TABLE order_product_modifier (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_product_id UUID NOT NULL REFERENCES order_product(id) ON DELETE CASCADE,
    modifier_option_id UUID NOT NULL REFERENCES modifier_option(id),
    quantity INTEGER DEFAULT 1,
    price_adjustment DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- =============================================
-- 12. 触发器和函数（保持不变，因为是后台逻辑）
-- =============================================

-- 库存变动后自动更新库存快照
CREATE OR REPLACE FUNCTION update_inventory_stock_from_movement()
RETURNS TRIGGER AS $$
DECLARE
    v_stock_record inventory_stock;
    v_new_avg_cost DECIMAL(10,4);
BEGIN
    -- 查找或创建库存记录
    SELECT * INTO v_stock_record
    FROM inventory_stock
    WHERE landmark_id = NEW.landmark_id 
      AND item_id = NEW.item_id
      AND COALESCE(batch_number, '') = COALESCE(NEW.batch_number, '')
    FOR UPDATE;
    
    IF NOT FOUND THEN
        -- 创建新库存记录
        INSERT INTO inventory_stock (
            landmark_id, item_id, batch_number,
            auto_current_stock, auto_weighted_avg_cost,
            production_date, expiry_date
        ) VALUES (
            NEW.landmark_id, NEW.item_id, NEW.batch_number,
            NEW.quantity, NEW.unit_cost,
            NEW.production_date, NEW.expiry_date
        );
    ELSE
        -- 计算新的加权平均成本（仅入库时）
        IF NEW.quantity > 0 AND NEW.unit_cost IS NOT NULL THEN
            v_new_avg_cost := (
                (v_stock_record.auto_current_stock * COALESCE(v_stock_record.auto_weighted_avg_cost, 0)) +
                (NEW.quantity * NEW.unit_cost)
            ) / NULLIF(v_stock_record.auto_current_stock + NEW.quantity, 0);
        ELSE
            v_new_avg_cost := v_stock_record.auto_weighted_avg_cost;
        END IF;
        
        -- 更新库存
        UPDATE inventory_stock
        SET auto_current_stock = auto_current_stock + NEW.quantity,
            auto_weighted_avg_cost = v_new_avg_cost,
            auto_last_movement_at = NEW.created_at,
            updated_at = NOW()
        WHERE id = v_stock_record.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_inventory_stock
    AFTER INSERT ON inventory_movement
    FOR EACH ROW EXECUTE FUNCTION update_inventory_stock_from_movement();

-- 订单完成时自动扣减库存
CREATE OR REPLACE FUNCTION consume_inventory_for_order()
RETURNS TRIGGER AS $$
DECLARE
    v_product_modifier RECORD;
    v_recipe RECORD;
    v_modifier_material RECORD;
    v_total_quantity DECIMAL(10,4);
BEGIN
    -- 只处理状态变为ready的订单产品
    IF NEW.preparation_status = 'ready' AND OLD.preparation_status != 'ready' THEN
        
        -- 处理基础配方
        FOR v_recipe IN 
            SELECT rm.*, i.stock_unit_id
            FROM recipe_material rm
            JOIN inventory_item i ON rm.item_id = i.id
            WHERE rm.product_id = NEW.product_id
        LOOP
            v_total_quantity := v_recipe.quantity * NEW.quantity;
            
            -- 检查修改器对配方的影响
            FOR v_product_modifier IN
                SELECT mo.id, rmi.quantity_multiplier, rmi.additional_quantity
                FROM order_product_modifier opm
                JOIN modifier_option mo ON opm.modifier_option_id = mo.id
                JOIN recipe_modifier_impact rmi ON rmi.modifier_option_id = mo.id
                WHERE opm.order_product_id = NEW.id
                  AND rmi.recipe_material_id = v_recipe.id
            LOOP
                v_total_quantity := v_total_quantity * v_product_modifier.quantity_multiplier 
                                  + COALESCE(v_product_modifier.additional_quantity, 0);
            END LOOP;
            
            -- 创建库存消耗记录
            INSERT INTO inventory_movement (
                landmark_id, item_id, movement_kind_id,
                quantity, quantity_unit_id,
                consumed_by_order_product_id,
                notes
            ) VALUES (
                (SELECT landmark_id FROM "order" WHERE id = NEW.order_id),
                v_recipe.item_id, 'consumption',
                -v_total_quantity, v_recipe.quantity_unit_id,
                NEW.id,
                '{"en": "Product consumption", "zh": "产品制作消耗"}'::jsonb
            );
        END LOOP;
        
        -- 处理修改器直接添加的原料
        FOR v_modifier_material IN
            SELECT rmm.*
            FROM order_product_modifier opm
            JOIN recipe_modifier_material rmm ON opm.modifier_option_id = rmm.modifier_option_id
            WHERE opm.order_product_id = NEW.id
        LOOP
            INSERT INTO inventory_movement (
                landmark_id, item_id, movement_kind_id,
                quantity, quantity_unit_id,
                consumed_by_order_product_id,
                notes
            ) VALUES (
                (SELECT landmark_id FROM "order" WHERE id = NEW.order_id),
                v_modifier_material.item_id, 'consumption',
                -v_modifier_material.quantity * NEW.quantity, v_modifier_material.quantity_unit_id,
                NEW.id,
                '{"en": "Modifier consumption", "zh": "修改器原料消耗"}'::jsonb
            );
        END LOOP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_consume_inventory
    AFTER UPDATE ON order_product
    FOR EACH ROW EXECUTE FUNCTION consume_inventory_for_order();

-- 订单完成时创建营收记录和分配
CREATE OR REPLACE FUNCTION create_revenue_from_order()
RETURNS TRIGGER AS $$
DECLARE
    v_revenue_id UUID;
    v_schedule_assignment RECORD;
    v_total_hours DECIMAL(10,2);
    v_bonus_pool DECIMAL(12,2);
    v_bonus_rate DECIMAL(5,4) := 0.01; -- 1%奖金池
BEGIN
    -- 只处理刚完成的订单
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        
        -- 创建营收记录
        INSERT INTO revenue_entry (
            brand_id, landmark_id, revenue_source_kind_id,
            amount, revenue_date, revenue_datetime,
            order_id, description
        ) VALUES (
            (SELECT brand_id FROM landmark WHERE id = NEW.landmark_id),
            NEW.landmark_id, 'sales',
            NEW.total_amount, NEW.order_time::date, NEW.order_time,
            NEW.id, 
            jsonb_build_object(
                'en', 'Order revenue: ' || NEW.order_number,
                'zh', '订单收入: ' || NEW.order_number
            )
        ) RETURNING id INTO v_revenue_id;
        
        -- 计算奖金池
        v_bonus_pool := NEW.total_amount * v_bonus_rate;
        
        -- 获取当前在岗员工的总工时
        SELECT SUM(EXTRACT(EPOCH FROM (COALESCE(actual_end, NOW()) - actual_start)) / 3600)
        INTO v_total_hours
        FROM schedule_assignment sa
        JOIN schedule s ON sa.schedule_id = s.id
        WHERE s.landmark_id = NEW.landmark_id
          AND s.scheduled_date = CURRENT_DATE
          AND sa.status IN ('checked_in', 'completed')
          AND sa.actual_start <= NEW.order_time
          AND (sa.actual_end IS NULL OR sa.actual_end >= NEW.order_time);
        
        -- 按工时分配奖金
        IF v_total_hours > 0 THEN
            FOR v_schedule_assignment IN
                SELECT sa.payroll_profile_id,
                       EXTRACT(EPOCH FROM (COALESCE(sa.actual_end, NOW()) - sa.actual_start)) / 3600 as hours_worked
                FROM schedule_assignment sa
                JOIN schedule s ON sa.schedule_id = s.id
                WHERE s.landmark_id = NEW.landmark_id
                  AND s.scheduled_date = CURRENT_DATE
                  AND sa.status IN ('checked_in', 'completed')
                  AND sa.actual_start <= NEW.order_time
                  AND (sa.actual_end IS NULL OR sa.actual_end >= NEW.order_time)
            LOOP
                INSERT INTO revenue_allocation (
                    revenue_entry_id, payroll_profile_id,
                    allocation_kind, allocated_amount,
                    calculation_basis
                ) VALUES (
                    v_revenue_id, v_schedule_assignment.payroll_profile_id,
                    'hourly_bonus', v_bonus_pool * (v_schedule_assignment.hours_worked / v_total_hours),
                    jsonb_build_object(
                        'en', format('Hours ratio: %.2f/%.2f = %.2f%%', 
                                   v_schedule_assignment.hours_worked, v_total_hours, 
                                   (v_schedule_assignment.hours_worked / v_total_hours * 100)),
                        'zh', format('工时占比: %.2f/%.2f = %.2f%%', 
                                   v_schedule_assignment.hours_worked, v_total_hours, 
                                   (v_schedule_assignment.hours_worked / v_total_hours * 100))
                    )
                );
            END LOOP;
            
            -- 标记已分配
            UPDATE revenue_entry SET is_allocated = true WHERE id = v_revenue_id;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_create_revenue
    AFTER UPDATE ON "order"
    FOR EACH ROW EXECUTE FUNCTION create_revenue_from_order();

-- =============================================
-- 13. 成本分摊示例
-- =============================================

-- 计算产品成本视图
CREATE VIEW product_cost_analysis AS
WITH material_cost AS (
    -- 直接材料成本
    SELECT 
        p.id as product_id,
        p.name as product_name,
        SUM(rm.quantity * COALESCE(is.auto_weighted_avg_cost, 0)) as direct_material_cost
    FROM product p
    LEFT JOIN recipe_material rm ON p.id = rm.product_id
    LEFT JOIN inventory_item ii ON rm.item_id = ii.id
    LEFT JOIN inventory_stock is ON ii.id = is.item_id
    WHERE rm.is_essential = true
    GROUP BY p.id, p.name
),
allocated_cost AS (
    -- 已分摊的间接成本（月平均到日）
    SELECT 
        ca.allocated_to_product_id as product_id,
        SUM(ca.allocated_amount) / 30 as daily_allocated_cost
    FROM cost_allocation ca
    JOIN cost_entry ce ON ca.cost_entry_id = ce.id
    WHERE ca.allocated_to_product_id IS NOT NULL
      AND ce.cost_date >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY ca.allocated_to_product_id
)
SELECT 
    mc.product_id,
    mc.product_name,
    COALESCE(mc.direct_material_cost, 0) as material_cost,
    COALESCE(ac.daily_allocated_cost, 0) as allocated_cost,
    COALESCE(mc.direct_material_cost, 0) + COALESCE(ac.daily_allocated_cost, 0) as total_cost
FROM material_cost mc
LEFT JOIN allocated_cost ac ON mc.product_id = ac.product_id
ORDER BY total_cost DESC;

-- =============================================
-- 14. 索引优化
-- =============================================

-- 单位系统索引
CREATE INDEX idx_unit_semantic_brand ON unit_semantic(brand_id, is_active);
CREATE INDEX idx_unit_conversion_lookup ON unit_conversion(semantic_unit_id, base_unit_id);

-- 库存系统索引
CREATE INDEX idx_inventory_item_brand ON inventory_item(brand_id, is_active);
CREATE INDEX idx_inventory_movement_lookup ON inventory_movement(landmark_id, item_id, created_at);
CREATE INDEX idx_inventory_stock_lookup ON inventory_stock(landmark_id, item_id);
CREATE INDEX idx_inventory_stock_low ON inventory_stock(landmark_id, item_id) 
    WHERE auto_current_stock < (SELECT min_stock_threshold FROM inventory_item WHERE id = inventory_stock.item_id);

-- 配方系统索引
CREATE INDEX idx_recipe_material_product ON recipe_material(product_id);
CREATE INDEX idx_recipe_modifier_impact_lookup ON recipe_modifier_impact(recipe_material_id, modifier_option_id);

-- 订单系统索引
CREATE INDEX idx_order_product_modifier_lookup ON order_product_modifier(order_product_id);

-- 人员和排班索引
CREATE INDEX idx_staff_skill_lookup ON staff_skill(payroll_profile_id, position_id);
CREATE INDEX idx_schedule_lookup ON schedule(landmark_id, scheduled_date);
CREATE INDEX idx_schedule_assignment_lookup ON schedule_assignment(schedule_id, payroll_profile_id);

-- JSONB索引（支持多语言查询）
CREATE INDEX idx_inventory_item_name ON inventory_item USING GIN (name);
CREATE INDEX idx_modifier_group_name ON modifier_group USING GIN (name);
CREATE INDEX idx_modifier_option_name ON modifier_option USING GIN (name);

-- =============================================
-- 数据库设计说明
-- =============================================

/*
🏗️ 核心设计理念:

1. **国际化支持**
   - 所有用户可见文本使用JSONB存储多语言内容
   - 格式: {"en": "English", "zh": "中文", "ko": "한국어", "ja": "日本語"}
   - 应用层使用getLocalizedText(jsonb, locale)获取对应语言文本

2. **基础表 (Base Tables)**
   - profile → payroll_profile: 所有人员 → 员工子集
   - landmark: 所有位置（门店、仓库）
   - brand: 租户/品牌
   - order → order_product: 订单主表 → 订单明细

3. **扩展系统**
   - unit_*: 单位转换系统（语义单位↔科学单位）
   - inventory_*: 库存管理（物品、移动、快照）
   - modifier_*: 产品变体系统（甜度、温度、加料）
   - recipe_*: 配方系统（BOM物料清单）
   - supplier_* & purchase_*: 供应链管理
   - staff_* & schedule_*: 人员排班管理
   - cost_* & revenue_*: 财务管理

4. **关键设计特点**
   - 使用多个nullable外键替代reference_type模式
   - 强类型关系替代JSONB存储（除了国际化文本）
   - 触发器自动维护计算字段（auto_*前缀）
   - 支持批次管理和质量追踪
   - 灵活的单位转换系统
   - 通用的排班系统（支持班次、课程、活动）

5. **业务流程集成**
   - 订单 → 库存消耗 → 成本计算
   - 采购 → 库存入库 → 成本分摊
   - 排班 → 考勤 → 营收分配 → 工资计算
   - 配方 + 修改器 → 精确物料需求

6. **促销规则建议**
   - 使用product_price表存储不同价格策略
   - 创建promotion_rule表定义促销条件
   - 在结账时应用规则计算最优价格

7. **国际化最佳实践**
   - 系统生成的文本使用JSONB
   - 用户输入的商家名称等保持原样
   - 代码/标识符使用英文
   - UI显示时动态获取对应语言
*/