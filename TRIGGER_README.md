# Trigger.dev Integration with HanaWeb

This project uses [Trigger.dev](https://trigger.dev) to handle background tasks and workflows through the [triggerkit](https://github.com/EnoughXP/triggerkit) library, which enables seamless integration with SvelteKit server functions.

## Setup

The integration is already set up with the following components:

1. `trigger.config.ts` - Configuration for Trigger.dev
2. Vite plugin in `vite.config.ts` - Configured to scan server functions
3. Server-side functions in `src/lib/server` and `src/routes/**/triggers` directories

## Environment Variables

The following environment variables need to be set for the Trigger.dev integration to work:

```
TRIGGER_API_KEY=your_trigger_api_key
TRIGGER_API_URL=your_trigger_api_url (optional)
NOVU_SECRET_KEY=your_novu_key
PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_key
```

## Available Workflows

### Product Cancellation

The product cancellation workflow is triggered when a product is canceled in the admin interface. It:

1. Fetches relevant data from Supabase about the event, registrations, and brand
2. Sends cancellation notifications to all registered users via Novu
3. Returns a summary of the notification process

#### How to Use

The workflow is triggered automatically when submitting the product cancellation form. The code in `src/routes/private/request/product-cancel/+page.server.ts` sends an event to Trigger.dev, which then processes the request asynchronously:

```typescript
await client.sendEvent({
	name: 'product.cancelled',
	payload: {
		eventId,
		reasonId,
		comments,
		brandId: brand.id
	}
});
```

## Adding New Workflows

To add a new workflow:

1. Create server functions in either `src/lib/server` or in a `triggers` directory within the relevant route
2. Export the functions so they can be discovered by triggerkit
3. Import the functions in your Trigger.dev job definitions using `import { yourFunction } from 'virtual:triggerkit'`
4. Create a new job definition in `src/lib/server/triggers.ts` or create a new file
5. Trigger the job using `client.sendEvent()` from your SvelteKit routes

## Development

For local development, you'll need to set up a Trigger.dev dev environment:

1. Install the Trigger.dev CLI: `npm install -g @trigger.dev/cli`
2. Run the dev server: `npx trigger@latest dev`
3. Configure your app to point to the local Trigger.dev instance

## Troubleshooting

- Make sure all environment variables are properly set
- Check that the Trigger.dev CLI is running during development
- Verify that server functions are properly exported and can be discovered by triggerkit
- Review Trigger.dev logs for any errors during job execution
