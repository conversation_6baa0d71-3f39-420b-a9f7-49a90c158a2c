import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';
import { vite as vidstack } from 'vidstack/plugins';
import { triggerkit } from 'triggerkit';
import tailwindcss from '@tailwindcss/vite';
import { paraglideVitePlugin } from '@inlang/paraglide-js';

export default defineConfig({
	plugins: [
		tailwindcss(),
		vidstack({
			include: /private\/event\// // Only parse files in event directory
		}),
		paraglideVitePlugin({
			project: './project.inlang',
			outdir: './src/lib/paraglide',
			strategy: ['url', 'cookie', 'baseLocale']
		}),
		triggerkit({
			includeDirs: ['src/lib/server', 'src/routes/api/novu/workflows'],
			filePatterns: ['**/*.ts', '**/*.js']
		}),
		sveltekit()
	],
	server: {
		fs: {
			allow: ['.']
		},
		hmr: {
			timeout: 5000,
			overlay: true
		}
	}
});
