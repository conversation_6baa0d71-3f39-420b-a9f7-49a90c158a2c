import { defineConfig } from '@trigger.dev/sdk/v3';

export default defineConfig({
	project: 'hana-web',
	runtime: 'node',
	maxDuration: 300, // 300 seconds (5 minutes) max duration for tasks
	dirs: ['./trigger'], // Scan the /trigger directory for tasks
	onStart: async (payload, { ctx }) => {
		console.log(`Task ${ctx.task.id} started`);
	},
	onSuccess: async (payload, output, { ctx }) => {
		console.log(`Task ${ctx.task.id} completed successfully`);
	},
	onFailure: async (payload, error, { ctx }) => {
		console.error(`Task ${ctx.task.id} failed: ${error}`);
	}
});
