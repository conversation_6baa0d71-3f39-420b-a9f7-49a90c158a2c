-- Term Tables for Small Business Legal Documents System

-- Drop everything first to make this re-runnable
-- Drop functions
DROP FUNCTION IF EXISTS client_check_term_compliance(uuid, uuid) CASCADE;
DROP FUNCTION IF EXISTS term_agreement_update_currency() CASCADE;
DROP FUNCTION IF EXISTS term_version_ensure_single_current() CASCADE;

-- Drop triggers
DROP TRIGGER IF EXISTS term_agreement_update_currency_trigger ON term_version;
DROP TRIGGER IF EXISTS term_version_ensure_single_current_trigger ON term_version;

-- Drop policies
DROP POLICY IF EXISTS term_agreement_user_access ON term_agreement;
DROP POLICY IF EXISTS term_group_item_write_brand_owner ON term_group_item;
DROP POLICY IF EXISTS term_group_item_read ON term_group_item;
DROP POLICY IF EXISTS term_group_write_brand_owner ON term_group;
DROP POLICY IF EXISTS term_group_read_all ON term_group;
DROP POLICY IF EXISTS term_version_write_brand_owner ON term_version;
DROP POLICY IF EXISTS term_version_read_published ON term_version;
DROP POLICY IF EXISTS term_write_brand_owner ON term;
DROP POLICY IF EXISTS term_read_all ON term;

-- Drop indexes
DROP INDEX IF EXISTS idx_term_agreement_revoked;
DROP INDEX IF EXISTS idx_term_agreement_current;
DROP INDEX IF EXISTS idx_term_agreement_consumer;
DROP INDEX IF EXISTS idx_term_agreement_signer;
DROP INDEX IF EXISTS idx_term_version_published;
DROP INDEX IF EXISTS idx_term_version_current;
DROP INDEX IF EXISTS idx_term_group_brand;
DROP INDEX IF EXISTS idx_term_kind;
DROP INDEX IF EXISTS idx_term_brand;

-- Drop tables (in dependency order)
DROP TABLE IF EXISTS term_agreement CASCADE;
DROP TABLE IF EXISTS term_group_item CASCADE;
DROP TABLE IF EXISTS term_version CASCADE;
DROP TABLE IF EXISTS term CASCADE;
DROP TABLE IF EXISTS term_group CASCADE;
DROP TABLE IF EXISTS term_kind CASCADE;

-- Drop types
DROP TYPE IF EXISTS term_compliance_result CASCADE;
DROP TYPE IF EXISTS term_compliance_item CASCADE;

-- Now create everything fresh

-- 1. Term kind lookup table with grouping
CREATE TABLE term_kind (
    id text PRIMARY KEY,
    title jsonb NOT NULL,
    subtitle jsonb NOT NULL, -- Brief description shown to users
    group_id text NOT NULL, -- For UI sections like 'legal', 'health', 'financial', etc.
    icon text,
    semantic_order integer NOT NULL DEFAULT 0,
    created_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Comprehensive term kinds for small businesses
INSERT INTO term_kind (id, title, subtitle, group_id, semantic_order) VALUES
-- Core Legal Documents
('terms_of_service', 
 '{"en": "Terms of Service", "zh": "服务条款", "es": "Términos de Servicio"}', 
 '{"en": "General terms and conditions for service usage", "zh": "服务使用的一般条款和条件", "es": "Términos y condiciones generales para el uso del servicio"}', 
 'legal',
 10),

('privacy_policy', 
 '{"en": "Privacy Policy", "zh": "隐私政策", "es": "Política de Privacidad"}', 
 '{"en": "How we collect, use, and protect your personal data", "zh": "我们如何收集、使用和保护您的个人数据", "es": "Cómo recopilamos, usamos y protegemos sus datos personales"}', 
 'legal',
 20),

('liability_waiver', 
 '{"en": "Liability Waiver", "zh": "免责声明", "es": "Exención de Responsabilidad"}', 
 '{"en": "Release of liability and assumption of risk for physical activities", "zh": "身体活动的责任免除和风险承担", "es": "Liberación de responsabilidad y asunción de riesgo para actividades físicas"}', 
 'legal',
 30),

-- Health & Safety Documents
('health_questionnaire', 
 '{"en": "Health Questionnaire", "zh": "健康问卷", "es": "Cuestionario de Salud"}', 
 '{"en": "Medical history and health conditions disclosure", "zh": "病史和健康状况披露", "es": "Historial médico y divulgación de condiciones de salud"}', 
 'health',
 40),

('medical_consent', 
 '{"en": "Medical Treatment Consent", "zh": "医疗同意书", "es": "Consentimiento para Tratamiento Médico"}', 
 '{"en": "Authorization for emergency medical treatment", "zh": "紧急医疗授权", "es": "Autorización para tratamiento médico de emergencia"}', 
 'health',
 45),

('allergy_disclosure', 
 '{"en": "Allergy & Dietary Restrictions", "zh": "过敏与饮食限制", "es": "Alergias y Restricciones Dietéticas"}', 
 '{"en": "Disclosure of allergies and dietary requirements", "zh": "过敏和饮食要求披露", "es": "Divulgación de alergias y requisitos dietéticas"}', 
 'health',
 50),

('health_safety_waiver', 
 '{"en": "Health & Safety Waiver", "zh": "健康与安全免责", "es": "Exención de Salud y Seguridad"}', 
 '{"en": "Acknowledgment of health risks and safety protocols", "zh": "健康风险和安全协议确认", "es": "Reconocimiento de riesgos de salud y protocolos de seguridad"}', 
 'health',
 55),

-- Media & Privacy
('photo_release', 
 '{"en": "Photo/Video Release", "zh": "照片/视频使用授权", "es": "Autorización de Foto/Video"}', 
 '{"en": "Permission to use photos and videos for marketing", "zh": "营销用途的照片和视频使用许可", "es": "Permiso para usar fotos y videos con fines de marketing"}', 
 'media',
 60),

('social_media_consent', 
 '{"en": "Social Media Consent", "zh": "社交媒体同意书", "es": "Consentimiento para Redes Sociales"}', 
 '{"en": "Permission to share content on social media platforms", "zh": "在社交媒体平台分享内容的许可", "es": "Permiso para compartir contenido en plataformas de redes sociales"}', 
 'media',
 65),

-- Financial & Business Terms
('cancellation_policy', 
 '{"en": "Cancellation & Refund Policy", "zh": "取消和退款政策", "es": "Política de Cancelación y Reembolso"}', 
 '{"en": "Terms for cancellations, no-shows, and refunds", "zh": "取消、缺席和退款条款", "es": "Términos para cancelaciones, ausencias y reembolsos"}', 
 'financial',
 70),

('payment_terms', 
 '{"en": "Payment Terms", "zh": "付款条款", "es": "Términos de Pago"}', 
 '{"en": "Payment schedules, methods, and late fee policies", "zh": "付款时间表、方式和滞纳金政策", "es": "Calendarios de pago, métodos y políticas de cargos por mora"}', 
 'financial',
 75),

('membership_agreement', 
 '{"en": "Membership Agreement", "zh": "会员协议", "es": "Acuerdo de Membresía"}', 
 '{"en": "Terms and benefits of membership programs", "zh": "会员计划的条款和权益", "es": "Términos y beneficios de programas de membresía"}', 
 'financial',
 80),

('package_terms', 
 '{"en": "Package/Bundle Terms", "zh": "套餐条款", "es": "Términos de Paquetes"}', 
 '{"en": "Terms for class packages and service bundles", "zh": "课程套餐和服务包条款", "es": "Términos para paquetes de clases y servicios"}', 
 'financial',
 85),

-- Behavioral & Conduct
('code_of_conduct', 
 '{"en": "Code of Conduct", "zh": "行为准则", "es": "Código de Conducta"}', 
 '{"en": "Expected behavior and community guidelines", "zh": "预期行为和社区准则", "es": "Comportamiento esperado y pautas comunitarias"}', 
 'conduct',
 90),

('studio_rules', 
 '{"en": "Studio/Facility Rules", "zh": "场馆规则", "es": "Reglas del Estudio/Instalación"}', 
 '{"en": "Rules for facility usage and equipment care", "zh": "设施使用和设备维护规则", "es": "Reglas para el uso de instalaciones y cuidado del equipo"}', 
 'conduct',
 95),

('dress_code', 
 '{"en": "Dress Code Policy", "zh": "着装规定", "es": "Política de Código de Vestimenta"}', 
 '{"en": "Appropriate attire requirements for activities", "zh": "活动着装要求", "es": "Requisitos de vestimenta apropiada para actividades"}', 
 'conduct',
 100),

-- Minors & Guardians
('parental_consent', 
 '{"en": "Parental/Guardian Consent", "zh": "家长/监护人同意书", "es": "Consentimiento de Padres/Tutores"}', 
 '{"en": "Parent or guardian authorization for minors", "zh": "未成年人的家长或监护人授权", "es": "Autorización de padres o tutores para menores"}', 
 'minors',
 110),

('pickup_authorization', 
 '{"en": "Child Pickup Authorization", "zh": "儿童接送授权", "es": "Autorización para Recoger Niños"}', 
 '{"en": "Authorized persons for child pickup", "zh": "授权接送儿童人员", "es": "Personas autorizadas para recoger niños"}', 
 'minors',
 115),

('minor_medical_form', 
 '{"en": "Minor Medical Information", "zh": "未成年人医疗信息", "es": "Información Médica del Menor"}', 
 '{"en": "Emergency contacts and medical info for minors", "zh": "未成年人紧急联系人和医疗信息", "es": "Contactos de emergencia e información médica para menores"}', 
 'minors',
 120),

-- Equipment & Property
('equipment_rental', 
 '{"en": "Equipment Rental Agreement", "zh": "设备租赁协议", "es": "Acuerdo de Alquiler de Equipos"}', 
 '{"en": "Terms for renting equipment or gear", "zh": "设备或器材租赁条款", "es": "Términos para alquilar equipos o implementos"}', 
 'property',
 130),

('property_damage', 
 '{"en": "Property Damage Policy", "zh": "财产损坏政策", "es": "Política de Daños a la Propiedad"}', 
 '{"en": "Responsibility for damage to facilities or equipment", "zh": "设施或设备损坏责任", "es": "Responsabilidad por daños a instalaciones o equipos"}', 
 'property',
 135),

('locker_agreement', 
 '{"en": "Locker Rental Agreement", "zh": "储物柜租赁协议", "es": "Acuerdo de Alquiler de Casilleros"}', 
 '{"en": "Terms for locker rental and lost property", "zh": "储物柜租赁和失物条款", "es": "Términos para alquiler de casilleros y objetos perdidos"}', 
 'property',
 140),

-- Professional Services
('professional_service', 
 '{"en": "Professional Service Agreement", "zh": "专业服务协议", "es": "Acuerdo de Servicio Profesional"}', 
 '{"en": "Terms for professional services (therapy, consultation)", "zh": "专业服务条款（治疗、咨询）", "es": "Términos para servicios profesionales (terapia, consulta)"}', 
 'professional',
 150),

('treatment_consent', 
 '{"en": "Treatment Consent Form", "zh": "治疗同意书", "es": "Formulario de Consentimiento para Tratamiento"}', 
 '{"en": "Consent for specific treatments or procedures", "zh": "特定治疗或程序的同意", "es": "Consentimiento para tratamientos o procedimientos específicos"}', 
 'professional',
 155),

('consultation_agreement', 
 '{"en": "Consultation Agreement", "zh": "咨询协议", "es": "Acuerdo de Consulta"}', 
 '{"en": "Terms for consultation services", "zh": "咨询服务条款", "es": "Términos para servicios de consulta"}', 
 'professional',
 160),

-- Insurance & Indemnity
('insurance_waiver', 
 '{"en": "Insurance Waiver", "zh": "保险免责", "es": "Exención de Seguro"}', 
 '{"en": "Acknowledgment of personal insurance responsibility", "zh": "个人保险责任确认", "es": "Reconocimiento de responsabilidad de seguro personal"}', 
 'insurance',
 170),

('indemnity_agreement', 
 '{"en": "Indemnity Agreement", "zh": "赔偿协议", "es": "Acuerdo de Indemnización"}', 
 '{"en": "Agreement to indemnify business from claims", "zh": "免除企业索赔责任协议", "es": "Acuerdo para indemnizar al negocio de reclamos"}', 
 'insurance',
 175),

-- Specialized Services
('competition_waiver', 
 '{"en": "Competition/Event Waiver", "zh": "比赛/活动免责", "es": "Exención para Competencia/Evento"}', 
 '{"en": "Waiver for competitions and special events", "zh": "比赛和特殊活动免责声明", "es": "Exención para competencias y eventos especiales"}', 
 'events',
 180),

('travel_consent', 
 '{"en": "Travel Consent Form", "zh": "旅行同意书", "es": "Formulario de Consentimiento para Viajes"}', 
 '{"en": "Consent for off-site activities and travel", "zh": "场外活动和旅行同意", "es": "Consentimiento para actividades fuera del sitio y viajes"}', 
 'events',
 185),

('workshop_terms', 
 '{"en": "Workshop/Seminar Terms", "zh": "工作坊/研讨会条款", "es": "Términos de Taller/Seminario"}', 
 '{"en": "Terms for workshops and educational events", "zh": "工作坊和教育活动条款", "es": "Términos para talleres y eventos educativos"}', 
 'events',
 190),

-- General
('emergency_contact', 
 '{"en": "Emergency Contact Form", "zh": "紧急联系表", "es": "Formulario de Contacto de Emergencia"}', 
 '{"en": "Emergency contact information", "zh": "紧急联系信息", "es": "Información de contacto de emergencia"}', 
 'general',
 200),

('newsletter_consent', 
 '{"en": "Newsletter/Marketing Consent", "zh": "通讯/营销同意", "es": "Consentimiento para Boletín/Marketing"}', 
 '{"en": "Permission for email marketing and newsletters", "zh": "电子邮件营销和通讯许可", "es": "Permiso para marketing por correo y boletines"}', 
 'general',
 210),

('additional_terms', 
 '{"en": "Additional Terms & Conditions", "zh": "附加条款和条件", "es": "Términos y Condiciones Adicionales"}', 
 '{"en": "Specific terms for this service or program", "zh": "此服务或项目的特定条款", "es": "Términos específicos para este servicio o programa"}', 
 'general',
 999);

-- 2. Master term table
CREATE TABLE term (
    id uuid PRIMARY KEY DEFAULT generate_ulid(),
    brand_id uuid NOT NULL REFERENCES brand(id) ON DELETE CASCADE,
    
    -- Term identification
    title jsonb NOT NULL,
    term_kind_id text NOT NULL REFERENCES term_kind(id),
    
    -- Signature requirements (by default, checkbox is sufficient)
    require_signature_full boolean NOT NULL DEFAULT false,
    require_signature_initials boolean NOT NULL DEFAULT false,
    
    -- Metadata
    legal_jurisdiction text[] NOT NULL DEFAULT '{}',
    
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    creator_id uuid NOT NULL REFERENCES profile(id)
);

-- 3. Term versions
CREATE TABLE term_version (
    id uuid PRIMARY KEY DEFAULT generate_ulid(),
    term_id uuid NOT NULL REFERENCES term(id) ON DELETE CASCADE,
    
    -- Version information
    version_number text NOT NULL,
    version_date date NOT NULL DEFAULT CURRENT_DATE,
    
    -- Content
    content jsonb NOT NULL,
    change_summary jsonb,
    
    -- Status
    published_at timestamp with time zone,
    is_current boolean NOT NULL DEFAULT false,
    
    -- Legal metadata
    hash text NOT NULL,
    ip_address_created inet,
    
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    creator_id uuid NOT NULL REFERENCES profile(id),
    
    UNIQUE(term_id, version_number)
);

-- 4. Term group for organizing terms
CREATE TABLE term_group (
    id uuid PRIMARY KEY DEFAULT generate_ulid(),
    brand_id uuid NOT NULL REFERENCES brand(id) ON DELETE CASCADE,
    
    -- Group identification
    title jsonb NOT NULL,
    
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- 5. Term group items (with is_required moved here)
CREATE TABLE term_group_item (
    id uuid PRIMARY KEY DEFAULT generate_ulid(),
    term_group_id uuid NOT NULL REFERENCES term_group(id) ON DELETE CASCADE,
    term_id uuid NOT NULL REFERENCES term(id) ON DELETE CASCADE,
    
    -- Order and configuration
    semantic_order integer NOT NULL DEFAULT 0,
    is_required boolean NOT NULL DEFAULT true,
    
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    
    UNIQUE(term_group_id, term_id)
);

-- 6. Term agreements (with signer/consumer separation)
CREATE TABLE term_agreement (
    id uuid PRIMARY KEY DEFAULT generate_ulid(),
    
    -- References
    term_version_id uuid NOT NULL REFERENCES term_version(id),
    signer_profile_id uuid NOT NULL REFERENCES profile(id), -- Who signed
    consumer_profile_id uuid NOT NULL REFERENCES profile(id), -- For whom (can be same as signer)
    source_product_id uuid REFERENCES product(id), -- Just for legal tracking
    term_group_item_id uuid REFERENCES term_group_item(id), -- Track if it was required

    -- Signature data
    agreed_at timestamp with time zone NOT NULL DEFAULT now(),
    signature_full text, -- Base64 encoded full signature image
    signature_initials text, -- Base64 encoded initials image
    
    -- Track if this agreement is for current version
    is_current boolean NOT NULL DEFAULT true,
    
    -- Revocation support
    revoked_at timestamp with time zone,
    revocation_reason text,
    revoked_by uuid REFERENCES profile(id),
    
    -- Legal compliance data
    ip_address inet NOT NULL,
    user_agent text,
    browser_fingerprint text,
    
    -- Additional legal fields
    signer_full_name text,
    signer_email text,
    consumer_full_name text, -- Name of person agreement applies to
    consumer_email text,
    
    -- Relationship info (when signer != consumer)
    signer_consumer_relation text, -- 'parent', 'guardian', 'self', etc.
    
    UNIQUE(term_version_id, consumer_profile_id)
);

-- Create types for compliance check results
CREATE TYPE term_compliance_item AS (
    term_id uuid,
    term_group_item_id uuid,
    term_title jsonb,
    term_kind_id text,
    term_kind_title jsonb,
    term_kind_subtitle jsonb,
    term_require_signature_full boolean,
    term_require_signature_initials boolean,
    last_agreed_version text,
    current_version text
);

CREATE TYPE term_compliance_result AS (
    required_not_agreed term_compliance_item[],
    required_outdated term_compliance_item[],
    optional_not_agreed term_compliance_item[],
    optional_outdated term_compliance_item[],
    required_all_agreed boolean,
    all_agreed boolean
);

-- Create indexes
CREATE INDEX idx_term_brand ON term(brand_id);
CREATE INDEX idx_term_kind ON term(term_kind_id);
CREATE INDEX idx_term_group_brand ON term_group(brand_id);
CREATE INDEX idx_term_version_current ON term_version(term_id, is_current) WHERE is_current = true;
CREATE INDEX idx_term_version_published ON term_version(published_at) WHERE published_at IS NOT NULL;
CREATE INDEX idx_term_agreement_signer ON term_agreement(signer_profile_id);
CREATE INDEX idx_term_agreement_consumer ON term_agreement(consumer_profile_id);
CREATE INDEX idx_term_agreement_current ON term_agreement(consumer_profile_id, is_current) WHERE is_current = true;
CREATE INDEX idx_term_agreement_revoked ON term_agreement(revoked_at) WHERE revoked_at IS NOT NULL;

-- Enable RLS on all tables
ALTER TABLE term ENABLE ROW LEVEL SECURITY;
ALTER TABLE term_version ENABLE ROW LEVEL SECURITY;
ALTER TABLE term_group ENABLE ROW LEVEL SECURITY;
ALTER TABLE term_group_item ENABLE ROW LEVEL SECURITY;
ALTER TABLE term_agreement ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Term policies: brand owners can all, everyone can read
CREATE POLICY term_read_all ON term
    FOR SELECT
    USING (true);

CREATE POLICY term_write_brand_owner ON term
    FOR ALL
    USING (
        brand_id IN (
            SELECT id FROM brand WHERE owner_profile_id = auth.uid()
        )
    )
    WITH CHECK (
        brand_id IN (
            SELECT id FROM brand WHERE owner_profile_id = auth.uid()
        )
    );

-- Term version policies: brand owners can all, everyone can read published
CREATE POLICY term_version_read_published ON term_version
    FOR SELECT
    USING (published_at IS NOT NULL);

CREATE POLICY term_version_write_brand_owner ON term_version
    FOR ALL
    USING (
        term_id IN (
            SELECT t.id FROM term t
            JOIN brand b ON t.brand_id = b.id
            WHERE b.owner_profile_id = auth.uid()
        )
    )
    WITH CHECK (
        term_id IN (
            SELECT t.id FROM term t
            JOIN brand b ON t.brand_id = b.id
            WHERE b.owner_profile_id = auth.uid()
        )
    );

-- Term group policies: brand owners can all, everyone can read
CREATE POLICY term_group_read_all ON term_group
    FOR SELECT
    USING (true);

CREATE POLICY term_group_write_brand_owner ON term_group
    FOR ALL
    USING (
        brand_id IN (
            SELECT id FROM brand WHERE owner_profile_id = auth.uid()
        )
    )
    WITH CHECK (
        brand_id IN (
            SELECT id FROM brand WHERE owner_profile_id = auth.uid()
        )
    );

-- Term group item policies: inherit from term_group
CREATE POLICY term_group_item_read ON term_group_item
    FOR SELECT
    USING (true);

CREATE POLICY term_group_item_write_brand_owner ON term_group_item
    FOR ALL
    USING (
        term_group_id IN (
            SELECT tg.id FROM term_group tg
            JOIN brand b ON tg.brand_id = b.id
            WHERE b.owner_profile_id = auth.uid()
        )
    )
    WITH CHECK (
        term_group_id IN (
            SELECT tg.id FROM term_group tg
            JOIN brand b ON tg.brand_id = b.id
            WHERE b.owner_profile_id = auth.uid()
        )
    );

-- Term agreement policies: users can see agreements where they are signer or consumer, or agreements for their managed profiles
CREATE POLICY term_agreement_user_access ON term_agreement
    FOR ALL
    USING (
        signer_profile_id = auth.uid() 
        OR consumer_profile_id = auth.uid()
        OR consumer_profile_id IN (
            SELECT managed_profile_id FROM user_managed_profile WHERE id = auth.uid()
        )
        OR term_version_id IN (
            SELECT tv.id FROM term_version tv
            JOIN term t ON tv.term_id = t.id
            JOIN brand b ON t.brand_id = b.id
            WHERE b.owner_profile_id = auth.uid()
        )
    )
    WITH CHECK (
        signer_profile_id = auth.uid()
        OR signer_profile_id IN (
            SELECT id FROM profile WHERE id = auth.uid()
            AND consumer_profile_id IN (
                SELECT managed_profile_id FROM user_managed_profile WHERE id = auth.uid()
            )
        )
    );

-- Trigger to ensure only one current version per term
CREATE OR REPLACE FUNCTION term_version_ensure_single_current()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_current = true THEN
        UPDATE term_version 
        SET is_current = false 
        WHERE term_id = NEW.term_id 
        AND id != NEW.id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER term_version_ensure_single_current_trigger
BEFORE INSERT OR UPDATE ON term_version
FOR EACH ROW
EXECUTE FUNCTION term_version_ensure_single_current();

-- Trigger to update term_agreement.is_current when term version changes
CREATE OR REPLACE FUNCTION term_agreement_update_currency()
RETURNS TRIGGER AS $$
BEGIN
    -- If a version is being set as current
    IF NEW.is_current = true AND OLD.is_current = false THEN
        -- Mark all agreements for old versions as not current
        UPDATE term_agreement ta
        SET is_current = false
        WHERE ta.term_version_id IN (
            SELECT id FROM term_version 
            WHERE term_id = NEW.term_id 
            AND id != NEW.id
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER term_agreement_update_currency_trigger
AFTER UPDATE OF is_current ON term_version
FOR EACH ROW
WHEN (NEW.is_current IS DISTINCT FROM OLD.is_current)
EXECUTE FUNCTION term_agreement_update_currency();

-- Helper function to check if user (as consumer) has agreed to all required terms
CREATE OR REPLACE FUNCTION client_check_term_compliance(
    input_consumer_profile_id uuid,
    input_term_group_id uuid
) RETURNS term_compliance_result AS $$
DECLARE
    var_result term_compliance_result;
    var_required_not_agreed term_compliance_item[];
    var_required_outdated term_compliance_item[];
    var_optional_not_agreed term_compliance_item[];
    var_optional_outdated term_compliance_item[];
    var_required_all_agreed boolean;
    var_all_agreed boolean;
    var_required_count integer;
    var_required_agreed_count integer;
    var_optional_count integer;
    var_optional_agreed_count integer;
BEGIN
    -- First, check if all required terms are agreed (most common case)
    SELECT 
        COUNT(*) FILTER (WHERE tgi.is_required = true),
        COUNT(*) FILTER (WHERE tgi.is_required = true AND ta.id IS NOT NULL AND ta.is_current = true)
    INTO var_required_count, var_required_agreed_count
    FROM term_group_item tgi
    JOIN term t ON tgi.term_id = t.id
    JOIN term_version tv ON t.id = tv.term_id AND tv.is_current = true
    LEFT JOIN term_agreement ta ON tv.id = ta.term_version_id 
        AND ta.consumer_profile_id = input_consumer_profile_id
        AND ta.revoked_at IS NULL
    WHERE tgi.term_group_id = input_term_group_id;
    
    var_required_all_agreed := var_required_count = var_required_agreed_count;
    
    -- If all required terms are agreed, check optional terms
    IF var_required_all_agreed THEN
        SELECT 
            COUNT(*) FILTER (WHERE tgi.is_required = false),
            COUNT(*) FILTER (WHERE tgi.is_required = false AND ta.id IS NOT NULL AND ta.is_current = true)
        INTO var_optional_count, var_optional_agreed_count
        FROM term_group_item tgi
        JOIN term t ON tgi.term_id = t.id
        JOIN term_version tv ON t.id = tv.term_id AND tv.is_current = true
        LEFT JOIN term_agreement ta ON tv.id = ta.term_version_id 
            AND ta.consumer_profile_id = input_consumer_profile_id
            AND ta.revoked_at IS NULL
        WHERE tgi.term_group_id = input_term_group_id;
        
        var_all_agreed := var_optional_count = var_optional_agreed_count;
    ELSE
        var_all_agreed := false;
    END IF;
    
    -- If everything is agreed (common case), return early with empty arrays
    IF var_all_agreed THEN
        var_result.required_not_agreed := '{}'::term_compliance_item[];
        var_result.required_outdated := '{}'::term_compliance_item[];
        var_result.optional_not_agreed := '{}'::term_compliance_item[];
        var_result.optional_outdated := '{}'::term_compliance_item[];
        var_result.required_all_agreed := true;
        var_result.all_agreed := true;
        RETURN var_result;
    END IF;
    
    -- Only run detailed queries if some terms are not agreed
    -- Get required terms not agreed
    SELECT array_agg(
        ROW(
            t.id,
            tgi.id,
            t.title,
            t.term_kind_id,
            tk.title,
            tk.subtitle,
            t.require_signature_full,
            t.require_signature_initials,
            NULL::text,  -- last_agreed_version is NULL for never agreed
            tv_current.version_number
        )::term_compliance_item
    ) INTO var_required_not_agreed
    FROM term_group_item tgi
    JOIN term t ON tgi.term_id = t.id
    JOIN term_kind tk ON t.term_kind_id = tk.id
    JOIN term_version tv_current ON t.id = tv_current.term_id AND tv_current.is_current = true
    WHERE tgi.term_group_id = input_term_group_id
    AND tgi.is_required = true
    AND NOT EXISTS (
        SELECT 1 FROM term_agreement ta
        WHERE ta.term_version_id = tv_current.id
        AND ta.consumer_profile_id = input_consumer_profile_id
        AND ta.revoked_at IS NULL
    )
    AND NOT EXISTS (
        -- Never agreed to any version
        SELECT 1 FROM term_agreement ta2
        JOIN term_version tv2 ON ta2.term_version_id = tv2.id
        WHERE tv2.term_id = t.id
        AND ta2.consumer_profile_id = input_consumer_profile_id
        AND ta2.revoked_at IS NULL
    );
    
    -- Get required terms that are agreed but outdated
    SELECT array_agg(
        ROW(
            t.id,
            tgi.id,
            t.title,
            t.term_kind_id,
            tk.title,
            tk.subtitle,
            t.require_signature_full,
            t.require_signature_initials,
            (SELECT tv_old.version_number 
             FROM term_agreement ta_old 
             JOIN term_version tv_old ON ta_old.term_version_id = tv_old.id 
             WHERE tv_old.term_id = t.id 
             AND ta_old.consumer_profile_id = input_consumer_profile_id 
             AND ta_old.is_current = false 
             AND ta_old.revoked_at IS NULL
             ORDER BY ta_old.agreed_at DESC
             LIMIT 1),
            tv_current.version_number
        )::term_compliance_item
    ) INTO var_required_outdated
    FROM term_group_item tgi
    JOIN term t ON tgi.term_id = t.id
    JOIN term_kind tk ON t.term_kind_id = tk.id
    JOIN term_version tv_current ON t.id = tv_current.term_id AND tv_current.is_current = true
    WHERE tgi.term_group_id = input_term_group_id
    AND tgi.is_required = true
    AND EXISTS (
        SELECT 1 FROM term_agreement ta
        JOIN term_version tv_old ON ta.term_version_id = tv_old.id
        WHERE tv_old.term_id = t.id
        AND ta.consumer_profile_id = input_consumer_profile_id
        AND ta.is_current = false
        AND ta.revoked_at IS NULL
    )
    AND NOT EXISTS (
        SELECT 1 FROM term_agreement ta2
        WHERE ta2.term_version_id = tv_current.id
        AND ta2.consumer_profile_id = input_consumer_profile_id
        AND ta2.revoked_at IS NULL
    );
    
    -- Only check optional terms if needed
    IF NOT var_required_all_agreed THEN
        var_optional_not_agreed := '{}'::term_compliance_item[];
        var_optional_outdated := '{}'::term_compliance_item[];
    ELSE
        -- Get optional terms not agreed
        SELECT array_agg(
            ROW(
                t.id,
                tgi.id,
                t.title,
                t.term_kind_id,
                tk.title,
                tk.subtitle,
                t.require_signature_full,
                t.require_signature_initials,
                NULL::text,  -- last_agreed_version is NULL for never agreed
                tv_current.version_number
            )::term_compliance_item
        ) INTO var_optional_not_agreed
        FROM term_group_item tgi
        JOIN term t ON tgi.term_id = t.id
        JOIN term_kind tk ON t.term_kind_id = tk.id
        JOIN term_version tv_current ON t.id = tv_current.term_id AND tv_current.is_current = true
        WHERE tgi.term_group_id = input_term_group_id
        AND tgi.is_required = false
        AND NOT EXISTS (
            SELECT 1 FROM term_agreement ta
            WHERE ta.term_version_id = tv_current.id
            AND ta.consumer_profile_id = input_consumer_profile_id
            AND ta.revoked_at IS NULL
        )
        AND NOT EXISTS (
            -- Never agreed to any version
            SELECT 1 FROM term_agreement ta2
            JOIN term_version tv2 ON ta2.term_version_id = tv2.id
            WHERE tv2.term_id = t.id
            AND ta2.consumer_profile_id = input_consumer_profile_id
            AND ta2.revoked_at IS NULL
        );
        
        -- Get optional terms that are agreed but outdated
        SELECT array_agg(
            ROW(
                t.id,
                tgi.id,
                t.title,
                t.term_kind_id,
                tk.title,
                tk.subtitle,
                t.require_signature_full,
                t.require_signature_initials,
                (SELECT tv_old.version_number 
                 FROM term_agreement ta_old 
                 JOIN term_version tv_old ON ta_old.term_version_id = tv_old.id 
                 WHERE tv_old.term_id = t.id 
                 AND ta_old.consumer_profile_id = input_consumer_profile_id 
                 AND ta_old.is_current = false 
                 AND ta_old.revoked_at IS NULL
                 ORDER BY ta_old.agreed_at DESC
                 LIMIT 1),
                tv_current.version_number
            )::term_compliance_item
        ) INTO var_optional_outdated
        FROM term_group_item tgi
        JOIN term t ON tgi.term_id = t.id
        JOIN term_kind tk ON t.term_kind_id = tk.id
        JOIN term_version tv_current ON t.id = tv_current.term_id AND tv_current.is_current = true
        WHERE tgi.term_group_id = input_term_group_id
        AND tgi.is_required = false
        AND EXISTS (
            SELECT 1 FROM term_agreement ta
            JOIN term_version tv_old ON ta.term_version_id = tv_old.id
            WHERE tv_old.term_id = t.id
            AND ta.consumer_profile_id = input_consumer_profile_id
            AND ta.is_current = false
            AND ta.revoked_at IS NULL
        )
        AND NOT EXISTS (
            SELECT 1 FROM term_agreement ta2
            WHERE ta2.term_version_id = tv_current.id
            AND ta2.consumer_profile_id = input_consumer_profile_id
            AND ta2.revoked_at IS NULL
        );
    END IF;
    
    -- Build result
    var_result.required_not_agreed := COALESCE(var_required_not_agreed, '{}'::term_compliance_item[]);
    var_result.required_outdated := COALESCE(var_required_outdated, '{}'::term_compliance_item[]);
    var_result.optional_not_agreed := COALESCE(var_optional_not_agreed, '{}'::term_compliance_item[]);
    var_result.optional_outdated := COALESCE(var_optional_outdated, '{}'::term_compliance_item[]);
    var_result.required_all_agreed := var_required_all_agreed;
    var_result.all_agreed := var_all_agreed;
    
    RETURN var_result;
END;
$$ LANGUAGE plpgsql; 