{"name": "class-registration", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite --host --port 8888", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check .", "supabase": "supabase gen types typescript --project-id tilepcwykspsgbllptbf > src/lib/supabase/database.types.ts", "dev:clean": "rimraf .svelte-kit && npm run dev"}, "devDependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/svelte": "^2.1.12", "@event-calendar/core": "^4.4.0", "@event-calendar/interaction": "^3.12.0", "@event-calendar/list": "^3.12.0", "@event-calendar/time-grid": "^3.12.0", "@exodus/schemasafe": "^1.3.0", "@iconify/icons-lucide": "^1.2.135", "@iconify/svelte": "^5.0.0", "@inlang/paraglide-js": "2.1.0", "@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.515.0", "@sinclair/typebox": "^0.34.35", "@sveltejs/adapter-vercel": "^5.7.2", "@sveltejs/kit": "^2.21.5", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@tailwindcss/vite": "^4.1.10", "@types/luxon": "^3.6.2", "@types/node": "^24.0.1", "@types/uuid": "^10.0.0", "@typeschema/class-validator": "^0.3.0", "@vinejs/vine": "^3.0.1", "ai": "^4.3.16", "arktype": "^2.1.20", "bits-ui": "2.8.8", "class-validator": "^0.14.2", "clsx": "^2.1.1", "embla-carousel-svelte": "^8.6.0", "formsnap": "2.0.1", "joi": "^17.13.3", "layerchart": "^2.0.0-next.18", "mode-watcher": "^1.0.8", "paneforge": "1.0.0-next.5", "postcss": "^8.5.5", "prettier": "^3.5.3", "prettier-plugin-sql": "^0.19.1", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.12", "shadcn-svelte": "1.0.3", "superstruct": "^2.0.2", "svelte": "^5.34.3", "svelte-check": "^4.2.1", "svelte-render-scan": "^1.1.0", "svelte-sonner": "^1.0.5", "sveltekit-superforms": "^2.26.1", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.10", "triggerkit": "^2.0.1", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "valibot": "^1.1.0", "vaul-svelte": "1.0.0-next.7", "vite": "^6.3.5", "yup": "^1.6.1", "zod": "^3.25.64"}, "dependencies": {"@keycloakify/svelte-email": "^0.0.6", "@mapbox/search-js-web": "^1.1.0", "@mastra/client-js": "^0.10.4", "@novu/api": "^1.3.0", "@novu/framework": "^2.6.7", "@pdfme/pdf-lib": "^1.18.4", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/table-core": "^8.21.3", "@trigger.dev/sdk": "^3.3.17", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "fontkit": "^2.0.4", "luxon": "^3.6.1", "marked": "^15.0.12", "media-icons": "^1.1.5", "openai": "^5.3.0", "stripe": "^18.2.1", "uuid": "^11.1.0", "vidstack": "^1.12.13"}}